package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"
	"io/ioutil"
	"math/rand"
	"os"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"

	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
)

var (
	configFile = flag.String("config", "id.json", "config file")
)

func GetRecallConfig(content string) *apollo.RecallConfig {
	newCfg := apollo.SearchRecallConfig{}
	err := json.Unmarshal([]byte(content), &newCfg)
	if err != nil {
		return nil
	}

	// 初始化一个空的召回配置
	tmpRecallConfig := apollo.RecallConfig{}
	tmpRecallConfig.DishRecalls = make([]*apollo.StoreRecallConfig, 0)

	for _, recallConfig := range newCfg.RecallConfig.StoreRecalls {
		tmpRecallConfig.StoreRecalls = append(tmpRecallConfig.StoreRecalls, recallConfig)
	}
	for _, recallConfig := range newCfg.RecallConfig.DishRecalls {
		tmpRecallConfig.DishRecalls = append(tmpRecallConfig.DishRecalls, recallConfig)
	}
	tmpRecallConfig.Common = newCfg.RecallConfig.Common
	tmpRecallConfig.DishCommon = newCfg.RecallConfig.DishCommon
	return &tmpRecallConfig
}

func main() {
	flag.Parse()

	// context
	ctx := context.Background()

	// 打开json文件
	jsonFile, err := os.Open(*configFile)

	// 最好要处理以下错误
	if err != nil {
		fmt.Println(err)
	}

	// 要记得关闭
	defer jsonFile.Close()

	byteValue, _ := ioutil.ReadAll(jsonFile)

	unsettled_store.NewQueryUnsettledStoreDao()

	// 加载配置
	config := GetRecallConfig(string(byteValue))
	fmt.Println(config)

	// 创建req
	req := createReq()

	// 创建 traceInfo
	traceInfo := createRandomTraceInfo(req)

	// 数据池
	dataSourceMap := predata.InitDataSourceMap(ctx, traceInfo)
	dataSourceParameters := parse.GetDataSourceParams(dataSourceMap)
	traceInfo.RecallConfigurationDataParams = dataSourceParameters
	traceInfo.RecallConfigurationDataSource = dataSourceMap

	// 统计数字
	totalCnt := 0
	noDiffCnt := 0
	diffCnt := 0

	for i := 0; i < 10000; i++ {
		totalCnt += 1
		if compareDsl(ctx, i, traceInfo, config, req) {
			noDiffCnt += 1
		} else {
			diffCnt += 1
		}
	}

	fmt.Println("totalCnt:", totalCnt)
	fmt.Println("noDiffCnt:", noDiffCnt)
	fmt.Println("diffCnt:", diffCnt)
}

func compareDsl(ctx context.Context, index int, traceInfo *traceinfo.TraceInfo, config *apollo.RecallConfig, req *foodalgo_search.SearchRequest) bool {

	storeIds := []uint64{1, 2, 3}
	configDsl := buildDishRecall(ctx, traceInfo, config, req, storeIds)

	// original es
	searchReq := model.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
	dishReq := searchReq.ToDishSecondaryRecallNew(traceInfo, storeIds)
	normalDsl := dishReq.DslString(ctx)

	if configDsl == normalDsl {
		return true
	} else {
		fmt.Println(fmt.Sprintf("########## %d original dsl ##########", index))
		fmt.Println(dishReq.DslString(ctx))
		fmt.Println(fmt.Sprintf("########## %d configuration dsl ##########", index))
		fmt.Println(configDsl)
		fmt.Println(fmt.Sprintf("########## %d traceInfo ##########", index))
		traceInfoStr, _ := json.Marshal(traceInfo)
		fmt.Println(string(traceInfoStr))
		return false
	}
}

func createReq() *foodalgo_search.SearchRequest {
	req := &foodalgo_search.SearchRequest{
		Keyword:   proto.String("mcdonalds"),
		Longitude: proto.Float32(103.743),
		Latitude:  proto.Float32(1.49785),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			IsPreferredMerchant: proto.Bool(true),
		},
		AbTest:   proto.String("foodsch.recall=group_yy"),
		SortType: foodalgo_search.SearchRequest_Relevance.Enum(),
	}
	return req
}

// 定义 QueryStoreIntention 的随机选项
var queryStoreOptions = []string{
	"",                 // 空字符串
	"single_word",      // 单词字符串
	"double word",      // 双词字符串
	"triple word here", // 三词字符串
}

var queryDishOptions = []string{
	"",                                // 空字符串
	"nasi hainam",                     // 单词字符串
	"double words",                    // 双词字符串
	"triple words for dish intention", // 三词字符串
}

// 定义 Segments 的随机选项
var segmentsOptions = [][]string{
	{"a"},           // 单元素切片
	{"a", "b"},      // 双元素切片
	{"a", "b", "c"}, // 三元素切片
}

// 定义 MaxWordSegments 的随机选项
var maxWordSegmentsOptions = [][]string{
	{},              // 空切片
	{"a"},           // 单元素切片
	{"a", "b"},      // 双元素切片
	{"a", "b", "c"}, // 三元素切片
}

// 定义 StoreIntents 的随机选项
var storeIntentsOptions = [][]string{
	{},                          // 空切片
	{"rice"},                    // 单元素切片
	{"rice", "beef"},            // 双元素切片
	{"rice", "beef", "chicken"}, // 三元素切片
}

func createRandomTraceInfo(req *foodalgo_search.SearchRequest) *traceinfo.TraceInfo {

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	var traceInfo = &traceinfo.TraceInfo{
		QueryKeyword:  req.GetKeyword(),
		OriginKeyword: req.GetKeyword(),
		QPResult: &traceinfo.QPResult{
			ProbFilterCategoryIntentions: []*qp.CategoryIntention{
				{Level1Name: proto.String("mcdonalds"), Level2Name: proto.String("fried chicken")},
				{Level1Name: proto.String("mcdonalds"), Level2Name: proto.String("burger")},
			},
			Segments:            segmentsOptions[rand.Intn(len(segmentsOptions))],
			MaxWordSegments:     maxWordSegmentsOptions[rand.Intn(len(maxWordSegmentsOptions))],
			StoreIntents:        storeIntentsOptions[rand.Intn(len(storeIntentsOptions))],
			OtherSegments:       &traceinfo.OtherSegments{},
			QueryStoreIntention: queryStoreOptions[rand.Intn(len(queryStoreOptions))],
			QueryDishIntention:  queryDishOptions[rand.Intn(len(queryDishOptions))],
			NerResult: []*qp.Ner{
				{
					Token:   proto.String("rice"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("ice coffee"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("kfc"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("mcdonald's"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("GreatWall No.1"),
					NerType: []qp.NERType{qp.NERType_LOCATION},
				},
				{
					Token:   proto.String("Sorry Fatpo"),
					NerType: []qp.NERType{qp.NERType_UNKNOWN},
				},
			},
			RewriteNerResult: [][]*qp.Ner{
				{
					{
						Token:   proto.String("kfc"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("mcdonald's"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("GreatWall No.1"),
						NerType: []qp.NERType{qp.NERType_LOCATION},
					},
					{
						Token:   proto.String("Sorry Fatpo"),
						NerType: []qp.NERType{qp.NERType_UNKNOWN},
					},
				},
			},
			QueryStringList: []traceinfo.QueryStringItem{
				{
					Fields: []string{"store_name^1.000000", "real_store_name^1.000000", "branch_name^0.100000",
						"main_category.level1_name.keyword^0.600000",
						"main_category.level2_name.keyword^0.600000",
						"sub_category.level1_name.keyword^0.600000",
						"sub_category.level2_name.keyword^0.600000",
						"dish_name^0.800000"},
					Query: "bakso^0.8489",
				},
				{
					Fields: []string{"store_name^1.000000",
						"real_store_name^1.000000",
						"branch_name^0.100000",
						"main_category.level1_name.keyword^0.600000",
						"main_category.level2_name.keyword^0.600000",
						"sub_category.level1_name.keyword^0.600000",
						"sub_category.level2_name.keyword^0.600000",
						"dish_name^0.200000"},
					Query: "pak^0.0715 muh^0.0795",
				},
			},
			QueryTag: []string{"a", "b"},
		},
		OptIntervention: &traceinfo.OptIntervention{
			IsStoreInterventionRecall:    true,
			InterventionRecallBrandID:    []uint64{1, 2, 3},
			InterventionRecallMerchantID: []uint64{1, 2, 3},
		},
		IsDebug:         true,
		UnsettledStores: &traceinfo.UnsettledStoreInfo{},
		UserContext:     &traceinfo.UserContext{},
		PredictConfig:   &traceinfo.PredictConfig{},
	}
	return traceInfo
}

func buildDishRecall(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.RecallConfig, req *foodalgo_search.SearchRequest, storeIds []uint64) string {
	if recallConfig == nil {
		fmt.Println("no recallConfig, exit")
		return ""
	}

	if len(recallConfig.DishRecalls) == 0 {
		fmt.Println("no dishRecalls, exit")
		return ""
	}

	recallConfigs := recallConfig.DishRecalls
	for i := range recallConfigs {
		q := parse.GenDishRecallQuery(ctx, traceInfo, recallConfig.DishCommon, recallConfigs[i], storeIds)
		if q == nil {
			fmt.Println("dishRecall", recallConfigs[i].RecallName, "get a nil query")
			continue
		}
		if traceInfo.IsDebug == false {
			q.FilterPath = []string{"aggregations"}
			q.SourceInclude = []string{}
		}
		//fmt.Println(recallConfigs[i].RecallId, recallConfigs[i].RecallName)
		//fmt.Println(q.DslString(ctx))
		return q.DslString(ctx)
	}
	return ""
}
