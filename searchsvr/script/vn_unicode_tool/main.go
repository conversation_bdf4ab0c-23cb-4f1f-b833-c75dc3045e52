package main

import (
	"bufio"
	"fmt"
	common "git.garena.com/shopee/o2o-intelligence/common/common-lib/math"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"
	"io/ioutil"
	"math"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
	"unicode"
)

func OpeningStatusFunc() {
	regularVal := "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"

	specialStartVal := "1696698000000"

	specialEndVal := "1697216399999"

	specialStatusVal := "000000000000000000000000000000000000000000000000"

	openStatusVal := "2"

	curSec, now, weekDay := timeutil.GetTZOrCIDNowSecondsWeekDay(timeutil.Now(), timeutil.GetLocName())
	cur := int(math.Round(float64(curSec) / 30.0 / 60.0))

	openingStatus := "0"
	openingStatusRatio := "0.2"
	var specialOpenStart int64 = 0
	var specialOpenEnd int64 = 0
	specialTime := "0"
	regularTime := "0"
	index := 10000
	if len(specialStartVal) > 0 {
		specialOpenStart, _ = strconv.ParseInt(specialStartVal, 10, 64)
	}
	if len(specialEndVal) > 0 {
		specialOpenEnd, _ = strconv.ParseInt(specialEndVal, 10, 64)
	}
	if len(specialStatusVal) > 0 {
		specialTime = specialStatusVal
	}
	if len(regularVal) > 0 {
		regularTime = regularVal
	}
	index = int(weekDay)*48 + cur

	if now*1000 > specialOpenStart && now*1000 < specialOpenEnd {
		if specialTime != "0" && len(specialTime) > cur && specialTime[cur] == '1' {
			openingStatus = "2" // open
			openingStatusRatio = "1"
		} else {
			openingStatus = "1" // close
			openingStatusRatio = "0.2"
		}
	} else {
		if regularTime != "0" && len(regularTime) > index && regularTime[index] == '1' {
			openingStatus = "2" // open
			openingStatusRatio = "1"
		} else {
			openingStatus = "1" // close
			openingStatusRatio = "0.2"
		}
	}
	if openingStatus == "2" && openStatusVal == "3" {
		openingStatus = "3" // busy
		openingStatusRatio = "0.2"
	}
	fmt.Println(openingStatus)
	fmt.Println(openingStatusRatio)
}

type Msg struct {
	row map[string]string
}

func main() {
	//statisticQuery()
	//statisticStore()
	//getSpecialChar()
	//test()
	//replaceTest()
	//now := time.Now()
	//sixMonthsAgo := now.AddDate(0, -6, 0)
	//timestamp := sixMonthsAgo.UnixNano() / int64(time.Millisecond)
	//fmt.Println(timestamp)
	//OpeningStatusFunc()

	//msgList := make([]*Msg, 0, 0)
	//msgList = append(msgList, &Msg{row: map[string]string{"a": "A", "b": "B"}})
	//msgList = append(msgList, &Msg{row: map[string]string{"c": "C", "d": "D"}})
	//
	//list := make([]map[string]string, 0, 0)
	//for i := 0; i < len(msgList); i++ {
	//	one := msgList[i]
	//	list = append(list, one.row)
	//}
	//
	//for i := 0; i < len(list); i++ {
	//	one := list[i]
	//	one["z"] = "Z"
	//}
	//
	//for i := 0; i < len(msgList); i++ {
	//	one := msgList[i]
	//	fmt.Println(one)
	//}

	//str := "bún bò huế o Mai"
	//fmt.Println(str)
	//fmt.Println(strings.ToLower(str))
	//fmt.Println(strings.SplitN(str, "-", 2))

	workers := 5
	// 计算每份的大小
	chunkSize := (12 + workers - 1) / workers // 向上取整

	wg2 := sync.WaitGroup{}
	wg2.Add(workers)
	for i := 0; i < workers; i++ {
		if i+1 < workers {
			fmt.Println(i*chunkSize, (i+1)*chunkSize)
		} else {
			fmt.Println(i * chunkSize)
		}
	}
}

var acc common.Accuracy = func() float64 { return 0.000001 }

func replaceTest() {
	dat, _ := ioutil.ReadFile("./vn_char_mapping_live_vn.txt")
	lines := strings.Split(string(dat), "\n")
	charMapping := make(map[string]string, len(lines))
	for i := 0; i < len(lines); i++ {
		line := lines[i]
		tmp := strings.Split(line, "\t")
		if len(tmp) == 2 {
			charMapping[tmp[0]] = tmp[1]
		}
	}

	// Test
	str := "Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp Cuốn N Roll Restaurant - Đoàn Trần Nghiệp"
	//newStr := ""
	now := time.Now()
	fmt.Println(len(str))
	for i := 0; i < 100; i++ {
		for key, val := range charMapping {
			str = strings.ReplaceAll(str, key, val)
		}
	}
	end := time.Now()
	fmt.Println(end.Sub(now).Milliseconds())
	//fmt.Println(newStr)
	//fmt.Println(str)
}

func test() {
	str := "ế"

	fmt.Println(len(str))
	for i, i2 := range str {
		fmt.Println(i)
		if unicode.Is(unicode.Mn, i2) {
			char := str[i-2 : i+2]
			fmt.Println(char)
			tmp := ""
			fmt.Println(len(char))
			for _, ii := range char {
				tmp = fmt.Sprintf("%s %U", tmp, ii)
			}
			fmt.Println(tmp)
		}
	}
}

func statisticQuery() {
	file, err := os.OpenFile("/Users/<USER>/go/src/git.garena.com/yikai.lin/my-golang-demo/src/vietnamese-characters-unicode/query_result.csv", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println("文件打开失败", err)
	}
	defer file.Close()
	write := bufio.NewWriter(file)

	dat, _ := ioutil.ReadFile("/Users/<USER>/go/src/git.garena.com/yikai.lin/my-golang-demo/src/vietnamese-characters-unicode/query_log_vn.csv")
	lines := strings.Split(string(dat), "\n")
	for _, line := range lines {
		//println(line)
		strs := strings.Split(line, ",")
		if len(strs) != 9 {
			continue
		}
		for _, r := range strs[0] {
			if unicode.Is(unicode.Mn, r) {
				//write.WriteString(fmt.Sprintf("[%s] has an accent at:%d with Unicode [%U], total Unicode is:", strs[0], i, r))
				write.WriteString(fmt.Sprintf("%s,", strs[0]))
				ff := ""
				for i, rr := range strs[0] {
					write.WriteString(fmt.Sprintf("%U", rr))
					if unicode.Is(unicode.Mn, rr) {
						ff = fmt.Sprintf("%s,%d,%U,", ff, i, rr)
					}
				}
				write.WriteString(ff)
				write.WriteString(fmt.Sprintf("\n"))
				break
			}
		}
	}
	write.Flush()
}

func statisticStore() {
	file, err := os.OpenFile("/Users/<USER>/go/src/git.garena.com/yikai.lin/my-golang-demo/src/vietnamese-characters-unicode/store_result.csv", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println("文件打开失败", err)
	}
	defer file.Close()
	write := bufio.NewWriter(file)

	dat, _ := ioutil.ReadFile("/Users/<USER>/go/src/git.garena.com/yikai.lin/my-golang-demo/src/vietnamese-characters-unicode/store_tab_vn.csv")
	lines := strings.Split(string(dat), "\n")
	for _, line := range lines {
		//println(line)
		strs := strings.Split(line, ",")
		if len(strs) != 8 {
			continue
		}
		for _, r := range strs[1] {
			if unicode.Is(unicode.Mn, r) {
				//write.WriteString(fmt.Sprintf("[%s] has an accent at:%d with Unicode [%U], total Unicode is:", strs[0], i, r))
				write.WriteString(fmt.Sprintf("%s, %s, %s,", strs[6], strs[0], strs[1]))
				ff := ""
				for i, rr := range strs[1] {
					write.WriteString(fmt.Sprintf("%U", rr))
					if unicode.Is(unicode.Mn, rr) {
						ff = fmt.Sprintf("%s,%d,%U,", ff, i, rr)
					}
				}
				write.WriteString(ff)
				write.WriteString(fmt.Sprintf("\n"))
				break
			}
		}
	}
	write.Flush()
}

func getSpecialChar() {
	file, err := os.OpenFile("./tmp.csv", os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		fmt.Println("文件打开失败", err)
	}
	defer file.Close()
	write := bufio.NewWriter(file)

	dat, _ := ioutil.ReadFile("./store_result.csv")
	lines := strings.Split(string(dat), "\n")
	charList := make([]string, 0, 0)
	for _, line := range lines {
		//println(line)
		strs := strings.Split(line, ",")
		if len(strs) < 3 {
			continue
		}
		for _, r := range strs[2] {
			if unicode.Is(unicode.Mn, r) {
				//write.WriteString(fmt.Sprintf("[%s] has an accent at:%d with Unicode [%U], total Unicode is:", strs[0], i, r))
				write.WriteString(fmt.Sprintf("%s,", strs[2]))
				ff := ""
				prevIndex := -1
				var prevVal int32
				prevVal = -1
				for i, rr := range strs[2] {
					//write.WriteString(fmt.Sprintf("%U", rr))
					if unicode.Is(unicode.Mn, rr) {
						char := ""
						if prevIndex >= 0 {
							if unicode.Is(unicode.Mn, prevVal) {
								oldChar := charList[len(charList)-1]
								char = oldChar + strs[2][i:i+2]
								charList = charList[0 : len(charList)-1]
							} else {
								char = strs[2][prevIndex : i+2]
								//char = strs[2][i-2 : i+2]
								//charList = append(charList, char)
							}
							charList = append(charList, char)
						} else {
							fmt.Println("error")
						}
						ff = fmt.Sprintf("%s,%d,%U,%v,", ff, i, rr, char)
					}
					prevVal = rr
					prevIndex = i
				}
				write.WriteString(ff)
				write.WriteString(fmt.Sprintf("\n"))
				break
			}
		}
	}

	dat, _ = ioutil.ReadFile("./query_result.csv")
	lines = strings.Split(string(dat), "\n")
	for _, line := range lines {
		//println(line)
		strs := strings.Split(line, ",")
		if len(strs) < 1 {
			continue
		}
		for _, r := range strs[0] {
			if unicode.Is(unicode.Mn, r) {
				//write.WriteString(fmt.Sprintf("[%s] has an accent at:%d with Unicode [%U], total Unicode is:", strs[0], i, r))
				write.WriteString(fmt.Sprintf("%s,", strs[0]))
				ff := ""
				prevIndex := -1
				for i, rr := range strs[0] {
					//write.WriteString(fmt.Sprintf("%U", rr))
					if unicode.Is(unicode.Mn, rr) {
						char := ""
						if prevIndex >= 0 {
							char = strs[0][prevIndex : i+2]
							charList = append(charList, char)
							//char = strs[0][i-2 : i+2]
							//charList = append(charList, char)
							//fmt.Println(char)
							//tmp := ""
							//fmt.Println(len(char))
							//for _, i2 := range char {
							//	tmp = fmt.Sprintf("%s %U", tmp, i2)
							//}
							//fmt.Println(tmp)
						} else {
							fmt.Println("error")
						}
						ff = fmt.Sprintf("%s,%d,%U,%v", ff, i, rr, char)
					}
					prevIndex = i
				}
				write.WriteString(ff)
				write.WriteString(fmt.Sprintf("\n"))
				break
			}
		}
	}
	write.Flush()

	tmpMap := make(map[string]bool, len(charList))
	for _, s := range charList {
		_, ok := tmpMap[s]
		if !ok {
			tmpMap[s] = true
		}
	}

	file2, err1 := os.OpenFile("./char.csv", os.O_WRONLY|os.O_CREATE, 0666)
	if err1 != nil {
		fmt.Println("文件打开失败", err1)
	}
	defer file2.Close()
	write2 := bufio.NewWriter(file2)
	for s, _ := range tmpMap {
		write2.WriteString(s + ",")
		for _, ss := range s {
			write2.WriteString(fmt.Sprintf("%U", ss))
		}
		write2.WriteString(",\n")
	}
	write2.Flush()
}
