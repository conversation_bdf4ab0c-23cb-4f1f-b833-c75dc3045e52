package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/common_client"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"io"
	"os"
	"strconv"
	"strings"
	"time"
)

var (
	query    = flag.String("query", "beef", "query")
	storeIds = flag.String("storeIds", "", "store id list")
	debug    = flag.Bool("debug", false, "")
	env      = flag.String("env", "test", "")
	cid      = flag.String("cid", "id", "")
	file     = flag.String("file", "", "file")
)

var (
	rateLimiter = rate.NewLimiter(1000, 1000)
)

func main() {
	flag.Parse()
	zk := "microkit.zk01.i.sz.shopee.io:2181,microkit.zk02.i.sz.shopee.io:2181,microkit.zk03.i.sz.shopee.io:2181,microkit.zk04.i.sz.shopee.io:2181,microkit.zk05.i.sz.shopee.io:2181"
	if *env == "test" {
		zk = "microkit.zk01.i.test.sz.shopee.io:2181,microkit.zk02.i.test.sz.shopee.io:2181,microkit.zk03.i.test.sz.shopee.io:2181"
	}
	clientConf := common_client.MicroClientConfig{
		Region:       *cid,
		Env:          *env,
		ZkAddress:    zk,
		ConnPoolSize: 100,
	}
	logkit.Init(
		logkit.Level("debug"),
		logkit.EnableConsole(false),
	)

	dishSearcherClient := o2oalgo_dishsearcher.InitDishSearcherClient("o2oalgo_dishsearcher", &clientConf)
	fmt.Println("file:" + *file)
	if *file == "" {
		querys := strings.Split(*query, " ")
		storeIdStr := strings.Split(*storeIds, ",")

		storeIdList := make([]uint64, 0)
		if *storeIds != "" {
			for _, s := range storeIdStr {
				id, _ := strconv.ParseUint(s, 10, 64)
				storeIdList = append(storeIdList, id)
			}
		}

		fmt.Println(querys)
		fmt.Println(storeIdList)

		req := o2oalgo_dishsearcher.SearcherRequest{
			Keyword: querys,
			StoreId: storeIdList,
			IsDebug: false,
		}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
		defer cancel()

		resp, err := dishSearcherClient.Search(ctx, &req)
		if err != nil {
			fmt.Println("search fail..")
			fmt.Println(err.Error())
		} else {
			fmt.Println("resp:")
			bytes, _ := json.Marshal(resp)
			fmt.Println(string(bytes))
		}
	} else {
		defer time.Sleep(2 * time.Second)
		fs, err := os.Open(*file)
		if err != nil {
			fmt.Println("Failed to open file")
			return
		}
		defer fs.Close()
		buffer := bufio.NewReader(fs)
		buffer = bufio.NewReaderSize(buffer, 65535)
		timeNow := time.Now()
		now := timeNow.UnixNano()
		fw, wErr := os.Create("./result-" + strconv.FormatUint(uint64(now), 10) + ".txt")
		if wErr != nil {
			fmt.Println("Failed to open file: result.txt")
			return
		}
		defer fw.Close()
		fmt.Println("start")
		for {
			line, _, end := buffer.ReadLine()
			if end == io.EOF {
				break
			}
			row := strings.Split(strings.Trim(string(line), "  "), "\t")
			if len(row) != 3 {
				logkit.Error("error row", zap.String("row", string(line)))
				continue
			}
			queryList := strings.Split(row[0], "###")
			maxWordList := strings.Split(row[1], "###")
			storeList := strings.Split(row[2], "###")
			storeIdList := make([]uint64, 0)
			for _, s := range storeList {
				id, _ := strconv.ParseUint(s, 10, 64)
				storeIdList = append(storeIdList, id)
			}

			querys := make([]string, 0)
			for _, s := range queryList {
				querys = append(querys, strings.ToLower(s))
			}
			if err := rateLimiter.WaitN(context.Background(), 1); err != nil {
				logkit.With(zap.Error(err)).Error("limiter.Wait")
				return
			}

			req := o2oalgo_dishsearcher.SearcherRequest{
				Keyword:    querys,
				StoreId:    storeIdList,
				IsDebug:    false,
				KeywordSeg: maxWordList,
			}
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()

			resp, err := dishSearcherClient.Search(ctx, &req)
			if err != nil {
				logkit.Error("search error", zap.String("err", err.Error()))
			} else {
				bytes, _ := json.Marshal(resp)
				fw.Write(bytes)
				fw.WriteString("\n")
			}
		}
	}

}
