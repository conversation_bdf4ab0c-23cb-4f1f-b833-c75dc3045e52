package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"
	"io/ioutil"
	"os"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"

	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
)

var (
	configFile = flag.String("config", "id.json", "config file")
)

func GetRecallConfig(content string) *apollo.RecallConfig {
	newCfg := apollo.SearchRecallConfig{}
	err := json.Unmarshal([]byte(content), &newCfg)
	if err != nil {
		return nil
	}

	// 初始化一个空的召回配置
	tmpRecallConfig := apollo.RecallConfig{}
	tmpRecallConfig.StoreRecalls = make([]*apollo.StoreRecallConfig, 0)

	for _, recallConfig := range newCfg.RecallConfig.StoreRecalls {
		tmpRecallConfig.StoreRecalls = append(tmpRecallConfig.StoreRecalls, recallConfig)
	}
	tmpRecallConfig.Common = newCfg.RecallConfig.Common
	return &tmpRecallConfig
}

func main() {
	flag.Parse()

	// 打开json文件
	jsonFile, err := os.Open(*configFile)

	// 最好要处理以下错误
	if err != nil {
		fmt.Println(err)
	}

	// 要记得关闭
	defer jsonFile.Close()

	byteValue, _ := ioutil.ReadAll(jsonFile)
	fmt.Println(string(byteValue))

	// 加载配置
	config := GetRecallConfig(string(byteValue))

	unsettled_store.NewQueryUnsettledStoreDao()

	req := traceinfo.RequestInfo{
		QueryRaw:  "mcdonalds",
		Longitude: 103.743,
		Latitude:  1.49785,
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			IsPreferredMerchant: proto.Bool(true),
		},
		SortType: foodalgo_search.SearchRequest_Relevance.Enum(),
		CityId:   217,
	}
	var traceInfo = traceinfo.TraceInfo{
		QueryKeyword: req.QueryRaw,
		TraceRequest: &req,
		QPResult: &traceinfo.QPResult{
			ProbFilterCategoryIntentions: []*qp.CategoryIntention{
				{Level1Name: proto.String("mcdonalds"), Level2Name: proto.String("fried chicken")},
				{Level1Name: proto.String("mcdonalds"), Level2Name: proto.String("burger")},
			},
			Segments:            []string{"a", "b"},
			MaxWordSegments:     []string{"a", "b"},
			StoreIntents:        []string{"rice", "beef"},
			OtherSegments:       &traceinfo.OtherSegments{},
			QueryStoreIntention: "",
			QueryDishIntention:  "nasi hainam",
			NerResult: []*qp.Ner{
				{
					Token:   proto.String("rice"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("ice coffee"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("kfc"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("mcdonald's"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("GreatWall No.1"),
					NerType: []qp.NERType{qp.NERType_LOCATION},
				},
				{
					Token:   proto.String("Sorry Fatpo"),
					NerType: []qp.NERType{qp.NERType_UNKNOWN},
				},
			},
			RewriteNerResult: [][]*qp.Ner{
				{
					{
						Token:   proto.String("kfc"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("mcdonald's"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("GreatWall No.1"),
						NerType: []qp.NERType{qp.NERType_LOCATION},
					},
					{
						Token:   proto.String("Sorry Fatpo"),
						NerType: []qp.NERType{qp.NERType_UNKNOWN},
					},
				},
			},
			QueryStringList: []traceinfo.QueryStringItem{
				{
					Fields: []string{"store_name^1.000000", "real_store_name^1.000000", "branch_name^0.100000",
						"main_category.level1_name.keyword^0.600000",
						"main_category.level2_name.keyword^0.600000",
						"sub_category.level1_name.keyword^0.600000",
						"sub_category.level2_name.keyword^0.600000",
						"dish_name^0.800000"},
					Query: "bakso^0.8489",
				},
				{
					Fields: []string{"store_name^1.000000",
						"real_store_name^1.000000",
						"branch_name^0.100000",
						"main_category.level1_name.keyword^0.600000",
						"main_category.level2_name.keyword^0.600000",
						"sub_category.level1_name.keyword^0.600000",
						"sub_category.level2_name.keyword^0.600000",
						"dish_name^0.200000"},
					Query: "pak^0.0715 muh^0.0795",
				},
			},
			QueryTag: []string{"a", "b"},
		},
		OptIntervention: &traceinfo.OptIntervention{
			IsStoreInterventionRecall:    true,
			InterventionRecallBrandID:    []uint64{1, 2, 3},
			InterventionRecallMerchantID: []uint64{1, 2, 3},
		},
		IsDebug:         true,
		UnsettledStores: &traceinfo.UnsettledStoreInfo{},
		UserContext:     &traceinfo.UserContext{},
		PredictConfig:   &traceinfo.PredictConfig{},
	}

	ctx := context.Background()
	dataSourceMap := predata.InitDataSourceMap(ctx, &traceInfo)
	dataSourceParameters := parse.GetDataSourceParams(dataSourceMap)
	traceInfo.RecallConfigurationDataParams = dataSourceParameters
	traceInfo.RecallConfigurationDataSource = dataSourceMap

	for _, c := range config.StoreRecalls {
		q := parse.GenRecallQuery(ctx, &traceInfo, config.Common, c)
		if q == nil {
			continue
		}
		fmt.Println(c.RecallName)
		fmt.Println(q.DslString(ctx))
		fmt.Println()
	}
}
