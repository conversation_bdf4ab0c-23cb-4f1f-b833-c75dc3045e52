package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"
	"io/ioutil"
	"os"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"

	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
)

var (
	configFile = flag.String("config", "vn.json", "config file")
)

func GetRecallConfig(content string) *apollo.RecallConfig {
	newCfg := apollo.SearchRecallConfig{}
	err := json.Unmarshal([]byte(content), &newCfg)
	if err != nil {
		return nil
	}

	// 初始化一个空的召回配置
	tmpRecallConfig := apollo.RecallConfig{}
	tmpRecallConfig.StoreRecalls = make([]*apollo.StoreRecallConfig, 0)

	for _, recallConfig := range newCfg.RecallConfig.StoreRecalls {
		tmpRecallConfig.StoreRecalls = append(tmpRecallConfig.StoreRecalls, recallConfig)
	}

	tmpRecallConfig.Common = newCfg.RecallConfig.Common
	return &tmpRecallConfig
}

func genDslByRecallConfiguration(config *apollo.RecallConfig, traceInfo *traceinfo.TraceInfo, req foodalgo_search.SearchRequest) string {
	ctx := context.Background()
	dataSourceMap := predata.InitDataSourceMap(ctx, traceInfo)
	dataSourceParameters := parse.GetDataSourceParams(dataSourceMap)
	traceInfo.RecallConfigurationDataParams = dataSourceParameters
	for _, c := range config.StoreRecalls {
		fmt.Println(c.RecallName)
		q := parse.GenRecallQuery(ctx, traceInfo, config.Common, c)
		if q == nil {
			continue
		}
		return q.DslString(ctx)
	}
	return ""
}

func genDslByVNBaseline(traceInfo *traceinfo.TraceInfo) string {
	searchVNReq := recall.NewESRecallRequest(traceInfo)
	req2 := searchVNReq.BuildRecallDSLByCodeVN(context.TODO(), traceInfo)
	return req2.StoreQueries[0].DslString(context.Background())
}

func main() {
	flag.Parse()

	// 打开json文件
	jsonFile, err := os.Open(*configFile)

	// 最好要处理以下错误
	if err != nil {
		fmt.Println(err)
	}

	// 要记得关闭
	defer jsonFile.Close()

	byteValue, _ := ioutil.ReadAll(jsonFile)
	fmt.Println(string(byteValue))

	// 加载配置
	config := GetRecallConfig(string(byteValue))
	unsettled_store.NewQueryUnsettledStoreDao()

	traceInfo := &traceinfo.TraceInfo{
		QPResult: &traceinfo.QPResult{
			QueryStoreIntention: "kfc",
			Segments:            []string{"boom", "rice", "beef"},
			MaxWordSegments:     []string{"maxBoom", "maxRice", " maxBeef"},
			ProbFilterCategoryIntentions: []*qp.CategoryIntention{
				{Level1Name: proto.String("food"), Level2Name: proto.String("rice")},
				{Level1Name: proto.String("bread"), Level2Name: proto.String("fat bread")},
				{Level1Name: proto.String("nice")},
			},
			StoreIntents:  []string{"rice", "beef"},
			OtherSegments: &traceinfo.OtherSegments{},
			NerResult: []*qp.Ner{
				{
					Token:   proto.String("rice"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("ice coffee"),
					NerType: []qp.NERType{qp.NERType_DISH},
				},
				{
					Token:   proto.String("kfc"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("mcdonald's"),
					NerType: []qp.NERType{qp.NERType_STORE},
				},
				{
					Token:   proto.String("GreatWall No.1"),
					NerType: []qp.NERType{qp.NERType_LOCATION},
				},
				{
					Token:   proto.String("Sorry Fatpo"),
					NerType: []qp.NERType{qp.NERType_UNKNOWN},
				},
			},
			RewriteNerResult: [][]*qp.Ner{
				{
					{
						Token:   proto.String("rice"),
						NerType: []qp.NERType{qp.NERType_DISH},
					},
					{
						Token:   proto.String("ice coffee"),
						NerType: []qp.NERType{qp.NERType_DISH},
					},
					{
						Token:   proto.String("kfc"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("mcdonald's"),
						NerType: []qp.NERType{qp.NERType_STORE},
					},
					{
						Token:   proto.String("GreatWall No.1"),
						NerType: []qp.NERType{qp.NERType_LOCATION},
					},
					{
						Token:   proto.String("I am category"),
						NerType: []qp.NERType{qp.NERType_CATEGORY},
					},
					{
						Token:   proto.String("Sorry Fatpo"),
						NerType: []qp.NERType{qp.NERType_UNKNOWN},
					}},
			},
		},
		OptIntervention: &traceinfo.OptIntervention{},
		IsDebug:         true,
		UnsettledStores: &traceinfo.UnsettledStoreInfo{},
		UserContext:     &traceinfo.UserContext{},
		PredictConfig:   &traceinfo.PredictConfig{},
	}

	// 越南语 & token == 1 [done]
	fmt.Println("越南语 & token == 1")
	req1 := traceinfo.RequestInfo{
		QueryRaw:  "Ăn",
		PageSize:  20,
		Longitude: 106.68579864501953,
		Latitude:  10.773409843444824,
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
		},
	}
	traceInfo.TraceRequest = &req1
	str1 := genDslByRecallConfiguration(config, traceInfo, req1)
	str2 := genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}

	// 越南语 & token == 2 [done]
	fmt.Println("越南语 & token == 2")
	req2 := traceinfo.RequestInfo{
		Keyword:   proto.String("Sầu Riêng"),
		PageSize:  proto.Uint32(20),
		Distance:  proto.Uint32(20000.0),
		Longitude: proto.Float32(106.68579864501953),
		Latitude:  proto.Float32(10.773409843444824),
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
		},
		AbTest: proto.String("foodsch.recall=group_yy"),
	}
	traceInfo.TraceRequest = &req2
	str1 = genDslByRecallConfiguration(config, traceInfo, req2)
	str2 = genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}

	// 越南语 & token >= 3
	fmt.Println("越南语 & token >= 3")
	req3 := foodalgo_search.SearchRequest{
		Keyword:   proto.String("Ăn Vặt Sầu Riêng"),
		PageSize:  proto.Uint32(20),
		Distance:  proto.Uint32(20000.0),
		Longitude: proto.Float32(106.68579864501953),
		Latitude:  proto.Float32(10.773409843444824),
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
		},
		AbTest: proto.String("foodsch.recall=group_yy"),
	}
	traceInfo.OriginRequest = &req3
	str1 = genDslByRecallConfiguration(config, traceInfo, req3)
	str2 = genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}

	// 非越南语 & token == 1 [done]
	fmt.Println("非越南语 & token == 1")
	req11 := foodalgo_search.SearchRequest{
		Keyword:   proto.String("burger"),
		PageSize:  proto.Uint32(20),
		Distance:  proto.Uint32(20000.0),
		Longitude: proto.Float32(106.68579864501953),
		Latitude:  proto.Float32(10.773409843444824),
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
		},
		AbTest: proto.String("foodsch.recall=group_yy"),
	}
	traceInfo.OriginRequest = &req11
	str1 = genDslByRecallConfiguration(config, traceInfo, req11)
	str2 = genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}

	// 越南语 & token == 2 [done]
	fmt.Println("非越南语 & token == 2")
	req22 := foodalgo_search.SearchRequest{
		Keyword:   proto.String("burger king"),
		PageSize:  proto.Uint32(20),
		Distance:  proto.Uint32(20000.0),
		Longitude: proto.Float32(106.68579864501953),
		Latitude:  proto.Float32(10.773409843444824),
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
		},
		AbTest: proto.String("foodsch.recall=group_yy"),
	}
	traceInfo.OriginRequest = &req22
	str1 = genDslByRecallConfiguration(config, traceInfo, req22)
	str2 = genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}

	//// 越南语 & token >= 3
	fmt.Println("非越南语 & token >= 3")
	req33 := foodalgo_search.SearchRequest{
		Keyword:   proto.String("burger king kfc mcd"),
		PageSize:  proto.Uint32(20),
		Distance:  proto.Uint32(20000.0),
		Longitude: proto.Float32(106.68579864501953),
		Latitude:  proto.Float32(10.773409843444824),
		SortType:  foodalgo_search.SearchRequest_Relevance.Enum(),
		FilterType: &foodalgo_search.SearchRequest_FilterType{
			CategoryType:        []uint32{1001},
			IsPartnerMerchant:   proto.Bool(true),
			IsPreferredMerchant: proto.Bool(true),
			StoreIds:            []uint32{650883, 951820, 239375, 96530, 964099, 796950, 252442, 875675, 207900, 881690, 995870, 200863, 790692, 930743, 719038, 694079, 911692, 681685, 223833, 263004, 767966, 710887, 154729, 675219, 165238, 743800},
		},
		AbTest: proto.String("foodsch.recall=group_yy"),
	}
	traceInfo.OriginRequest = &req33
	str1 = genDslByRecallConfiguration(config, traceInfo, req33)
	str2 = genDslByVNBaseline(traceInfo)
	if str1 != str2 {
		fmt.Println(str1)
		fmt.Println(str2)
		fmt.Println()
	}
}
