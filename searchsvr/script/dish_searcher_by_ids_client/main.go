package main

import (
	"context"
	"flag"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/common_client"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
)

var (
	dishIds = flag.String("dishIds", "", "store id list")
	debug   = flag.Bool("debug", false, "")
	env     = flag.String("env", "test", "")
	cid     = flag.String("cid", "id", "")
)

func main() {
	flag.Parse()
	zk := "microkit.zk01.i.sz.shopee.io:2181,microkit.zk02.i.sz.shopee.io:2181,microkit.zk03.i.sz.shopee.io:2181,microkit.zk04.i.sz.shopee.io:2181,microkit.zk05.i.sz.shopee.io:2181"
	if *env == "test" {
		zk = "microkit.zk01.i.test.sz.shopee.io:2181,microkit.zk02.i.test.sz.shopee.io:2181,microkit.zk03.i.test.sz.shopee.io:2181"
	}
	clientConf := common_client.MicroClientConfig{
		Region:       *cid,
		Env:          *env,
		ZkAddress:    zk,
		ConnPoolSize: 100,
	}

	dishIdStr := strings.Split(*dishIds, ",")

	dishIdList := make([]uint64, 0)
	if *dishIds != "" {
		for _, s := range dishIdStr {
			id, _ := strconv.ParseUint(s, 10, 64)
			dishIdList = append(dishIdList, id)
		}
	}

	fmt.Println(dishIdList)

	dishSearcherClient := o2oalgo_dishsearcher.InitDishSearcherClient("o2oalgo_dishsearcher", &clientConf)
	req := o2oalgo_dishsearcher.SearcherIdRequest{
		DishId:  dishIdList,
		IsDebug: *debug,
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
	defer cancel()
	fmt.Println(time.Now())
	resp, err := dishSearcherClient.SearchById(ctx, &req)
	if err != nil {
		fmt.Println("search fail..")
		fmt.Println(err.Error())
	} else {
		fmt.Println("resp:")
		for _, d := range resp.GetDishs() {
			fmt.Println(d)
		}
	}
	fmt.Println(time.Now())
}
