package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_rank_factor"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/feed/microkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/pass_config"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/spex"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"

	"github.com/micro/go-micro"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_top_cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/wire"

	_ "git.garena.com/shopee/feed/comm_lib/detector"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/platform/golang_splib/server"
)

type MerchantSvr struct {
	microkit.CommSvr
	config          config2.ShopConfig
	service         micro.Service
	taskPool        *async.TaskPool
	storeTopSyncJob *store_top_cron.LoadStoreTopJob
	spexServer      server.Server
}

func (h *MerchantSvr) Init(conf string) error {
	// 配置初始化
	err := h.config.Scan(conf)
	if err != nil {
		return err
	}
	// 监控上报初始化
	reporter.Init()
	go reporter.ListenAndServe()

	// 加载pass config center
	err = pass_config.Init()
	if err != nil {
		logkit.With(logkit.Err(err)).Error("pass_config init error")
	}
	passConfigPrefix := "search_search_"
	serviceName := pass_config.Get(passConfigPrefix + "service_name")
	h.config.Redis.Password = pass_config.Get(passConfigPrefix + "redis_password")
	h.config.Elastic.UserName = pass_config.Get(passConfigPrefix + "elastic_user")
	h.config.Elastic.Password = pass_config.Get(passConfigPrefix + "elastic_password")
	h.config.ReplicaElastic.UserName = pass_config.Get(passConfigPrefix + "replica_elastic_user")
	h.config.ReplicaElastic.Password = pass_config.Get(passConfigPrefix + "replica_elastic_password")

	h.config.DataStreamMysql.User = pass_config.GetWithoutPrefix(passConfigPrefix + "datastream_mysql_user")
	h.config.DataStreamMysql.Password = pass_config.GetWithoutPrefix(passConfigPrefix + "datastream_mysql_password")
	h.config.DataPlatformMysql.User = pass_config.GetWithoutPrefix(passConfigPrefix + "datastream_mysql_user")
	h.config.DataPlatformMysql.Password = pass_config.GetWithoutPrefix(passConfigPrefix + "datastream_mysql_password")

	h.config.S3Config.AccessKey = pass_config.GetWithoutPrefix(passConfigPrefix + "s3_access_key")
	h.config.S3Config.SecretKey = pass_config.GetWithoutPrefix(passConfigPrefix + "s3_secret_key")
	h.config.GeoConfig.ClientKey = pass_config.GetWithoutPrefix(passConfigPrefix + "geo_client_key")

	// 手动初始化 Apollo 配置
	apollo.InitApolloConfig()
	// 注册 spex
	spex.InitService(serviceName)

	// 初始化各种client
	integrate.InitIntegrate(&h.config, serviceName)
	// 初始化本地free cache
	localcache.InitStoreCacheSys()

	// 监控上报初始化
	metric_reporter.InitSearchRaw(metric_reporter2.FoodSearchDur99, metric_reporter2.FoodSearchNum, metric_reporter2.FoodSearchReq)
	// 日志初始化, 需要在监控上报初始化之后，否则日志会被写在log/prometheus 目录
	logger.InitLogkit()
	// spex
	hdl := wire.InitializeHandler()
	h.spexServer, _ = spex.NewServer(foodalgo_search.NewServer, hdl)
	// 同步任务初始化
	if env.GetEnv() == "live" || env.GetEnv() == "liveish" {
		h.InitJob()
	} else {
		go h.InitJob()
	}
	// 异步任务初始化
	h.taskPool = wire.InitializeAsyncTask(&h.config.Elastic, &h.config.Redis, &h.config.GDS, h.config.GdsWatchSchema, &h.config.DataStreamMysql, &h.config.DataPlatformMysql, h.config.S3Config, h.config.S3FileName)
	goroutine.WithGo(context.TODO(), "taskPool.Start", func(params ...interface{}) {
		h.taskPool.Start()
	})
	return nil
}

func (h *MerchantSvr) InitJob() {
	st := time.Now()
	s3Client := s3.NewS3Service(h.config.S3Config)
	wg := sync.WaitGroup{}

	// 四地区共有任务
	wg.Add(1)
	goroutine.WithGo(context.TODO(), "InitializeStoreIntervention", func(params ...interface{}) {
		defer wg.Done()
		syncJob := wire.InitializeStoreIntervention(&h.config.DataStreamMysql)
		syncJob.Run()
	})

	wg.Add(1)
	goroutine.WithGo(context.TODO(), "InitializeReloadStoreCtrCvr", func(params ...interface{}) {
		defer wg.Done()
		syncStoreCtrCvrJob := wire.InitializeReloadStoreCtrCvr(h.config.S3Config, h.config.S3FileName)
		syncStoreCtrCvrJob.Run()
	})

	wg.Add(1)
	goroutine.WithGo(context.TODO(), "storeBlacklistJob", func(params ...interface{}) {
		defer wg.Done()
		fmt.Println("start job: InitializeStoreBlacklistJob...")
		wire.InitializeStoreBlacklistJob(&h.config.DataPlatformMysql)
	})

	wg.Add(1)
	goroutine.WithGo(context.TODO(), "storeRealNameJob", func(params ...interface{}) {
		defer wg.Done()
		fmt.Println("start job: storeRealNameJob...")
		storeRealNameJob := wire.InitializeStoreRealNameJob(h.config.S3Config)
		storeRealNameJob.Run()
	})

	if cid.IsVNRegion() {
		// VN 独有
		wg.Add(1)
		goroutine.WithGo(context.TODO(), "charMappingJob", func(params ...interface{}) {
			defer wg.Done()
			fmt.Println("start job: charMappingJob...")
			charMappingJob := wire.InitializeCharMappingJob(h.config.S3Config)
			charMappingJob.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "storeIncubation", func(params ...interface{}) {
			defer wg.Done()
			fmt.Println("start job: storeIncubation...")
			storeIncubation := wire.InitalizeStoreIncubation(h.config.S3Config)
			storeIncubation.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "InitializeLoadVnBrandProtectionKeywordJob", func(params ...interface{}) {
			// VN品牌保护的keywords
			defer wg.Done()
			fmt.Println("start job: InitializeLoadVnBrandProtectionKeywordJob...")
			loadVnBrandProtectionKeywordJob := wire.InitializeLoadVnBrandProtectionKeywordJob(&h.config.DataPlatformMysql)
			loadVnBrandProtectionKeywordJob.Run()
		})
	} else {
		// MIT独有
		wg.Add(1)
		goroutine.WithGo(context.TODO(), "InitializeReloadStoreTop", func(params ...interface{}) {
			defer wg.Done()
			h.storeTopSyncJob = wire.InitializeReloadStoreTop(h.config.S3Config, h.config.S3FileName)
			h.storeTopSyncJob.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "InitializeReloadNotSettledStore", func(params ...interface{}) {
			defer wg.Done()
			// 未入驻门店扩召回加载
			syncNotSettledStoreJob := wire.InitializeReloadNotSettledStore(&h.config.Redis, h.config.S3Config, h.config.S3FileName)
			syncNotSettledStoreJob.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "InitializeUserLevel", func(params ...interface{}) {
			defer wg.Done()
			syncUserLevelJob := wire.InitializeUserLevel(h.config.S3Config, h.config.S3FileName)
			syncUserLevelJob.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "pcFactorJob", func(params ...interface{}) {
			defer wg.Done()
			pcFactorJob := wire.InitializePcFactor(h.config.S3Config, h.config.S3FileName)
			pcFactorJob.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "storeRankFactor", func(params ...interface{}) {
			defer wg.Done()
			job := store_rank_factor.NewStoreRankFactorLoadPlugin(s3Client)
			job.Run()
		})

		wg.Add(1)
		goroutine.WithGo(context.TODO(), "defaultStoreRankFactor", func(params ...interface{}) {
			defer wg.Done()
			job := store_rank_factor.NewDefaultStoreRankFactorLoadPlugin(s3Client)
			job.Run()
		})
	}
	wg.Wait()
	logkit.Info("InitJob finished", zap.String("cost", time.Since(st).String()))
}

// Run service
func (h *MerchantSvr) Run() {
	// 服务结束执行清理步骤
	defer func() {
		fmt.Println("stop log: stop spex server and task pool")
		h.taskPool.Stop()
		spex.Stop(h.spexServer)
	}()
	if err := spex.Run(h.spexServer); err != nil {
		fmt.Println("fail to run server, err: ", err.Error())
	}
}
