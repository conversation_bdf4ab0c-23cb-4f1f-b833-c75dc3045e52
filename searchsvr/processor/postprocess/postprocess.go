package postprocess

import (
	"context"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"go.uber.org/zap"
)

// recallStores 原始召回的门店列表
// mixerStores 混合搜索结果
func PostProcessor(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo, recallStores, mixerStores model2.StoreInfos) {
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenFinal, mixerStores.DishLength())
	// 算法需求：记录最后的final stores的平均距离
	traceInfo.AddPhraseStoreLength(ctx, traceinfo.AvgDistanceAtFinal, int(mixerStores.GetAvgDistance()))

	if traceInfo.IsDebug {
		debugInfo.FillMixStores(traceInfo, mixerStores)
		debugInfo.FillNormalStores(traceInfo, mixerStores)
		traceinfo.PrintFilteredType(ctx, traceInfo)
		resStore := mixerStores
		if len(resStore) > 20 {
			resStore = resStore[:20]
		}
		logger.MyDebug(ctx, traceInfo.IsDebug, "mixerStores", zap.Any("searchStoresResult", resStore))
	}

	goroutine.WithGo(ctx, "PrintDumpLog", func(params ...interface{}) {
		PrintDumpLog(ctx, traceInfo, recallStores, mixerStores)
		PrintDumpLogWithJson(ctx, traceInfo, recallStores, mixerStores)
	})
}

func PostProcessorAckDump(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalStores, mixerStores model2.StoreInfos) {
	goroutine.WithGo(ctx, "doCtrAck", func(params ...interface{}) {
		rank.DoPredictAck(ctx, traceInfo, mixerStores, normalStores.StoreInfoMap())
	})
}
