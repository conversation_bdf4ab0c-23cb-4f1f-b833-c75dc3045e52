package postprocess

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type LogEntry struct {
	Topic        string         `json:"topic"`          // kafka topic name
	Keyword      string         `json:"keyword"`        // 关键词
	PubContextID string         `json:"pub_context_id"` // 请求ID
	UserID       uint64         `json:"userid"`         // 用户ID
	SearchTime   string         `json:"search_time"`    // 搜索时间
	Longitude    float64        `json:"longitude"`      // 经度
	Latitude     float64        `json:"latitude"`       // 纬度
	ResultList   []LogEntryItem `json:"result_list"`    // 最终返回的关键词结果
	DT           string         `json:"dt"`             // 日期/时间
	GrassRegion  string         `json:"grass_region"`   // 区域信息
}

type LogEntryItem struct {
	StoreId     uint64   `json:"store_id"`           // 门店 id
	DishId      uint64   `json:"dish_id"`            // 菜品 id
	StoreScore  float64  `json:"store_score"`        // 门店分数
	RecallTypes []string `json:"store_recall_types"` // 门店召回列表
}

func PrintDumpLogForMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, finalResult model2.StoreInfos) {
	if !isNeedPrintDumpLog(traceInfo) {
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrintDumpLogForMainSite, time.Since(pt))
	}()

	topicName := apollo.SearchApolloCfg.MainSiteDumpLogKafkaTopicName
	if len(topicName) == 0 {
		topicName = "foodysearch.logdump.mp.search_main_site"
	}

	// 组装 rsp list
	itemList := make([]LogEntryItem, 0)
	cutSize := 100
	if apollo.SearchApolloCfg.MainSiteDumpLogCutSize > 0 {
		cutSize = apollo.SearchApolloCfg.MainSiteDumpLogCutSize
	}
	for i, item := range finalResult {
		if i >= cutSize {
			break
		}
		dishId := uint64(0)
		if len(item.DishInfos) > 0 {
			dishId = item.DishInfos[0].DishId
		}
		itemList = append(itemList, LogEntryItem{
			StoreId:     item.StoreId,
			DishId:      dishId,
			StoreScore:  item.Score,
			RecallTypes: item.RecallTypeAndIds,
		})
	}

	logEntry := LogEntry{
		Topic:        topicName,
		Keyword:      strings.ToLower(traceInfo.QueryKeyword),
		PubContextID: traceInfo.TraceRequest.PublishId,
		UserID:       traceInfo.UserId,
		SearchTime:   pt.Format("2006-01-02 15:04:05"),
		Longitude:    traceInfo.TraceRequest.Longitude,
		Latitude:     traceInfo.TraceRequest.Latitude,
		ResultList:   itemList,
		DT:           pt.Format("20060102"),
		GrassRegion:  env.GetCID(),
	}
	jsonStr, _ := json.Marshal(logEntry)
	logkit.FromContext(ctx).Info("[LOG_DUMP_MAIN_SITE]", logkit.String("logDump", string(jsonStr)))
}
