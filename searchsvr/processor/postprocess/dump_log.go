package postprocess

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func isNeedPrintDumpLog(traceInfo *traceinfo.TraceInfo) bool {
	if !apollo.SearchApolloCfg.PrintRecallStat || traceInfo.ShadowFlag {
		return false
	}
	// 仅Search，SearchFood, SearchGlobal Food 类别需要打印日志， Mart 类请求不打印, total num food 也不打印
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearch {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchFood {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchGlobal && traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchStoresWithListingDish {
		return true
	}
	// search mainsite 也要 dump log
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMainSite {
		return true
	}
	return false
}

// 创建一个字符串构建器对象池
var builderPool = &sync.Pool{
	New: func() interface{} {
		builder := new(strings.Builder)
		return builder
	},
}

func PrintDumpLog(ctx context.Context, traceInfo *traceinfo.TraceInfo, allRecallResult, finalResult model2.StoreInfos) {
	if !isNeedPrintDumpLog(traceInfo) {
		return
	}
	// 主站 dumplog 和站内区分开
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMainSite {
		PrintDumpLogForMainSite(ctx, traceInfo, finalResult)
		return
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrintDumpLog, time.Since(pt))
	}()

	stringBuilder := builderPool.Get().(*strings.Builder)
	stringBuilder.Reset()
	defer builderPool.Put(stringBuilder)

	stringBuilder.WriteString("[LOG_DUMP]")
	stringBuilder.WriteString(traceInfo.TraceRequest.QueryRaw) // 原始词
	stringBuilder.WriteString("[Q_SEP]")
	stringBuilder.WriteString(traceInfo.QueryKeyword) // 实际搜索词
	stringBuilder.WriteString("[Q_SEP]")
	stringBuilder.WriteString(env.GetCID())
	stringBuilder.WriteString("[Q_SEP]")
	stringBuilder.WriteString(traceInfo.QPResult.QPLog)
	stringBuilder.WriteString("[Q_SEP]")
	stringBuilder.WriteString(env.GetHost())
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(traceInfo.TraceRequest.PublishId)
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(strconv.FormatInt(time.Now().Unix(), 10))
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(strconv.FormatUint(traceInfo.UserId, 10))
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(strconv.FormatFloat(traceInfo.TraceRequest.Longitude, 'f', 5, 64))
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(strconv.FormatFloat(traceInfo.TraceRequest.Latitude, 'f', 5, 64))
	stringBuilder.WriteString("#")
	stringBuilder.WriteString(strings.Join(traceInfo.EmbeddingSuppressRule, ","))
	stringBuilder.WriteString("#")

	stringBuilder = buildItemsString(ctx, traceInfo, allRecallResult, finalResult, stringBuilder)

	logkit.Info(stringBuilder.String())
}

func buildItemsString(ctx context.Context, traceInfo *traceinfo.TraceInfo, allRecallResult, finalResult model2.StoreInfos, stringBuilder *strings.Builder) *strings.Builder {
	notFiltered := make(map[uint64]bool, len(finalResult))
	for _, store := range finalResult {
		notFiltered[store.StoreId] = true
	}

	recallTypeMap := apollo.GetRecallTypeMap()
	isFirstAlert := true
	for _, store := range allRecallResult {
		stringBuilder.WriteString(strconv.FormatUint(store.StoreId, 10))

		// 拼接 dish ids
		if traceInfo.IsDishListing {
			dishLen := len(store.DishInfosForFeature)
			if dishLen > 0 {
				stringBuilder.WriteString("-")
				for i := 0; i < dishLen; i++ {
					stringBuilder.WriteString(strconv.FormatUint(store.DishInfosForFeature[i].DishId, 10))
					if i < dishLen-1 {
						stringBuilder.WriteString("|")
					}
				}
			}
		} else {
			dishLen := len(store.DishInfos)
			if dishLen > 0 {
				stringBuilder.WriteString("-")
				for i := 0; i < dishLen; i++ {
					stringBuilder.WriteString(strconv.FormatUint(store.DishInfos[i].DishId, 10))
					if i < dishLen-1 {
						stringBuilder.WriteString("|")
					}
				}
			}
		}

		stringBuilder.WriteString(",")

		// 拼接召回类型及召回分数
		for i, recallType := range store.RecallTypes {
			val, isOk := recallTypeMap[recallType]
			if isOk {
				stringBuilder.WriteString(strconv.FormatInt(int64(val), 10))
			} else {
				if isFirstAlert {
					// 防止一次性刷太多 error log
					isFirstAlert = false
					logkit.Error("PrintRecallStatVN buildItemsStringVN failed to find RecallTypeInt", logkit.String("recallType", recallType), logkit.Any("store", store))
				}
				stringBuilder.WriteString("0")
			}

			if len(store.RecallScores) == len(store.RecallTypes) {
				stringBuilder.WriteString("-")
				stringBuilder.WriteString(strconv.FormatFloat(store.RecallScores[i], 'f', 5, 64))
			}
			if i < len(store.RecallTypes)-1 {
				stringBuilder.WriteString("|")
			}
		}

		stringBuilder.WriteString(",")
		stringBuilder.WriteString(strconv.FormatUint(uint64(store.DisplayOpeningStatus), 10))
		stringBuilder.WriteString(",")
		stringBuilder.WriteString(strconv.FormatFloat(store.Distance, 'f', 0, 64))
		stringBuilder.WriteString(",")
		stringBuilder.WriteString(strconv.FormatUint(store.StoreSellWeek, 10))
		stringBuilder.WriteString(",")
		if notFiltered[store.StoreId] {
			stringBuilder.WriteString("1") // 过滤标记：1表示没被过滤掉
			stringBuilder.WriteString(",")
			stringBuilder.WriteString("1") // 预估标记，1表示进入预估（目前没被过滤的都会进预估）
		} else {
			stringBuilder.WriteString("0") // 过滤标记：0表示被过滤掉
			stringBuilder.WriteString(",")
			stringBuilder.WriteString("0") // 预估标记，0表示没进入预估
		}
		stringBuilder.WriteString(",")

		// 拼接排序分数
		if notFiltered[store.StoreId] {
			stringBuilder.WriteString(strconv.FormatFloat(store.PCtrScore, 'f', 4, 64))
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.FormatFloat(store.PCvrScore, 'f', 4, 64))
			stringBuilder.WriteString(",")
			// 仅ID,TH,MY 需要，VN 没有这2个字段
			if traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
				stringBuilder.WriteString(strconv.FormatFloat(store.RelevanceScore, 'f', 4, 64))
				stringBuilder.WriteString(",")
				stringBuilder.WriteString(strconv.FormatFloat(store.RelevanceLevel, 'f', -1, 64))
				stringBuilder.WriteString(",")
			}
			stringBuilder.WriteString(strconv.FormatFloat(store.ReRankScore, 'f', 4, 64))
			// 新+ p_ue score
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.FormatFloat(store.PUEScore, 'f', 4, 64))
			// 拼接A30店铺的前后位置
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.Itoa(store.StoreA30ReRankBeforePos))
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.Itoa(store.StoreA30ReRankAfterPos))
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.Itoa(int(store.MergeType)))
			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.FormatFloat(store.MergeScore, 'f', 4, 64))

			sortAttr := fmt.Sprintf(",%v:%v:%v:%v:%v:%v:%v", store.IsInterForSort, store.ExactMatch, store.StoreAvailableDishCnt, store.GetOpeningStateWithPrecise(), store.IsPreciseStore, store.RecallPriority, store.SortPriority)
			stringBuilder.WriteString(sortAttr)

			stringBuilder.WriteString(",")
			stringBuilder.WriteString(strconv.FormatFloat(store.PRelevanceScore, 'f', 4, 64)) // 相关性模型分数

			stringBuilder.WriteString(",")
			if traceInfo.IsDishListing {
				// 表示新接口
				stringBuilder.WriteString("1,")

				// dump全量的菜品
				dishLen := len(store.DishInfos)
				if dishLen > 0 {
					for idx, dish := range store.DishInfos {
						// 菜品召回类型
						dishRecallTypes := make([]string, 0)
						for _, recallType := range dish.DishRecallTypes {
							val, isOk := recallTypeMap[recallType]
							if isOk {
								dishRecallTypes = append(dishRecallTypes, fmt.Sprintf("%d", val))
							}
						}
						stringBuilder.WriteString(fmt.Sprintf("%d-%s-%f", dish.DishId, strings.Join(dishRecallTypes, "@"), dish.Score))
						if idx != dishLen-1 {
							stringBuilder.WriteString("|")
						}
					}
				}
				stringBuilder.WriteString(",")
			} else {
				// 表示老接口
				stringBuilder.WriteString("0,,")
			}

			// 是否被pRel打压
			stringBuilder.WriteString(strconv.Itoa(store.IsFilterByPRelevanceScore))
			stringBuilder.WriteString(",")
		} else {
			if traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
				stringBuilder.WriteString(",,,,,,,,,,,,,,,")
			} else {
				stringBuilder.WriteString(",,,,,,,,,,,,,")
			}
		}

		// 是否属于少无结果 1=属于少无结果，0属于主搜，-1表示默认值
		if store.ItemSceneType == foodalgo_search.ItemSceneType_NoFewItem {
			stringBuilder.WriteString("1,") // 1 表示少无结果
		} else {
			if store.NormalRecallFilterFlag == 1 {
				stringBuilder.WriteString("-1,") // -1 表示主搜召回了，但是没留到最后
			} else if store.NormalRecallFilterFlag == 2 {
				stringBuilder.WriteString("0,") // 1 表示主搜召回了，并且留到最后
			}
		}

		// 是否是normal进入精排, 1表示进入了精排
		stringBuilder.WriteString(fmt.Sprintf("%d,", store.IsNormalEnterPredict))

		// 是否被normal召回但是最终被filter过滤掉：0表示不是normal召回，1表示被normal召回但被过滤了，2表示被normal召回且没被过滤
		stringBuilder.WriteString(fmt.Sprintf("%d,", store.NormalRecallFilterFlag))

		stringBuilder.WriteString(";")
	}
	return stringBuilder
}

func PrintDumpLogWithJson(ctx context.Context, traceInfo *traceinfo.TraceInfo, allRecallResult, finalResult model2.StoreInfos) {
	if !abtest.GetNewDumpLogSwitch(traceInfo.AbParamClient) {
		return
	}
	if !isNeedPrintDumpLog(traceInfo) {
		return
	}
	// 主站 dump log 和站内区分开
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMainSite {
		PrintDumpLogForMainSite(ctx, traceInfo, finalResult)
		return
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePrintDumpLogJSON, time.Since(pt))
	}()

	stringBuilder := builderPool.Get().(*strings.Builder)
	stringBuilder.Reset()
	defer builderPool.Put(stringBuilder)

	batchSize := abtest.GetNewDumpLogPageSize(traceInfo.AbParamClient)

	topicName := apollo.SearchApolloCfg.DumpLogKafkaTopicName
	if len(topicName) == 0 {
		topicName = "foodysearch.logdump.mp.search"
	}

	// 把 finalResult 按照 batchSize 分批
	total := len(finalResult) / batchSize
	if len(finalResult)%batchSize > 0 {
		total++
	}
	// 无结果 fast return
	if total == 0 {
		tempDumpLog := DumpLog{
			Topic:     topicName,
			BatchNum:  1,
			BatchSize: batchSize,
			PublishId: traceInfo.TraceRequest.PublishId,
			DumpLogRequest: &DumpLogRequest{
				QueryRaw:     traceInfo.TraceRequest.QueryRaw,
				QueryKeyword: traceInfo.QueryKeyword,
				CID:          env.GetCID(),
				Host:         env.GetHost(),
				Timestamp:    time.Now().Unix(),
				UserId:       traceInfo.UserId,
				Latitude:     traceInfo.TraceRequest.Latitude,
			},
			DumpLogContext: &DumpLogContext{
				QPLog:                 traceInfo.QPResult.QPLog,
				EmbeddingSuppressRule: traceInfo.EmbeddingSuppressRule,
			},
			StoreFinalItems: nil,
		}
		tempDumpLog.StoreFilterItems, tempDumpLog.DishFilterItems = createDumpLogFilterItem(traceInfo)
		jsonStr, _ := json.Marshal(tempDumpLog)
		logkit.FromContext(ctx).Info("[LOG_NEW_DUMP]", logkit.String("logDump", string(jsonStr)))
		return
	}

	recallTypeMapping := apollo.GetRecallTypeMap()

	// 分批打日志，不需要开并发
	for i := 0; i < total; i++ {
		var tempStores model2.StoreInfos
		if i+1 < total {
			tempStores = finalResult[i*batchSize : (i+1)*batchSize]
		} else {
			tempStores = finalResult[i*batchSize:]
		}
		if i == 0 {
			// 第一页会携带全部上下文
			tempDumpLog := DumpLog{
				Topic:     topicName,
				BatchNum:  1,
				BatchSize: batchSize,
				PublishId: traceInfo.TraceRequest.PublishId,
				DumpLogRequest: &DumpLogRequest{
					QueryRaw:     traceInfo.TraceRequest.QueryRaw,
					QueryKeyword: traceInfo.QueryKeyword,
					CID:          env.GetCID(),
					Host:         env.GetHost(),
					Timestamp:    time.Now().Unix(),
					UserId:       traceInfo.UserId,
					Latitude:     traceInfo.TraceRequest.Latitude,
				},
				DumpLogContext: &DumpLogContext{
					QPLog:                 traceInfo.QPResult.QPLog,
					EmbeddingSuppressRule: traceInfo.EmbeddingSuppressRule,
				},
				StoreFinalItems: createDumpLogStore(ctx, tempStores, recallTypeMapping),
			}
			tempDumpLog.StoreFilterItems, tempDumpLog.DishFilterItems = createDumpLogFilterItem(traceInfo)
			jsonStr, _ := json.Marshal(tempDumpLog)
			logkit.FromContext(ctx).Info("[NEW_LOG_DUMP]", logkit.String("logDump", string(jsonStr)))
		} else {
			// 第二页开始，都只要携带门店分页详情即可
			tempDumpLog := DumpLog{
				Topic:           topicName,
				BatchNum:        i + 1,
				BatchSize:       batchSize,
				PublishId:       traceInfo.TraceRequest.PublishId,
				StoreFinalItems: createDumpLogStore(ctx, tempStores, recallTypeMapping),
				DumpLogRequest:  nil,
				DumpLogContext:  nil,
			}
			jsonStr, _ := json.Marshal(tempDumpLog)
			logkit.FromContext(ctx).Info("[LOG_NEW_DUMP]", logkit.String("logDump", string(jsonStr)))
		}
	}
}

func createDumpLogFilterItem(traceInfo *traceinfo.TraceInfo) ([]*DumpLogStoreFilterItem, []*DumpLogDishFilterItem) {
	filteredStores := make([]*DumpLogStoreFilterItem, 0)
	filteredDishes := make([]*DumpLogDishFilterItem, 0)

	for reason, storeList := range traceInfo.FilteredStores {
		for _, s := range storeList {

			if s.DishId > 0 {
				filteredDishes = append(filteredDishes, &DumpLogDishFilterItem{
					DishId:       s.DishId,
					FilterReason: reason,
				})
			}
			if s.StoreId > 0 {
				filteredStores = append(filteredStores, &DumpLogStoreFilterItem{
					StoreId:      s.StoreId,
					FilterReason: reason,
				})
			}

		}
	}
	return filteredStores, filteredDishes
}

type DumpLog struct {
	Topic            string                    `json:"topic"`                        // 需要转发的kafka topic name
	BatchNum         int                       `json:"batch_num"`                    // 针对 StoreFilterItems 的 分页数，只有也一页有全部信息，其他分页只有 StoreFilterItems 的分页的信息
	BatchSize        int                       `json:"batch_size"`                   // 针对 StoreFilterItems 的 分页大小
	PublishId        string                    `json:"publish_id"`                   // publish_id
	DumpLogRequest   *DumpLogRequest           `json:"dump_log_request,omitempty"`   // 请求参数
	DumpLogContext   *DumpLogContext           `json:"dump_log_context,omitempty"`   // context参数
	StoreFinalItems  []*DumpLogStore           `json:"store_final_items,omitempty"`  // 最终store列表
	StoreFilterItems []*DumpLogStoreFilterItem `json:"store_filter_items,omitempty"` // 被过滤的store列表
	DishFilterItems  []*DumpLogDishFilterItem  `json:"dish_filter_items,omitempty"`  // 被过滤的dish列表
}

type DumpLogRequest struct {
	QueryRaw     string  `json:"query_raw,omitempty"`     // 原始词
	QueryKeyword string  `json:"query_keyword,omitempty"` // 实际搜索词
	CID          string  `json:"cid,omitempty"`           // CID
	Host         string  `json:"host,omitempty"`          // host
	Timestamp    int64   `json:"timestamp,omitempty"`     // 时间戳
	UserId       uint64  `json:"user_id,omitempty"`       // 用户ID
	Longitude    float64 `json:"longitude,omitempty"`     // 经度
	Latitude     float64 `json:"latitude,omitempty"`      // 纬度
}

type DumpLogContext struct {
	QPLog                 string   `json:"qp_log,omitempty"`                  // 查询处理日志
	EmbeddingSuppressRule []string `json:"embedding_suppress_rule,omitempty"` // 嵌入抑制规则
}

type DumpLogStore struct {
	StoreId          uint64            `json:"store_id,omitempty"`           // 商店ID
	RecallTypes      []*RecallTypeInfo `json:"recall_types,omitempty"`       // 召回类型信息
	DishInfos        []*DumpLogDish    `json:"dish_infos,omitempty"`         // 全部的菜品详情
	DishFeatureInfos []*DumpLogDish    `json:"dish_feature_infos,omitempty"` // 参与打分的菜品详情

	DisplayOpeningStatus    int32   `json:"display_opening_status,omitempty"`     // DisplayOpeningStatus
	Distance                float64 `json:"distance,omitempty"`                   // 距离
	StoreSellWeek           uint64  `json:"store_sell_week,omitempty"`            // 周销量
	AvailableDishCnt        int64   `json:"available_dish_cnt,omitempty"`         // 可用菜品数量
	OpeningStateWithPrecise int32   `json:"opening_state_with_precise,omitempty"` // OpeningState

	IsFiltered     int32  `json:"is_filtered,omitempty"`       // 是否被过滤
	IsEstimated    int32  `json:"is_estimated,omitempty"`      // 是否进入预估
	IsInterForSort int32  `json:"is_inter_for_sort,omitempty"` // 是否用于排序的干预
	IsPreciseStore uint32 `json:"is_precise_store,omitempty"`  // 是否精确商店
	ExactMatch     uint32 `json:"exact_match,omitempty"`       // 是否精确匹配

	PCtrScore       float64 `json:"p_ctr_score,omitempty"`       // 点击率预估分数
	PCvrScore       float64 `json:"p_cvr_score,omitempty"`       // 转化率预估分数
	RelevanceScore  float64 `json:"relevance_score,omitempty"`   // 相关性分数
	RelevanceLevel  float64 `json:"relevance_level,omitempty"`   // 相关性等级
	ReRankScore     float64 `json:"re_rank_score,omitempty"`     // 重排序分数
	PUEScore        float64 `json:"p_ue_score,omitempty"`        // 用户体验预估分数
	PRelevanceScore float64 `json:"p_relevance_score,omitempty"` // 相关性模型分数
	MergeType       uint32  `json:"merge_type,omitempty"`        // 合并类型
	MergeScore      float64 `json:"merge_score,omitempty"`       // 合并分数

	A30BeforePos int `json:"a30_before_pos,omitempty"` // A30店铺重排序前位置
	A30AfterPos  int `json:"a30_after_pos,omitempty"`  // A30店铺重排序后位置

	RecallPriority int `json:"recall_priority,omitempty"` // 召回优先级
	SortPriority   int `json:"sort_priority,omitempty"`   // 排序优先级
}

type DumpLogStoreFilterItem struct {
	StoreId      uint64 `json:"store_id,omitempty"`      // 商店ID
	FilterReason string `json:"filter_reason,omitempty"` // 被过滤的原因
}

type DumpLogDishFilterItem struct {
	DishId       uint64 `json:"dish_id,omitempty"`       // 商店ID
	FilterReason string `json:"filter_reason,omitempty"` // 被过滤的原因
}

type RecallTypeInfo struct {
	RecallType int32   `json:"recall_type,omitempty"` // 召回类型
	Score      float64 `json:"score,omitempty"`       // 召回分数
}

type DumpLogDish struct {
	DishId      uint64  `json:"dish_id,omitempty"`      // 菜品ID
	RecallTypes []int32 `json:"recall_types,omitempty"` // 菜品召回类型
	Score       float64 `json:"score,omitempty"`        // 菜品分数
}

func createDumpLogStore(ctx context.Context, stores model2.StoreInfos, recallTypeMapping map[string]int32) []*DumpLogStore {
	rsp := make([]*DumpLogStore, 0, len(stores))
	for _, s := range stores {
		// 门店召回类型
		storeRecallTypes := make([]*RecallTypeInfo, 0)
		for idx, recallType := range s.RecallTypes {
			val, isOk := recallTypeMapping[recallType]
			if isOk && idx < len(s.RecallScores) {
				storeRecallTypes = append(storeRecallTypes, &RecallTypeInfo{
					RecallType: val,
					Score:      s.RecallScores[idx],
				})
			} else {
				logkit.FromContext(ctx).Error("createDumpLogStore invalid recallType", logkit.String("recallType", recallType))
			}
		}
		// 菜品infos
		dishInfos := make([]*DumpLogDish, 0, len(s.DishInfos))
		for _, dish := range s.DishInfos {
			// 菜品召回类型
			dishRecallTypes := make([]int32, 0)
			for _, recallType := range dish.DishRecallTypes {
				val, isOk := recallTypeMapping[recallType]
				if isOk {
					dishRecallTypes = append(dishRecallTypes, val)
				}
			}
			dishInfos = append(dishInfos, &DumpLogDish{
				DishId:      dish.DishId,
				RecallTypes: dishRecallTypes,
				Score:       dish.Score,
			})
		}
		// 送feature的菜品infos
		dishInfosForFeature := make([]*DumpLogDish, 0, len(s.DishInfosForFeature))
		for _, dish := range s.DishInfosForFeature {
			// 菜品召回类型
			dishRecallTypes := make([]int32, 0)
			for _, recallType := range dish.DishRecallTypes {
				val, isOk := recallTypeMapping[recallType]
				if isOk {
					dishRecallTypes = append(dishRecallTypes, val)
				}
			}
			dishInfosForFeature = append(dishInfosForFeature, &DumpLogDish{
				DishId:      dish.DishId,
				RecallTypes: dishRecallTypes,
				Score:       dish.Score,
			})
		}

		rsp = append(rsp, &DumpLogStore{
			StoreId:                 s.StoreId,
			RecallTypes:             storeRecallTypes,
			DishInfos:               dishInfos,
			DishFeatureInfos:        dishInfosForFeature,
			DisplayOpeningStatus:    int32(s.DisplayOpeningStatus),
			Distance:                s.Distance,
			StoreSellWeek:           s.StoreSellWeek,
			AvailableDishCnt:        s.StoreAvailableDishCnt,
			OpeningStateWithPrecise: s.GetOpeningStateWithPrecise(),
			IsFiltered:              1,
			IsEstimated:             1,
			IsInterForSort:          s.IsInterForSort,
			IsPreciseStore:          s.IsPreciseStore,
			ExactMatch:              s.ExactMatch,
			PCtrScore:               s.PCtrScore,
			PCvrScore:               s.PCvrScore,
			RelevanceScore:          s.RelevanceScore,
			RelevanceLevel:          s.RelevanceLevel,
			ReRankScore:             s.ReRankScore,
			PUEScore:                s.PUEScore,
			PRelevanceScore:         s.PRelevanceScore,
			MergeType:               s.MergeType,
			MergeScore:              s.MergeScore,
			A30BeforePos:            s.StoreA30ReRankBeforePos,
			A30AfterPos:             s.StoreA30ReRankAfterPos,
			RecallPriority:          s.RecallPriority,
			SortPriority:            s.SortPriority,
		})
	}
	return rsp
}
