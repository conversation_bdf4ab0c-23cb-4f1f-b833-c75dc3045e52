package predata

// ValueType枚举 - 数据类型定义
const (
	TypeString         = 1  // string
	TypeStringList     = 2  // string[]
	TypeBool           = 3  // bool
	TypeUint64List     = 4  // uint64[]
	TypeStringListList = 5  // string[][]
	TypeUint32         = 6  // uint32
	TypeUint32List     = 7  // []uint32
	TypeUint64         = 8  // uint64
	TypeInt32          = 9  // int32
	TypeInt64          = 10 // int64
	TypeFloat32        = 11 // float32
	TypeFloat32List    = 12 // []float32
	TypeFloat64        = 13 // float64
	TypeFloat64List    = 14 // []float64
	TypeInt            = 15 // int
	TypeInt32List      = 16 // []int32
	TypeInt64List      = 17 // []int64
)

// 查询源类型
const (
	QuerySourceTypeString      = "string"
	QuerySourceTypeBool        = "bool"
	QuerySourceTypeList        = "list"
	QuerySourceTypeStrFromList = "str_from_list"
	QuerySourceTypeListUint32  = "list_uint32"
	QuerySourceTypeListUint64  = "list_uint64"
	QuerySourceTypeListInt32   = "list_int32"
	QuerySourceTypeListInt64   = "list_int64"
	QuerySourceTypeInt         = "int"
	QuerySourceTypeInt32       = "int32"
	QuerySourceTypeInt64       = "int64"
	QuerySourceTypeUint32      = "uint32"
	QuerySourceTypeUint64      = "uint64"
	QuerySourceTypeFloat32     = "float32"
	QuerySourceTypeFloat64     = "float64"
	QuerySourceTypeListFloat32 = "list_float32"
	QuerySourceTypeListFloat64 = "list_float64"
)

// NER类型定义常量
const (
	NerTypeStore      = "Store"
	NerTypeDish       = "Dish"
	NerTypeLocation   = "Location"
	NerTypeCategory   = "Category"
	NerTypeBrand      = "Brand"
	NerTypeIngredient = "Ingredient"
	NerTypeProduct    = "Product"
	NerTypeCook       = "Cook"
	NerTypeUnknown    = "Unknown"
)

// 数据源键名前缀
const (
	KeyPrefixRequest         = "Request_"
	KeyPrefixQPResult        = "QPResult_"
	KeyPrefixUnsettledStore  = "UnsettledStoreInfo_"
	KeyPrefixOptIntervention = "OptIntervention_"
	KeyPrefixRewriteQuery    = "RewriteQuery_"
	KeyPrefixRewriteSegment  = "RewriteSegment_"
	KeyPrefixRewriteNer      = "RewriteNer_"
	KeyPrefixEnlargeRewrite  = "EnlargeRewriteQuery_"
)

// NerResult 定义一个结构用于存储带有原始词、ASCII字段的结果
type NerResult struct {
	Original []string
	Ascii    []string
}

// RangeItem 定义范围项结构
type RangeItem struct {
	From        *float64 `json:"from,omitempty"`
	FromInclude bool     `json:"fromInclude,omitempty"`
	To          *float64 `json:"to,omitempty"`
	ToInclude   bool     `json:"toInclude,omitempty"`
}

// 所有支持的NER类型
var defaultNerTypes = []string{
	NerTypeStore,
	NerTypeDish,
	NerTypeLocation,
	NerTypeCategory,
	NerTypeBrand,
	NerTypeIngredient,
	NerTypeProduct,
	NerTypeCook,
	NerTypeUnknown,
}
