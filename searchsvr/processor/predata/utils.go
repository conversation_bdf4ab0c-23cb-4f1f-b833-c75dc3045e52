package predata

import (
	"context"
	"encoding/json"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// createDataSourceItem 创建DataSourceItem辅助函数
func createDataSourceItem(value interface{}, valueType int) traceinfo.DataSourceItem {
	return traceinfo.DataSourceItem{
		Value:     value,
		ValueType: valueType,
	}
}

// IsDataSourceNil 检查数据源项是否为空
func IsDataSourceNil(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, dataResult *traceinfo.DataSourceItem) bool {
	// 空指针或空值检查
	if dataResult == nil || dataResult.Value == nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil",
			logkit.String("recallName", recallName))
		return true
	}

	// 根据不同类型判断是否为空
	switch dataResult.ValueType {
	case TypeString:
		if value, ok := dataResult.Value.(string); ok {
			if len(value) == 0 {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is empty string",
					logkit.String("recallName", recallName))
				return true
			}
			return false
		}
		// 类型转换失败
		logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil type conversion failed for string",
			logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		return true

	case TypeStringList:
		if value, ok := dataResult.Value.([]string); ok {
			if len(value) == 0 {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is empty string list",
					logkit.String("recallName", recallName))
				return true
			}
			return false
		}
		// 类型转换失败
		logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil type conversion failed for string list",
			logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		return true

	case TypeBool:
		if value, ok := dataResult.Value.(bool); ok {
			// 布尔值为false时被视为空
			if !value {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil boolean data is false",
					logkit.String("recallName", recallName))
				return true
			}
			return false
		}
		// 类型转换失败
		logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil type conversion failed for bool",
			logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		return true

	case TypeUint64List:
		if value, ok := dataResult.Value.([]uint64); ok {
			if len(value) == 0 {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is empty uint64 list",
					logkit.String("recallName", recallName))
				return true
			}
			return false
		}
		// 类型转换失败
		logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil type conversion failed for uint64 list",
			logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		return true

	case TypeStringListList:
		if value, ok := dataResult.Value.([][]string); ok {
			if len(value) == 0 {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is empty string list list",
					logkit.String("recallName", recallName))
				return true
			}
			return false
		}
		// 类型转换失败
		logkit.FromContext(ctx).Error("RecallConfiguration IsDataSourceNil type conversion failed for string list list",
			logkit.String("recallName", recallName), logkit.Any("data", dataResult.Value))
		return true

	default:
		// 对于其他类型，简单检查空值
		if dataResult.Value == nil {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration IsDataSourceNil data is nil for unknown type",
				logkit.String("recallName", recallName), logkit.Int("valueType", dataResult.ValueType))
			return true
		}
		return false
	}
}

// GetDataSourceParams 将数据源映射转换为参数映射
func GetDataSourceParams(dataSourceMap map[string]traceinfo.DataSourceItem) map[string]interface{} {
	params := make(map[string]interface{}, len(dataSourceMap))
	for key, item := range dataSourceMap {
		params[key] = item.Value
	}
	return params
}

// GetQuerySource 获取指定字段的数据源项
func GetQuerySource(ctx context.Context, traceInfo *traceinfo.TraceInfo, field string) *traceinfo.DataSourceItem {
	value, exists := traceInfo.RecallConfigurationDataSource[field]
	if !exists {
		logkit.FromContext(ctx).Error("RecallConfiguration GetQuerySource field not found",
			logkit.String("field", field))
		return nil
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GetQuerySource",
		logkit.String("field", field), logkit.Any("value", value))
	return &value
}

// MarshalRange 将范围项数组序列化为JSON字符串
func MarshalRange(ctx context.Context, rangeItems []RangeItem) string {
	jsonBytes, err := json.Marshal(rangeItems)
	if err != nil {
		logkit.FromContext(ctx).Error("RecallConfiguration MarshalRange failed",
			logkit.String("error", err.Error()), logkit.Any("rangeItems", rangeItems))
		return ""
	}
	return string(jsonBytes)
}

// UnMarshalRange 将JSON字符串反序列化为范围项数组
func UnMarshalRange(ctx context.Context, jsonStr string) []RangeItem {
	if jsonStr == "" {
		return nil
	}

	var rangeItems []RangeItem
	err := json.Unmarshal([]byte(jsonStr), &rangeItems)
	if err != nil {
		logkit.FromContext(ctx).Error("RecallConfiguration UnMarshalRange failed",
			logkit.String("error", err.Error()), logkit.String("jsonStr", jsonStr))
		return nil
	}

	return rangeItems
}
