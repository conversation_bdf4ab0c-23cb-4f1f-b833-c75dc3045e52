package predata

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 初始化查询字符串
func initQueryString(ctx context.Context, traceInfo *traceinfo.TraceInfo, dsMap map[string]traceinfo.DataSourceItem, recallConfigs []*apollo.StoreRecallConfig) {
	for _, r := range recallConfigs {
		if len(r.QueryStringMap) > 0 {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initQueryString with QueryStringMap",
				logkit.String("recallType", r.RecallTypeStr),
				logkit.String("recallName", r.RecallName),
				logkit.Any("queryStringMap", r.QueryStringMap))
			initEveryQueryString(ctx, traceInfo, dsMap, r.RecallLogName, r.RecallType, r.RecallId, r.QueryStringMap)
		} else {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initQueryString without QueryStringMap",
				logkit.String("recallType", r.RecallTypeStr),
				logkit.String("recallName", r.RecallName))
		}
	}
}

// initEveryQueryString 初始化每个查询字符串
func initEveryQueryString(ctx context.Context, traceInfo *traceinfo.TraceInfo, dsMap map[string]traceinfo.DataSourceItem, recallName string, recallType int32, recallId string, queryStringMap map[string][]string) {
	queryStringKey := fmt.Sprintf("QPResult_QueryStringList_Queries_%d_%s", recallType, recallId)
	queryFieldKey := fmt.Sprintf("QPResult_QueryStringList_Fields_%d_%s", recallType, recallId)

	if len(traceInfo.QPResult.NerAndRecallResult) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initQueryString traceInfo.QPResult.NerAndRecallResult is nil, return",
			logkit.String("recallName", recallName),
			logkit.String("queryStringKey", queryStringKey),
			logkit.String("queryFieldKey", queryFieldKey))
		dsMap[queryStringKey] = createDataSourceItem(nil, TypeStringList)
		dsMap[queryFieldKey] = createDataSourceItem(nil, TypeStringList)
		return
	}

	queryStringList := makeQueryStringList(ctx, traceInfo, queryStringMap)
	traceInfo.QPResult.QueryStringListMap[recallType] = queryStringList

	if len(queryStringList) > 0 {
		queryStringListQueries := make([]string, 0)
		queryStringListFields := make([]string, 0)
		for _, item := range queryStringList {
			queryStringListQueries = append(queryStringListQueries, item.Query)
			queryStringListFields = append(queryStringListFields, strings.Join(item.Fields, ","))
		}
		dsMap[queryStringKey] = createDataSourceItem(queryStringListQueries, TypeStringList)
		dsMap[queryFieldKey] = createDataSourceItem(queryStringListFields, TypeStringList)
	} else {
		dsMap[queryStringKey] = createDataSourceItem(nil, TypeStringList)
		dsMap[queryFieldKey] = createDataSourceItem(nil, TypeStringList)
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initQueryString",
		logkit.String("recallName", recallName),
		logkit.String("queryStringKey", queryStringKey),
		logkit.Any("queryStringValue", dsMap[queryStringKey]),
		logkit.String("queryFieldKey", queryFieldKey),
		logkit.Any("queryFieldValue", dsMap[queryFieldKey]))
}

// makeQueryStringList 创建查询字符串列表
func makeQueryStringList(ctx context.Context, traceInfo *traceinfo.TraceInfo, queryStringMap map[string][]string) []traceinfo.QueryStringItem {
	termWeightMap := make(map[string]float32, 0)
	for _, i := range traceInfo.QPResult.TermWeightList {
		termWeightMap[*i.Token] = *i.Weight
	}
	queryStringItemList := make([]traceinfo.QueryStringItem, 0)

	// 按照 nerType 分组, eg: { "STORE": ["sushi yay", "beef rice"], "DISH": ["d", "f"], "UNKNOWN": ["g", "h]}
	tokenGroups := make(map[qp.NERType][]string)
	for _, ner := range traceInfo.QPResult.NerAndRecallResult {
		for _, nerType := range ner.GetNerType() {
			if val, ok := tokenGroups[nerType]; ok {
				val = append(val, ner.GetToken())
				tokenGroups[nerType] = val
			} else {
				t := make([]string, 0)
				t = append(t, ner.GetToken())
				tokenGroups[nerType] = t
			}
		}
	}

	// nerType="STORE", tokens = ["sushi yay", "beef rice"],
	for nerType, tokens := range tokenGroups {
		// queryString 的fields 不一定有配置
		nerTypeStr := qp.NERType_name[int32(nerType)]
		fields, ok := queryStringMap[nerTypeStr]
		if !ok {
			// 算法要求，如果遇到没配置的，比如 category，product 等类型，一律当成 default 处理
			if defaultFields, defaultOk := queryStringMap["DEFAULT"]; defaultOk {
				fields = defaultFields
			}
		}
		// 若没有配置，fast return
		if len(fields) == 0 {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration makeQueryStringList fields is nil",
				logkit.Any("nerType", nerType),
				logkit.Strings("tokens", tokens))
			continue
		}

		for _, token := range tokens {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration makeQueryStringList",
				logkit.Any("nerType", nerType),
				logkit.Strings("tokens", tokens),
				logkit.String("token", token))

			tmpQuery := ""
			tmpTokens := strings.Split(token, " ")
			for _, tt := range tmpTokens {
				tt = strings.TrimSpace(tt)
				// 单字符，比如 -,&,$# 会引起 es 的报错
				if len(tt) == 1 {
					tt = removePunctuation(tt)
				}
				if len(tt) > 0 {
					// 用原始 token 去拿信号，用过滤后 token 做 queryString 的拼接
					validToken := handleEsReservedChar(tt)
					if len(validToken) > 0 {
						if weight, ok := termWeightMap[tt]; ok {
							tmpQuery += fmt.Sprintf("%s^%f", validToken, weight) + " "
						} else {
							// 算法要求，如果没有词权重信号，默认是1.0
							tmpQuery += fmt.Sprintf("%s^%f", validToken, 1.0) + " "
						}
					}
				}
			}
			tmpQuery = strings.TrimSpace(tmpQuery)
			if len(tmpQuery) > 0 {
				t := traceinfo.QueryStringItem{
					Fields: fields,
					Query:  strings.TrimRight(tmpQuery, " "),
				}
				queryStringItemList = append(queryStringItemList, t)
			}
		}
	}

	// 兜底
	if len(queryStringItemList) == 0 {
		fields, ok := queryStringMap["DEFAULT"]
		if ok {
			query := strings.TrimSpace(traceInfo.QueryKeyword)
			query = removePunctuation(query)
			if len(query) > 0 {
				t := traceinfo.QueryStringItem{
					Fields: fields,
					Query:  query,
				}
				queryStringItemList = append(queryStringItemList, t)
			}
		}
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration makeQueryStringList",
		logkit.Any("queryStringMap", queryStringMap),
		logkit.Any("tokenGroups", tokenGroups),
		logkit.Any("termWeightMap", termWeightMap),
		logkit.Any("queryStringItemList", queryStringItemList))
	return queryStringItemList
}

var singleCharRe = regexp.MustCompile(`[\p{P}+\p{S}+]`)

// removePunctuation 移除标点符号
func removePunctuation(input string) string {
	txt := singleCharRe.ReplaceAllString(input, "")
	return txt
}

// 定义需要替换的保留字符和替换后的字符
var replacements = map[string]string{
	"[":  `\[`,
	"]":  `\]`,
	"(":  `\(`,
	")":  `\)`,
	"{":  `\{`,
	"}":  `\}`,
	"+":  `\+`,
	"-":  `\-`,
	"&&": `\&\&`,
	"||": `\|\|`,
	"!":  `\!`,
	`"`:  `\"`,
	":":  `\:`,
	"/":  `\/`,
	"*":  `\*`,
	"~":  `\~`,
}

// 构建替换的正则表达式
var pattern = "[" + regexp.QuoteMeta(`[](){}+&&||!"`) + ":/*~]"

// 编译正则表达式
var esCharRe = regexp.MustCompile(pattern)

// handleEsReservedChar 处理ES保留字符
func handleEsReservedChar(input string) string {
	// es 保留词： https://www.elastic.co/guide/en/elasticsearch/reference/current/query-dsl-query-string-query.html#_reserved_characters
	newInput := esCharRe.ReplaceAllStringFunc(input, func(match string) string {
		if replacement, ok := replacements[match]; ok {
			return replacement
		}
		return match
	})
	return newInput
}
