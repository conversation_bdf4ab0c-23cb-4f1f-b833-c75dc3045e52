package predata

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func InitDataSourceMap(ctx context.Context, traceInfo *traceinfo.TraceInfo) map[string]traceinfo.DataSourceItem {
	keyword := traceInfo.QueryKeyword
	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration InitDataSourceMap", logkit.String("query", keyword))

	// 创建有足够初始容量的map
	dataSourceMap := make(map[string]traceinfo.DataSourceItem, 128)

	// 分段初始化各类数据，提高代码可读性
	initRequestBasicData(ctx, traceInfo, dataSourceMap)
	initFilterTypeData(ctx, traceInfo, dataSourceMap)
	initPriceRange(ctx, traceInfo, dataSourceMap)
	initCollectionFilter(ctx, traceInfo.TraceRequest.GetFilterType(), dataSourceMap)
	initQPResultData(ctx, traceInfo, dataSourceMap)
	initPredictConfigData(traceInfo, dataSourceMap)
	initOptInterventionData(ctx, traceInfo, dataSourceMap)
	initUnsettledStore(ctx, traceInfo, keyword, dataSourceMap)
	initCategoryIntentions(traceInfo, dataSourceMap)
	initNerData(ctx, traceInfo, dataSourceMap)
	initRewriteKeywords(traceInfo, dataSourceMap)
	initOpenStatusScriptPram(dataSourceMap)
	initDropWord(ctx, traceInfo, dataSourceMap)
	// 其它杂项数据
	dataSourceMap["IsBrandProtectionKeyword"] = createDataSourceItem(traceInfo.IsHitBrandProtection, TypeBool)
	dataSourceMap["IsVectorRecall"] = createDataSourceItem(true, TypeBool)

	return dataSourceMap
}

// 补充ES数据源，调用初始化城市距离和查询字符串函数
func InitSupplementESDataSource(ctx context.Context, traceInfo *traceinfo.TraceInfo, dsMap map[string]traceinfo.DataSourceItem, recallConfigs []*apollo.StoreRecallConfig, recallCommon *apollo.RecallCommon) {
	// 初始化城市距离配置
	initCityDistance(ctx, dsMap, traceInfo.TraceRequest.CityId, recallCommon.CityDistanceConf)

	// 初始化查询字符串配置
	initQueryString(ctx, traceInfo, dsMap, recallConfigs)

	// 日志记录
	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration InitSupplementESDataSource completed",
		logkit.String("query", traceInfo.QueryKeyword),
		logkit.Uint32("cityId", traceInfo.TraceRequest.CityId))
}
