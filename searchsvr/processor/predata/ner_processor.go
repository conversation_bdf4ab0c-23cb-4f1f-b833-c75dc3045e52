package predata

import (
	"context"
	"fmt"
	"strconv"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

// initNerData 初始化所有NER相关数据
func initNerData(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	// 初始化三种NER结果
	initNer(ctx, traceInfo, "NerResult", dataSourceMap)
	initNer(ctx, traceInfo, "NerAndRecallResult", dataSourceMap)
	initNer(ctx, traceInfo, "NerOrRecallResult", dataSourceMap)
	initRewriteNer(ctx, traceInfo, dataSourceMap)
}

// initNer 初始化命名实体识别结果
func initNer(ctx context.Context, traceInfo *traceinfo.TraceInfo, nerResultType string, dataSourceMap map[string]traceinfo.DataSourceItem) {
	var nerResult, nerAsciiResult []*qp.Ner

	// 根据不同的NER结果类型选择对应的数据
	switch nerResultType {
	case "NerResult":
		nerResult = traceInfo.QPResult.NerResult
		nerAsciiResult = traceInfo.QPResult.NerAsciiResult
	case "NerAndRecallResult":
		nerResult = traceInfo.QPResult.NerAndRecallResult
		nerAsciiResult = traceInfo.QPResult.NerAndRecallAsciiResult
	case "NerOrRecallResult":
		nerResult = traceInfo.QPResult.NerOrRecallResult
		nerAsciiResult = traceInfo.QPResult.NerOrRecallAsciiResult
	}

	// 初始化所有NER类型的默认值
	initDefaultNerValues := func() {
		for _, nerType := range defaultNerTypes {
			// 设置原始字段和ASCII字段的默认值
			originalKey := fmt.Sprintf("QPResult_%s_%s", nerResultType, nerType)
			asciiKey := fmt.Sprintf("QPResult_%s_%s_Ascii", nerResultType, nerType)

			dataSourceMap[originalKey] = createDataSourceItem(nil, TypeStringList)
			dataSourceMap[asciiKey] = createDataSourceItem(nil, TypeStringList)
		}
	}

	// 空值处理
	if len(nerResult) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initNer without ner info.",
			logkit.String("nerResultType", nerResultType))
		initDefaultNerValues()
		return
	}

	// 提取NER结果
	nerWords := extractNerResult(nerResult, nerAsciiResult)

	// 设置数据源值
	for nerType, words := range nerWords {
		// 设置原始字段
		originalKey := fmt.Sprintf("QPResult_%s_%s", nerResultType, nerType)
		dataSourceMap[originalKey] = createDataSourceItem(words.Original, TypeStringList)

		// 设置ASCII字段
		asciiKey := fmt.Sprintf("QPResult_%s_%s_Ascii", nerResultType, nerType)
		dataSourceMap[asciiKey] = createDataSourceItem(words.Ascii, TypeStringList)
	}
}

// initRewriteNer 初始化改写NER结果
func initRewriteNer(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	// 处理NER改写结果
	for i, nerResults := range traceInfo.QPResult.RewriteNerResult {
		index := strconv.Itoa(i)
		nerWords := extractNerResult(nerResults, nil)

		for nerType, words := range nerWords {
			// 设置原始字段
			originalKey := fmt.Sprintf("RewriteNer_%s_%s", nerType, index)
			dataSourceMap[originalKey] = createDataSourceItem(words.Original, TypeStringList)

			// 设置ASCII字段
			asciiKey := fmt.Sprintf("RewriteNer_%s_Ascii_%s", nerType, index)
			dataSourceMap[asciiKey] = createDataSourceItem(words.Ascii, TypeStringList)
		}
	}

	// 处理查询改写
	for i, rewriteQuery := range traceInfo.QPResult.RewriteQuery {
		index := strconv.Itoa(i)
		dataSourceMap["RewriteQuery_"+index] = createDataSourceItem(rewriteQuery, TypeString)
	}

	// 处理分段改写
	for i, rewriteSegment := range traceInfo.QPResult.RewriteSegments {
		index := strconv.Itoa(i)
		dataSourceMap["RewriteSegment_"+index] = createDataSourceItem(rewriteSegment.Segments, TypeStringList)
	}

	// 处理扩展改写查询
	for i, enlargeRewriteQuery := range traceInfo.QPResult.EnlargeRewriteQuerys {
		index := strconv.Itoa(i)
		key := "EnlargeRewriteQuery_" + index
		dataSourceMap[key] = createDataSourceItem(enlargeRewriteQuery, TypeString)
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration init EnlargeRewriteQuery fill with value",
			logkit.String("key", key), logkit.String("value", enlargeRewriteQuery))
	}

	// 为缺失的数据设置默认值，避免错误日志
	padMissingValues := func(prefix string, maxIndex int, valueType int) {
		for i := 0; i < maxIndex; i++ {
			index := strconv.Itoa(i)
			key := prefix + index

			if _, exists := dataSourceMap[key]; !exists {
				logger.MyDebug(ctx, traceInfo.IsDebug,
					"RecallConfiguration init "+prefix+" fill with nil",
					logkit.String("index", index))
				dataSourceMap[key] = createDataSourceItem(nil, valueType)
			}
		}
	}

	// 补充到20个索引位，避免算法冗余配置后导致的错误日志
	padMissingValues("EnlargeRewriteQuery_", 20, TypeString)
	padMissingValues("RewriteQuery_", 20, TypeString)
	padMissingValues("RewriteSegment_", 20, TypeStringList)
}

// initRewriteKeywords 初始化改写关键词
func initRewriteKeywords(traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	rewriteKeywordList := make([]string, 0)
	rewriteKeywordTop3List := make([]string, 0)
	rewriteKeywordSet := make(map[string]struct{})

	for index, keyword := range traceInfo.QPResult.RewriteQuery {
		if _, exists := rewriteKeywordSet[keyword]; !exists {
			rewriteKeywordSet[keyword] = struct{}{}
			if index < 3 {
				rewriteKeywordTop3List = append(rewriteKeywordTop3List, keyword)
			}
			rewriteKeywordList = append(rewriteKeywordList, keyword)
		}
	}

	dataSourceMap["QPResult_Rewrite_Keywords"] = createDataSourceItem(rewriteKeywordList, TypeStringList)
	dataSourceMap["QPResult_Rewrite_Keywords_TOP3"] = createDataSourceItem(rewriteKeywordTop3List, TypeStringList)
}

// extractNerResult 从NER结果中提取分类信息
func extractNerResult(nerResult, nerAsciiResult []*qp.Ner) map[string]*NerResult {
	// 为所有NER类型初始化结果
	nerWords := make(map[string]*NerResult, len(defaultNerTypes))
	for _, nerType := range defaultNerTypes {
		nerWords[nerType] = &NerResult{
			Original: []string{},
			Ascii:    []string{},
		}
	}

	// 如果没有nerAsciiResult，使用nerResult的token并转换为ASCII
	if len(nerResult) > 0 && len(nerAsciiResult) == 0 {
		for _, ner := range nerResult {
			token := ner.GetToken()
			asciiToken := util.ToAscii(token)

			for _, nerType := range ner.NerType {
				key := getNerKey(nerType)
				nerWords[key].Original = append(nerWords[key].Original, token)
				nerWords[key].Ascii = append(nerWords[key].Ascii, asciiToken)
			}
		}
	} else if len(nerResult) > 0 && len(nerAsciiResult) > 0 {
		// 分别处理原始结果和ASCII结果

		// 处理原始结果
		for _, ner := range nerResult {
			token := ner.GetToken()
			for _, nerType := range ner.NerType {
				key := getNerKey(nerType)
				nerWords[key].Original = append(nerWords[key].Original, token)
			}
		}

		// 处理ASCII结果
		for _, ner := range nerAsciiResult {
			for _, nerType := range ner.NerType {
				token := ner.GetToken()
				key := getNerKey(nerType)
				asciiToken := util.ToAscii(token)
				nerWords[key].Ascii = append(nerWords[key].Ascii, asciiToken)
			}
		}
	}

	// 去重
	for key := range nerWords {
		nerWords[key].Original = util.RemoveDuplicates(nerWords[key].Original)
		nerWords[key].Ascii = util.RemoveDuplicates(nerWords[key].Ascii)
	}

	return nerWords
}

// getNerKey 将NER类型枚举转换为字符串键
func getNerKey(nerType qp.NERType) string {
	switch nerType {
	case qp.NERType_STORE:
		return NerTypeStore
	case qp.NERType_DISH:
		return NerTypeDish
	case qp.NERType_LOCATION:
		return NerTypeLocation
	case qp.NERType_CATEGORY:
		return NerTypeCategory
	case qp.NERType_BRAND:
		return NerTypeBrand
	case qp.NERType_INGREDIENT:
		return NerTypeIngredient
	case qp.NERType_PRODUCT:
		return NerTypeProduct
	case qp.NERType_COOK:
		return NerTypeCook
	case qp.NERType_UNKNOWN:
		return NerTypeUnknown
	default:
		return NerTypeUnknown
	}
}
