package predata

import (
	"context"
	"math"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"github.com/gogo/protobuf/proto"
)

// 初始化请求基本数据
func initRequestBasicData(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	keyword := traceInfo.QueryKeyword

	// 基本请求信息
	dataSourceMap["Request_IsDishListing"] = createDataSourceItem(traceInfo.IsDishListing, TypeBool)
	dataSourceMap["Request_IsVnMart"] = createDataSourceItem(traceInfo.IsVnMart, TypeBool)
	dataSourceMap["Request_PipelineType"] = createDataSourceItem(traceInfo.PipelineType.String(), TypeString)
	dataSourceMap["Request_Keyword"] = createDataSourceItem(keyword, TypeString)
	dataSourceMap["Request_Keyword_Ascii"] = createDataSourceItem(util.ToAscii(keyword), TypeString)
	dataSourceMap["Request.Keyword"] = createDataSourceItem(keyword, TypeString)
	dataSourceMap["Request_Is_Ascii_Terms"] = createDataSourceItem(util.IsAsciiTerms(keyword), TypeBool)
	dataSourceMap["Request_Token_Length"] = createDataSourceItem(util.GetTokenLen(keyword), TypeUint32)
	dataSourceMap["Request_Sort_Type"] = createDataSourceItem(int32(traceInfo.TraceRequest.GetSortType()), TypeInt32)

	// 地理位置信息
	dataSourceMap["Request_Distance"] = createDataSourceItem(traceInfo.DistanceLimit, TypeUint32)
	dataSourceMap["Request.Distance"] = createDataSourceItem(traceInfo.DistanceLimit, TypeUint32)
	dataSourceMap["Request_Longitude"] = createDataSourceItem(traceInfo.TraceRequest.Longitude, TypeFloat32)
	dataSourceMap["Request_Latitude"] = createDataSourceItem(traceInfo.TraceRequest.Latitude, TypeFloat32)

	// 分页信息
	dataSourceMap["Request_PageSize"] = createDataSourceItem(traceInfo.TraceRequest.PageSize, TypeUint32)
}

// 初始化过滤类型数据
func initFilterTypeData(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	filterType := traceInfo.TraceRequest.GetFilterType()

	// 基本过滤条件
	dataSourceMap["Request_FilterType_StoreIds"] = createDataSourceItem(filterType.GetStoreIds(), TypeUint32List)
	dataSourceMap["Request_FilterType_IsJustFilterStores"] = createDataSourceItem(filterType.GetIsJustFilterStores(), TypeBool)
	dataSourceMap["Request_FilterType_CategoryType"] = createDataSourceItem(filterType.GetCategoryType(), TypeUint32List)

	// 分类相关
	categoryType := filterType.GetCategoryType()
	if len(categoryType) > 0 {
		dataSourceMap["Request_FilterType_CategoryType[0]"] = createDataSourceItem(categoryType[0], TypeUint32List)
		dataSourceMap["Request_FilterType_CategoryType_0"] = createDataSourceItem(categoryType[0], TypeUint32List)
	}

	dataSourceMap["Request_FilterType_L1Categories"] = createDataSourceItem(filterType.GetL1Categories(), TypeUint32List)
	dataSourceMap["Request_FilterType_L2Categories"] = createDataSourceItem(filterType.GetL2Categories(), TypeUint32List)

	// 商家类型过滤
	dataSourceMap["Request_FilterType_IsPreferredMerchant"] = createDataSourceItem(filterType.GetIsPreferredMerchant(), TypeBool)
	dataSourceMap["Request_FilterType_IsPartnerMerchant"] = createDataSourceItem(filterType.GetIsPartnerMerchant(), TypeBool)
	dataSourceMap["Request_FilterType_ShippingDistance"] = createDataSourceItem(filterType.GetShippingDistance(), TypeUint32)

	// 订单类型过滤
	hasOrderTypeFilter := false
	orderTypeList := filterType.GetOrderTypeFilterList()
	for i := range orderTypeList {
		if orderTypeList[i] == 1 {
			hasOrderTypeFilter = true
			break
		}
	}
	dataSourceMap["Request_FilterType_OrderType"] = createDataSourceItem(hasOrderTypeFilter, TypeBool)
}

// QP 初始化查询处理结果数据
func initQPResultData(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	qpResult := traceInfo.QPResult

	// 词段信息
	dataSourceMap["QPResult_MaxWordSegments"] = createDataSourceItem(qpResult.MaxWordSegments, TypeStringList)
	dataSourceMap["QPResult_Segments"] = createDataSourceItem(qpResult.Segments, TypeStringList)

	// 扩展段信息
	dataSourceMap["QPResult_OtherSegments_ExpandCallMainTagSegments"] =
		createDataSourceItem(qpResult.OtherSegments.ExpandCallMainTagSegments, TypeStringList)
	dataSourceMap["QPResult_OtherSegments_ExpandCallDishNameSegments"] =
		createDataSourceItem(qpResult.OtherSegments.ExpandCallDishNameSegments, TypeStringList)
	dataSourceMap["QPResult_OtherSegments_ExpandCallOtherTagsSegments"] =
		createDataSourceItem(qpResult.OtherSegments.ExpandCallOtherTagsSegments, TypeStringListList)

	// 意图信息
	dataSourceMap["QPResult_QueryStoreIntention"] = createDataSourceItem(qpResult.QueryStoreIntention, TypeString)
	dataSourceMap["QPResult_QueryDishIntention"] = createDataSourceItem(qpResult.QueryDishIntention, TypeString)
	dataSourceMap["QPResult_StoreIntents"] = createDataSourceItem(qpResult.StoreIntents, TypeStringList)
	dataSourceMap["QPResult_QueryTag"] = createDataSourceItem(qpResult.QueryTag, TypeStringList)
	dataSourceMap["QPResult_IsNeedDishRecall"] = createDataSourceItem(qpResult.IsNeedDishRecall, TypeBool)
	dataSourceMap["QPResult_KeywordLabel"] = createDataSourceItem(qpResult.KeywordLabel, TypeInt32)

	// term weight信息
	var termWeightList []string
	if len(qpResult.TermWeightList) > 0 {
		for i := range qpResult.TermWeightList {
			if qpResult.TermWeightList[i].GetLevel() == 1 && len(qpResult.TermWeightList[i].GetToken()) > 0 {
				termWeightList = append(termWeightList, qpResult.TermWeightList[i].GetToken())
			}
		}
	}
	dataSourceMap["QPResult_TermWeight_Level1_Tokens"] = createDataSourceItem(termWeightList, TypeStringList)
	dataSourceMap["QPResult_TermWeight_Level1_Tokens_Len"] = createDataSourceItem(util.GetTokenLen(strings.Join(termWeightList, " ")), TypeUint32)
	dataSourceMap["QPResult_TermWeight_Level1_Tokens_String"] = createDataSourceItem(strings.Join(termWeightList, " "), TypeString)
}

// 初始化预测配置数据
func initPredictConfigData(traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	if traceInfo.PredictConfig != nil {
		dataSourceMap["IsNewCateRecall"] = createDataSourceItem(traceInfo.PredictConfig.IsNewCateRecall, TypeBool)
		dataSourceMap["NewCateIsOnlyRecallStoreIntention"] = createDataSourceItem(
			traceInfo.PredictConfig.NewCateIsOnlyRecallStoreIntention, TypeBool)
		dataSourceMap["IsNewCateExpandRecallV2"] = createDataSourceItem(traceInfo.PredictConfig.IsNewCateExpandRecallV2, TypeBool)
		dataSourceMap["NewCateIsKeepGrabfoodTag"] = createDataSourceItem(traceInfo.PredictConfig.NewCateIsKeepGrabfoodTag, TypeBool)
	}
}

// 初始化干预数据
func initOptInterventionData(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	if traceInfo.OptIntervention == nil {
		return
	}

	optIntervention := traceInfo.OptIntervention

	// 基本干预信息
	dataSourceMap["OptIntervention_QueryStoreTop"] = createDataSourceItem(
		optIntervention.QueryStoreTop, TypeStringList)
	dataSourceMap["OptIntervention_InterventionRecallStoreIDList"] = createDataSourceItem(
		optIntervention.InterventionRecallStoreIDList, TypeUint64List)
	dataSourceMap["OptIntervention_InterventionRecallBrandID"] = createDataSourceItem(
		optIntervention.InterventionRecallBrandID, TypeUint64List)
	dataSourceMap["OptIntervention_InterventionRecallMerchantID"] = createDataSourceItem(
		optIntervention.InterventionRecallMerchantID, TypeUint64List)
	dataSourceMap["OptIntervention_IsStoreInterventionRecall"] = createDataSourceItem(
		optIntervention.IsStoreInterventionRecall, TypeBool)
	dataSourceMap["OptIntervention_InterventionRecallStoreTagID"] = createDataSourceItem(
		optIntervention.InterventionRecallStoreTagID, TypeUint64List)
	dataSourceMap["OptIntervention_IsNewMerchantsOnly"] = createDataSourceItem(
		optIntervention.IsNewMerchantsOnly, TypeBool)

	// 时间范围
	now := time.Now()
	sixMonthsAgo := now.AddDate(0, -6, 0)
	timestamp := sixMonthsAgo.UnixNano() / int64(time.Millisecond)
	rangeItems := []RangeItem{{
		From:        proto.Float64(float64(timestamp)),
		FromInclude: true,
		To:          nil,
		ToInclude:   false,
	}}
	dataSourceMap["OptIntervention_LastSixMonthTimestamp_Range"] = createDataSourceItem(
		MarshalRange(ctx, rangeItems), TypeString)
}

// 初始化分类意图数据
func initCategoryIntentions(traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	categoryLevel1Names := make([]string, 0)
	categoryLevel2Names := make([]string, 0)
	categoryLevel1Ids := make([]uint32, 0)
	categoryLevel2Ids := make([]uint32, 0)

	for _, intent := range traceInfo.QPResult.ProbFilterCategoryIntentions {
		if intent.Level1Name != nil && len(*intent.Level1Name) > 0 {
			categoryLevel1Names = append(categoryLevel1Names, *intent.Level1Name)
		}
		if intent.Level1ID != nil && *intent.Level1ID > 0 {
			categoryLevel1Ids = append(categoryLevel1Ids, *intent.Level1ID)
		}
		if intent.Level2Name != nil && len(*intent.Level2Name) > 0 {
			categoryLevel2Names = append(categoryLevel2Names, *intent.Level2Name)
		}
		if intent.Level2ID != nil && *intent.Level2ID > 0 {
			categoryLevel2Ids = append(categoryLevel2Ids, *intent.Level2ID)
		}
	}

	dataSourceMap["QPResult_ProbFilterCategoryIntentions_Level1Name"] = createDataSourceItem(
		categoryLevel1Names, TypeStringList)
	dataSourceMap["QPResult_ProbFilterCategoryIntentions_Level2Name"] = createDataSourceItem(
		categoryLevel2Names, TypeStringList)
	dataSourceMap["QPResult_ProbFilterCategoryIntentions_Level1Ids"] = createDataSourceItem(
		categoryLevel1Ids, TypeUint32List)
	dataSourceMap["QPResult_ProbFilterCategoryIntentions_Level2Ids"] = createDataSourceItem(
		categoryLevel2Ids, TypeUint32List)
}

// 初始化开放状态脚本参数
func initOpenStatusScriptPram(dataSourceMap map[string]traceinfo.DataSourceItem) {
	// 获取当前时间相关信息（含时区转换）
	curSec, curUnix, weekDay := timeutil.GetTZOrCIDNowSecondsWeekDay(timeutil.Now(), timeutil.GetLocName())

	dataSourceMap["weekDay"] = createDataSourceItem(int(weekDay+1), TypeInt32)
	dataSourceMap["curUnix"] = createDataSourceItem(curUnix, TypeInt64)

	// 每30分钟是一位
	cur := int(math.Round(float64(curSec) / 30.0 / 60.0))
	dataSourceMap["cur"] = createDataSourceItem(cur, TypeInt32)
}

// 初始化价格范围
func initPriceRange(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	rangeItems := make([]RangeItem, 0)

	for _, priceRange := range traceInfo.TraceRequest.GetFilterType().GetPriceRange() {
		floor := priceRange.GetFloor()
		ceil := priceRange.GetCeil()

		// 根据不同情况设置价格范围
		rangeItem := RangeItem{
			From: proto.Float64(float64(floor)),
			To:   proto.Float64(float64(ceil)),
		}

		if floor == 0 {
			// 下限为0时不包含下限
			rangeItem.FromInclude = false
			rangeItem.ToInclude = false
		} else if ceil == math.MaxUint64 {
			// 上限为最大值时表示无上限
			rangeItem.FromInclude = false
			rangeItem.To = nil
		} else {
			// 常规范围，包含上下限
			rangeItem.FromInclude = true
			rangeItem.ToInclude = true
		}

		rangeItems = append(rangeItems, rangeItem)
	}

	dataSourceMap[KeyPrefixRequest+"FilterType_PriceRange"] = createDataSourceItem(
		MarshalRange(ctx, rangeItems), TypeString)
}

// 初始化集合过滤器数据
func initCollectionFilter(ctx context.Context, filterType *foodalgo_search.SearchRequest_FilterType, dataSourceMap map[string]traceinfo.DataSourceItem) {
	keyPrefix := KeyPrefixRequest + "FilterType_CollectionFilter_"
	collectionFilter := filterType.GetCollectionFilter()

	// 默认情况下设置所有字段为nil值
	defaultNilFields := map[string]int{
		"PartnerTypes":           TypeUint32List,
		"RatingScore":            TypeString,
		"StoreTags":              TypeUint64List,
		"StoreTagsLogic":         TypeInt32,
		"StoreCategoryLevel2Ids": TypeInt32List,
		"StoreCategoriesLogic":   TypeInt32,
	}

	for field, valueType := range defaultNilFields {
		dataSourceMap[keyPrefix+field] = createDataSourceItem(nil, valueType)
	}

	// 如果没有collectionFilter，使用默认空值
	if collectionFilter == nil {
		dataSourceMap[keyPrefix+"IsPickupSupported"] = createDataSourceItem(nil, TypeUint32)
		return
	}

	// 添加partnerType相关
	if partnerTypes := collectionFilter.GetPartnerTypes(); len(partnerTypes) > 0 {
		dataSourceMap[keyPrefix+"PartnerTypes"] = createDataSourceItem(partnerTypes, TypeUint32List)
	}

	// 添加评分范围
	if collectionFilter.GetRatingScoreMax() > 0 || collectionFilter.GetRatingScoreMin() > 0 {
		rangeItems := []RangeItem{{
			From:        proto.Float64(float64(collectionFilter.GetRatingScoreMin())),
			FromInclude: true,
			To:          proto.Float64(float64(collectionFilter.GetRatingScoreMax())),
			ToInclude:   true,
		}}
		dataSourceMap[keyPrefix+"RatingScore"] = createDataSourceItem(MarshalRange(ctx, rangeItems), TypeString)
	}

	// 添加storeTags相关
	if storeTags := collectionFilter.GetStoreTags(); len(storeTags) > 0 {
		tagIds := make([]uint64, 0, len(storeTags))
		for _, tagStr := range storeTags {
			if tagId, err := strconv.ParseUint(tagStr, 10, 64); err == nil {
				tagIds = append(tagIds, tagId)
			}
		}
		dataSourceMap[keyPrefix+"StoreTags"] = createDataSourceItem(tagIds, TypeUint64List)
		dataSourceMap[keyPrefix+"StoreTagsLogic"] = createDataSourceItem(collectionFilter.GetStoreTagsLogic(), TypeInt32)
	}

	// 添加分类相关
	if categoryIds := collectionFilter.GetStoreCategoryLevel2Ids(); len(categoryIds) > 0 {
		dataSourceMap[keyPrefix+"StoreCategoryLevel2Ids"] = createDataSourceItem(categoryIds, TypeInt32List)
		dataSourceMap[keyPrefix+"StoreCategoriesLogic"] = createDataSourceItem(collectionFilter.GetStoreCategoriesLogic(), TypeInt32)
	}

	// 自提信息（0-不支持自提，1-自提，2-放弃自提）
	dataSourceMap[keyPrefix+"IsPickupSupported"] = createDataSourceItem(collectionFilter.GetIsPickupSupported(), TypeUint32)
}
