package predata

import (
	"context"
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 初始化城市距离配置
func initCityDistance(ctx context.Context, dsMap map[string]traceinfo.DataSourceItem, cityId uint32, cityDistanceConf *apollo.CityDistanceConfig) {
	// 空配置检查
	if cityDistanceConf == nil || cityDistanceConf.Default == nil {
		return
	}
	if len(cityDistanceConf.Cities) == 0 {
		return
	}

	// 城市ID为0表示获取城市失败，使用默认值
	if cityId == 0 {
		dsMap["Request_City_Distance"] = createDataSourceItem(cityDistanceConf.Default.Distance, TypeFloat64)
		return
	}

	// 查找城市配置
	cityIdStr := fmt.Sprintf("%d", cityId)
	if cityDistance, exists := cityDistanceConf.Cities[cityIdStr]; exists {
		// 根据城市订单数量决定使用哪个距离值
		// 当城市订单数 >= 阈值时，使用城市自己的distance值
		// 否则使用默认值
		if cityDistance.CurrentOrderCnt > cityDistanceConf.Default.OrderThreshold {
			dsMap["Request_City_Distance"] = createDataSourceItem(cityDistance.Distance, TypeFloat64)
		} else {
			dsMap["Request_City_Distance"] = createDataSourceItem(cityDistanceConf.Default.Distance, TypeFloat64)
		}
	} else {
		// 如果城市没配置，则采用"unknown"配置
		if cityDistance, exists := cityDistanceConf.Cities["unknown"]; exists {
			dsMap["Request_City_Distance"] = createDataSourceItem(cityDistance.Distance, TypeFloat64)
		} else {
			// 如果"unknown"也没配置，报错并采用默认值8.0
			logkit.FromContext(ctx).Error("RecallConfiguration initCityDistance has no config unknown, use 8.0")
			dsMap["Request_City_Distance"] = createDataSourceItem(8.0, TypeFloat64)
		}
	}
}
