package predata

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
)

// initUnsettledStore 初始化未入驻店铺信息
func initUnsettledStore(ctx context.Context, traceInfo *traceinfo.TraceInfo, queryWord string, dataSourceMap map[string]traceinfo.DataSourceItem) {
	// 越南地区跳过处理
	if cid.IsVN() {
		return
	}

	// 设置默认空值
	initDefaultUnsettledStoreValues := func() {
		// 即使没有也要写有这个字段，否则 go expr.Evaluate function 会报错
		fields := []string{
			"Category", "Category[0]", "Category[1:]",
			"CategoryTokenizer", "CategoryTokenizer[0]", "CategoryTokenizer[1:]",
			"DishName", "DishNameTokenizer",
		}

		for _, field := range fields {
			valueType := TypeStringList
			if field == "Category[0]" || field == "CategoryTokenizer[0]" {
				valueType = TypeString
			}
			dataSourceMap[KeyPrefixUnsettledStore+field] = createDataSourceItem(nil, valueType)
		}
	}

	// 获取未入驻店铺信息
	unsettledStore := getUnsettledStoreInfos(ctx, queryWord)
	if unsettledStore == nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initUnsettledStore without unsettled info.",
			logkit.String("query", queryWord))
		initDefaultUnsettledStoreValues()
		return
	}

	// 更新跟踪信息中的未入驻店铺数据
	traceInfo.UnsettledStores = &traceinfo.UnsettledStoreInfo{
		StoreName:         unsettledStore.StoreName,
		Category:          unsettledStore.Category,
		DishName:          unsettledStore.DishName,
		CategoryTokenizer: unsettledStore.CategoryTokenizer,
		DishNameTokenizer: unsettledStore.DishNameTokenizer,
	}

	// 处理分类信息
	if categories := traceInfo.UnsettledStores.Category; len(categories) > 0 {
		dataSourceMap[KeyPrefixUnsettledStore+"Category"] = createDataSourceItem(categories, TypeStringList)
		dataSourceMap[KeyPrefixUnsettledStore+"Category[0]"] = createDataSourceItem(categories[0], TypeString)

		// 提取非主要分类
		if len(categories) > 1 {
			nonMainCategories := categories[1:]
			dataSourceMap[KeyPrefixUnsettledStore+"Category[1:]"] = createDataSourceItem(nonMainCategories, TypeStringList)
		} else {
			dataSourceMap[KeyPrefixUnsettledStore+"Category[1:]"] = createDataSourceItem([]string{}, TypeStringList)
		}
	} else {
		// 设置空值
		dataSourceMap[KeyPrefixUnsettledStore+"Category"] = createDataSourceItem(nil, TypeStringList)
		dataSourceMap[KeyPrefixUnsettledStore+"Category[0]"] = createDataSourceItem(nil, TypeString)
		dataSourceMap[KeyPrefixUnsettledStore+"Category[1:]"] = createDataSourceItem(nil, TypeStringList)
	}

	// 处理分类标记化信息
	if tokenizers := traceInfo.UnsettledStores.CategoryTokenizer; len(tokenizers) > 0 {
		dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer"] = createDataSourceItem(tokenizers, TypeStringList)
		dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer[0]"] = createDataSourceItem(tokenizers[0], TypeString)

		// 提取非主要分类标记
		if len(tokenizers) > 1 {
			nonMainTokenizers := tokenizers[1:]
			dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer[1:]"] = createDataSourceItem(nonMainTokenizers, TypeStringList)
		} else {
			dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer[1:]"] = createDataSourceItem([]string{}, TypeStringList)
		}
	} else {
		// 设置空值
		dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer"] = createDataSourceItem(nil, TypeStringList)
		dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer[0]"] = createDataSourceItem(nil, TypeString)
		dataSourceMap[KeyPrefixUnsettledStore+"CategoryTokenizer[1:]"] = createDataSourceItem(nil, TypeStringList)
	}

	// 处理菜品名称信息
	if dishNames := traceInfo.UnsettledStores.DishName; len(dishNames) > 0 {
		dataSourceMap[KeyPrefixUnsettledStore+"DishName"] = createDataSourceItem(dishNames, TypeStringList)
	} else {
		dataSourceMap[KeyPrefixUnsettledStore+"DishName"] = createDataSourceItem(nil, TypeStringList)
	}

	// 处理菜品名称标记化信息
	if dishTokenizers := traceInfo.UnsettledStores.DishNameTokenizer; len(dishTokenizers) > 0 {
		dataSourceMap[KeyPrefixUnsettledStore+"DishNameTokenizer"] = createDataSourceItem(dishTokenizers, TypeStringList)
	} else {
		dataSourceMap[KeyPrefixUnsettledStore+"DishNameTokenizer"] = createDataSourceItem(nil, TypeStringList)
	}
}

// getUnsettledStoreInfos 获取未入驻店铺信息
func getUnsettledStoreInfos(ctx context.Context, queryWord string) *unsettled_store.UnsettledStoreInfo {
	if unsettled_store.QueryUnsettledStoreDao == nil {
		return nil
	}

	normalizedQuery := strings.ToLower(strings.TrimSpace(queryWord))
	storeInfo, isItemRecallHit := unsettled_store.QueryUnsettledStoreDao.GetUnsettledStoreFromDict(ctx, normalizedQuery)

	if !isItemRecallHit {
		return nil
	}

	return storeInfo
}

// 初始化I2I数据源
func InitI2IDataSource(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	i2iSize := 0
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCntUserFsI2I, i2iSize)
	}()

	// 获取I2I标签
	i2iStoreIds, err := mlplatform.GetI2ITagFromFeatureServer(
		ctx, traceInfo.IsDebug, traceInfo.TraceRequest.PublishId, traceInfo.UserId)

	i2iSize = len(i2iStoreIds)

	// 错误处理
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration GenRecallQuery getI2IStoreIds features failed")
		traceInfo.RecallConfigurationDataSource["FSUserI2IStoreIds"] = createDataSourceItem(nil, TypeUint64List)
		traceInfo.RecallConfigurationDataParams["FSUserI2IStoreIds"] = nil
		return
	}

	// 正常处理
	traceInfo.RecallConfigurationDataSource["FSUserI2IStoreIds"] = createDataSourceItem(i2iStoreIds, TypeUint64List)
	traceInfo.RecallConfigurationDataParams["FSUserI2IStoreIds"] = i2iStoreIds
}

// 初始化用户历史偏好分类ids
func InitUserHistoricalPreferredCategoriesDataSource(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	categoryIds, err := mlplatform.GetUserHistoricalPreferredCategories(ctx, traceInfo.TraceRequest.PublishId, traceInfo.UserId)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration initUserHistoricalPreferredCategories features failed")
		traceInfo.RecallConfigurationDataSource["UserHistoricalPreferredCategories"] = traceinfo.DataSourceItem{ValueType: TypeUint32List, Value: nil}
		traceInfo.RecallConfigurationDataParams["UserHistoricalPreferredCategories"] = nil
		return
	}

	traceInfo.RecallConfigurationDataSource["UserHistoricalPreferredCategories"] = traceinfo.DataSourceItem{ValueType: TypeUint32List, Value: nil}
	traceInfo.RecallConfigurationDataParams["UserHistoricalPreferredCategories"] = categoryIds
}

func initDropWord(ctx context.Context, traceInfo *traceinfo.TraceInfo, dataSourceMap map[string]traceinfo.DataSourceItem) {
	// ID drop word
	dataSourceMap["QPResult_DropWordStoreIntention"] = traceinfo.DataSourceItem{Value: traceInfo.QPResult.DropWordStoreIntents, ValueType: TypeStringList}
	dataSourceMap["QPResult_DropWordDishIntention"] = traceinfo.DataSourceItem{Value: traceInfo.QPResult.DropWordDishIntents, ValueType: TypeStringList}
}
