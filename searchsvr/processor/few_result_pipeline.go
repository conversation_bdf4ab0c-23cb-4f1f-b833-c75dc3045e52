package processor

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/prerank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
)

// 少结果推荐流程
func FewResultPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.StoreInfos, model.StoreInfos, error) {
	// 每个pipeline进来要初始化数据源
	preprocess.TraceInfoReInitDataSource(ctx, traceInfo)

	pt := time.Now()
	var err error
	var storeFilling, storeInfos model.StoreInfos
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseFewResultPipeline, time.Since(pt))
		postprocess.PostProcessorAckDump(ctx, traceInfo, storeInfos, storeInfos)
	}()

	// 门店多路召回
	recallStores, err := recall.MultiRecallStores(ctx, traceInfo)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("FewResultPipeline recall failed, return error")
		return nil, nil, err
	}

	// 门店多路合并
	mergeStores := merge.DoMergeStores(ctx, traceInfo, recallStores)
	// 正排信息填充. vn 需要填充导航距离
	storeFilling, err = filling.Filling(ctx, traceInfo, mergeStores)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("FewResultPipeline filling meta data failed, return error")
		return nil, nil, err
	}
	// 召回融合截断
	storeTruncate := merge.DoStoresTruncate(ctx, traceInfo, storeFilling, recallStores)

	// 门店过滤
	storeInfos = filter.Filter(ctx, storeTruncate, traceInfo)

	// 门店粗排
	storeInfos = prerank.CoarseRank(ctx, traceInfo, storeInfos)

	// 门店挂菜
	storeInfos = RecallListingDishesWithoutCache(ctx, traceInfo, storeInfos)

	// 门店精排(模型排序)
	storeInfos = rank.FusionRankFewResult(ctx, traceInfo, storeInfos)

	return storeFilling, storeInfos, nil
}
