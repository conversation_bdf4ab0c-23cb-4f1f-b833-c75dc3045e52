package prerank

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"go.uber.org/zap"
)

func buildUserEmbContextFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo) *food.ContextFeature {
	contextFeature := &food.ContextFeature{
		UserId:     int64(traceInfo.UserId),
		CQueryRaw:  traceInfo.TraceRequest.QueryRaw,
		GeoHash_4:  traceInfo.UserContext.GeoHash4,
		GeoHash_5:  traceInfo.UserContext.GeoHash5,
		GeoHash_6:  traceInfo.UserContext.GeoHash6,
		CHour:      traceInfo.UserContext.Hour,
		CIsHoliday: traceInfo.UserContext.IsHoliday,
		CDayOfWeek: traceInfo.UserContext.DayOfWeek,
	}
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "buildUserEmbContextFeature", zap.Any("contextFeature", contextFeature))
	}
	return contextFeature
}

func GetUserEmbedding(ctx context.Context, traceInfo *traceinfo.TraceInfo, modelName, scoreType string) []float64 {
	pct := time.Now()
	errCode := "failed"
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseCoarseRankUserEmb, time.Since(pct))
		metric_reporter.ReportClientRequestError(1, "predict-user-embedding-"+modelName, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.PRERANK_ERROR)
		}
	}()

	contextFeatureByte, _ := buildUserEmbContextFeature(ctx, traceInfo).Marshal()
	predictReq := &predictor.PredictReq{
		Uid:    traceInfo.UserId,
		CtxFea: contextFeatureByte,
		Reqid:  traceInfo.TraceRequest.PublishId,
		Models: []string{modelName + "@uservec_predict"},
	}
	startTime := time.Now()
	resp, _, err := mlplatform.Predict(ctx, predictReq, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-user-embedding-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-user-embedding-"+modelName)
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:user embedding predict response", logkit.Any("request", predictReq), logkit.Any("response", resp))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict user embedding", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", modelName))
		return nil
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict user embedding", logkit.Any("rsp is ", "nil"))
		return nil
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict user embedding", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return nil
	}
	embeddingScores := resp.GetModScoresInfo()[modelName].GetScores()[scoreType].Scores
	errCode = "0"
	return embeddingScores
}
