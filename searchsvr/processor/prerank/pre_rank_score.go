package prerank

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
)

func buildRoughContextFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, userEmbedding []float64) *food.ContextFeature {
	contextFeature := &food.ContextFeature{
		UserId:        int64(traceInfo.UserId),
		CQueryRaw:     traceInfo.TraceRequest.QueryRaw,
		GeoHash_4:     traceInfo.UserContext.GeoHash4,
		GeoHash_5:     traceInfo.UserContext.GeoHash5,
		GeoHash_6:     traceInfo.UserContext.GeoHash6,
		CHour:         traceInfo.UserContext.Hour,
		CIsHoliday:    traceInfo.UserContext.IsHoliday,
		CDayOfWeek:    traceInfo.UserContext.DayOfWeek,
		UserEmbedding: util.SliceFloat64ToFloat32(userEmbedding),
	}
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "buildRoughContextFeature", zap.Any("contextFeature", contextFeature))
	}
	return contextFeature
}

func buildRoughItemFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) ([][]byte, []uint64) {
	nStores := len(stores)
	itemIDs := make([]uint64, nStores, nStores)
	itemFeatures := make([][]byte, nStores, nStores)

	for i := 0; i < nStores; i++ {

		itemFeature := &food.ItemFeature{
			CStoreId:       stores[i].StoreId,
			StoreState:     int64(stores[i].DisplayOpeningStatus),
			CStoreDistance: float32(stores[i].Distance),
			CRecallType:    stores[i].RecallTypes,
		}
		//if traceInfo.IsDebug {
		//	logger.MyDebug(ctx, traceInfo.IsDebug, "buildRoughItemFeature", zap.Uint64("storeId", stores[i].StoreId), zap.Any("itemFeature", itemFeature))
		//}

		data, _ := proto.Marshal(itemFeature)
		itemIDs[i] = stores[i].StoreId
		itemFeatures[i] = data
	}
	return itemFeatures, itemIDs
}

func GetRoughScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, userEmbedding []float64, modelName, scoreType string) []float64 {
	pct := time.Now()
	errCode := "failed"
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseCoarseRankPredictorRough, time.Since(pct))
		metric_reporter.ReportClientRequestError(1, "predict-rough-"+modelName, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.PRERANK_ERROR)
		}
	}()

	contextFeatureByte, _ := buildRoughContextFeature(ctx, traceInfo, userEmbedding).Marshal()

	itemFeatures, itemIds := buildRoughItemFeature(ctx, traceInfo, stores)
	traceInfo.AddPhraseStoreLength(ctx, "CoarseRankCount", len(stores))
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  itemIds,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{modelName + "@rough_predict"},
		CtxFea:   contextFeatureByte,
		ItemFeas: itemFeatures,
	}
	startTime := time.Now()
	resp, _, err := mlplatform.Predict(ctx, predictReq, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-rough-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-rough-"+modelName)
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:rough predict response", logkit.Any("request", predictReq), logkit.Any("response", resp))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict rough", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", modelName))
		return nil
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict rough", logkit.Any("rsp is ", "nil"))
		return nil
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict rough", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return nil
	}
	roughScores := resp.GetModScoresInfo()[modelName].GetScores()[scoreType].Scores
	errCode = "0"
	return roughScores
}
