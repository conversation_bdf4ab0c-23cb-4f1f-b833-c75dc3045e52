package prerank

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 粗排
func CoarseRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) []*model.StoreInfo {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictCoarse) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictCoarse")
		metric_reporter.ReportClientRequestError(1, "predict_coarse_downgrade", "true")
		return stores
	}
	metric_reporter.ReportClientRequestError(1, "predict_coarse_downgrade", "0")
	modelName := traceInfo.AbParamClient.GetParamWithString("Search.CoarseRank.ModelName", "")
	if len(modelName) == 0 {
		return stores
	}
	traceInfo.CoarseModelName = modelName
	scoreType := traceInfo.AbParamClient.GetParamWithString("Search.CoarseRank.ScoreType", "ctr")

	pt := time.Now()
	res := stores
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseCoarseRank, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenCoarseRank, len(res))
	}()
	coarseTriggerSize := traceInfo.AbParamClient.GetParamWithInt("Search.CoarseRank.TriggerSize", 1000) // 粗排触发条件，默认1000
	coarseLimit := traceInfo.AbParamClient.GetParamWithInt("Search.CoarseRank.Limit", 0)                // 粗排排序后截断数量
	if coarseLimit == 0 || len(stores) <= coarseTriggerSize {
		return res
	}
	userEmbedding := GetUserEmbedding(ctx, traceInfo, modelName, scoreType)
	if len(userEmbedding) == 0 {
		metric_reporter.ReportCounter("zero_val_of_user_embedding", float64(1), reporter.Label{Key: "user_embedding", Val: "user_embedding"})
	}
	if len(userEmbedding) == 32 {
		traceInfo.CoarseUserEmbedding = userEmbedding
		roughScores := GetRoughScore(ctx, traceInfo, stores, userEmbedding, modelName, scoreType)
		zeroValueOfRough := 0
		if len(stores) == len(roughScores) {
			for index, store := range stores {
				store.CoarseRankScore = roughScores[index]
				if store.CoarseRankScore == 0.0 {
					zeroValueOfRough++
				}
			}
		}
		metric_reporter.ReportCounter("zero_val_of_rough", float64(zeroValueOfRough), reporter.Label{Key: "rough", Val: "rough"})
	}
	stores.SortByCoarseRankScore()
	if len(stores) > coarseLimit {
		res = stores[0:coarseLimit]
		for _, store := range stores[coarseLimit:] {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByCoarseRank, store.CoarseRankScore)
		}
	} else {
		res = stores
	}
	return res
}
