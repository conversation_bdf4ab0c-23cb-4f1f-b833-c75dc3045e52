package processor

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
)

func SearchNormalAndFewResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]*model.StoreInfo, uint32) {
	var normalErr, fewResultErr error
	var recallStores, normalRecallStores, fewRecallStores, normalStores, fewResultStores, finalStores model.StoreInfos
	var fewResultIndex uint32
	var fewTraceInfo *traceinfo.TraceInfo
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseNormalAndFewResultPipeline, time.Since(pt))
		// 后置处理：打印dump log,  上报&打印各阶段门店长度
		postprocess.PostProcessor(ctx, traceInfo, debugInfo, recallStores, finalStores)
	}()
	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)

	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "SearchNormal", func(params ...interface{}) {
		defer wg.Done()
		traceInfo.PipelineType = traceinfo.PipelineTypeSearch
		normalRecallStores, normalStores, normalErr = NormalPipeline(ctx, traceInfo, debugInfo)
		if normalErr != nil {
			traceInfo.AddErrorToTraceInfo(normalErr)
			logkit.FromContext(ctx).Error("SearchNormal error", logkit.Err(normalErr))
			return
		}
		normalStores = filling.StoreItemTypeFilling(ctx, traceInfo, normalStores, foodalgo_search.ItemSceneType_NormalItem)
	})

	if decision.IsNeedFewResult(ctx, traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "SearchFewResult", func(params ...interface{}) {
			defer wg.Done()
			fewTraceInfo = traceinfo.NewTraceInfo()
			_ = preprocess.TraceInfoClone(ctx, traceInfo, fewTraceInfo)
			fewTraceInfo.PipelineType = traceinfo.PipelineTypeSearchFewResult
			fewTraceInfo.AbParamClient.SceneType = fewTraceInfo.PipelineType.String()
			fewTraceInfo.AddPredictConfigToTraceInfo()
			fewTraceInfo.TraceRequest.PublishId = "FewResult_" + fewTraceInfo.TraceRequest.PublishId
			if cid.IsVNRegion() {
				if len(traceInfo.TraceRequest.FilterType.CategoryType) == 1 && traceInfo.TraceRequest.FilterType.CategoryType[0] == 1002 {
					fewTraceInfo.IsVnMart = true
				}
			}

			fewRecallStores, fewResultStores, fewResultErr = FewResultPipeline(ctx, fewTraceInfo, debugInfo)
			if fewResultErr != nil {
				traceInfo.AddErrorToTraceInfo(fewResultErr)
				logkit.FromContext(ctx).Error("SearchFewResult error", logkit.Err(fewResultErr))
				return
			}
			fewResultStores = filling.StoreItemTypeFilling(ctx, traceInfo, fewResultStores, foodalgo_search.ItemSceneType_NoFewItem)
		})
	}
	wg.Wait()
	traceInfo.MergeTraceInfo(fewTraceInfo, "FewResult")

	// 给normal结果中召回了但是过滤掉的打上标签
	flagWasInNormalButFiltered(normalRecallStores, fewRecallStores)

	// merge normal & fewResult
	finalStores = mergeNormalAndFewResult(ctx, traceInfo, normalStores, fewResultStores, true)
	recallStores = mergeNormalAndFewResult(ctx, traceInfo, normalRecallStores, fewRecallStores, false)
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenRecallMergeNormalFewResult, len(recallStores))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinalMergeNormalFewResult, len(finalStores))
	}()

	// 并标记index
	for i, s := range finalStores {
		if s.ItemSceneType == foodalgo_search.ItemSceneType_NoFewItem {
			fewResultIndex = uint32(i + 1)
			break
		}
	}
	return finalStores, fewResultIndex
}

func flagWasInNormalButFiltered(normalRecallStores, normalFinalStores []*model.StoreInfo) {
	storeIdFinalMap := make(map[uint64]*model.StoreInfo, len(normalFinalStores))
	for _, s := range normalFinalStores {
		storeIdFinalMap[s.StoreId] = s
		s.NormalRecallFilterFlag = 2
	}
	for _, s := range normalRecallStores {
		if s == nil || s.StoreId == 0 {
			continue
		}
		if _, exist := storeIdFinalMap[s.StoreId]; !exist {
			s.NormalRecallFilterFlag = 1
		} else {
			s.NormalRecallFilterFlag = 2
		}
	}
}

// 去重，合并
func mergeNormalAndFewResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores1, stores2 []*model.StoreInfo, isMergeRecallType bool) []*model.StoreInfo {
	stores := make([]*model.StoreInfo, 0, len(stores1)+len(stores2))
	storeIdMap := make(map[uint64]*model.StoreInfo, len(stores1)+len(stores2))
	brandIdMap := make(map[uint64]bool, 0)
	for _, s := range stores1 {
		if s == nil || s.StoreId == 0 {
			continue
		}
		stores = append(stores, s)
		storeIdMap[s.StoreId] = s
		if s.GetBrandId() > 0 {
			brandIdMap[s.GetBrandId()] = true
		}
	}
	// 去重,门店和品牌维度
	for _, s := range stores2 {
		if s == nil || s.StoreId == 0 {
			continue
		}
		if _, exist := storeIdMap[s.StoreId]; exist {
			if isMergeRecallType {
				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDupFewResultStore, nil)
			}
			continue
		}
		if s.GetBrandId() > 0 && brandIdMap[s.GetBrandId()] {
			traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDupFewResultBrand, nil)
			continue
		}
		stores = append(stores, s)
	}
	return stores
}
