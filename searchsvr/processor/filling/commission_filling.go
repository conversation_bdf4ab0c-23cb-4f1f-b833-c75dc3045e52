package filling

import (
	"context"
	"encoding/json"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	affiliate_client "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/affiliate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

const ItemCommissionTypeStore = 1
const ItemCommissionTypeDish = 2

func StoreAffiliateFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	if integrate.DataManageServiceClient == nil || len(stores) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "skip affiliate commission info filling")
		return
	}
	if traceInfo.IsDowngradeDataServer {
		logger.MyDebug(ctx, traceInfo.IsDebug, "data server downgrade, skip affiliate commission info filling")
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreCommission, time.Since(pt))
	}()
	storeCommResp, err, partFailedFlag := integrate.DataManageServiceClient.GetItemCommissionPlansWithMetaPool(ctx, ItemCommissionTypeStore, stores.StoreIDs(), traceInfo.MetaPool, true)
	if partFailedFlag {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_META_ERROR)
	}
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetItemCommissionPlansWithMetaPool"), zap.String("t1", time.Since(pt).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(pt), metric_reporter2.SearchReportTypeRpc, "", "", "GetItemCommissionPlansWithMetaPool")
	if traceInfo.IsDebug {
		rspStr, _ := json.Marshal(storeCommResp)
		logkit.FromContext(ctx).Info("GetItemCommissionPlansWithMetaPool", logkit.Any("StoreIDs", stores.StoreIDs()), logkit.String("rsp", string(rspStr)))
	}
	if err != nil {
		traceInfo.AddErrorToTraceInfo(err)
		logkit.FromContext(ctx).WithError(err).Error("GetItemCommissionPlansWithMetaPool failed", zap.Any("cost", time.Since(pt)))
		traceInfo.IsDowngradeDataServer = true
	}
	commMap := storeCommResp.GetMapCommPlan()
	for _, store := range stores {
		comm, exist := commMap[store.StoreId]
		if exist == false {
			continue
		}
		store.CommissionPlan = comm
		store.BaseCommissionRate = affiliate_client.GetItemBaseCommissionRate(traceInfo.PlanCommRate, comm)
	}
	return
}

func DishAffiliateFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) (model.DishInfos, error) {
	if integrate.DataManageServiceClient == nil || len(dishes) == 0 || decision.IsDishMetaDowngrade(traceInfo) {
		return dishes, nil
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingDishCommission, time.Since(pt))
	}()
	dishCommResp, err, partFailedFlag := integrate.DataManageServiceClient.GetItemCommissionPlansWithMetaPool(ctx, ItemCommissionTypeDish, dishes.DishIds(), traceInfo.MetaPool, true)
	if partFailedFlag {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_META_ERROR)
	}
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetItemCommissionPlansWithMetaPool"), zap.String("t1", time.Since(pt).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(pt), metric_reporter2.SearchReportTypeRpc, "", "", "GetItemCommissionPlansWithMetaPool")
	if traceInfo.IsDebug {
		rspStr, _ := json.Marshal(dishCommResp)
		logkit.FromContext(ctx).Info("GetItemCommissionPlansWithMetaPool", logkit.Any("DishIds", dishes.DishIds()), logkit.String("rsp", string(rspStr)))
	}
	if err != nil {
		traceInfo.AddErrorToTraceInfo(err)
		logkit.FromContext(ctx).WithError(err).Error("GetItemCommissionPlansWithMetaPool failed", zap.Any("cost", time.Since(pt)))
		traceInfo.IsDowngradeDataServer = true
	}
	dishCommMap := dishCommResp.GetMapCommPlan()
	for _, dish := range dishes {
		dishComm, exist := dishCommMap[dish.DishId]
		// 当菜品正排调用失败时，如果菜品中有门店ID, 那么菜品信息自动降级保留，否则直接去掉。
		if exist == false {
			continue
		}
		dish.CommissionPlan = dishComm
		dish.BaseCommissionRate = affiliate_client.GetItemBaseCommissionRate(traceInfo.PlanCommRate, dishComm)
	}
	return dishes, nil
}
