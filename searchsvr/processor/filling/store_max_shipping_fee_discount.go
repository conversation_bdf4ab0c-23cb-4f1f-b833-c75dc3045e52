package filling

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	promotion2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/promotion"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func StoreMaxShippingFeeDiscountFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreMaxShippingFeeDiscount, time.Since(pt))
	}()
	var storeMaxShippingFeeDiscountDataMap map[uint64]uint64
	storeMaxShippingFeeDiscountDataMap, _ = getStoreMaxShippingFeeDiscountData(ctx, traceInfo, stores)
	for _, store := range stores {
		maxDiscount, ok := storeMaxShippingFeeDiscountDataMap[store.StoreId]
		if ok && maxDiscount > 0 {
			store.MaxShippingFeeDiscount = maxDiscount
		}
	}
}

func getStoreMaxShippingFeeDiscountData(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos []*model.StoreInfo) (map[uint64]uint64, error) {
	idLen := len(storeInfos)
	if idLen == 0 {
		return nil, nil
	}
	count := idLen / 50
	if idLen%50 != 0 {
		count += 1
	}
	var (
		dataMap = make(map[uint64]uint64, idLen)
		errs    = make(chan error, count)
	)

	wg := &sync.WaitGroup{}
	wg.Add(count)
	lock := sync.RWMutex{}
	for i := 0; i < count; i++ {
		var value []*model.StoreInfo
		if (i+1)*50 < idLen {
			value = storeInfos[i*50 : (i+1)*50]
		} else {
			value = storeInfos[i*50:]
		}

		goroutine.WithGo(ctx, "GetStorePromotion", func(params ...interface{}) {
			defer wg.Done()
			startTime := time.Now()
			retMap, err := promotion2.GetStorePromotion(ctx, traceInfo.UserId, value)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "storePromotion"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "storePromotion")
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				logkit.FromContext(ctx).WithError(err).Error(
					"SearchService.getStorePromotionData error",
					zap.Any("storeIds", value))
				reporter.ReportClientRequestError(1, "GetStorePromotion", "failed")
				errs <- err
			} else {
				lock.Lock()
				defer lock.Unlock()
				for k, v := range retMap {
					dataMap[k] = v
				}
				reporter.ReportClientRequestError(1, "GetStorePromotion", "0")
			}
		})
	}
	wg.Wait()
	if len(errs) == count {
		return nil, <-errs
	}
	return dataMap, nil
}
