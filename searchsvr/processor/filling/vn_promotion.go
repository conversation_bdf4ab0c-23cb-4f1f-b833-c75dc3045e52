package filling

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/httpclient"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func VNPromotionFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingVNPromotion, time.Since(pt))
	}()
	promotionDataMap := doGetPromotionDataForVN(ctx, traceInfo, stores.StoreIDs())
	for _, storeInfo := range stores {
		if promotionData, exist := promotionDataMap[storeInfo.StoreId]; exist {
			storeInfo.Promotion = promotionData
		}
	}
}

func doGetPromotionDataForVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIDs []uint64) map[uint64]*model.PromotionData {
	idLen := len(storeIDs)
	promotionDataMap := make(map[uint64]*model.PromotionData, idLen)
	if idLen == 0 {
		return promotionDataMap
	}
	count := idLen / 50
	if idLen%50 != 0 {
		count += 1
	}
	var errs = make(chan error, count)
	wg := &sync.WaitGroup{}
	wg.Add(count)
	lock := sync.RWMutex{}
	for i := 0; i < count; i++ {
		var value []uint64
		if (i+1)*50 < idLen {
			value = storeIDs[i*50 : (i+1)*50]
		} else {
			value = storeIDs[i*50:]
		}
		goroutine.WithGo(ctx, "getPromotionDataForVN", func(params ...interface{}) {
			defer wg.Done()
			startTime := time.Now()
			retMap, err := httpclient.GetPromotionData(ctx, traceInfo, value)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "vn_promotion"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeHttp, "", "", "vn_promotion")
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				logkit.FromContext(ctx).WithError(err).Error("SearchService.getPromotionDataForVN error", zap.Any("storeIds", value))
				reporter.ReportClientRequestError(1, "vn_promotion", "failed")
				errs <- err
				return
			}
			reporter.ReportClientRequestError(1, "vn_promotion", "0")
			lock.Lock()
			defer lock.Unlock()
			for k, v := range retMap {
				promotionDataMap[k] = v
			}
		})
	}
	wg.Wait()
	if len(errs) == count {
		logkit.FromContext(ctx).Error("SearchService.getPromotionDataForVN batch failed", logkit.Int("totalSize", idLen), logkit.Int("count", count), logkit.Uint64s("storeIds", storeIDs))
	}
	return promotionDataMap
}
