package filling

import (
	"context"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreCategoryScoreFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreCategoryScore, time.Since(pt))
	}()

	//类目匹配分
	//如果query.categoryL1 和 store 的 L1 category 信号匹配: 0.5
	//如果query.categoryL2 和 store 的 L2 category 信号匹配: 1 【 最高只能是 1 分】

	for _, s := range stores {
		categoryScore := 0.0

		if s.MainCategory != nil {
			for _, ci := range traceInfo.QPResult.CategoryIntentions {
				tmp := 0.0
				if ci.GetLevel1Name() == s.MainCategory.Level1Name {
					tmp += 0.5
				}
				if ci.GetLevel2Name() == s.MainCategory.Level2Name {
					tmp += 0.5
				}
				categoryScore = util2.MaxFloat64(categoryScore, tmp)
			}
		}

		for _, sub := range s.SubCategory {
			if sub != nil {
				for _, ci := range traceInfo.QPResult.CategoryIntentions {
					tmp := 0.0
					if ci.GetLevel1Name() == sub.Level1Name {
						tmp += 0.5
					}
					if ci.GetLevel2Name() == sub.Level2Name {
						tmp += 0.5
					}
					categoryScore = util2.MaxFloat64(categoryScore, tmp)
				}
			}
		}
		s.StoreCategoryScore = categoryScore
	}
}
