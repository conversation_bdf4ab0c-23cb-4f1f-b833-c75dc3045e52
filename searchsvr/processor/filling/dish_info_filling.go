package filling

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 菜品正排和特征填充
func DishInfoFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) model.DishInfos {
	if len(dishes) == 0 {
		return dishes
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFilling, time.Since(pt))
	}()
	wg := &sync.WaitGroup{}

	wg.Add(1)
	goroutine.WithGo(ctx, "DishMetaFilling", func(params ...interface{}) {
		defer wg.Done()
		DishMetaFillingWithoutReturn(ctx, traceInfo, dishes)
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "DishFeaturesFilling", func(params ...interface{}) {
		defer wg.Done()
		DishFeaturesFilling(ctx, traceInfo, dishes)
	})

	wg.Add(1)
	goroutine.WithGo(ctx, "DishFlashSaleFilling", func(params ...interface{}) {
		defer wg.Done()
		DishFlashSaleFilling(ctx, traceInfo, dishes)
	})

	wg.Wait()

	return dishes
}

// 专门为主站补充菜品(best sell dish)
func FillDishWithStoreBestSelling(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	bestSelling := make([]*model.DishInfo, 0, len(storeInfos))
	for _, store := range storeInfos {
		// from 2024Q4_ivy: 当搜索词是门店意图，一定要挂上最佳销量菜品
		if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
			if store.BestSellingDishId > 0 {
				bestSelling = append(bestSelling, &model.DishInfo{
					DishId:  store.BestSellingDishId,
					StoreId: store.StoreId,
				})
			}
		} else {
			// 正常情况下，最佳销量菜品是没有菜的时候才挂上
			if len(store.DishInfos) == 0 && store.BestSellingDishId > 0 {
				bestSelling = append(bestSelling, &model.DishInfo{
					DishId:  store.BestSellingDishId,
					StoreId: store.StoreId,
				})
			}
		}
	}
	// 查询菜品正排
	if len(bestSelling) > 0 {
		dishInfos, _ := DishMetaFilling(ctx, traceInfo, bestSelling) // es 召回没有返回store id,需要查询正排获取
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenBestSelling, len(dishInfos))
		dishMap := dishInfos.StoreDishInfosMap()
		for _, store := range storeInfos {
			dishes := dishMap[store.StoreId]
			if len(dishes) > 0 {
				store.DishInfos = dishes
			}
		}

		traceInfo.AppendRecallsDishFinal("HardcodeRecallIdMainSiteDishBestSelling")
	}
	return storeInfos
}
