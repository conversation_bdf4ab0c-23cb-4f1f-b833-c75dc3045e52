package filling

import (
	"context"
	"errors"
	"strings"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_datamanagement"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 填充菜品正排信息
func DishMetaFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) (model.DishInfos, error) {
	if integrate.DataManageServiceClient == nil || len(dishes) == 0 || decision.IsDishMetaDowngrade(traceInfo) {
		return dishes, nil
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingMeta, time.Since(pt))
	}()
	timeOut := 200
	if apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut > 0 {
		timeOut = apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut
	}
	dishMetaMap, _ := MGetDishMeta(ctx, traceInfo, dishes.DishIds(), timeOut, apollo.SearchApolloCfg.DishFreeCacheTime)
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish meta", zap.Any("dish", dishMetaMap))
	dishInfos := make([]*model.DishInfo, 0, len(dishes))
	for _, dish := range dishes {
		dishMetaInfo, dishExist := dishMetaMap[dish.DishId]
		// 当菜品正排调用失败时，如果菜品中有门店ID, 那么菜品信息自动降级保留，否则直接去掉。
		if dishExist == false || dishMetaInfo == nil {
			if traceInfo.IsDowngradeDataServer == true && dish.DishId > 0 && dish.StoreId > 0 { // 避免保留的菜品都是门店ID=="0"
				dishInfos = append(dishInfos, dish)
			}
			continue
		}
		dish.DishId = dishMetaInfo.GetId()
		dish.StoreId = dishMetaInfo.GetStoreId()
		dish.DishName = dishMetaInfo.GetName()
		dish.DishNameNorm = strings.TrimSpace(strings.ToLower(dishMetaInfo.GetName()))
		dish.CatalogName = dishMetaInfo.GetCatalogName()
		dish.CreateTime = dishMetaInfo.GetCreateTime()
		dish.Price = dishMetaInfo.GetPrice()
		dish.SalesVolume = dishMetaInfo.GetSalesVolume()
		dish.Picture = dishMetaInfo.GetPicture()
		dish.CrawlerPicture = dishMetaInfo.GetCrawlerPicture()
		dish.MmsImage = dishMetaInfo.GetMmsImage()
		dish.SaleStatus = dishMetaInfo.GetSaleStatus()
		dish.OutOfStockFlag = dishMetaInfo.GetOutOfStockFlag()
		dish.ESScore = dish.Score
		dish.HasPicture = false
		dish.HideFlagForVn = dishMetaInfo.GetFlag().GetHideFlagForVn()
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
			// 仅主站需要计算
			dish.DishTextMatchScore, dish.DishTextMatchScoreCalc = dish.GetDishTextMatchScore(traceInfo.IsDebug, traceInfo.QueryKeyword, traceInfo.QPResult.RewriteQuery, dish.DishName, dish.CatalogName)
		}
		dish.SearchNonHalalFlag = dishMetaInfo.GetSearchNonHalalFlag()

		if cid.IsVN() {
			if len(dishMetaInfo.GetMmsImage()) > 0 {
				dish.HasPicture = true
			}
		} else {
			if len(dishMetaInfo.GetCrawlerPicture()) > 0 || len(dishMetaInfo.GetPicture()) > 0 {
				dish.HasPicture = true
			}
		}
		dish.Available = foodalgo_search.Available_AVAILABLE
		if dishMetaInfo.GetAvailable() == o2oalgo.DishSupply_DISH_SUPPLY_UNAVAILABLE {
			dish.Available = foodalgo_search.Available_UNAVAILABLE
		}
		dish.ListingStatus = foodalgo_search.DishListingStatus_ACTIVE
		if dishMetaInfo.GetListingStatus() == o2oalgo.DishListing_DISH_LISTING_INACTIVE {
			dish.ListingStatus = foodalgo_search.DishListingStatus_INACTIVE
		}
		if dish.Available == foodalgo_search.Available_AVAILABLE && dish.ListingStatus == foodalgo_search.DishListingStatus_ACTIVE {
			dishInfos = append(dishInfos, dish)
		}
	}
	return dishInfos, nil
}

// 填充菜品正排信息
func DishMetaFillingWithoutReturn(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if integrate.DataManageServiceClient == nil || len(dishes) == 0 || decision.IsDishMetaDowngrade(traceInfo) {
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingMeta, time.Since(pt))
	}()
	timeOut := 200
	if apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut > 0 {
		timeOut = apollo.SearchApolloCfg.ClientTimeOutConfig.DataManageTimeOut
	}

	dishMetaMap, _ := MGetDishMeta(ctx, traceInfo, dishes.DishIds(), timeOut, apollo.SearchApolloCfg.DishFreeCacheTime)
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish meta", zap.Any("dish", dishMetaMap))
	for _, dish := range dishes {
		dishMetaInfo, dishExist := dishMetaMap[dish.DishId]
		// 当菜品正排调用失败时，如果菜品中有门店ID, 那么菜品信息自动降级保留，否则直接去掉。
		if dishExist == false || dishMetaInfo == nil {
			continue
		}
		dish.DishId = dishMetaInfo.GetId()
		dish.StoreId = dishMetaInfo.GetStoreId()
		dish.DishName = dishMetaInfo.GetName()
		dish.DishNameNorm = strings.TrimSpace(strings.ToLower(dishMetaInfo.GetName()))
		dish.CatalogName = dishMetaInfo.GetCatalogName()
		dish.CreateTime = dishMetaInfo.GetCreateTime()
		dish.Price = dishMetaInfo.GetPrice()
		dish.SalesVolume = dishMetaInfo.GetSalesVolume()
		dish.Picture = dishMetaInfo.GetPicture()
		dish.SaleStatus = dishMetaInfo.GetSaleStatus()
		dish.OutOfStockFlag = dishMetaInfo.GetOutOfStockFlag()
		dish.ESScore = dish.Score
		dish.HasPicture = false
		dish.HideFlagForVn = dishMetaInfo.GetFlag().GetHideFlagForVn()
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
			// 仅主站需要计算
			dish.DishTextMatchScore, dish.DishTextMatchScoreCalc = dish.GetDishTextMatchScore(traceInfo.IsDebug, traceInfo.QueryKeyword, traceInfo.QPResult.RewriteQuery, dish.DishName, dish.CatalogName)
		}
		dish.SearchNonHalalFlag = dishMetaInfo.GetSearchNonHalalFlag()

		if cid.IsVN() {
			if len(dishMetaInfo.GetMmsImage()) > 0 {
				dish.HasPicture = true
			}
		} else {
			if len(dishMetaInfo.GetCrawlerPicture()) > 0 || len(dishMetaInfo.GetPicture()) > 0 {
				dish.HasPicture = true
			}
		}
		dish.Available = foodalgo_search.Available_AVAILABLE
		if dishMetaInfo.GetAvailable() == o2oalgo.DishSupply_DISH_SUPPLY_UNAVAILABLE {
			dish.Available = foodalgo_search.Available_UNAVAILABLE
		}
		dish.ListingStatus = foodalgo_search.DishListingStatus_ACTIVE
		if dishMetaInfo.GetListingStatus() == o2oalgo.DishListing_DISH_LISTING_INACTIVE {
			dish.ListingStatus = foodalgo_search.DishListingStatus_INACTIVE
		}
	}
	return
}

func MGetDishMeta(ctx context.Context, traceInfo *traceinfo.TraceInfo, ids []uint64, timeoutMs int, cacheTimeSec int) (map[uint64]*o2oalgo.Dish, error) {
	useCache := cacheTimeSec > 0
	pt := time.Now()
	totalLen := len(ids)
	hitLen := totalLen
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingMetaMGet, time.Since(pt))
		metric_reporter2.ReportHitRateRequest(totalLen, hitLen, "DishMetaFreeCache")
	}()
	dishMetaMap := make(map[uint64]*o2oalgo.Dish, len(ids))
	var noCache []uint64
	if useCache {
		noCache = localcache.StoreCacheSysInstance.MGetDishMetaCache(ctx, ids, dishMetaMap, useCache)
	} else {
		noCache = ids
	}
	fromInfo := &o2oalgo_datamanagement.FromInfo{
		Scene: traceInfo.HandlerType.String(),
	}
	if len(noCache) > 0 {
		hitLen = totalLen - len(noCache)
		dishResp := integrate.DataManageServiceClient.GetDishMetaV3(ctx, noCache, traceInfo.TraceRequest.SearchTime, timeoutMs, nil, fromInfo)
		if dishResp.ErrCount > 0 {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_META_ERROR)
		}
		//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetDishMetaV3"), zap.String("t1", time.Since(pt).String()))
		metric_reporter2.ReportDurationAndQPS(time.Since(pt), metric_reporter2.SearchReportTypeRpc, "", "", "GetDishMetaV3")
		dishRetRatio := float64(dishResp.QueryCount-dishResp.ErrCount) / float64(dishResp.QueryCount)
		DataServerReturnSizePercent := apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeDataServerReturnSizePercent
		if DataServerReturnSizePercent > 0.0 && dishRetRatio < DataServerReturnSizePercent {
			traceInfo.AddErrorToTraceInfo(errors.New("dish meta return not enough"))
			logkit.FromContext(ctx).Error("GetDishMeta failed to get dish meta info", zap.Any("query count", dishResp.QueryCount),
				zap.Any("err count", dishResp.ErrCount), zap.Any("cost", time.Since(pt)))
			traceInfo.IsDowngradeDataServer = true
		}

		for _, dish := range dishResp.GetMapDish() {
			dishMetaMap[dish.GetId()] = dish
		}
		if useCache {
			goroutine.WithGo(ctx, "MSetDishMetaCache", func(params ...interface{}) {
				localcache.StoreCacheSysInstance.MSetDishMetaCache(ctx, dishResp.GetMapDish(), cacheTimeSec)
			})
		}
	}
	return dishMetaMap, nil
}
