package filling

import (
	"context"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	foodalgo_shippingfee "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/shipping_fee"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/golang/protobuf/proto"
)

func ShippingFeeFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	if integrate.ShippingFeeClient == nil {
		logkit.FromContext(ctx).Error("shipping fee client is nil")
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingShippingFee, time.Since(pt))
	}()
	serviceType := foodalgo_shippingfee.ServiceType_ShopeeFood
	nStores := len(stores)
	batch := apollo.SearchApolloCfg.ShippingFeeReqBatch
	if batch == 0 {
		batch = 50
	}
	nBatch := nStores / batch
	if nStores%batch > 0 {
		nBatch += 1
	}

	totalBatches := make([][]*foodalgo_shippingfee.StoreDetail, 0, nBatch)
	for i := 0; i < nBatch; i++ {
		singleBatch := make([]*foodalgo_shippingfee.StoreDetail, 0, batch)
		totalBatches = append(totalBatches, singleBatch)
	}

	batchInd := 0
	deliverMode := foodalgo_shippingfee.DeliveryMode_Platform
	for i := 0; i < nStores; i++ {
		batchInd = i % nBatch
		storeDetail := &foodalgo_shippingfee.StoreDetail{
			StoreId: proto.Uint64(stores[i].StoreId),
			StoreLocation: &foodalgo_shippingfee.Location{
				State:     proto.String(stores[i].Location.GetState()),
				City:      proto.String(stores[i].Location.GetCity()),
				District:  proto.String(stores[i].Location.GetDistrict()),
				Address:   proto.String(stores[i].Location.GetAddress()),
				Latitude:  proto.Float32(stores[i].Location.GetLatitude()),
				Longitude: proto.Float32(stores[i].Location.GetLongitude()),
			},
			StoreOption: &foodalgo_shippingfee.StoreOption{
				PartnerType: partnerTypeTransform(stores[i].PartnerType),
			},
			DeliveryMode: &deliverMode,
		}
		totalBatches[batchInd] = append(totalBatches[batchInd], storeDetail)
	}

	batchGroup := sync.WaitGroup{}
	batchGroup.Add(nBatch)
	resultBatch := make([]map[uint64]*foodalgo_shippingfee.StoreListShippingFee, nBatch, nBatch)
	timeOut := apollo.SearchApolloCfg.ClientTimeOutConfig.ShippingFeeReqTimeOut
	if timeOut == 0 {
		timeOut = 200
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Millisecond)
	defer cancel()
	for i := 0; i < nBatch; i++ {
		go func(batchInd int) {
			defer func() {
				batchGroup.Done()
				if p := recover(); p != nil {
					debug.PrintStack()
				}
			}()

			if len(totalBatches[batchInd]) == 0 {
				resultBatch[batchInd] = nil
				return
			}

			request := &foodalgo_shippingfee.MGetStoreListShippingFeeRequest{
				ServiceType: &serviceType,
				BuyerId:     proto.Uint64(traceInfo.UserId),
				BuyerLocation: &foodalgo_shippingfee.Location{
					//City:      proto.String(strCity),
					Latitude:  proto.Float32(float32(traceInfo.TraceRequest.Latitude)),
					Longitude: proto.Float32(float32(traceInfo.TraceRequest.Longitude)),
				},
				StoreDetailList: totalBatches[batchInd],
			}
			st := time.Now()
			rsp, err := integrate.ShippingFeeClient.MGetStoreListShippingFee(ctx, request)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "MGetStoreListShippingFee"), zap.String("cost", time.Since(st).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "MGetStoreListShippingFee")
			if err != nil || rsp == nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				reporter.ReportClientRequestError(1, "MGetStoreListShippingFee", "failed")
				logkit.FromContext(ctx).WithError(err).Error("failed to get shipping fee", logkit.String("cost", time.Since(st).String()))
				return
			}
			reporter.ReportClientRequestError(1, "MGetStoreListShippingFee", "0")
			resultBatch[batchInd] = rsp.GetShippingFeeMap()
		}(i)
	}
	batchGroup.Wait()

	storeShippingFeeMap := make(map[uint64]uint64, nStores)
	for i := 0; i < nBatch; i++ {
		if len(resultBatch[i]) > 0 {
			for k, v := range resultBatch[i] {
				storeShippingFeeMap[k] = v.GetBuyerFee()
			}
		}
	}
	for _, store := range stores {
		shippingFee, exist := storeShippingFeeMap[store.StoreId]
		if exist && shippingFee > 0 {
			store.ShippingFee = shippingFee
		}
	}
}

func partnerTypeTransform(partnerType o2oalgo.PartnerType) *foodalgo_shippingfee.StoreOption_PartnerType {
	if partnerType > o2oalgo.PartnerType_PARTNER_TYPE_LISTED {
		return foodalgo_shippingfee.StoreOption_Partner.Enum()
	} else {
		return foodalgo_shippingfee.StoreOption_NonPartner.Enum()
	}
}
