package filling

import (
	"context"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const StoreSegmentBid = 163
const StoreSegmentFeatureId = 10682369
const SegmentUnknown = "Unknown"

var StoreSegments = []string{"Unknown", "Enterprise", "ST", "MT", "Incubation", "Unmanaged", "Non Partner", "Mart"}

func StoreSegmentFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreSegment, time.Since(pt))
	}()
	nStores := len(stores)
	segmentBoostStr := traceInfo.AbParamClient.GetParamWithString("Search.MultiFactor.StoreSegmentBoostFactor", "")
	segmentBoosts := strings.Split(segmentBoostStr, ",")
	boostFactors := make(map[string]float64, len(segmentBoosts))
	boostFactors[SegmentUnknown] = traceInfo.AbParamClient.GetParamWithFloat("Search.MultiFactor.DefaultUnknownSegmentBoostFactor", 0.1) // unknown segment 默认值
	for _, segmentBoost := range segmentBoosts {
		kv := strings.Split(segmentBoost, "=")
		if len(kv) != 2 {
			logkit.FromContext(ctx).Error("StoreSegmentBoostFactor parse failed", logkit.String("segment boost", segmentBoost))
			continue
		}
		factor, err := strconv.ParseFloat(kv[1], 64)
		if err != nil {
			logkit.FromContext(ctx).Error("StoreSegmentBoostFactor parse failed", logkit.String("segment boost", segmentBoost))
			continue
		}
		boostFactors[strings.TrimSpace(kv[0])] = factor
	}

	if env.GetEnv() == "test" {
		for iStore := 0; iStore < nStores; iStore++ {
			se := StoreSegments[rand.Intn(len(StoreSegments))]
			stores[iStore].StoreSegment = se
			stores[iStore].StoreSegmentBoostFactor = boostFactors[se]
			continue
		}
		return
	}

	keyList := make([]string, len(stores), len(stores))
	for i, _ := range stores {
		keyList[i] = strconv.FormatInt(int64(stores[i].StoreId), 10)
	}
	storeSegmentBid := config2.SearchApolloCfg.StoreSegmentBid
	if storeSegmentBid == 0 {
		storeSegmentBid = StoreSegmentBid
	}
	rsp, err := integrate.DataManageServiceClient.GetData(ctx, storeSegmentBid, keyList)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
		logkit.FromContext(ctx).Error("failed to get store segment data", logkit.Err(err))
		return
	}
	if rsp == nil || rsp.GetItems() == nil {
		logkit.FromContext(ctx).Error("store segment data is nil", logkit.Err(err))
		return
	}

	segmentFeatureId := config2.SearchApolloCfg.StoreSegmentFeatureId
	if segmentFeatureId == 0 {
		segmentFeatureId = StoreSegmentFeatureId
	}
	storeSegments := rsp.GetItems()
	if len(storeSegments) == 0 {
		logkit.FromContext(ctx).Error("store segment data is empty")
		return
	}
	for iStore := 0; iStore < nStores; iStore++ {
		// 全部先初始化默认值
		storeId := stores[iStore].StoreId
		stores[iStore].StoreSegment = SegmentUnknown
		stores[iStore].StoreSegmentBoostFactor = boostFactors[SegmentUnknown]

		val, ok := storeSegments[keyList[iStore]]
		if !ok || val == nil {
			logkit.FromContext(ctx).Error("val is nil", logkit.Uint64("storeId", storeId))
			continue
		}
		mem, ok := val.GetData()[segmentFeatureId]
		if !ok || mem == nil {
			logkit.FromContext(ctx).Error("mem is nil", logkit.Uint64("storeId", storeId))
			continue
		}
		if mem.GetBytesList() == nil {
			logkit.FromContext(ctx).Error("data is nil", logkit.Uint64("storeId", storeId))
			continue
		}
		segmentData := mem.GetBytesList().GetValue()
		if len(segmentData) == 0 {
			logkit.FromContext(ctx).Error("segment list is 0", logkit.Uint64("storeId", storeId))
			continue
		}
		segment := string(segmentData[0])
		stores[iStore].StoreSegment = segment
		if _, exist := boostFactors[segment]; !exist {
			logger.MyDebug(ctx, traceInfo.IsDebug, "segment not found in boostFactors, use unknown segment boost factor",
				logkit.Uint64("storeId", storeId),
				logkit.String("segment", segment),
				logkit.Any("boostFactors", boostFactors),
				logkit.Float64("unknown segment boost factor", boostFactors[SegmentUnknown]))
			continue
		}
		stores[iStore].StoreSegmentBoostFactor = boostFactors[segment]
	}
}
