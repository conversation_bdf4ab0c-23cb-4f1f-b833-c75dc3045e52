package filling

import (
	"context"
	o2oalgo2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/proto/o2oalgo"
	"math"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_ctr_cvr"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// VN 原始排序
func RankingScoreFillingV1(ctx context.Context, traceInfo *traceinfo.TraceInfo, s model.StoreInfos) {
	query := strings.ToLower(traceInfo.QueryKeyword)
	queryAscii := strings.ToLower(util.ToAscii(traceInfo.QueryKeyword))

	isNeedEnhancement, targetL1Id := model.IsSortEnhancement(traceInfo)
	nonFoodSortRate := 1.0
	if isNeedEnhancement && apollo.SearchApolloCfg.NonFoodSortRate > 0.0 {
		nonFoodSortRate = apollo.SearchApolloCfg.NonFoodSortRate
	}
	for _, store := range s {
		store.OpeningScore = store.GetOpeningScore(query, queryAscii)
		store.NonFoodSortRate = 1.0
		if isNeedEnhancement && nonFoodSortRate != 1.0 && store.IsStoreNeedEnhancement(ctx, targetL1Id) {
			store.NonFoodSortRate = nonFoodSortRate
			store.OpeningScore = store.OpeningScore * nonFoodSortRate
			store.DistanceScore = store.DistanceScore * nonFoodSortRate // vn 旧逻辑
		}
		distanceRate := model.DistanceBoostPercentage * model.DistancePivot / (model.DistancePivot + store.Distance/1000)
		store.ReRankScore = store.OpeningScore * (1.0 + 50.0*distanceRate)
	}
}

func OfflineCtrCvrDataDefaultFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	if store_ctr_cvr.StoreCtrCvrDaoDict == nil {
		logkit.FromContext(ctx).Info("store_ctr_cvr.StoreCtrCvrDaoDict is nil, skip")
		return
	}

	for _, store := range stores {
		storeOfflineData := store_ctr_cvr.StoreCtrCvrDaoDict.GetStoreOfflineData(store.StoreId)
		if storeOfflineData == nil {
			storeOfflineData = &o2oalgo2.StoreOfflineData{}
		}

		store.PriceScore1 = math.Max(math.Min(storeOfflineData.GetStorePriceScore1(), traceInfo.PredictConfig.MaxTruncPrice), traceInfo.PredictConfig.MinTruncPrice)
		store.PriceScore2 = math.Max(math.Min(storeOfflineData.GetStorePriceScore2(), traceInfo.PredictConfig.MaxTruncPrice), traceInfo.PredictConfig.MinTruncPrice)
		store.PriceScore3 = math.Max(math.Min(storeOfflineData.GetStorePriceScore3(), traceInfo.PredictConfig.MaxTruncPrice), traceInfo.PredictConfig.MinTruncPrice)
		store.UE = storeOfflineData.GetUe()
		store.UERadio = storeOfflineData.GetUeRatio()
		store.Revenue = storeOfflineData.GetRevenue()
		store.Commission = storeOfflineData.GetCommission()
		store.IsTargetA30 = storeOfflineData.GetIsTargetA30()
		store.StoreImpCnt180d = storeOfflineData.GetStoreImpCnt_180D()
		store.StoreOrderCnt180d = storeOfflineData.GetStoreOrderCnt_180D()
		store.StoreCtCvr180d = storeOfflineData.GetStoreCtCvr_180D()
		store.IsMeetA30QualityConstraint = storeOfflineData.GetIsMeetA30QualityConstraint()

		// 预测失败的使用离线统计的数据兜底, 且上下限调整
		if traceInfo.CtrModelInfo.IsPredictSuccess == false {
			ctr := storeOfflineData.GetStoreCtr()
			store.PCtrScore = ctr
			store.CtrScoreOffline = ctr
		}
		if traceInfo.PredictConfig.MaxTruncPctr != 0 {
			store.PCtrScore = math.Max(math.Min(store.PCtrScore, traceInfo.PredictConfig.MaxTruncPctr), traceInfo.PredictConfig.MinTruncPctr)
		}
		// 预测失败的使用离线统计的数据兜底, 且上下限调整
		if traceInfo.CvrModelInfo.IsPredictSuccess == false {
			cvr := storeOfflineData.GetStoreCvr()
			store.PCvrScore = cvr
			store.CvrScoreOffline = cvr
		}
		// 预测失败的使用离线统计的数据兜底, 且上下限调整
		if traceInfo.UEModelInfo.IsPredictSuccess == false {
			store.PUEScore = store.UE
		}
		if traceInfo.PredictConfig.MaxTruncPcvr != 0 {
			store.PCvrScore = math.Max(math.Min(store.PCvrScore, traceInfo.PredictConfig.MaxTruncPcvr), traceInfo.PredictConfig.MinTruncPcvr)
		}
		if traceInfo.PredictConfig.MaxTruncUE != 0 {
			store.PUEScore = math.Max(math.Min(store.PUEScore, traceInfo.PredictConfig.MaxTruncUE), traceInfo.PredictConfig.MinTruncUE)
		}
	}
}

// ID,TH,MY 默认值: level:0,   score:store.GetRelevanceScore(1)
func RelevanceScoresFilling(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, level, score, semanticScore float64) {
	//新实验，给结果集分档
	store.RelevanceLevel = level
	if (traceInfo.PredictConfig.NewStrategy == 1 || traceInfo.PredictConfig.NewStrategy == 2) && traceInfo.PredictConfig.RelResLayer {
		if (level == 1 || level == 2) && store.Distance < traceInfo.PredictConfig.Distance {
			store.RelevanceLevelForSort = 1
		} else {
			store.RelevanceLevelForSort = 0
		}
	}
	if traceInfo.PredictConfig != nil && traceInfo.PredictConfig.RelevanceUseLevel == 1 {
		store.RelevanceScore = level
	} else {
		store.RelevanceScore = score
		if traceInfo.PredictConfig.MaxTruncRelevance != 0 {
			store.RelevanceScore = math.Max(math.Min(score, traceInfo.PredictConfig.MaxTruncRelevance), traceInfo.PredictConfig.MinTruncRelevance)
		}
	}
	store.SemanticRelevanceScore = semanticScore
	if traceInfo.PredictConfig != nil && traceInfo.PredictConfig.ReleFusionFunc == 1 {
		if level == 0 || semanticScore == 0 {
			store.SemanticRelevanceScore = 0
		} else {
			if traceInfo.PredictConfig.MaxTruncSemantic != 0 {
				store.SemanticRelevanceScore = math.Max(math.Min(semanticScore, traceInfo.PredictConfig.MaxTruncSemantic), traceInfo.PredictConfig.MinTruncSemantic)
			}
		}
	}
	if traceInfo.PredictConfig.ReleFusionFunc == 1 {
		store.RelevanceScore = (store.SemanticRelevanceScore + level) / 3.0
		if traceInfo.PredictConfig.MaxTruncRelevance != 0 {
			store.RelevanceScore = math.Max(math.Min(store.RelevanceScore, traceInfo.PredictConfig.MaxTruncRelevance), traceInfo.PredictConfig.MinTruncRelevance)
		}
	}
	// relevance失败,ES分数兜底
	if store.RelevanceScore == 0 {
		store.RelevanceScore = store.GetRelevanceScore(1)
	}
}

func RelevanceDefaultFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	// 1. relevance 默认分数
	truncESScore := apollo.AlgoApolloCfg.TruncESScore
	if truncESScore == 0 {
		truncESScore = 256
	}

	query := strings.ToLower(traceInfo.QueryKeyword)
	queryAscii := strings.ToLower(util.ToAscii(traceInfo.QueryKeyword))
	for _, store := range stores {
		if store.RelevanceScore == 0 {
			RelevanceScoresFilling(traceInfo, store, 0, store.GetRelevanceScore(1), 0)
		}
		if env.GetCID() == cid.VN {
			store.RelevanceScore = math.Min(store.ESScore, truncESScore) / truncESScore

			store.OpeningScore = 0.25
			if store.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
				store.OpeningScore = 0.75
			}

			store.OpeningRatio = store.GetOpeningRatio(query, queryAscii)
			if store.OpeningRatio == 0 {
				store.OpeningRatio = 1
			}
		}
	}
}
