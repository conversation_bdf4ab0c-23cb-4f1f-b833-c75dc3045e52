package filling

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/httpclient"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

// VNOpeningFilling 因为夜间下沉的营业状态比较敏感，要额外拿 open_status, 夜间100%流量, 因为这是公共的逻辑，要限制只有分页逻辑才夜间下沉状态
func VNOpeningFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingVNIsOpening, time.Since(pt))
	}()
	isOpeningResult := doGetIsOpeningDataForVN(ctx, traceInfo, stores.StoreIDs())
	for _, storeInfo := range stores {
		isOpeningData, ok := isOpeningResult[storeInfo.StoreId]
		if ok {
			switch isOpeningData {
			case int(httpclient.StoreOpeningStatusOpen):
				storeInfo.DisplayOpeningStatus = o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN
			case int(httpclient.StoreOpeningStatusClose):
				storeInfo.DisplayOpeningStatus = o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE
			case int(httpclient.StoreOpeningStatusBusy):
				storeInfo.DisplayOpeningStatus = o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE
			}
		}
	}
}

func doGetIsOpeningDataForVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIDs []uint64) map[uint64]int {
	idLen := len(storeIDs)
	isOpeningDataMap := make(map[uint64]int, idLen)
	if idLen == 0 {
		return isOpeningDataMap
	}
	count := idLen / 50
	if idLen%50 != 0 {
		count += 1
	}
	var errs = make(chan error, count)
	wg := &sync.WaitGroup{}
	wg.Add(count)
	lock := sync.RWMutex{}
	startTime := time.Now()
	for i := 0; i < count; i++ {
		var value []uint64
		if (i+1)*50 < idLen {
			value = storeIDs[i*50 : (i+1)*50]
		} else {
			value = storeIDs[i*50:]
		}
		goroutine.WithGo(ctx, "doGetIsOpeningDataForVN", func(params ...interface{}) {
			defer wg.Done()
			retMap, err := httpclient.GetIsOpeningData(ctx, traceInfo, value)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "vn_is_opening"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeHttp, "", "", "vn_is_opening")
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				logkit.FromContext(ctx).WithError(err).Error("SearchService.doGetIsOpeningDataForVN error", zap.String("cost", time.Since(startTime).String()), zap.Any("storeIds", value))
				reporter.ReportClientRequestError(1, "vn_is_opening", "failed")
				errs <- err
				return
			}
			reporter.ReportClientRequestError(1, "vn_is_opening", "0")
			lock.Lock()
			defer lock.Unlock()
			for k, v := range retMap {
				isOpeningDataMap[k] = v
			}
		})
	}
	wg.Wait()
	if len(errs) == count || len(isOpeningDataMap) == 0 {
		logkit.FromContext(ctx).Error("SearchService.doGetIsOpeningDataForVN batch failed", logkit.Int("totalSize", idLen), logkit.Int("count", count), logkit.Uint64s("storeIds", storeIDs))
	}
	return isOpeningDataMap
}
