package filling

import (
	"context"
	"math"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func RecallPriorityFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingPriority, time.Since(pt))
	}()
	recallMaxSize := 3000
	if traceInfo.RecallMaxSize > 0 {
		recallMaxSize = traceInfo.RecallMaxSize
	}
	traceInfo.RecallPriorityConfig = abtest.GetRecallPriorityConfig(ctx, traceInfo.AbParamClient, traceInfo.IsDebug, recallMaxSize)

	traceInfo.SortPriorityConfig = abtest.GetSortPriorityConfig(ctx, traceInfo.AbParamClient, traceInfo.IsDebug)
	for _, store := range stores {
		p := getStorePriority(store, traceInfo.RecallPriorityConfig.RecallPriority)
		store.RecallPriority = p
		store.SortPriority = p
		if traceInfo.SortPriorityConfig.IsUseSortPriority {
			store.SortPriority = getStorePriority(store, traceInfo.SortPriorityConfig.SortPriority)
		}
	}
}

func getStorePriority(store *model.StoreInfo, recallPriority map[string]int) int {
	p := math.MaxInt
	for _, recallType := range store.RecallTypes {
		if pn, exist := recallPriority[recallType]; exist && pn < p {
			p = pn
		}
	}
	if p == math.MaxInt {
		p = 0
	}
	return p
}
