package filling

import (
	"context"
	"fmt"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreRoutingDistanceFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingRoutingDistance, time.Since(pt))
	}()
	var distanceMap = make(map[string]uint64, len(stores))
	lng := traceInfo.TraceRequest.Longitude
	lat := traceInfo.TraceRequest.Latitude
	for _, store := range stores {
		key := fmt.Sprintf("%.4f_%.4f_%.4f_%.4f", store.Location.GetLongitude(), store.Location.GetLatitude(), lng, lat)
		distanceMap[key] = 0
	}
	startTime := time.Now()
	err := localcache.StoreCacheSysInstance.MGetRoutingDistance(ctx, traceInfo, distanceMap)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "MGetRoutingDistance"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "MGetRoutingDistance")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
	}
	for _, store := range stores {
		key := fmt.Sprintf("%.4f_%.4f_%.4f_%.4f", store.Location.GetLongitude(), store.Location.GetLatitude(), lng, lat)
		distance := distanceMap[key]
		if distance > 0 {
			store.RoutingDistance = float64(distance)
			store.Distance = store.RoutingDistance
			store.DistanceScore = store.CountDistanceScore(traceInfo.HandlerType)
			store.DistanceGroup5km = store.CountDistanceGroup5km()
		}
	}
}
