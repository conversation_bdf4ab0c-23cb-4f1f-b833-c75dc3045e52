package filling

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"google.golang.org/protobuf/proto"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	promotion2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/promotion"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func StorePromotionFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStorePromotion, time.Since(pt))
	}()
	loc := &foodalgo_search.Geo{
		Longitude: proto.Float32(float32(traceInfo.TraceRequest.Longitude)),
		Latitude:  proto.Float32(float32(traceInfo.TraceRequest.Latitude)),
	}
	var err error
	var storePromotionDataMap map[uint64]*model.StorePromotionData
	if !apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		storePromotionDataMap, err = integrate.PromotionCacheRedis.GetStorePromo(ctx, loc, traceInfo.QueryKeyword, traceInfo.UserId)
	} else {
		logkit.FromContext(ctx).Debug("get store promotion cache, close coids")
	}

	if err != nil || storePromotionDataMap == nil {
		storePromotionDataMap, err = getStorePromotionData(ctx, traceInfo, stores.DistinctStoreIDs())
		if err != nil || storePromotionDataMap == nil {
			logkit.FromContext(ctx).Error(
				"SearchService.filterByPromotion, get promotion data fail!",
				zap.Int("len", len(stores)),
				zap.Any("error", err))
			return
		}
		if !apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
			goroutine.WithGo(ctx, "SetStorePromo", func(params ...interface{}) {
				err = integrate.PromotionCacheRedis.SetStorePromo(ctx, loc, traceInfo.QueryKeyword, traceInfo.UserId, storePromotionDataMap)
				if err != nil {
					logkit.FromContext(ctx).WithError(err).Error("SetStorePromo error")
				}
			})
		}

	}
	for _, store := range stores {
		storePromotionData, ok := storePromotionDataMap[store.StoreId]
		if ok && storePromotionData != nil {
			store.StorePromotion = storePromotionData
		}
	}
}

func getStorePromotionData(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIDs []uint64) (map[uint64]*model.StorePromotionData, error) {
	idLen := len(storeIDs)
	if idLen == 0 {
		return nil, nil
	}
	count := idLen / 50
	if idLen%50 != 0 {
		count += 1
	}

	var (
		promotionDataMap = make(map[uint64]*model.StorePromotionData, idLen)
		errs             = make(chan error, count)
	)

	isSelfPickup := false
	if traceInfo.TraceRequest.GetFilterType() != nil {
		orderTypeList := traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList()
		for i := range orderTypeList {
			if orderTypeList[i] == 1 {
				isSelfPickup = true
				break
			}
		}
	}

	wg := &sync.WaitGroup{}
	wg.Add(count)
	lock := sync.RWMutex{}
	for i := 0; i < count; i++ {
		var value []uint64
		if (i+1)*50 < idLen {
			value = storeIDs[i*50 : (i+1)*50]
		} else {
			value = storeIDs[i*50:]
		}

		goroutine.WithGo(ctx, "GetStorePromotionBrief", func(params ...interface{}) {
			defer wg.Done()
			startTime := time.Now()
			retMap, err := promotion2.GetStorePromotionBrief(ctx, traceInfo.UserId, value, isSelfPickup)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "storePromotion"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "storePromotion")
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
				logkit.FromContext(ctx).WithError(err).Error(
					"SearchService.getStorePromotionData error",
					zap.Any("storeIds", value))
				reporter.ReportClientRequestError(1, "storePromotion", "failed")
				errs <- err
			} else {
				lock.Lock()
				defer lock.Unlock()
				for k, v := range retMap {
					promotionDataMap[k] = v
				}

				reporter.ReportClientRequestError(1, "storePromotion", "0")
			}
		})
	}
	wg.Wait()
	if len(errs) == count {
		return nil, <-errs
	}
	return promotionDataMap, nil
}
