package filling

import (
	"context"
	"errors"
	"fmt"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func Filling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) (model.StoreInfos, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseStoreFilling, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilling, len(stores))
	}()

	stores = StoreMetaFilling(ctx, traceInfo, stores)
	if traceInfo.IsDowngradeDataServer {
		logkit.FromContext(ctx).Error("StoreMetaFilling failed")
		return nil, errors.New(fmt.Sprintf("batch meta server failed"))
	}

	AfterStoreMetaFilling(ctx, traceInfo, stores)
	return stores, nil
}

func AfterStoreMetaFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingAfterStoreMeta, time.Since(pt))
	}()
	wg := sync.WaitGroup{}
	if decision.IsNeedStorePolygon(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StorePolygonFilling", func(params ...interface{}) {
			defer wg.Done()
			StorePolygonFillingBatch(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedMapMatrix(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreRoutingDistanceFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreRoutingDistanceFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedVNOpeningFilling(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "VNOpeningFilling", func(params ...interface{}) {
			defer wg.Done()
			VNOpeningFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedVNPromotion(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "VNPromotionFilling", func(params ...interface{}) {
			defer wg.Done()
			VNPromotionFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedPromotion(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "PromotionFilling", func(params ...interface{}) {
			defer wg.Done()
			PromotionFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedShippingFee(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "ShippingFeeFilling", func(params ...interface{}) {
			defer wg.Done()
			ShippingFeeFilling(ctx, traceInfo, stores)
		})
	}

	if decision.IsNeedMaxedShippingFeeDiscountAmount(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "MaxedShippingFeeDiscountAmount", func(params ...interface{}) {
			defer wg.Done()
			StoreMaxShippingFeeDiscountFilling(ctx, traceInfo, stores)
		})
	}

	if decision.IsNeedStorePromotion(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StorePromotionFilling", func(params ...interface{}) {
			defer wg.Done()
			StorePromotionFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedStorePrice(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StorePriceFilling", func(params ...interface{}) {
			defer wg.Done()
			StorePriceFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedStoreTextMatchScore(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreTextMatchScoreFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreTextScoreMatchFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedStoreCategoryScore(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreCategoryScoreFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreCategoryScoreFilling(ctx, traceInfo, stores)
		})
	}

	wg.Add(1)
	goroutine.WithGo(ctx, "RecallPriorityFilling", func(params ...interface{}) {
		defer wg.Done()
		RecallPriorityFilling(ctx, traceInfo, stores)
	})

	if decision.IsNeedStoreAffiliateFilling(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "IsNeedStoreAffiliateFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreAffiliateFilling(ctx, traceInfo, stores)
		})
	}
	if decision.IsNeedStoreSegment(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreSegmentFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreSegmentFilling(ctx, traceInfo, stores)
		})
	}

	if decision.IsNeedStoreNormalizedScore(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreNormalizedScoreFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreNormalizedScoreFilling(ctx, traceInfo, stores)
		})
	}

	wg.Add(1)
	goroutine.WithGo(ctx, "StoreNameMatchFilling", func(params ...interface{}) {
		defer wg.Done()
		keywordNor := util2.StringNormalize(traceInfo.QueryKeyword)
		if len(stores) < 50 {
			fillingIsPreciseAndExactMatch(ctx, traceInfo, stores, keywordNor)
		} else {
			// 并发 5 路去计算
			workers := 5
			// 计算每份的大小
			chunkSize := (len(stores) + workers - 1) / workers // 向上取整

			wg2 := sync.WaitGroup{}
			wg2.Add(workers)
			for i := 0; i < workers; i++ {
				var tempStores model.StoreInfos
				if i+1 < workers {
					tempStores = stores[i*chunkSize : (i+1)*chunkSize]
				} else {
					tempStores = stores[i*chunkSize:]
				}
				goroutine.WithGo(ctx, "StoreNameMatchFilling-sub-goroutine", func(params ...interface{}) {
					defer wg2.Done()
					param := params[0].([]interface{})
					handleStores := param[0].(model.StoreInfos)
					fillingIsPreciseAndExactMatch(ctx, traceInfo, handleStores, keywordNor)
				}, tempStores)
			}
			wg2.Wait()
		}
	})

	// 计算 category 自定义分数
	if decision.IsNeedStoreCategoryDeboostScore(traceInfo) {
		wg.Add(1)
		goroutine.WithGo(ctx, "StoreCategoryDeboostScoreFilling", func(params ...interface{}) {
			defer wg.Done()
			StoreCategoryDeboostScoreFilling(ctx, traceInfo, stores)
		})
	}
	wg.Wait()
}
