package filling

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"strings"
	"time"
)

func StoreCategoryDeboostScoreFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreCategoryDeboostScore, time.Since(pt))
	}()
	for _, store := range stores {
		if len(traceInfo.QPResult.QueryCateIntentsManualReviewL1Ids) == 0 || len(store.L1CategoryId) == 0 {
			store.DeboostCategoryScore = -1.0
		} else if CheckIntersection(traceInfo.QPResult.QueryCateIntentsManualReviewL2Ids, store.L2CategoryId) {
			store.DeboostCategoryScore = 0.9
		} else if CheckIntersection(traceInfo.QPResult.QueryCateIntentsManualReviewL1Ids, store.L1CategoryId) {
			store.DeboostCategoryScore = 0.7
		} else if CheckIntersection(traceInfo.QPResult.QueryCateIntentsManualReviewL2Ids, apollo.GetEasyConfuseCategoryID()) &&
			CheckIntersection(store.L2CategoryId, apollo.GetEasyConfuseCategoryID()) {
			store.DeboostCategoryScore = 0.6
		} else if strings.Contains(strings.ToLower(store.StoreName), strings.ToLower(traceInfo.QueryKeyword)) {
			store.DeboostCategoryScore = 0.5
		} else {
			store.DeboostCategoryScore = 0.01
		}
	}
}

func CheckIntersection(a, b map[uint32]struct{}) bool {
	if len(a) == 0 || len(b) == 0 {
		return false
	}
	for k := range a {
		if _, ok := b[k]; ok {
			return true
		}
	}
	return false
}
