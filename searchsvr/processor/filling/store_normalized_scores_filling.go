package filling

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
	"time"
)

func StoreNormalizedScoreFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreNormalizedScore, time.Since(pt))
	}()

	sortType := traceInfo.TraceRequest.GetSortType()
	maxFactor := 0.0

	for _, store := range stores {
		switch sortType {
		case foodalgo_search.SearchRequest_Nearby:
			if store.Distance > maxFactor {
				maxFactor = store.Distance
			}
		case foodalgo_search.SearchRequest_TopSales:
			if float64(store.StoreSellWeek) > maxFactor {
				maxFactor = float64(store.StoreSellWeek)
			}
		case foodalgo_search.SearchRequest_BestRated:
			if store.RatingScore > maxFactor {
				maxFactor = store.RatingScore
			}
		}
	}

	switch sortType {
	case foodalgo_search.SearchRequest_Nearby:
		logkit.Debug("NearBy", zap.Float64("max factor", maxFactor))
	case foodalgo_search.SearchRequest_TopSales:
		logkit.Debug("TopSales", zap.Float64("max factor", maxFactor))
	case foodalgo_search.SearchRequest_BestRated:
		logkit.Debug("BestRated", zap.Float64("max factor", maxFactor))
	}

	for _, store := range stores {
		if maxFactor == 0 {
			store.NormalizedScore = 0
			continue
		}
		switch sortType {
		case foodalgo_search.SearchRequest_Nearby:
			store.NormalizedScore = (maxFactor - store.Distance) / maxFactor
		case foodalgo_search.SearchRequest_TopSales:
			store.NormalizedScore = float64(store.StoreSellWeek) / maxFactor
		case foodalgo_search.SearchRequest_BestRated:
			store.NormalizedScore = store.RatingScore / maxFactor
		}
	}
}
