package filling

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreTextScoreMatchFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStoreTextMatchScore, time.Since(pt))
	}()

	for _, s := range stores {
		s.StoreTextMatchScore, s.StoreTextMatchScoreCalc = calcStoreTextMatchScore(traceInfo.IsDebug, traceInfo.QueryKeyword, traceInfo.QPResult.RewriteQuery, s.StoreName)
	}
}

// 计算最终的文本匹配分数
func calcStoreTextMatchScore(isDebug bool, query string, rewriteSet []string, storeName string) (float64, string) {
	fields := util.RemoveDuplicates(util.SplitToWords(storeName))

	// 构造 Q+ 集合，包括原始查询 query 和改写集合 rewriteSet
	Q := append([]string{query}, rewriteSet...)

	// 初始化最大得分
	maxScore := 0.0

	// 遍历 Q+ 集合中的每个查询词，计算 R(t, F) 的最大值
	for _, q := range Q {
		score := util.TfScore(q, fields)
		if score > maxScore {
			maxScore = score
		}
	}

	// default = 5.0
	truncStoreTextMatchScore := apollo.SearchApolloCfg.MainSiteStoreTruncTextMatchScore
	if truncStoreTextMatchScore == 0 {
		truncStoreTextMatchScore = 5.0
	}

	// 对得分进行归一化，归一化公式为：min(dish_text_match_score, trunc_dish_text_match_score) / trunc_dish_text_match_score
	normalizedScore := util.MinFloat64(maxScore, truncStoreTextMatchScore) / truncStoreTextMatchScore

	if isDebug {
		debugInfo := map[string]interface{}{
			"query":             query,
			"rewrite_set":       rewriteSet,
			"store_name":        storeName,
			"fields":            fields,
			"max_score":         maxScore,
			"trunc_match_score": truncStoreTextMatchScore,
			"normalized_score":  normalizedScore,
		}

		debugJSON, _ := json.Marshal(debugInfo)
		return normalizedScore, string(debugJSON)
	}

	return normalizedScore, ""
}
