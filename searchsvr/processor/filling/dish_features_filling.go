package filling

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 填充菜品特征信息
func DishFeaturesFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if len(dishes) == 0 {
		return
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
		return
	}
	if decision.IsNeedDishListingRank(traceInfo) == false {
		return
	}

	pt := time.Now()
	dishIds := dishes.DishIds()
	totalLen := len(dishIds)
	hitLen := totalLen
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingFeature, time.Since(pt))
		metric_reporter2.ReportHitRateRequest(totalLen, hitLen, "DishFeatureFreeCache")
	}()
	dishMap := dishes.DishMap()
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish features filling", logkit.Any("dishIds", dishIds))
	dishFeatureMap := make(map[uint64]*foodalgo_search.DishFeature, len(dishIds))
	pt1 := time.Now()

	// 看debug请求放弃缓存
	var noCacheIds []uint64
	if decision.IsSkipDishFeaturesCache(ctx, traceInfo) {
		noCacheIds = dishIds
	} else {
		noCacheIds = localcache.StoreCacheSysInstance.MetGetDishFeatureCacheBatch(ctx, dishIds, dishFeatureMap)
	}

	traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingFeatureGetCache, time.Since(pt1))
	if len(noCacheIds) > 0 {
		hitLen = totalLen - len(noCacheIds)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish features filling with rpc", logkit.Any("dishIds", noCacheIds))
		pt2 := time.Now()
		features, err := mlplatform.GetDishFeatureList(ctx, traceInfo.IsDebug, traceInfo.TraceRequest.PublishId, noCacheIds)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish features filling with rpc", logkit.Any("features", features))
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingFeatureRPC, time.Since(pt2))
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("GetDishFeatureList failed")
		}
		for id, feature := range features {
			if dish, ok := dishMap[id]; ok && dish != nil {
				dishFeatureMap[id] = feature
			}
		}
		// 批量写入缓存
		goroutine.WithGo(ctx, "MSetDishFeatureCache", func(params ...interface{}) {
			pt3 := time.Now()
			localcache.StoreCacheSysInstance.MSetDishFeatureCache(ctx, features, 1800)
			traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFillingFeatureSetCache, time.Since(pt3))
		})
	}
	for _, dish := range dishes {
		dishId := dish.DishId
		if feature, ok := dishFeatureMap[dishId]; ok && feature != nil {
			dish.DishFeature = feature
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish features filling with rpc", logkit.Any("dishFeatureMap", dishFeatureMap))
	return
}
