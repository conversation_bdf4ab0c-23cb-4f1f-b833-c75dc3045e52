package filling

import (
	"context"
	"strconv"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const GlobalStorePriceBid = 87
const GlobalPriceFeatureId = 5701633

func StorePriceFilling(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingStorePrice, time.Since(pt))
	}()
	keyList := make([]string, len(stores), len(stores))
	for i, _ := range stores {
		keyList[i] = strconv.FormatInt(int64(stores[i].StoreId), 10)
	}
	storePriceBid := config2.SearchApolloCfg.StorePriceBid
	if storePriceBid == 0 {
		storePriceBid = GlobalStorePriceBid
	}
	rsp, err := integrate.DataManageServiceClient.GetData(ctx, storePriceBid, keyList)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_META_ERROR)
		logkit.FromContext(ctx).Error("failed to get store price data", logkit.Err(err))
		return
	}
	if rsp == nil || rsp.GetItems() == nil {
		logkit.FromContext(ctx).Error("store price data is nil", logkit.Err(err))
		return
	}

	priceFeatureId := config2.SearchApolloCfg.PriceFeatureId
	if priceFeatureId == 0 {
		priceFeatureId = GlobalPriceFeatureId
	}
	storePrice := rsp.GetItems()
	if len(storePrice) == 0 {
		logkit.FromContext(ctx).Error("store price data is empty")
		return
	}
	nStores := len(stores)
	for iStore := 0; iStore < nStores; iStore++ {
		val, ok := storePrice[keyList[iStore]]
		if !ok || val == nil {
			logkit.FromContext(ctx).Error("val is nil", logkit.Any("storePrice", storePrice[keyList[iStore]]))
			continue
		}
		mem, ok := val.GetData()[priceFeatureId]
		if !ok || mem == nil {
			logkit.FromContext(ctx).Error("mem is nil", logkit.Any("mem", val))
			continue
		}
		if mem.GetInt64List() == nil {
			logkit.FromContext(ctx).Error("data is nil", logkit.Any("data", mem))
			continue
		}
		priceData := mem.GetInt64List().GetValue()
		if len(priceData) == 0 {
			logkit.FromContext(ctx).Error("price list is 0")
			continue
		}
		stores[iStore].StorePrice = uint64(priceData[0])
	}
}
