package preprocess

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"sort"
	"strings"
	"time"

	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/o2o-intelligence/search/nlp/components/tokenizer/algo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"github.com/gogo/protobuf/proto"
)

func BuildContextFeature(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	var isHitTopLogic int64 = 0
	if len(traceInfo.OptIntervention.QueryStoreTop) > 0 {
		isHitTopLogic = 1
	}
	timeNow := timeutil.GetCIDTime(timeutil.Now())
	day := int(timeNow.Day())
	month := int(timeNow.Month())
	activityCalendar := "normal"
	if month == day {
		activityCalendar = "double_promotion"
	} else if day == 25 || day == 26 || day == 27 {
		activityCalendar = "pay_day"
	} else if day == 28 || day == 29 || day == 30 {
		activityCalendar = "shopee_day"
	}

	foodRange := "supper"
	if traceInfo.UserContext.Hour >= 6 && traceInfo.UserContext.Hour < 10 {
		foodRange = "breakfast"
	} else if traceInfo.UserContext.Hour >= 10 && traceInfo.UserContext.Hour < 13 {
		foodRange = "lunch"
	} else if traceInfo.UserContext.Hour >= 13 && traceInfo.UserContext.Hour < 17 {
		foodRange = "teabreak"
	} else if traceInfo.UserContext.Hour >= 17 && traceInfo.UserContext.Hour < 21 {
		foodRange = "dinner"
	}

	feature := &food.ContextFeature{
		UserId:                int64(traceInfo.UserId),
		Brand:                 traceInfo.UserContext.Brand,
		Model:                 traceInfo.UserContext.Model,
		Os:                    traceInfo.UserContext.Os,
		Wifi:                  traceInfo.UserContext.Wifi,
		DeviceId:              traceInfo.UserContext.DeviceId,
		CQueryMaxWordSegments: traceInfo.QPResult.MaxWordSegments,
		CHour:                 traceInfo.UserContext.Hour,
		CQueryDishIntention:   convertStringToBoolInt64(traceInfo.QPResult.QueryDishIntention),
		CQueryRaw:             strings.ToLower(traceInfo.TraceRequest.QueryRaw),
		CIsHoliday:            traceInfo.UserContext.IsHoliday,
		CQuerySegments:        traceInfo.QPResult.Segments,
		CQueryStoreIntention:  convertStringToBoolInt64(traceInfo.QPResult.QueryStoreIntention),
		CQueryTagIntention:    traceInfo.QPResult.StoreIntents,
		CDayOfWeek:            traceInfo.UserContext.DayOfWeek,
		Longitude:             traceInfo.UserContext.Longitude,
		Latitude:              traceInfo.UserContext.Latitude,
		Query:                 traceInfo.QueryKeywordForDiff, // todo delete after diff
		//Query:                  traceInfo.QueryKeyword,
		CCorrectorRewriteQuery: traceInfo.QPResult.CorrectWord,
		CCorrectorRewriteType:  traceInfo.QPResult.CorrectionType.String(),
		GeoHash_4:              traceInfo.UserContext.GeoHash4,
		GeoHash_5:              traceInfo.UserContext.GeoHash5,
		GeoHash_6:              traceInfo.UserContext.GeoHash6,
		CQueryIsHitTopLogic:    isHitTopLogic,
		CSearchTimestamp:       time.Now().Unix(),
		CRewriteQuery:          traceInfo.QPResult.RewriteQuery,
		CDayOfMonth:            int64(day),
		CActivityCalendar:      activityCalendar,
		CFoodRange:             foodRange,
		CCtrModelName:          traceInfo.PredictConfig.CtrModuleName,
		CCvrModelName:          traceInfo.PredictConfig.CvrModuleName,
		CUeModelName:           traceInfo.PredictConfig.UEModuleName,
		CQueryCategory:         buildQueryCategoryFea(traceInfo.QPResult.ProbFilterCategoryIntentions),
	}
	if traceInfo.IsNeedCorrect == true {
		feature.CIsRequestCorrectorRewrite = 1
	}
	if env.GetCID() == cid.VN {
		fineTokenizer := algo.NewRegexpTokenizer()
		tokens := fineTokenizer.Tokenize(traceInfo.QueryKeyword)
		nTokens := len(tokens)
		segments := make([]string, nTokens, nTokens)
		for i := 0; i < nTokens; i++ {
			segments[i] = tokens[i].GetToken()
		}
		feature.CQuerySegments = segments
		feature.AppType = traceInfo.TraceRequest.AppType
		feature.CQueryRaw = strings.ToLower(traceInfo.TraceRequest.QueryRaw)
		feature.Query = strings.ToLower(traceInfo.QueryKeyword)
	}

	if decision.IsMockModelScore(ctx, traceInfo) {
		// 和时间有关的属性设置为固定值
		feature.CReqTimeInt = 1672531200
		feature.CSearchTimestamp = 1672531200
		feature.Timestamp = 1672531200
		// 和logid，publid都hardcode
		feature.PublishId = ""
		feature.LogId = ""
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:UserContextFeature", logkit.Any("feature", feature))
	traceInfo.ContextFeature = feature
	data, _ := proto.Marshal(feature)
	traceInfo.ContextFeatureBytes = data
	return
}

func buildQueryCategoryFea(intentions []*qp.CategoryIntention) string {
	names := "none category"
	if len(intentions) == 0 {
		return names
	}
	cateNames := make([]string, 0, len(intentions))
	for _, cate := range intentions {
		if cate == nil {
			continue
		}
		name := cate.GetLevel1Name() + " : " + cate.GetLevel2Name()
		if len(name) > 3 {
			cateNames = append(cateNames, name)
		}
	}
	sort.Slice(cateNames, func(i, j int) bool {
		// 拼接一级类目和二级类目的字符串进行比较
		return cateNames[i] < cateNames[j]
	})
	if len(cateNames) > 0 {
		names = strings.Join(cateNames, " ### ")
	}
	return names
}

func convertStringToBoolInt64(key string) int64 {
	if len(key) > 0 {
		return 1
	}
	return 0
}
