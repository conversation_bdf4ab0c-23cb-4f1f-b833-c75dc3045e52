package preprocess

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_intervention"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func Intervention(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	if traceInfo.IsDebug && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipIntervention() {
		traceInfo.IsSkipIntervention = true
		logkit.FromContext(ctx).Info("skip intervention")
		return
	}
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseOptSort {
		logkit.FromContext(ctx).Info("downgrade for intervention, skip")
		traceInfo.IsSkipIntervention = true
		return
	}
	// 只有search主搜才走干预, collection, few result都不需要
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		store_intervention.StoreInterventionDAO.GetStoreIntervention(traceInfo.QueryKeyword, traceInfo.TraceRequest.LocationGroupIds, traceInfo)
	}
}
