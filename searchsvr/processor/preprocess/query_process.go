package preprocess

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"
	"go.uber.org/zap"
)

func QueryProcess(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePreProcessQP, time.Since(pt))
	}()
	_, _, err := qp.QPServiceClient.GetQPResult(ctx, traceInfo.QueryKeyword, false, traceInfo, traceInfo.ABTestGroup.GetABTestString())
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "debug log: QPService trace info QPResult", zap.Any("QPResult", traceInfo.QPResult))
		logger.MyDebug(ctx, traceInfo.IsDebug, "debug log: QPService trace info QPResp", zap.Any("QPResp", traceInfo.QPResponse))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to get qp profile result", logkit.Any("err", err))
		traceInfo.AddErrorToTraceInfo(err)
	}
}
