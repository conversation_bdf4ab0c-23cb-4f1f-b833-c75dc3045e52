package preprocess

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	vn_brand_protection_keywords "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_brand_protect"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

func VnBrandProtectStoreIds(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	if cid.IsVN() == false {
		return
	}
	oldStoreIds := traceInfo.TraceRequest.GetFilterType().GetStoreIds()
	sort.Slice(oldStoreIds, func(i, j int) bool { return oldStoreIds[i] < oldStoreIds[j] })
	defer func() {
		logkit.FromContext(ctx).Info("vn brand protect: finally diff",
			logkit.Any("oldStoreIds", oldStoreIds),
			logkit.Any("newStoreIds", traceInfo.TraceRequest.GetFilterType().GetStoreIds()),
			logkit.Bool("isDiff", !util.IfSliceEqual(oldStoreIds, traceInfo.TraceRequest.GetFilterType().GetStoreIds())))
	}()

	isSkipBrandProtect := traceInfo.AbParamClient.GetParamWithInt("Search.General.IsSkipBrandProtect", 0)
	if isSkipBrandProtect == 1 {
		logkit.FromContext(ctx).Info("vn brand protect: Search.General.IsSkipBrandProtect == 1, skip")
		return
	}
	if traceInfo.TraceRequest.CityId <= 0 {
		logkit.FromContext(ctx).Info("vn brand protect: invalid city id, skip")
		return
	}
	key := fmt.Sprintf("%s_%d", util.ToAscii(strings.TrimSpace(strings.ToLower(traceInfo.QueryKeyword))), traceInfo.TraceRequest.CityId)
	newStoreIds := vn_brand_protection_keywords.GetVnBrandProtectStoreIds(key)
	sort.Slice(newStoreIds, func(i, j int) bool { return newStoreIds[i] < newStoreIds[j] })
	if len(newStoreIds) > 0 {
		isUseBrandProtect := traceInfo.AbParamClient.GetParamWithInt("Search.General.IsUseBrandProtect", 0)
		if isUseBrandProtect == 1 {
			traceInfo.TraceRequest.GetFilterType().StoreIds = newStoreIds
			// 老逻辑迁移：
			//if sort_type == RestaurantSortType.BEST_MATCH and top_restaurant_ids:
			//    sort_type = RestaurantSortType.NEAR_ME
			if traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
				traceInfo.TraceRequest.SortType = foodalgo_search.SearchRequest_Nearby.Enum()
			}
			logkit.FromContext(ctx).Info("vn brand protect: Search.General.IsUseBrandProtect == 1, use brand protect",
				logkit.Any("oldStoreIds", oldStoreIds),
				logkit.Any("newStoreIds", newStoreIds),
				logkit.Bool("isDiff", !util.IfSliceEqual(oldStoreIds, newStoreIds)),
				logkit.String("key", key))
		} else {
			isDiff, notInA, notInB := diffSlice(oldStoreIds, newStoreIds)
			logkit.FromContext(ctx).Info("vn brand protect: Search.General.IsUseBrandProtect == 0, not use brand protect",
				logkit.String("key", key),
				logkit.Any("oldStoreIds", oldStoreIds),
				logkit.Any("newStoreIds", newStoreIds),
				logkit.Bool("isDiff", isDiff),
				logkit.Any("notInA", notInA),
				logkit.Any("notInB", notInB),
			)
		}
	}
}

func diffSlice(a, b []uint32) (bool, []uint32, []uint32) {
	notInA := []uint32{}
	notInB := []uint32{}
	if len(a) == 0 && len(b) == 0 {
		return false, notInA, notInB
	}
	// 使用 map 统计元素出现的情况
	setA := make(map[uint32]struct{})
	setB := make(map[uint32]struct{})

	for _, num := range a {
		setA[num] = struct{}{}
	}
	for _, num := range b {
		setB[num] = struct{}{}
	}
	for k := range setB {
		if _, ok := setA[k]; !ok {
			notInA = append(notInA, k)
		}
	}
	for k := range setA {
		if _, ok := setB[k]; !ok {
			notInB = append(notInB, k)
		}
	}
	isDiff := true
	if len(notInA) == 0 && len(notInB) == 0 {
		isDiff = false
	}
	return isDiff, notInA, notInB
}
