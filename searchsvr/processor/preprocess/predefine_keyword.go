package preprocess

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

func PredefineKeyword(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	if traceInfo.IsDebug && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipPredefine() {
		logger.MyDebug(ctx, traceInfo.IsDebug, "skip predefine keyword")
		return
	}
	keyword := predefined_keyword.PredefinedKeywordDict.ReplacePredefinedKeyword(ctx, traceInfo.QueryKeyword)
	if strings.ToLower(traceInfo.QueryKeyword) != keyword {
		traceInfo.PredefineKeyword = keyword
		traceInfo.IsPredefineKeyword = true
	}
	traceInfo.QueryKeywordForDiff = keyword
	if env.GetCID() == cid.MY && !traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.QueryNormExp) {
		// 删除多余标点符号, MY站点
		keyword = util.RemoveAllPunctAndTrim(keyword)
	}
	traceInfo.QueryKeyword = keyword
}
