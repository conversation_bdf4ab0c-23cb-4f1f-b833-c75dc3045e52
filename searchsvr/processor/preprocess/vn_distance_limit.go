package preprocess

import (
	"context"
	"strconv"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func VNDistanceLimit(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	if cid.IsVN() == false {
		return
	}
	defer func() {
		metric_reporter2.ReportItemNumAndQPS(int(traceInfo.DistanceLimit), metric_reporter2.SearchReportTypePhrase, traceInfo.HandlerType.String(), strconv.Itoa(int(traceInfo.TraceRequest.CityId)), constants.DistanceLimit)
	}()
	defaultDis := traceInfo.AbParamClient.GetParamWithInt("Search.General.DistanceLimitDefault", 20000)
	IsUseDefaultDis := traceInfo.AbParamClient.GetParamWithInt("Search.General.IsUseDistanceLimitDefault", 0)
	if IsUseDefaultDis == 1 && defaultDis > 0 {
		traceInfo.DistanceLimit = uint32(defaultDis)
		logkit.FromContext(ctx).Info("DistanceLimit with default", logkit.Uint32("city id", traceInfo.TraceRequest.CityId), logkit.Uint32("DistanceLimit", traceInfo.DistanceLimit))
		return
	}
	isSkipPeakMode := traceInfo.AbParamClient.GetParamWithInt("Search.General.IsSkipPeakModeDistance", 0)
	if isSkipPeakMode == 1 {
		// 使用admin配置
		deliveryDisAdmin := config.VNAdminCfg.GetTimeRangeSettingForInt("delivery.distance.limit", int(traceInfo.TraceRequest.CityId), 0, 0, int32(defaultDis))
		if deliveryDisAdmin > 0 {
			traceInfo.DistanceLimit = uint32(deliveryDisAdmin)
			logkit.FromContext(ctx).Info("DistanceLimit with admin", logkit.Uint32("city id", traceInfo.TraceRequest.CityId), logkit.Uint32("DistanceLimit", traceInfo.DistanceLimit))
		}
	} else {
		// 使用peakmode
		deliveryDis := PeakModeDistance(ctx, traceInfo)
		if deliveryDis > 0 {
			traceInfo.DistanceLimit = deliveryDis
			logkit.FromContext(ctx).Info("DistanceLimit with peakmode", logkit.Uint32("city id", traceInfo.TraceRequest.CityId), logkit.Uint32("DistanceLimit", traceInfo.DistanceLimit))
		}
	}
}
