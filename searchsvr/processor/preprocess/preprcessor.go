package preprocess

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 召回前预处理
func CorrectedRecallPreProcess(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePreProcessCorrectedRecall, time.Since(pt))
	}()
	PredefineKeyword(ctx, traceInfo)
	Intervention(ctx, traceInfo)
	QueryProcess(ctx, traceInfo)
	BuildContextFeature(ctx, traceInfo)

	// 数据源初始化
	TraceInfoReInitDataSource(ctx, traceInfo)
}

func SearchPipelinePreProcess(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhrasePreProcess, time.Since(pt))
	}()
	traceInfo.QueryKeyword = UnicodeReplace(traceInfo.QueryKeyword)
	QueryNormalize(ctx, traceInfo)
	CorrectKeyword(ctx, traceInfo)
	PredefineKeyword(ctx, traceInfo)
	Intervention(ctx, traceInfo)
	QueryProcess(ctx, traceInfo)
	BuildContextFeature(ctx, traceInfo)

	// 数据源初始化
	TraceInfoReInitDataSource(ctx, traceInfo)
}
