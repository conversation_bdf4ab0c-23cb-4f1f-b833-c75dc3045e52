package preprocess

import (
	"context"
	"unicode"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/query_normalize"
	"go.uber.org/zap"
)

func QueryNormalize(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	if !traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.QueryNormExp) {
		return
	}
	query_normalize.Norm(ctx, traceInfo)
}

func UnicodeReplace(query string) string {
	if cid.IsVN() == false {
		return query
	}
	//newKeyword := query
	newKeyword := vn_char_mapping_job.CharMappingInstance.ReplaceChar(query)
	// 判断是否还有需要替换的unicode, 方便后续添加到词典中
	for _, i := range newKeyword {
		if unicode.Is(unicode.Mn, i) {
			logkit.Info("Has New replaced unicode", zap.String("query", newKeyword))
			break
		}
	}
	return newKeyword
}
