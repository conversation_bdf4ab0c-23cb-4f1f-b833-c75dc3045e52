package preprocess

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"
	"github.com/jinzhu/copier"
)

// 请注意 Clone后，需要重新初始化数据源
func TraceInfoClone(ctx context.Context, fromTraceInfo *traceinfo.TraceInfo, toTraceInfo *traceinfo.TraceInfo) error {
	err := copier.Copy(&toTraceInfo, &fromTraceInfo)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("Failed to clone traceInfo", logkit.Any("err", err))
		return err
	}

	// 上面拷贝完，底层的还是共用一份，所以需要重新初始化一下
	toTraceInfo.RecallsStoreHitAbtest = make([]string, 0)
	toTraceInfo.RecallsStoreNotHitAbtest = make([]string, 0)
	toTraceInfo.RecallsStoreFinal = make([]string, 0)
	toTraceInfo.RecallsDishHitAbtest = make([]string, 0)
	toTraceInfo.RecallsDishNotHitAbtest = make([]string, 0)
	toTraceInfo.RecallsDishFinal = make([]string, 0)
	toTraceInfo.RecallsDishConditionOkButGiveUp = make([]string, 0)
	toTraceInfo.RecallConfigurationDataParams = make(map[string]interface{})
	toTraceInfo.RecallConfigurationDataSource = make(map[string]traceinfo.DataSourceItem)
	toTraceInfo.PhraseTimeCost = make(map[traceinfo.CostPhrase]time.Duration)
	toTraceInfo.PhraseStoreLength = make(map[string]int)
	toTraceInfo.PhraseStoreInfos = make(map[string][]string)
	toTraceInfo.PhraseStoreESDsl = make(map[string]string)
	toTraceInfo.PhraseDishESDsl = make(map[string]string)
	toTraceInfo.FilteredStores = make(map[string][]*traceinfo.FilteredStore)
	return nil
}

// 请注意 Clone后，需要重新初始化数据源
func TraceInfoReInitDataSource(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseInitDataSource, time.Since(pt))
	}()
	// 数据源初始化
	traceInfo.RecallConfigurationDataSource = predata.InitDataSourceMap(ctx, traceInfo)
	traceInfo.RecallConfigurationDataParams = predata.GetDataSourceParams(traceInfo.RecallConfigurationDataSource)
	return
}
