package preprocess

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/peakmode"
)

func PeakModeDistance(ctx context.Context, traceInfo *traceinfo.TraceInfo) uint32 {
	if traceInfo.TraceRequest.CityId <= 0 {
		logkit.FromContext(ctx).Info("peakmode distance: invalid city id, skip")
		return 0
	}
	if traceInfo.TraceRequest.Longitude == 0 && traceInfo.TraceRequest.Latitude == 0 {
		logkit.FromContext(ctx).Info("peakmode distance: invalid lon and lat, skip")
		return 0
	}
	deliveryDis := peakmode.GetDeliveryDistance(ctx, traceInfo, traceInfo.TraceRequest.Longitude, traceInfo.TraceRequest.Latitude, int64(traceInfo.TraceRequest.CityId))
	return deliveryDis
}
