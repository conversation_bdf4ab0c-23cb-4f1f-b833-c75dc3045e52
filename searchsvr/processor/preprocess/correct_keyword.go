package preprocess

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func CorrectKeyword(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhrasePreProcessCorrect, time.Since(pt))
	}()
	if traceInfo.IsDebug && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipCorrection() {
		logkit.FromContext(ctx).Info("skip correction")
		traceInfo.IsNeedCorrect = false
	}
	traceInfo.OriginKeyword = traceInfo.QueryKeyword
	if traceInfo.IsNeedCorrect {
		qpResult, _, err := qp.QPServiceClient.GetQPResult(ctx, traceInfo.QueryKeyword, true, traceInfo, traceInfo.ABTestGroup.GetABTestString())
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).WithError(err).Error("failed to get qp result")
		}
		if qpResult == nil {
			logkit.FromContext(ctx).Info("qp result is nil")
		}
		traceInfo.CorrectQPResult = qpResult

		if qpResult != nil && qpResult.IsQPCorrect {
			traceInfo.CorrectKeyword = qpResult.CorrectWord
			//traceInfo.IsCorrected = true
			traceInfo.QueryKeyword = traceInfo.CorrectKeyword
		}
	}
	if traceInfo.CorrectQPResult != nil && traceInfo.CorrectQPResult.IsQPCorrect {
		traceInfo.IsCorrected = true
	}
}
