package rank

import (
	"context"
	"errors"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type RelRanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewRelRanker() *RelRanker {
	return &RelRanker{}
}

func (ranker *RelRanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ranker.contextFea = ctxFea
	ranker.itemFea = itemFea
	ranker.itemIds = itemIds
}

// stores 仅处理仅开门状态的店铺。  其他店铺需要赋默认值给 store.RelevanceScore = store.GetRelevanceScore(1)
func (ranker *RelRanker) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string, bPredict bool) error {
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  ranker.itemIds,
		CtxFea:   ranker.contextFea,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{modelName + "@rough_predict"},
		ItemFeas: ranker.itemFea,
	}

	// 检查是否为diff流量
	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, predictReq, "rel", false)
		if mockScores != nil && len(mockScores) == nStores {
			for i, s := range stores {
				s.PRelevanceScore = mockScores[i]
				s.IsMockPRelScore = 1
			}
		}
		return nil
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictRel, time.Since(pt))
	}()
	resp, err := mlplatform.PredictV2(ctx, predictReq, modelName, traceInfo.IsDebug)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		logkit.FromContext(ctx).Error("failed to predict rel", logkit.Any("err", err), logkit.String("modelName", modelName))
		return err
	}
	modScores := resp.GetModScoresInfo()[modelName]
	if len(modScores.GetScores()) == 0 {
		metric_reporter.ReportClientRequestError(1, "predict-rel", "len")
		logkit.FromContext(ctx).Error("failed to get rel score", logkit.Int("scoresLen", len(modScores.GetScores())), logkit.Int("storesLen", len(stores)))
		return errors.New("predict rel scores empty")
	}
	// 默认分数兜底
	defaultPRelScore := traceInfo.AbParamClient.GetParamWithFloat("Search.MultiFactor.DefaultPredictRelevanceScore", 0.01)
	for _, store := range stores {
		store.PRelevanceScore = defaultPRelScore
	}
	scoreList := modScores.GetScores()["p_relevance"]
	if scoreList == nil {
		logkit.FromContext(ctx).Error("failed to get rel score, score list empty", logkit.Any("modScores", modScores))
		return nil
	}
	scores := scoreList.GetScores()
	if len(scores) != len(stores) {
		logkit.FromContext(ctx).Error("failed to get rel score, scores length diff", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", len(stores)))
		return nil
	}
	for i, store := range stores {
		if scores[i] != 0.0 {
			store.PRelevanceScore = scores[i] // 模型可能返回0，用默认值
		}
	}
	return nil
}
func (ranker *RelRanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeRelevance) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradeRelevance")
		metric_reporter.ReportClientRequestError(1, "predict_rel_downgrade", "true")
		return nil
	}
	if cid.IsID() && traceInfo.PredictConfig.NewStrategy != 4 {
		ProcessWithRelevanceServer(ctx, traceInfo, stores)
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_rel_downgrade", "0")
	if traceInfo.PredictConfig.NewStrategy == 4 && len(traceInfo.RelModelInfo.ModelName) > 0 {
		err := ranker.doPredict(ctx, stores, traceInfo, nStores, traceInfo.RelModelInfo.ModelName, traceInfo.RelModelInfo.IsNeedPredict)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			return err
		}
	}
	return nil
}
