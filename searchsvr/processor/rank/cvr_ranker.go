package rank

import (
	"context"
	"errors"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type CvrRanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewCvrRanker() *CvrRanker {
	ranker := &CvrRanker{}
	return ranker
}

func (ranker *CvrRanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ranker.contextFea = ctxFea
	ranker.itemFea = itemFea
	ranker.itemIds = itemIds
}

func (ranker *CvrRanker) doPredict(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, nStores int, modelName string) error {
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  ranker.itemIds,
		CtxFea:   ranker.contextFea,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{modelName},
		UserFea:  nil,
		ItemFeas: ranker.itemFea,
	}

	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, predictReq, "cvr", false)
		if mockScores != nil && len(mockScores) == nStores {
			for i, s := range stores {
				s.PCvrScore = mockScores[i]
				s.IsMockPCvrScore = 1
			}
		}
		traceInfo.CvrModelInfo.IsPredictSuccess = true
		traceInfo.PredictConfig.CvrModuleName = modelName
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	defer func() {
		metric_reporter.ReportClientRequestError(1, "predict-cvr-"+modelName, errCode)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictCvr, time.Since(startTime))
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		}
	}()
	resp, _, err := mlplatform.Predict(ctx, predictReq, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-cvr-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-cvr-"+modelName)
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:cvr predict response", logkit.Any("response", resp))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict cvr", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", modelName))
		return err
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict cvr", logkit.Any("rsp is ", "nil"))
		return errors.New("predict cvr resp is nil")
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict cvr", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	scores := resp.GetModScores()["CVR"].GetScores()
	if len(scores) != nStores {
		metric_reporter.ReportClientRequestError(1, "predict-cvr", "len")
		logkit.FromContext(ctx).Error("failed to get cvr score", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", nStores))
		return errors.New("predict cvr scoresLen diff")
	}
	errCode = "0"

	if len(modelName) == 0 {
		modelName = "CVR"
	}
	// model只会有一个
	if len(resp.GetModelInfo()) > 0 {
		traceInfo.CvrModelInfo.RspModelInfo = resp.ModelInfo[0]
	}
	for i := 0; i < nStores; i++ {
		stores[i].PCvrScore = scores[i]
	}
	traceInfo.CvrModelInfo.IsPredictSuccess = true
	return nil
}

func (ranker *CvrRanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictCvr) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictCvr")
		metric_reporter.ReportClientRequestError(1, "predict_cvr_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_cvr_downgrade", "0")
	// 短期方案，后续单独优化
	if traceInfo.PredictConfig.IsCtrCvrUnified {
		logger.MyDebug(ctx, traceInfo.IsDebug, "CtrCvrUnified, skip cvr predict")
		return nil
	}
	if traceInfo.CvrModelInfo.IsNeedPredict {
		logger.MyDebug(ctx, traceInfo.IsDebug, "cvr model ", logkit.Any("name", traceInfo.CvrModelInfo.ModelName), logkit.Any("is predict", traceInfo.CvrModelInfo.IsNeedPredict))
		if len(traceInfo.CvrModelInfo.ModelName) > 0 {
			err := ranker.doPredict(ctx, traceInfo, stores, nStores, traceInfo.CvrModelInfo.ModelName)
			if err != nil {
				traceInfo.AddErrorToTraceInfo(err)
				return err
			}
		}
	}
	return nil
}
