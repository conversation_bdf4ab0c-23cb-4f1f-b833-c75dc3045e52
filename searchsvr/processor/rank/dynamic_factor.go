package rank

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type DynamicFactor struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewDynamicFactor() *DynamicFactor {
	df := &DynamicFactor{}
	return df
}

func (df *DynamicFactor) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	df.contextFea = ctxFea
	df.itemFea = itemFea
	df.itemIds = itemIds
}

func (df *DynamicFactor) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string) error {
	req := &predictor.PredictReq{
		Uid: traceInfo.UserId,
		//Itemids: df.itemIds,
		CtxFea: df.contextFea,
		Reqid:  traceInfo.TraceRequest.PublishId,
		Models: []string{modelName},
	}
	if df.itemIds != nil {
		req.Itemids = df.itemIds
	}
	if df.itemFea != nil {
		req.ItemFeas = df.itemFea
	}

	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, req, "DyFactor", false)
		if mockScores != nil && len(mockScores) == nStores {
			nList := len(mockScores)
			traceInfo.PredictConfig.DyFactorList = make([]float64, nList)
			for i := 0; i < nList; i++ {
				traceInfo.PredictConfig.DyFactorList[i] = mockScores[i]
			}
		}
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	name := "predict-df-" + modelName
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictDF, time.Since(startTime))
		metric_reporter.ReportClientRequestError(1, name, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RERANK_ERROR)
		}
	}()
	resp, _, err := mlplatform.Predict(ctx, req, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", name)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", traceInfo.PredictConfig.CvrModuleName))
		return err
	}
	if resp == nil || resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict dynamic factor", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		rspStr, _ := json.Marshal(resp)
		logkit.FromContext(ctx).Info("doPredict", logkit.String("modelName", modelName), logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
	}

	errCode = "0"
	scoreInfo := resp.GetModScoresInfo()[modelName]
	var scores *predictor.ScoreList
	if scoreInfo != nil {
		scores = scoreInfo.Scores["ltr"]
		if scores != nil && scores.Scores != nil {
			nList := len(scores.Scores)
			traceInfo.PredictConfig.DyFactorList = make([]float64, nList)
			for i := 0; i < nList; i++ {
				traceInfo.PredictConfig.DyFactorList[i] = scores.Scores[i]
			}
		}
	}
	if len(resp.GetModelInfo()) > 0 {
		traceInfo.DFModelInfo.RspModelInfo = resp.ModelInfo[0]
	}
	return nil
}
func (df *DynamicFactor) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictDF) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictDF")
		metric_reporter.ReportClientRequestError(1, "predict_df_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_df_downgrade", "0")
	if len(traceInfo.DFModelInfo.ModelName) > 0 {
		err := df.doPredict(ctx, stores, traceInfo, nStores, traceInfo.DFModelInfo.ModelName)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
		}
	}
	return nil
}
