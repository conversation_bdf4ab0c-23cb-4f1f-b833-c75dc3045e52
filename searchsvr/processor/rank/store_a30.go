package rank

import (
	"context"
	"encoding/json"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func isStoreA30ReRank(config *apollo.StoreA30ReRankConfig, storeInfo *model.StoreInfo) bool {
	// 判断规则：
	// store_imp_180d >= A offline	门店过去180d 曝光量
	// store_cnt_180d >= B offline	门店过去180d 订单量
	// store_ctcvr_180d >= C offline	门店过去180d ctcvr
	// distance <= M online	距离	引擎在线计算

	if storeInfo.IsTargetA30 {
		return false
	}

	if storeInfo.IsMeetA30QualityConstraint == false {
		return false
	}

	if storeInfo.StoreImpCnt180d < config.StoreImp180d {
		return false
	}

	if storeInfo.StoreOrderCnt180d < config.StoreOrderCnt180d {
		return false
	}

	if storeInfo.StoreCtCvr180d < config.StoreCtCvr180d {
		return false
	}

	if storeInfo.Distance > config.Distance {
		return false
	}

	return true
}

func StoreA30ReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, config *apollo.StoreA30ReRankConfig, storeInfos []*model.StoreInfo) []*model.StoreInfo {
	if config == nil {
		logkit.FromContext(ctx).Error("StoreA30ReRank failed with empty config")
		return storeInfos
	}
	if config.DivBoost == 0 {
		logkit.FromContext(ctx).Error("StoreA30ReRank failed with invalid DivBoost == 0")
		return storeInfos
	}
	if traceInfo.IsDebug {
		configStr, _ := json.Marshal(config)
		logkit.FromContext(ctx).Debug("StoreA30ReRank config", logkit.String("config", string(configStr)))
	}

	// 记录重排前位置
	for i, s := range storeInfos {
		s.StoreA30ReRankBeforePos = i
		s.StoreA30ReRankAfterPos = i
		s.ReRankScoreBeforeA30 = s.ReRankScore
	}
	if len(storeInfos) <= config.TopPos {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("StoreA30ReRank size < topPos, no need reRank", logkit.Int("size", len(storeInfos)), logkit.Int("topPos", config.TopPos))
		} else {
			logkit.FromContext(ctx).Debug("StoreA30ReRank size < topPos, no need reRank", logkit.Int("size", len(storeInfos)), logkit.Int("topPos", config.TopPos))
		}
		return storeInfos
	}

	// 抓取前N名的门店列表保持不动
	topStores := storeInfos[:config.TopPos]

	// 对剩余门店进行排序
	remainingStores := storeInfos[config.TopPos:]

	// 重新计算分数
	for _, s := range remainingStores {
		reRank := isStoreA30ReRank(config, s)
		if reRank {
			s.ReRankScore = s.ReRankScore / config.DivBoost
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("StoreA30ReRank hit rerank", logkit.Uint64("storeId", s.StoreId), logkit.Bool("IsTargetA30", s.IsTargetA30), logkit.Bool("IsMeetA30QualityConstraint", s.IsMeetA30QualityConstraint), logkit.Int64("StoreImpCnt180d", s.StoreImpCnt180d), logkit.Int64("StoreOrderCnt180d", s.StoreOrderCnt180d), logkit.Float64("StoreCtCvr180d", s.StoreCtCvr180d))
			} else {
				logkit.FromContext(ctx).Debug("StoreA30ReRank hit rerank", logkit.Uint64("storeId", s.StoreId), logkit.Bool("IsTargetA30", s.IsTargetA30), logkit.Bool("IsMeetA30QualityConstraint", s.IsMeetA30QualityConstraint), logkit.Int64("StoreImpCnt180d", s.StoreImpCnt180d), logkit.Int64("StoreOrderCnt180d", s.StoreOrderCnt180d), logkit.Float64("StoreCtCvr180d", s.StoreCtCvr180d))
			}
		}
	}

	// 重排
	remainingStores = StoreRank(ctx, traceInfo, remainingStores)
	afterReRankList := append(topStores, remainingStores...)

	// 记录重排后位置
	for i, s := range afterReRankList {
		s.StoreA30ReRankAfterPos = i
	}

	return afterReRankList
}
