package rank

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	relevance "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_relevance_server"
	"github.com/golang/protobuf/proto"
	"hash/fnv"

	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type AbstractRanker interface {
	doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error
	setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64)
}

const (
	CtrPredictor   = 0
	CvrPredictor   = 1
	UEPredictor    = 2
	RelPredictor   = 3
	DFPredictor    = 4
	TotalPredictor = 5
)

const (
	LtrPredictor = 5
)

// 根据索引获取预测器名称
func getPredictorName(index int) string {
	switch index {
	case CtrPredictor:
		return "CTR"
	case CvrPredictor:
		return "CVR"
	case UEPredictor:
		return "UE"
	case RelPredictor:
		return "Relevance"
	case DFPredictor:
		return "DynamicFactor"
	case LtrPredictor:
		return "LTR"
	default:
		return "Unknown"
	}
}

func mockPredictScoreWhenDiff(ctx context.Context, req *predictor.PredictReq, predictName string, isNeedConsiderOrder bool) []float64 {
	if req == nil {
		return nil
	}
	if len(req.GetModels()) == 0 {
		return nil
	}
	if len(req.GetItemids()) == 0 {
		return nil
	}

	tempReqId := req.Reqid
	tempItemIds := req.GetItemids()
	tempItemFeatures := req.GetItemFeas()

	logkit.FromContext(ctx).Info("mockPredictScoreWhenDiff", logkit.String("predictName", predictName), logkit.Uint64s("storeIds", tempItemIds))

	req.Reqid = "" // 忽略pub不一样
	req.Itemids = nil
	req.ItemFeas = nil

	reqBytes, _ := json.Marshal(req)
	reqHashValue := hashBytes(reqBytes)

	// 使用哈希值生成确定性的分数
	scores := make([]float64, len(tempItemIds))
	for i := 0; i < len(tempItemIds); i++ {
		itemId := tempItemIds[i]
		itemFeature := tempItemFeatures[i]
		itemFeaHashValue := hashBytes(itemFeature)
		var ss string
		if isNeedConsiderOrder {
			ss = fmt.Sprintf("%d_%s_%d_%s", i, reqHashValue, itemId, itemFeaHashValue)
		} else {
			ss = fmt.Sprintf("%s_%d_%s", reqHashValue, itemId, itemFeaHashValue)
		}
		storeHash := hashStr(ss)
		deterministicScore := generateDeterministicScore(storeHash)
		scores[i] = deterministicScore
	}

	// 还原
	req.Reqid = tempReqId
	req.Itemids = tempItemIds
	req.ItemFeas = tempItemFeatures
	return scores
}

func mockRelevanceScoreWhenDiff(req *relevance.RelevanceRequest) []*relevance.DocItem {
	if req == nil {
		return nil
	}
	if len(req.GetStoreItem()) == 0 {
		return nil
	}

	tempReqId := req.ReqId
	tempStoreItems := req.GetStoreItem()

	req.ReqId = proto.String("") // 忽略pub不一样
	req.StoreItem = nil

	reqBytes, _ := json.Marshal(req)
	reqHashValue := hashBytes(reqBytes)

	// 使用哈希值生成确定性的分数
	scores := make([]*relevance.DocItem, len(tempStoreItems))
	for i := 0; i < len(tempStoreItems); i++ {
		storeItem := tempStoreItems[i]
		storeId := storeItem.GetStoreId()
		storeItemBytes, _ := json.Marshal(storeItem)
		storeItemBytesHash := hashBytes(storeItemBytes)
		ss := fmt.Sprintf("%s_%s", reqHashValue, storeItemBytesHash)
		storeHash := hashStr(ss)
		relevanceScore := generateDeterministicScore(storeHash)
		scores[i] = &relevance.DocItem{
			StoreId:       proto.Uint64(storeId),
			Score:         proto.Float32(float32(relevanceScore)),
			SemanticScore: proto.Float32(float32(relevanceScore)),
			Level:         proto.Int32(int32(storeId % 2)),
		}
	}

	// 还原
	req.ReqId = tempReqId
	req.StoreItem = tempStoreItems
	return scores
}

func hashStr(str string) uint64 {
	h := fnv.New64a()
	h.Write([]byte(str))
	return h.Sum64()
}

// 计算请求的哈希值
func hashBytes(reqBytes []byte) string {
	h := fnv.New64a()
	h.Write(reqBytes)
	return fmt.Sprintf("%x", h.Sum64())
}

// 根据哈希值生成确定性的分数（范围在0-1之间）
func generateDeterministicScore(hash uint64) float64 {
	// 使用哈希值生成0-1之间的分数
	return float64(hash%1000) / 1000.0
}
