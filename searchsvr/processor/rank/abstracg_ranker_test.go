package rank

import (
	"context"
	"encoding/json"
	"testing"

	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"github.com/stretchr/testify/assert"
)

func TestMockPredictScoreWhenDiff(t *testing.T) {
	ctx := context.Background()

	// 测试用例1: 基本功能测试
	t.Run("BasicFunctionality", func(t *testing.T) {
		// 创建一个基本的预测请求
		req := &predictor.PredictReq{
			Uid:     12345,
			Reqid:   "test-req-id",
			Models:  []string{"test-model"},
			Itemids: []uint64{1001, 1002, 1003},
			ItemFeas: [][]byte{
				[]byte("feature1"),
				[]byte("feature2"),
				[]byte("feature3"),
			},
		}

		// 获取模拟分数
		scores := mockPredictScoreWhenDiff(ctx, req, "", false)

		// 验证结果
		assert.NotNil(t, scores, "Scores should not be nil")
		assert.Equal(t, len(req.Itemids), len(scores), "Number of scores should match number of items")

		// 验证所有分数都在0-1范围内
		for i, score := range scores {
			assert.GreaterOrEqual(t, score, 0.0, "Score should be >= 0")
			assert.Less(t, score, 1.0, "Score should be < 1")
			t.Logf("Item %d (ID: %d) got score: %f", i, req.Itemids[i], score)
		}
	})

	// 测试用例2: 相同请求应该产生相同的分数
	t.Run("ConsistentScores", func(t *testing.T) {
		// 创建两个相同的请求
		req1 := &predictor.PredictReq{
			Uid:     67890,
			Reqid:   "test-req-id-2-1",
			Models:  []string{"consistent-model"},
			Itemids: []uint64{2001, 2002},
			ItemFeas: [][]byte{
				[]byte("feature-a"),
				[]byte("feature-b"),
			},
		}

		req2 := &predictor.PredictReq{
			Uid:     67890,
			Reqid:   "test-req-id-2-2",
			Models:  []string{"consistent-model"},
			Itemids: []uint64{2001, 2002},
			ItemFeas: [][]byte{
				[]byte("feature-a"),
				[]byte("feature-b"),
			},
		}

		// 获取两次模拟分数
		scores1 := mockPredictScoreWhenDiff(ctx, req1, "", false)
		scores2 := mockPredictScoreWhenDiff(ctx, req2, "", false)

		// 验证两次结果相同
		assert.Equal(t, scores1, scores2, "Same requests should produce same scores")
	})

	// 测试用例3: 不同请求应该产生不同的分数
	t.Run("DifferentScores", func(t *testing.T) {
		// 创建两个不同的请求
		req1 := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"model-a"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-x"),
				[]byte("feature-y"),
			},
		}

		req2 := &predictor.PredictReq{
			Uid:     67890, // 不同的用户ID
			Models:  []string{"model-a"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-x"),
				[]byte("feature-y"),
			},
		}

		// 获取两次模拟分数
		scores1 := mockPredictScoreWhenDiff(ctx, req1, "", false)
		scores2 := mockPredictScoreWhenDiff(ctx, req2, "", false)

		// 验证两次结果不同
		assert.NotEqual(t, scores1, scores2, "Different requests should produce different scores")
	})

	// 测试用例4: 边界情况测试
	t.Run("EdgeCases", func(t *testing.T) {
		// 测试空请求
		assert.Nil(t, mockPredictScoreWhenDiff(ctx, nil, "", false), "Nil request should return nil scores")

		// 测试没有模型的请求
		reqNoModels := &predictor.PredictReq{
			Uid:     12345,
			Itemids: []uint64{4001, 4002},
		}
		assert.Nil(t, mockPredictScoreWhenDiff(ctx, reqNoModels, "", false), "Request with no models should return nil scores")

		// 测试没有商品ID的请求
		reqNoItems := &predictor.PredictReq{
			Uid:    12345,
			Models: []string{"test-model"},
		}
		assert.Nil(t, mockPredictScoreWhenDiff(ctx, reqNoItems, "", false), "Request with no items should return nil scores")
	})

	// 测试用例5: 验证reqId不影响结果
	t.Run("ReqIdIgnored", func(t *testing.T) {
		// 创建两个只有reqId不同的请求
		req1 := &predictor.PredictReq{
			Uid:     12345,
			Reqid:   "req-id-1",
			Models:  []string{"test-model"},
			Itemids: []uint64{5001, 5002},
			ItemFeas: [][]byte{
				[]byte("feature-1"),
				[]byte("feature-2"),
			},
		}

		req2 := &predictor.PredictReq{
			Uid:     12345,
			Reqid:   "req-id-2", // 不同的reqId
			Models:  []string{"test-model"},
			Itemids: []uint64{5001, 5002},
			ItemFeas: [][]byte{
				[]byte("feature-1"),
				[]byte("feature-2"),
			},
		}

		// 获取两次模拟分数
		scores1 := mockPredictScoreWhenDiff(ctx, req1, "", false)
		scores2 := mockPredictScoreWhenDiff(ctx, req2, "", false)

		// 验证两次结果相同（因为reqId被忽略）
		assert.Equal(t, scores1, scores2, "Different reqIds should not affect scores")
	})

	// 测试用例6: 验证JSON序列化和哈希的正确性
	t.Run("JsonAndHashCorrectness", func(t *testing.T) {
		req := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"test-model"},
			Itemids: []uint64{6001, 6002},
			ItemFeas: [][]byte{
				[]byte("feature-1"),
				[]byte("feature-2"),
			},
		}

		// 手动计算哈希
		reqCopy := *req
		reqCopy.Reqid = ""
		reqCopy.Itemids = nil
		reqCopy.ItemFeas = nil

		reqBytes, err := json.Marshal(reqCopy)
		assert.NoError(t, err, "JSON marshaling should not fail")

		manualHash := hashBytes(reqBytes)

		// 获取模拟分数
		scores := mockPredictScoreWhenDiff(ctx, req, "", false)

		// 验证结果
		assert.NotNil(t, scores, "Scores should not be nil")
		assert.Equal(t, len(req.Itemids), len(scores), "Number of scores should match number of items")

		// 记录哈希值和分数，用于调试
		t.Logf("Request hash: %s", manualHash)
		for i, score := range scores {
			t.Logf("Item %d (ID: %d) got score: %f", i, req.Itemids[i], score)
		}
	})

	// 测试用例7: 不同请求应该产生不同的分数
	t.Run("DifferentScores", func(t *testing.T) {
		// 创建两个不同的请求
		req1 := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"model-a"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-x"),
				[]byte("feature-y"),
			},
		}

		req2 := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"model-b"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-x"),
				[]byte("feature-y"),
			},
		}

		// 获取两次模拟分数
		scores1 := mockPredictScoreWhenDiff(ctx, req1, "", false)
		scores2 := mockPredictScoreWhenDiff(ctx, req2, "", false)

		// 验证两次结果不同
		assert.NotEqual(t, scores1, scores2, "Different requests should produce different scores")
	})

	// 测试用例8: 不同请求应该产生不同的分数
	t.Run("DifferentScores", func(t *testing.T) {
		// 创建两个不同的请求
		req1 := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"model-a"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-x"),
				[]byte("feature-y"),
			},
		}

		req2 := &predictor.PredictReq{
			Uid:     12345,
			Models:  []string{"model-a"},
			Itemids: []uint64{3001, 3002},
			ItemFeas: [][]byte{
				[]byte("feature-xx"),
				[]byte("feature-yy"),
			},
		}

		// 获取两次模拟分数
		scores1 := mockPredictScoreWhenDiff(ctx, req1, "", false)
		scores2 := mockPredictScoreWhenDiff(ctx, req2, "", false)

		// 验证两次结果不同
		assert.NotEqual(t, scores1, scores2, "Different requests should produce different scores")
	})
}
