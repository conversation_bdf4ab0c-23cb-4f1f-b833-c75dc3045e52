package rank

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/Knetic/govaluate"
)

// 门店下菜品排序
func StoreDishRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	// 少无结果
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
		return sortForFewResult(ctx, traceInfo, stores)
	}
	if !decision.IsNeedDishListingRank(traceInfo) {
		return stores
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingRank, time.Since(pt))
	}()
	expName := abtest.GetDishListingRankExpressionName(traceInfo.AbParamClient)
	// 公共权重
	parameters := util.BuildExpParameters(abtest.GetDishListingRankWeights(traceInfo.AbParamClient))
	//w1, w11, w12, w13, w2, w21, w22, w3 := buildStatic1WeightParams(parameters)
	w1, w2, w3, w4, w5, w6 := buildStatic1WeightParamsStatic2(parameters)
	expStr := abtest.GetDishListingRankExpression(traceInfo.AbParamClient)
	expression := util.BuildExpression(ctx, expStr)
	if expression == nil {
		logkit.FromContext(ctx).Error("StoreDishRank BuildExpression failed", logkit.String("expression", expStr))
		return stores
	}

	batchSize := traceInfo.AbParamClient.GetParamWithInt("Search.StoreDishRank.RankBatch", 20)
	storesLen := len(stores)
	totalBatch := (storesLen + batchSize - 1) / batchSize
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "calculateSimilarity", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > storesLen {
				end = storesLen
			}
			for index := begin; index < end; index++ {
				switch expName {
				//case constants.DishFormulaStatic1:
				//	stores[index].DishInfos = dishesCalScoresStatic1(ctx, traceInfo, stores[index].DishInfos, w1, w2, w3, w4, w5, w6)
				case constants.DishFormulaStatic2:
					stores[index].DishInfos = dishesCalScoresStatic2(ctx, traceInfo, stores[index].DishInfos, w1, w2, w3, w4, w5, w6)
				default:
					stores[index].DishInfos = dishesCalScoresFormula(ctx, traceInfo, stores[index].DishInfos, expression, parameters)
				}
			}
		}, i)
	}
	wg.Wait()

	// 增加排序后的菜品信息
	if traceInfo.IsDebug {
		for _, s := range stores {
			s.DishInfosAfterRank = s.DishInfos.DeepClone()
		}
	}
	return stores
}

func dishesCalScoresFormula(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos, expression *govaluate.EvaluableExpression, parameters map[string]interface{}) model.DishInfos {
	for _, dish := range dishes {
		if dish == nil {
			continue
		}
		if dish.DishFeature == nil {
			// 如果没有特征，打分置 0，并记录警告
			logkit.FromContext(ctx).Debug("dishCalAndRank No feature found for dishId", logkit.Uint64("dishId", dish.DishId))
			dish.Score = 0
			continue
		}
		//parameters := util.CloneParamsMap(params) // 这里明确param 所有字段都会被赋值，所以不用重新创建
		// 菜品特征
		parameters["dish_impr_to_atc_rate_30d"] = dish.DishFeature.GetDishCtr_30D()
		parameters["dish_atc_to_order_rate_30d"] = dish.DishFeature.GetDishCvr_30D()
		parameters["sales_volume_7d_score"] = dish.DishFeature.GetSalesRank_7DScore()
		parameters["rating_score"] = dish.DishFeature.RatingScore
		parameters["uv_repurchase_rate_30d_score"] = dish.DishFeature.GetUvRepurchaseScore()
		parameters["is_flash_sale"] = dish.GetIsFlashSale()
		dish.Score, _ = util.EvaluateScore(ctx, expression, parameters)
	}
	dishes.SortByPriorityAndScore()
	return dishes
}

func buildStatic1WeightParamsStatic1(params map[string]interface{}) (float64, float64, float64, float64, float64, float64, float64, float64) {
	w1, w11, w12, w13, w2, w21, w22, w3 := 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
	if v, ok := params["w1"].(float64); ok {
		w1 = v
	}
	if v, ok := params["w11"].(float64); ok {
		w11 = v
	}
	if v, ok := params["w12"].(float64); ok {
		w12 = v
	}
	if v, ok := params["w13"].(float64); ok {
		w13 = v
	}
	if v, ok := params["w2"].(float64); ok {
		w2 = v
	}
	if v, ok := params["w21"].(float64); ok {
		w21 = v
	}
	if v, ok := params["w22"].(float64); ok {
		w22 = v
	}
	if v, ok := params["w3"].(float64); ok {
		w3 = v
	}
	return w1, w11, w12, w13, w2, w21, w22, w3
}

func buildStatic1WeightParamsStatic2(params map[string]interface{}) (float64, float64, float64, float64, float64, float64) {
	w1, w2, w3, w4, w5, w6 := 1.0, 1.0, 1.0, 1.0, 1.0, 1.0
	if v, ok := params["w1"].(float64); ok {
		w1 = v
	}
	if v, ok := params["w2"].(float64); ok {
		w2 = v
	}
	if v, ok := params["w3"].(float64); ok {
		w3 = v
	}
	if v, ok := params["w4"].(float64); ok {
		w4 = v
	}
	if v, ok := params["w5"].(float64); ok {
		w5 = v
	}
	if v, ok := params["w6"].(float64); ok {
		w6 = v
	}
	return w1, w2, w3, w4, w5, w6
}

// "w1 * (w11 *  dish_impr_to_atc_rate_30d + w12 * dish_atc_to_order_rate_30d + w13 * sales_volume_7d_score) + w2 * ( w21 * rating_score + w22 * uv_repurchase_rate_30d_score)+ w3 * is_flash_sale"
func dishesCalScoresStatic1(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos, w1, w11, w12, w13, w2, w21, w22, w3 float64) model.DishInfos {
	for _, dish := range dishes {
		if dish == nil {
			continue
		}
		if dish.DishFeature == nil {
			// 如果没有特征，打分置 0，并记录警告
			logkit.FromContext(ctx).Debug("dishCalAndRank No feature found for dishId", logkit.Uint64("dishId", dish.DishId))
			dish.Score = 0
			continue
		}
		dish.Score = w1*(w11*float64(dish.DishFeature.GetDishCtr_30D())+
			w12*float64(dish.DishFeature.GetDishCvr_30D())+
			w13*float64(dish.DishFeature.GetSalesRank_7DScore())) +
			w2*(w21*float64(dish.DishFeature.GetRatingScore())+w22*float64(dish.DishFeature.GetUvRepurchaseScore())) +
			w3*float64(dish.GetIsFlashSale())
	}
	dishes.SortByPriorityAndScore()
	return dishes
}

// w1 * sales_volume_7d_score + w2 * rating_score + w3*uv_repurchase_rate_30d_score + w4 *  dish_impr_to_atc_rate_30d + w5 * dish_atc_to_order_rate_30d + w6 * is_flash_sale
func dishesCalScoresStatic2(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos, w1, w2, w3, w4, w5, w6 float64) model.DishInfos {
	for _, dish := range dishes {
		if dish == nil {
			continue
		}
		if dish.DishFeature == nil {
			// 如果没有特征，打分置 0，并记录警告
			logkit.FromContext(ctx).Debug("dishCalAndRank No feature found for dishId", logkit.Uint64("dishId", dish.DishId))
			dish.Score = 0
			continue
		}

		dish.Score = w1*float64(dish.DishFeature.GetSalesRank_7DScore()) +
			w2*float64(dish.DishFeature.GetRatingScore()) +
			w3*float64(dish.DishFeature.GetUvRepurchaseScore()) +
			w4*float64(dish.DishFeature.GetDishCtr_30D()) +
			w5*float64(dish.DishFeature.GetDishCvr_30D()) +
			w6*float64(dish.GetIsFlashSale())
	}
	dishes.SortByPriorityAndScore()
	return dishes
}

func sortForFewResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	batchSize := traceInfo.AbParamClient.GetParamWithInt("Search.StoreDishRank.RankBatchForFewResult", 100)
	storesLen := len(stores)
	totalBatch := (storesLen + batchSize - 1) / batchSize
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "sortForFewResult", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > storesLen {
				end = storesLen
			}
			for index := begin; index < end; index++ {
				stores[index].DishInfos.SortByPriorityAndId()
			}
		}, i)
	}
	wg.Wait()
	return stores
}
