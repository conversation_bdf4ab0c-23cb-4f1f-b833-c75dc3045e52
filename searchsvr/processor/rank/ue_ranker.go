package rank

import (
	"context"
	"errors"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	predictor "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/predictor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type UERanker struct {
	contextFea []byte
	itemFea    [][]byte
	itemIds    []uint64
}

func NewUERanker() *UERanker {
	ranker := &UERanker{}
	return ranker
}

func (ranker *UERanker) setFeatures(ctxFea []byte, itemFea [][]byte, itemIds []uint64) {
	ranker.contextFea = ctxFea
	ranker.itemFea = itemFea
	ranker.itemIds = itemIds
}

func (ranker *UERanker) doPredict(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int, modelName string) error {
	predictReq := &predictor.PredictReq{
		Uid:      traceInfo.UserId,
		Itemids:  ranker.itemIds,
		CtxFea:   ranker.contextFea,
		Reqid:    traceInfo.TraceRequest.PublishId,
		Models:   []string{modelName},
		UserFea:  nil,
		ItemFeas: ranker.itemFea,
	}

	// 检查是否为diff流量
	if decision.IsMockModelScore(ctx, traceInfo) {
		mockScores := mockPredictScoreWhenDiff(ctx, predictReq, "ue", false)
		if mockScores != nil && len(mockScores) == nStores {
			for i, s := range stores {
				s.PUEScore = mockScores[i]
				s.IsMockPUeScore = 1
			}
		}
		traceInfo.UEModelInfo.IsPredictSuccess = true
		return nil
	}

	startTime := time.Now()
	errCode := "failed"
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFusionRankPredictUE, time.Since(startTime))
		metric_reporter.ReportClientRequestError(1, "predict-ue-"+modelName, errCode)
		if errCode == "failed" {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.RANK_ERROR)
		}
	}()
	resp, _, err := mlplatform.Predict(ctx, predictReq, traceInfo.IsDebug)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "predict-ue-"+modelName), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "predict-ue-"+modelName)
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:ue predict response", logkit.Any("request", predictReq), logkit.Any("response", resp))
	}
	if err != nil {
		logkit.FromContext(ctx).Error("failed to predict ue", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err), logkit.Any("module_name", modelName))
		return err
	}
	if resp == nil {
		logkit.FromContext(ctx).Error("failed to predict ue", logkit.Any("rsp is ", "nil"))
		return errors.New("predict ue resp is nil")
	}
	if resp.GetErrcode() != 0 {
		logkit.FromContext(ctx).Error("failed to predict ue", logkit.Any("errCode", resp.GetErrcode()), logkit.Any("errReason", resp.GetErrdesc()))
		return errors.New(resp.GetErrdesc())
	}
	scores := resp.GetModScores()["CTR"].GetScores()
	if len(scores) != nStores {
		metric_reporter.ReportClientRequestError(1, "predict-ue", "len")
		logkit.FromContext(ctx).Error("failed to get ue score", logkit.Int("scoresLen", len(scores)), logkit.Int("storesLen", nStores))
		return errors.New("predict ue scoresLen diff")
	}
	// model只会有一个
	if len(resp.GetModelInfo()) > 0 {
		traceInfo.UEModelInfo.RspModelInfo = resp.ModelInfo[0]
	}
	for i := 0; i < nStores; i++ {
		stores[i].PUEScore = scores[i]
	}
	errCode = "0"
	traceInfo.UEModelInfo.IsPredictSuccess = true
	return nil
}

func (ranker *UERanker) doRank(ctx context.Context, stores model.StoreInfos, traceInfo *traceinfo.TraceInfo, nStores int) error {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradePredictUE) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "ranker doRank downgrade DowngradePredictUE")
		metric_reporter.ReportClientRequestError(1, "predict_ue_downgrade", "true")
		return nil
	}
	metric_reporter.ReportClientRequestError(1, "predict_ue_downgrade", "0")
	if traceInfo.UEModelInfo.IsNeedPredict && len(traceInfo.UEModelInfo.ModelName) > 0 {
		err := ranker.doPredict(ctx, stores, traceInfo, nStores, traceInfo.UEModelInfo.ModelName)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			return err
		}
	}
	return nil
}
