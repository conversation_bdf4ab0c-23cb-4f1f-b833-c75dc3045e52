package processor

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"github.com/golang/protobuf/proto"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
)

func SearchDishesPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]*foodalgo_search.SearchDishesResp_Store, error) {
	pt := time.Now()
	var err error
	var dishInfos model.DishInfos
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseSearchDishesPipeline, time.Since(pt))
		debugInfo.FillProcessInfo(traceInfo)
		debugInfo.FillDishes(traceInfo, dishInfos)
	}()
	dishInfos, err = recall.QueryStoreDishFromES(ctx, traceInfo)
	if err != nil {
		return nil, err
	}
	dishInfos, err = filling.DishMetaFilling(ctx, traceInfo, dishInfos)
	if err != nil {
		return nil, err
	}
	dishInfos = filter.MartSearchDishesFilter(ctx, traceInfo, dishInfos, traceInfo.TraceRequest.SearchTime)
	stores := buildStoreDishes(traceInfo.QueryKeyword, traceInfo.TraceRequest.GetFilterType().GetStoreIds(), dishInfos)
	return stores, err
}

func buildStoreDishes(query string, storeIds []uint32, dishInfos model.DishInfos) []*foodalgo_search.SearchDishesResp_Store {
	query = util.ToAscii(vn_char_mapping_job.CharMappingInstance.ReplaceChar(query))
	stores := make([]*foodalgo_search.SearchDishesResp_Store, 0, len(storeIds))
	dishMap := dishInfos.StoreDishInfosMap()
	for _, id := range storeIds {
		store := &foodalgo_search.SearchDishesResp_Store{
			StoreId: proto.Uint64(uint64(id)),
			Dishes:  []*foodalgo_search.SearchDishesResp_Dish{},
		}
		sds := dishMap[uint64(id)]
		sds = sds.SortByStatusSales() // 排序
		for _, sd := range sds {
			dish := &foodalgo_search.SearchDishesResp_Dish{
				DishId:       proto.Uint64(sd.DishId),
				HighlightPos: BuildHighlight(query, sd.DishName),
			}
			store.Dishes = append(store.Dishes, dish)
		}
		stores = append(stores, store)
	}
	return stores
}

func BuildHighlight(query, dishName string) []*foodalgo_search.HighlightPos {
	dishName = util.ToAscii(vn_char_mapping_job.CharMappingInstance.ReplaceChar(dishName))
	indexes := util.HighlightMatch(query, dishName)
	pos := make([]*foodalgo_search.HighlightPos, 0, len(indexes))
	if len(indexes) > 0 {
		for _, index := range indexes {
			if len(index) == 2 {
				pos = append(pos, &foodalgo_search.HighlightPos{
					StartPos: proto.Int32(int32(index[0])),
					EndPos:   proto.Int32(int32(index[1])),
				})
			}
		}
	} else {
		pos = append(pos, &foodalgo_search.HighlightPos{
			StartPos: proto.Int32(0),
			EndPos:   proto.Int32(0),
		})
	}
	return pos
}

func SearchL3CategoryPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]uint32, error) {
	pt := time.Now()
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseSearchL3CategoryPipeline, time.Since(pt))
	}()
	categories, err := recall.QueryL3CategoriesFromES(ctx, traceInfo)
	return categories, err
}
