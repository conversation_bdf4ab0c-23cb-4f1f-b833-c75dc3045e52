package processor

import (
	"context"
	"errors"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"
)

// 普通搜索流程
func NormalPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.StoreInfos, model.StoreInfos, error) {
	var normalErr, adsErr, err error
	var recallStores, normalStores, adsStores, mixerStores model.StoreInfos
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseNormalPipeline, time.Since(pt))
		postprocess.PostProcessorAckDump(ctx, traceInfo, normalStores, mixerStores)
	}()

	wg := sync.WaitGroup{}
	wg.Add(2)
	goroutine.WithGo(ctx, "SearchInternal", func(params ...interface{}) {
		defer wg.Done()
		recallStores, normalStores, normalErr = SearchInternalPipeline(ctx, traceInfo, debugInfo)
		if normalErr != nil {
			traceInfo.AddErrorToTraceInfo(normalErr)
			logkit.FromContext(ctx).Error("SearchInternal error", logkit.Err(normalErr))
			return
		}
	})
	goroutine.WithGo(ctx, "SearchAds", func(params ...interface{}) {
		defer wg.Done()
		if !decision.IsNeedAds(traceInfo) {
			logkit.Debug("skip ads")
			return
		}
		adsStores, adsErr = SearchAdsPipeline(ctx, traceInfo, debugInfo)
		if adsErr != nil {
			traceInfo.AddErrorToTraceInfo(adsErr)
			logkit.FromContext(ctx).Error("SearchAds error", logkit.Err(adsErr))
			return
		}
		// 广告菜品max size截断
		adsDishSize := abtest.GetDishListingRecallSize(traceInfo.AbParamClient)
		adsStores = filter.AdsDishSizeLimitFilter(ctx, traceInfo, adsStores, adsDishSize)
		// 广告菜品not show截断
		adsStores = filter.AdsDishNotShowSizeLimitFilter(ctx, traceInfo, adsStores)
	})
	wg.Wait()
	// 不返回单纯广告的结果
	if normalErr != nil {
		return nil, nil, errors.New("NormalPipeline failed")
	}
	// vn门店精排(模型排序)， ID/TH/MY在pipeline里面
	normalStores = rank.FusionRankVN(ctx, traceInfo, normalStores, adsStores)

	if len(normalStores) == 0 {
		mixerStores = normalStores
		return recallStores, mixerStores, nil // 不返回单纯广告的结果
	}

	// mixer
	mixerStores, err = merge.NormalAndAdsStoresMerge(ctx, traceInfo, normalStores, adsStores)

	// rerank
	mixerStores = rerank.StoresReRank(ctx, traceInfo, mixerStores)

	if len(mixerStores) == 0 {
		mixerStores = normalStores // mixer异常兜底
	}
	return recallStores, mixerStores, err
}
