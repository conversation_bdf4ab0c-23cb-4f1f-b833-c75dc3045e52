package processor

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	affiliate_client "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/affiliate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"github.com/gogo/protobuf/proto"
)

func SearchStoresAffiliatePipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.StoreInfos, error) {
	pt := time.Now()
	var err error
	var storeInfos model.StoreInfos
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseSearchDishesAffiliatePipeline, time.Since(pt))
		debugInfo.FillProcessInfo(traceInfo)
		debugInfo.FillNormalStores(traceInfo, storeInfos)
	}()
	// 预处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)

	// 使用佣金计划从ES召回门店， (1) text matching recall by both store name + dish name (2) store category relevance recall todo
	storeInfos, err = recall.QueryStoresWithAffiliateFromES(ctx, traceInfo)
	if err != nil {
		return nil, err
	}

	// 门店信息和门店佣金信息填充
	storeInfos, _ = filling.Filling(ctx, traceInfo, storeInfos)

	// 门店过滤
	storeInfos = filter.AffiliateFilter(ctx, storeInfos, traceInfo)

	storeInfos, err = recall.QueryDishesWithStoresWithAffiliateFromES(ctx, traceInfo, storeInfos)

	// 融合分数计算
	storeInfos = rank.CalculateAndRankByRelevance(ctx, traceInfo, storeInfos)

	// 品牌合并，依赖融合分数，并且会改变顺序
	storeInfos = DuplicateBrandFilterForAffiliate(ctx, traceInfo, storeInfos)

	storeInfos = rank.StoresAffiliateRank(ctx, traceInfo, storeInfos)

	return storeInfos, nil
}

func SameBrandCountPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo) []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo {
	brandIds := make([]uint64, 0, len(stores))
	for _, store := range stores {
		if store.GetBrandId() > 0 {
			brandIds = append(brandIds, store.GetBrandId())
		}
	}
	if len(brandIds) == 0 {
		return stores
	}
	// 召回门店id列表
	storeInfos, _ := recall.QuerySameBrandCountAffiliateFromES(ctx, traceInfo, brandIds)
	// 正排填充
	storeInfos = filling.StoreMetaFilling(ctx, traceInfo, storeInfos)
	// 门店过滤
	if cid.IsVN() == false {
		storeInfos = filter.InactiveDistrictFilter(ctx, traceInfo, storeInfos)
	}
	// 门店数量聚合
	brandCount := make(map[uint64]uint32, len(brandIds))
	for _, store := range storeInfos {
		if store.GetBrandId() > 0 {
			brandCount[store.GetBrandId()]++
		}
	}
	for _, store := range stores {
		if count, exists := brandCount[store.GetBrandId()]; exists {
			store.SameBrandCount = proto.Uint32(count)
		}
	}
	return stores
}

func SearchDishesAffiliatePipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.DishInfos, error) {
	pt := time.Now()
	var err error
	var dishInfos model.DishInfos
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseSearchDishesAffiliatePipeline, time.Since(pt))
		debugInfo.FillProcessInfo(traceInfo)
		debugInfo.FillDishesAffiliate(traceInfo, dishInfos)
	}()
	// 查询KOL生效的佣金计划
	storeCommPlans, dishCommPlans, planCommRate := affiliate_client.GetCommissionPlansByAffiliateId(ctx, traceInfo, traceInfo.TraceRequest.AffiliateId)
	if len(storeCommPlans) < 1 && len(dishCommPlans) < 1 {
		logkit.FromContext(ctx).Info("affiliate without any commission plans", logkit.Uint64("affiliateId", traceInfo.TraceRequest.AffiliateId))
		return dishInfos, nil
	}
	traceInfo.StoreCommPlans = storeCommPlans
	traceInfo.DishCommPlans = dishCommPlans
	traceInfo.PlanCommRate = planCommRate
	// 使用佣金计划从ES召回菜品
	dishInfos, err = recall.QueryDishesWithAffiliateFromES(ctx, traceInfo)
	if err != nil {
		return nil, err
	}

	// 菜品信息和菜品佣金信息填充
	dishInfos, err = filling.DishMetaFilling(ctx, traceInfo, dishInfos)
	if err != nil {
		return nil, err
	}
	dishInfos, err = filling.DishAffiliateFilling(ctx, traceInfo, dishInfos)
	dishInfos = filter.DishesWithoutCommissionFilter(ctx, traceInfo, dishInfos)
	// 菜品排序
	dishInfos.SortByDishCommissionRate()

	return dishInfos, err
}

func DuplicateBrandFilterForAffiliate(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}

	resStores := make([]*model.StoreInfo, 0, len(stores))
	brandStoreMap := make(map[uint64]model.StoreInfos)
	// 命中门店意图的brandID集合
	for _, store := range stores {
		brandID := store.GetBrandId()
		if brandID != 0 {
			brandStoreMap[brandID] = append(brandStoreMap[brandID], store)
		} else {
			resStores = append(resStores, store)
		}
	}
	// 品牌店铺取排序最前的第一个
	for _, v := range brandStoreMap {
		if len(v) > 1 {
			v = rank.StoresAffiliateRank(ctx, traceInfo, v)
		}
		resStores = append(resStores, v[0])
		if len(v) > 1 {
			for _, s := range v[1:] {
				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateBrand, v[0].StoreId) // 记录保留下来的同品牌门店ID
			}
		}
	}
	return resStores
}
