package processor

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
)

func RecallListingDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	if len(storeInfos) == 0 {
		return storeInfos
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseDishListing, time.Since(pt))
	}()

	// 老门店挂菜，不用往下走
	if !decision.IsUseDishRecallV2(traceInfo) {
		storeInfos, _ = recall.DishRecallWithStore(ctx, traceInfo, storeInfos)
		storeInfos = filter.DishFilter(ctx, traceInfo, storeInfos)
		return storeInfos
	}
	// 新门店挂菜
	storeInfos = RecallListingDishesWithoutCache(ctx, traceInfo, storeInfos)
	return storeInfos
}

func RecallListingDishesWithoutCache(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingWithoutCache, time.Since(pt))
	}()
	// recall
	dishInfos, _ := recall2.MultiRecallDishes(ctx, traceInfo, storeInfos.StoreIDs())

	// filling, 包括正排和特征
	dishInfos = filling.DishInfoFilling(ctx, traceInfo, dishInfos)

	// 菜品批量过滤
	dishInfos = filter.DishBatchFilter(ctx, traceInfo, dishInfos)

	// merge
	storeInfos = merge.StoreMergeDish(ctx, traceInfo, storeInfos, dishInfos)

	// filter, 不包括截断
	storeInfos = filter.DishFilterV2(ctx, traceInfo, storeInfos)

	// 门店下菜品排序
	storeInfos = rank.StoreDishRank(ctx, traceInfo, storeInfos)

	// 分流普通dishInfos和特征dishInfos
	splitDisplayDishesAndFeatureDishes(ctx, traceInfo, storeInfos)

	// 截断
	storeInfos = filter.DishTruncate(ctx, traceInfo, storeInfos)
	return storeInfos
}

func splitDisplayDishesAndFeatureDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) {
	if len(storeInfos) == 0 {
		return
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingSplit, time.Since(pt))
	}()
	isStoreIntention := len(traceInfo.QPResult.QueryStoreIntention) > 0

	for _, store := range storeInfos {
		if len(store.DishInfos) == 0 {
			continue
		}

		var displayDishInfos, featureDishInfos []*model.DishInfo

		for _, dish := range store.DishInfos {
			if dish == nil || dish.DishId == 0 {
				continue
			}
			// 只有es召回才能进入feature dish infos
			if dish.IsFromES {
				featureDishInfos = append(featureDishInfos, dish)
			}

			if isStoreIntention {
				// 门店意图，只有唯一的 DishRecallForStoreIntention 不放入 display
				if !(len(dish.DishRecallTypes) == 1 && dish.DishRecallTypes[0] == "DishRecallForStoreIntention") {
					displayDishInfos = append(displayDishInfos, dish)
				}
			} else {
				// 菜品意图 or UNKNOWN意图只要es的
				displayDishInfos = append(displayDishInfos, dish)
			}
		}

		// 分流后的dishInfos剔除门店意图的dish
		store.DishInfos = displayDishInfos

		// 截断top2进入features
		if len(featureDishInfos) > 2 {
			store.DishInfosForFeature = featureDishInfos[:2]
		} else {
			store.DishInfosForFeature = featureDishInfos
		}
	}
}
