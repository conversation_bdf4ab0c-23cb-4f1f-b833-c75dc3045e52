package processor

import (
	"context"
	"encoding/json"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/protobuf/foodads_adssearchengine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func SearchAdsPipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.StoreInfos, error) {
	pt := time.Now()
	var stores model.StoreInfos
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseSearchAds, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenAds, len(stores))
		metric_reporter2.ReportItemNumAndQPS(len(stores), metric_reporter2.SearchReportTypePhrase, traceInfo.HandlerType.String(), "", constants.AdsAdsStoreNum)
		debugInfo.FillAdsStores(traceInfo, stores)
	}()
	levelDowngradeConfig := apollo.SearchApolloCfg.LevelDowngradeConfigMap.GetLevelDowngradeConfigControl(traceInfo.DowngradeLevel)
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeAds) || (levelDowngradeConfig != nil && levelDowngradeConfig.DowngradeAds) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetAdsStores downgrade ads")
		metric_reporter.ReportClientRequestError(1, "ads_downgrade", "true")
		return nil, nil
	}
	metric_reporter.ReportClientRequestError(1, "ads_downgrade", "0")
	// 通过search req 查询广告接口，得到 resp, 里面有ids, global data， 以及debug info
	adsSearchResp, adsErr := integrate.AdsSearchService.GetAdsSearch(ctx, traceInfo, nil)
	if traceInfo.IsDebug {
		jsonStr, _ := json.Marshal(adsSearchResp)
		logkit.FromContext(ctx).Info("SearchAdsPipeline", logkit.String("publishId", traceInfo.TraceRequest.PublishId),
			logkit.String("keyword", traceInfo.QueryKeyword),
			logkit.String("result", string(jsonStr)))
	}

	if adsErr != nil || adsSearchResp == nil {
		logkit.FromContext(ctx).Error("GetAdsStores fail to get adsSearch")
	}
	// 将ads resp 结果转化为 storeInfos
	stores = buildAdsStoreInfos(traceInfo, adsSearchResp)

	return stores, adsErr
}

// 真的search, searchFood, searchMart ads 结果拼装，不支持searchGlobal
func buildAdsStoreInfos(traceInfo *traceinfo.TraceInfo, resp *foodads_adssearchengine.SearchResponse) model.StoreInfos {
	stores := getAdsStoreList(traceInfo, resp)
	res := make([]*model.StoreInfo, 0, len(stores))
	storeInfos := make([]model.StoreInfo, len(stores))
	for i, adsStore := range stores {
		storeInfos[i].StoreId = adsStore.GetStoreId()
		storeInfos[i].BusinessType = foodalgo_search.BusinessType_Ads
		storeInfos[i].PredictRelevance = adsStore.GetPredictRelevance()
		storeInfos[i].PredictRelevanceLevel = adsStore.GetPredictRelevanceLevel()
		storeInfos[i].AdsExtraInfo = adsStore.GetExtraInfo()
		storeInfos[i].MerchantId = adsStore.GetMerchantId()
		storeInfos[i].BrandId = adsStore.GetBrandId()
		storeInfos[i].AdsExtraInfoInternal = adsStore.GetExtraInfoInternal()
		if storeInfos[i].ItemFeature == nil {
			storeInfos[i].ItemFeature = &food.ItemFeature{}
		}
		storeInfos[i].ItemFeature.CIStoreType = model.StoreTypeAds
		dishInfos := make(model.DishInfos, 0, len(adsStore.GetDishIds()))
		for _, dishId := range adsStore.GetDishIds() {
			dishInfos = append(dishInfos, &model.DishInfo{
				DishId:  dishId,
				StoreId: adsStore.GetStoreId(),
			})
		}
		storeInfos[i].DishInfos = dishInfos
		res = append(res, &storeInfos[i])
	}
	return res
}

func getAdsStoreList(traceInfo *traceinfo.TraceInfo, resp *foodads_adssearchengine.SearchResponse) []*foodads_adssearchengine.SearchResponse_IDPair {
	if resp == nil {
		return []*foodads_adssearchengine.SearchResponse_IDPair{}
	}
	if len(resp.Ids) > 0 && traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		return resp.Ids
	}
	if resp.GlobalData == nil {
		return []*foodads_adssearchengine.SearchResponse_IDPair{}
	}
	for _, data := range resp.GlobalData {
		if (traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.IsVnMart == false && data.GetCategoryType() == traceinfo.CategoryTypeFood) ||
			(traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.IsVnMart == true && data.GetCategoryType() == traceinfo.CategoryTypeMart) {
			return data.GetIdPairs()
		}
	}
	return []*foodads_adssearchengine.SearchResponse_IDPair{}
}
