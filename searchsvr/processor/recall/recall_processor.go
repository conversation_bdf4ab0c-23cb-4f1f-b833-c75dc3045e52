package recall

import (
	"context"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
)

func MultiRecallStores(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	//var mergeStores model.StoreInfos
	var recallStores []*model.RecallStore
	var err error
	// VN & 新纠错词典的结果, 原始词和纠错词并发召回
	if env.GetCID() == cid.VN && !apollo.SearchApolloCfg.VNCloseNewCorrected && traceInfo.QPResult.CorrectLevel != nil {
		recallStores, err = doRecallForVN(ctx, traceInfo)
	} else {
		recallStores, err = StoreRecallFromMultiSource(ctx, traceInfo)
	}
	return recallStores, err
}

func MultiRecallDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	storeInfos, _ = DishRecallWithStore(ctx, traceInfo, storeInfos)
	storeInfos = filter.DishFilter(ctx, traceInfo, storeInfos)
	return storeInfos
}

func MultiRecallDishesForMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	if storeInfos == nil {
		return nil
	}

	// 菜品召回
	storeInfos, err := DishRecallWithStore(ctx, traceInfo, storeInfos)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("DishRecallWithStore failed")
		return storeInfos
	}

	// 填充热销菜品
	storeInfos = filling.FillDishWithStoreBestSelling(ctx, traceInfo, storeInfos)

	// 菜品过滤
	storeInfos = filter.DishFilterMainSite(ctx, traceInfo, storeInfos)
	return storeInfos
}

func doRecallForVN(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseStoreRecall, time.Since(pt))
	}()
	var originRecallStores, correctRecallStores []*model.RecallStore
	var originErr, correctErr error
	var recallStores []*model.RecallStore
	var originTraceInfo *traceinfo.TraceInfo

	wg := sync.WaitGroup{}
	wg.Add(2)

	goroutine.WithGo(ctx, "origin_recall", func(params ...interface{}) {
		defer wg.Done()
		originTraceInfo = traceinfo.NewTraceInfo()
		err := preprocess.TraceInfoClone(ctx, traceInfo, originTraceInfo)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("clone TraceInfo fail", zap.Error(err))
			return
		}

		// 将纠错前的 keyword 赋值到originTraceInfo中
		originTraceInfo.QueryKeyword = traceInfo.OriginKeyword
		preprocess.CorrectedRecallPreProcess(ctx, originTraceInfo)
		originRecallStores, originErr = StoreRecallFromMultiSource(ctx, originTraceInfo)
	})
	goroutine.WithGo(ctx, "correct_recall", func(params ...interface{}) {
		defer wg.Done()
		correctRecallStores, correctErr = StoreRecallFromMultiSource(ctx, traceInfo)
	})
	wg.Wait()
	if originErr != nil && correctErr != nil {
		return nil, correctErr
	}
	if len(originRecallStores) > 0 && len(correctRecallStores) > 0 {
		if traceInfo.QPResult != nil && *traceInfo.QPResult.CorrectLevel == qp.CorrectionLevel_HIGH {
			traceInfo.RspCorrectedType = foodalgo_search.SearchResponse_UseCorrectedKeyword
			recallStores = correctRecallStores
		} else {
			traceInfo.RspCorrectedType = foodalgo_search.SearchResponse_UseOriginalKeyword
			recallStores = originRecallStores
			traceInfo.QueryKeyword = originTraceInfo.QueryKeyword
		}
	} else if len(correctRecallStores) > 0 { // 原词无结果
		traceInfo.RspCorrectedType = foodalgo_search.SearchResponse_OriginalNoResultUseCorrectedKeyword
		recallStores = correctRecallStores
	} else if len(originRecallStores) > 0 { //纠错词无结果
		traceInfo.IsCorrected = false
		recallStores = originRecallStores
		traceInfo.QueryKeyword = originTraceInfo.QueryKeyword
	} else { // 都无结果
		traceInfo.IsCorrected = false
	}

	return recallStores, nil
}
