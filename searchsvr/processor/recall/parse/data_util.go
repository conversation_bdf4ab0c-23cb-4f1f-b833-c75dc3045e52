package parse

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GetDataLen(ctx context.Context, d *traceinfo.DataSourceItem) int {
	if d == nil {
		return 0
	}
	switch d.ValueType {
	case TypeString:
		return 1
	case TypeStringList:
		if value, ok := d.Value.([]string); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeStringList", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeBool:
		return 1
	case TypeInt:
		return 1
	case TypeInt32:
		return 1
	case TypeInt64:
		return 1
	case TypeUint32:
		return 1
	case TypeUint64:
		return 1
	case TypeUint32List:
		if value, ok := d.Value.([]uint32); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeUint32List", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeUint64List:
		if value, ok := d.Value.([]uint64); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeUint64List", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeFloat32:
		return 1
	case TypeFloat64:
		return 1
	case TypeFloat32List:
		if value, ok := d.Value.([]float32); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeFloat32List", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeFloat64List:
		if value, ok := d.Value.([]float64); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeFloat64List", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeStringListList:
		if value, ok := d.Value.([][]string); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeStringListList", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeInt32List:
		if value, ok := d.Value.([]int32); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeInt32List", logkit.Any("DataSourceItem", d))
			return 0
		}
	case TypeInt64List:
		if value, ok := d.Value.([]int64); ok {
			return len(value)
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen failed TypeInt64List", logkit.Any("DataSourceItem", d))
			return 0
		}
	}
	logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataLen invalid type", logkit.Any("DataSourceItem", d))
	return 0
}

func GetDataByIndex(ctx context.Context, d *traceinfo.DataSourceItem, index int) (string, string) {
	if d == nil {
		return "", ""
	}
	switch d.ValueType {
	case TypeStringList:
		if value, ok := d.Value.([]string); ok {
			return value[index], "string"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "string"
		}
	case TypeUint32List:
		if value, ok := d.Value.([]uint32); ok {
			return strconv.Itoa(int(value[index])), "uint32"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "uint32"
		}
	case TypeUint64List:
		if value, ok := d.Value.([]uint64); ok {
			return strconv.Itoa(int(value[index])), "uint64"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "uint64"
		}
	case TypeInt32List:
		if value, ok := d.Value.([]int32); ok {
			return strconv.Itoa(int(value[index])), "int32"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "int32"
		}
	case TypeInt64List:
		if value, ok := d.Value.([]int64); ok {
			return strconv.Itoa(int(value[index])), "int64"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "int64"
		}
	case TypeFloat32List:
		if value, ok := d.Value.([]float32); ok {
			return strconv.Itoa(int(value[index])), "float32"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "float32"
		}
	case TypeFloat64List:
		if value, ok := d.Value.([]float64); ok {
			return strconv.Itoa(int(value[index])), "float64"
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex failed", logkit.Any("DataSourceItem", d))
			return "", "float64"
		}
	}
	logkit.FromContext(ctx).Error("RecallConfiguration dataUtil.GetDataByIndex invalid type", logkit.Any("DataSourceItem", d))
	return "", ""
}

func TranToInterfaceList(ctx context.Context, d *traceinfo.DataSourceItem) []interface{} {

	res := make([]interface{}, 0)

	switch d.ValueType {
	case TypeString:
		res = append(res, d.Value)
		return res
	case TypeStringList:
		if value, ok := d.Value.([]string); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	case TypeBool:
		res = append(res, d.Value)
		return res
	case TypeInt32:
		res = append(res, d.Value)
		return res
	case TypeInt64:
		res = append(res, d.Value)
		return res
	case TypeUint32:
		res = append(res, d.Value)
		return res
	case TypeUint64:
		res = append(res, d.Value)
		return res
	case TypeUint32List:
		if value, ok := d.Value.([]uint32); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	case TypeUint64List:
		if value, ok := d.Value.([]uint64); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	case TypeFloat32:
		res = append(res, d.Value)
		return res
	case TypeFloat64:
		res = append(res, d.Value)
		return res
	case TypeFloat32List:
		if value, ok := d.Value.([]float32); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	case TypeFloat64List:
		if value, ok := d.Value.([]float64); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	case TypeStringListList:
		if value, ok := d.Value.([][]string); ok {
			for _, i := range value {
				res = append(res, i)
			}
			return res
		} else {
			logkit.FromContext(ctx).Error("dataUtil.DoTran failed", logkit.Any("DataSourceItem", d))
		}
	}

	return make([]interface{}, 0)
}

func TranDataByReflection(ctx context.Context, value string, valueType string) *traceinfo.DataSourceItem {
	switch valueType {
	case QuerySourceTypeInt:
		intValue, err := strconv.Atoi(value)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeInt32, Value: intValue}
	case QuerySourceTypeInt32:
		intValue, err := strconv.Atoi(value)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeInt32, Value: intValue}
	case QuerySourceTypeInt64:
		uint64Value, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeInt64, Value: uint64Value}
	case QuerySourceTypeString:
		return &traceinfo.DataSourceItem{ValueType: TypeString, Value: value}
	case QuerySourceTypeFloat32:
		floatValue, err := strconv.ParseFloat(value, 32)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeFloat32, Value: floatValue}
	case QuerySourceTypeFloat64:
		floatValue, err := strconv.ParseFloat(value, 64)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeFloat64, Value: floatValue}
	case QuerySourceTypeBool:
		boolValue, err := strconv.ParseBool(value)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration TranDataByReflection tranValue failed", logkit.String("value", value), logkit.String("type", valueType))
			return nil
		}
		return &traceinfo.DataSourceItem{ValueType: TypeBool, Value: boolValue}
	default:
		logkit.FromContext(ctx).Error("RecallConfiguration TranDataByReflection has no config tranValue", logkit.String("value", value), logkit.String("type", valueType))
		return nil
	}
}

func GetQueryData(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, querySource string, querySourceType string, queryMode string) []interface{} {
	if len(querySource) == 0 {
		var res = make([]interface{}, 0, 1)
		res = append(res, traceInfo.QueryKeyword)
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource get queryWord",
			logkit.String("recallName", recallName), logkit.String("queryWord", traceInfo.QueryKeyword), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
		return res
	}

	if queryMode == QueryModeDirect {
		dataResult := TranDataByReflection(ctx, querySource, querySourceType)
		if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
				logkit.String("recallName", recallName), logkit.String("dataType", "string"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			return nil
		}
		return TranToInterfaceList(ctx, dataResult)
	} else if queryMode == QueryModeParam || len(queryMode) == 0 {
		switch querySourceType {
		case QuerySourceTypeString:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "string"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(string); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "string"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
		case QuerySourceTypeList:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]string); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeStrFromList:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "str_from_list"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]string); ok {
				if len(val) > 0 {
					valurStr := strings.Join(val, " ")
					var res = make([]interface{}, 0, 1)
					res = append(res, valurStr)
					return res
				}
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "str_from_list"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
		case QuerySourceTypeListUint32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]uint32); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeListUint64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]uint64); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeListInt32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_int32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]int32); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_int32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeListInt64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_int64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]int64); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_int64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeInt:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(int); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "int"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil

		case QuerySourceTypeInt32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(int32); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "int32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeInt64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(int64); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "int64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil

		case QuerySourceTypeUint32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(uint32); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "uint32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeUint64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(uint64); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeFloat32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			// 先试试看32能否转成，否则试试看64
			if val, ok := dataResult.Value.(float32); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			}
			if val, ok := dataResult.Value.(float64); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			}
			logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
				logkit.String("recallName", recallName), logkit.String("dataType", "float32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))

			return nil
		case QuerySourceTypeFloat64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.(float64); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			}
			if val, ok := dataResult.Value.(float32); ok {
				var res = make([]interface{}, 0, 1)
				res = append(res, val)
				return res
			}
			logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
				logkit.String("recallName", recallName), logkit.String("dataType", "float64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))

			return nil
		case QuerySourceTypeListFloat32:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_uint32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]float32); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_float32"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		case QuerySourceTypeListFloat64:
			dataResult := GetQuerySource(ctx, traceInfo, querySource)
			if IsDataSourceNil(ctx, traceInfo, recallName, dataResult) {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSearchBoolQuery GetQuerySource is empty",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_float64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
				return nil
			}
			if val, ok := dataResult.Value.([]float64); ok {
				ret := make([]interface{}, 0, len(val))
				for i := 0; i < len(val); i++ {
					ret = append(ret, val[i])
				}
				return ret
			} else {
				logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery GetQuerySource tran failed",
					logkit.String("recallName", recallName), logkit.String("dataType", "list_float64"), logkit.String("querySource", querySource), logkit.String("querySourceType", querySourceType), logkit.String("queryMode", queryMode))
			}
			return nil
		}
	}

	logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery getQueryData invalid source type",
		logkit.String("recallName", recallName), logkit.String("type", querySourceType), logkit.String("source", querySource), logkit.String("queryMode", queryMode))
	return make([]interface{}, 0)
}
