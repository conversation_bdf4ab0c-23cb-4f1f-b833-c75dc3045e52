package parse

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"testing"

	"github.com/gogo/protobuf/proto"
)

func TestCheckCondition(t *testing.T) {
	type args struct {
		ctx        context.Context
		parameters map[string]interface{}
		condition  *string
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test for ignore RewriteNer_Store_0",
			args: args{
				ctx:        context.Background(),
				parameters: make(map[string]interface{}),
				condition:  proto.String("listLen(RewriteNer_Store_0) > 0 || listLen(RewriteNer_Dish_0) > 0 || listLen(RewriteNer_Location_0) > 0 || listLen(RewriteNer_Unknown_0) > 0 || listLen(RewriteNer_Category_0) > 0"),
			},
			want: false,
		},
		{
			name: "test for ignore RewriteNer_Store_1",
			args: args{
				ctx:        context.Background(),
				parameters: make(map[string]interface{}),
				condition:  proto.String("listLen(RewriteNer_Store_1) > 0 || listLen(RewriteNer_Dish_0) > 0 || listLen(RewriteNer_Location_0) > 0 || listLen(RewriteNer_Unknown_0) > 0 || listLen(RewriteNer_Category_0) > 0"),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CheckCondition(tt.args.ctx, tt.args.parameters, tt.args.condition)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckCondition() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("CheckCondition() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Func(args ...interface{}) (interface{}, error) {
	if args == nil {
		return false, nil
	}
	if len(args) == 0 {
		return false, nil
	}
	str, ok := args[0].(string)
	if !ok {
		return false, nil
	}
	if util.IsAsciiTerms(str) {
		return false, nil
	} else {
		return true, nil
	}
}

func TestFunc(t *testing.T) {
	// 测试参数为nil的情况
	result, err := Func(nil)
	if err != nil {
		t.Errorf("Expected error to be nil, but got: %v", err)
	}
	if result != false {
		t.Errorf("Expected result to be false, but got: %v", result)
	}

	// 测试参数为ASCII字符串的情况
	result, err = Func("Hello")
	if err != nil {
		t.Errorf("Expected error to be nil, but got: %v", err)
	}
	if result != false {
		t.Errorf("Expected result to be false, but got: %v", result)
	}

	// 测试参数为非ASCII字符串的情况
	result, err = Func("Xin chào")
	if err != nil {
		t.Errorf("Expected error to be nil, but got: %v", err)
	}
	if result != true {
		t.Errorf("Expected result to be true, but got: %v", result)
	}
}
