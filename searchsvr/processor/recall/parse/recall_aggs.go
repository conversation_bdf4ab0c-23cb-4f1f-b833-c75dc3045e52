package parse

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"github.com/olivere/elastic/v7"
)

func GetTermsAggregations(ctx context.Context, traceInfo *traceinfo.TraceInfo, aggsConf *apollo.AggsConfig) *elastic.TermsAggregation {
	if aggsConf == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration GetTermsAggregations failed, aggsConfig is nil")
		return nil
	}
	// 针对SearchRestaurantDishes 特殊调整agg size
	topHitSize := int(aggsConf.TopHitsAggregationSize)
	aggTopHist := elastic.NewTopHitsAggregation().Size(topHitSize).FetchSource(false)
	for _, s := range aggsConf.TopHitsAggregationSortConf {
		sortDirection := strings.ToLower(s.Order)
		if sortDirection == OrderDesc {
			aggTopHist.Sort(s.EsField, false)
		} else {
			aggTopHist.Sort(s.EsField, true)
		}
	}

	// 创建Terms桶聚合, top_dishes 这个聚合名称需要与结果解析时的json 保持一致，见 searchsvr/dao/es.go
	aggs := elastic.NewTermsAggregation().Field(aggsConf.AggsField).Size(int(aggsConf.AggsSize)).SubAggregation(aggsConf.SubAggregationName, aggTopHist)

	if aggsConf.AggsMaxScoreConfig != nil {
		aggMaxScore := elastic.NewMaxAggregation().Script(elastic.NewScript(aggsConf.AggsMaxScoreConfig.Script))
		if aggsConf.AggsMaxScoreConfig.Order == OrderDesc {
			aggs.SubAggregation(aggsConf.AggsMaxScoreConfig.AggName, aggMaxScore).OrderByAggregation(aggsConf.AggsMaxScoreConfig.AggName, false)
		} else {
			aggs.SubAggregation(aggsConf.AggsMaxScoreConfig.AggName, aggMaxScore).OrderByAggregation(aggsConf.AggsMaxScoreConfig.AggName, true)
		}
	}
	return aggs
}
