package parse

import (
	"context"
	"fmt"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

const (
	MatchTypeTerm            = "term"
	MatchTypeTerms           = "terms"
	MatchTypeMatch           = "match"
	MatchTypeMultiMatch      = "multi_match"
	MatchTypeFunctionScore   = "function_score"
	MatchTypeMatchBoolPrefix = "match_bool_prefix"
	MatchTypeMatchPhrase     = "match_phrase"
	MatchTypeConstantScore   = "constant_score"
	MatchTypeGeoBoundingBox  = "geo_bounding_box"
	MatchTypeGeoDistance     = "geo_distance"
	MatchTypeScoreFunc       = "score_func"   // es 没有，自定义的，表示直接加分
	MatchTypeQueryString     = "query_string" // 在 query 词中加权重 kfc^1 food^0.5
	MatchTypeRange           = "range"
	MatchTypePrefix          = "prefix"

	OuterMatchTypeShould  = "should"
	OuterMatchTypeMust    = "must"
	OuterMatchTypeMustNot = "must_not"
	OuterMatchTypeFilter  = "filter"

	QueryModeDirect = "direct"
	QueryModeParam  = "param"
	QueryModeCopy   = "copy"
)

func GenBasicQueryList(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, boolQueryConf *apollo.RecallQueryBoolItem) []elastic.Query {
	recallName := recallConfig.RecallLogName

	// 判断condition
	if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &boolQueryConf.Condition); !isOk {
		return nil
	}
	// 判断 abTest
	if !isHitAbTest(ctx, traceInfo, boolQueryConf.AbKey) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenBasicQueryList check abTestKey is false",
			logkit.String("recallName", recallName), logkit.String("AbKey", boolQueryConf.AbKey), logkit.Any("conf", boolQueryConf))
		return nil
	}

	queries := make([]elastic.Query, 0)

	switch boolQueryConf.MatchType {
	case MatchTypeQueryString:
		// 补全_recallType_recallId，因为每一路召回都可能有一个 QPResult_QueryStringList_Queries，数据源无法区分，需要更细粒度
		if len(boolQueryConf.QuerySource) > 0 && len(strings.Split(boolQueryConf.QuerySource, "_")) == 3 {
			boolQueryConf.QuerySource = fmt.Sprintf("QPResult_QueryStringList_Queries_%d_%s", recallConfig.RecallType, recallConfig.RecallId)
		}
		if len(boolQueryConf.EsField) > 0 && len(strings.Split(boolQueryConf.EsField, "_")) == 3 {
			boolQueryConf.EsField = fmt.Sprintf("QPResult_QueryStringList_Fields_%d_%s", recallConfig.RecallType, recallConfig.RecallId)
		}

		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}

		// query string 和 配置的 fields 必须一一对应
		fields := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.EsField, boolQueryConf.EsFieldType, boolQueryConf.EsFieldMode)
		if len(values) != len(fields) {
			return nil
		}
		for i := 0; i < len(values); i++ {
			q := elastic.NewQueryStringQuery(values[i].(string))
			filedStr := fields[i].(string)
			if strings.Contains(filedStr, ",") {
				tmpFields := strings.Split(filedStr, ",")
				for _, f := range tmpFields {
					q.Field(f)
				}
			} else {
				q.Field(filedStr)
			}
			handleQueryStringAttribute(boolQueryConf, q)
			queries = append(queries, q)
		}

	case MatchTypeTerm:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			q := elastic.NewTermQuery(boolQueryConf.EsField, val)
			handleTermAttribute(boolQueryConf, q)
			queries = append(queries, q)
		}
	case MatchTypeTerms:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		tmpValues := util2.ToInterfaceSlice(values)
		fields := strings.Split(boolQueryConf.EsField, ",")
		for _, field := range fields {
			q := elastic.NewTermsQuery(field, tmpValues...)
			handleTermsAttribute(boolQueryConf, q)
			queries = append(queries, q)
		}
	case MatchTypeMatch:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			matchQuery := elastic.NewMatchQuery(boolQueryConf.EsField, val)
			handleMatchAttribute(boolQueryConf, matchQuery)
			queries = append(queries, matchQuery)
		}
	case MatchTypeMultiMatch:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		fields := strings.Split(boolQueryConf.EsField, ",")
		for _, val := range values {
			matchQuery := elastic.NewMultiMatchQuery(val, fields...)
			handleMultiMatchQueryAttribute(boolQueryConf, matchQuery)
			queries = append(queries, matchQuery)
		}
	case MatchTypeMatchPhrase:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		fields := strings.Split(boolQueryConf.EsField, ",")
		if len(fields) > 1 {
			for _, val := range values {
				matchQuery := elastic.NewMultiMatchQuery(val, fields...)
				handleMultiMatchQueryAttribute(boolQueryConf, matchQuery)
				matchQuery.Type("phrase")
				queries = append(queries, matchQuery)
			}
		} else {
			for _, val := range values {
				matchQuery := elastic.NewMatchPhraseQuery(boolQueryConf.EsField, val)
				handleMatchPhraseAttribute(boolQueryConf, matchQuery)
				queries = append(queries, matchQuery)
			}
		}

	case MatchTypeMatchBoolPrefix:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}

		fields := strings.Split(boolQueryConf.EsField, ",")
		if len(fields) > 1 {
			for _, val := range values {
				matchQuery := elastic.NewMultiMatchQuery(val, fields...)
				handleMultiMatchQueryAttribute(boolQueryConf, matchQuery)
				matchQuery.Type("bool_prefix")
				queries = append(queries, matchQuery)
			}
		} else {
			for _, val := range values {
				matchQuery := elastic.NewMatchBoolPrefixQuery(boolQueryConf.EsField, val)
				handleMatchBoolPrefixAttribute(boolQueryConf, matchQuery)
				queries = append(queries, matchQuery)
			}
		}

	case MatchTypeConstantScore:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		fields := strings.Split(boolQueryConf.EsField, ",")
		for _, field := range fields {
			for _, val := range values {
				filterQuery := elastic.NewTermQuery(field, val)
				constantScoreQuery := elastic.NewConstantScoreQuery(filterQuery)
				if boolQueryConf.Boost != nil {
					constantScoreQuery.Boost(*boolQueryConf.Boost)
				}
				queries = append(queries, constantScoreQuery)
			}
		}
	case MatchTypeGeoBoundingBox:
		t := GenGeoBoundingQuery(ctx, traceInfo, recallName, boolQueryConf)
		if t != nil {
			queries = append(queries, t)
		}
	case MatchTypeGeoDistance:
		t := GenGeoDistanceQuery(ctx, traceInfo, recallName, boolQueryConf)
		if t != nil {
			queries = append(queries, t)
		}
	case MatchTypeRange:
		if len(boolQueryConf.RangeConf) == 0 {
			return nil
		}
		for _, rc := range boolQueryConf.RangeConf {
			var rangeItems []RangeItem
			if rc.QueryMode == QueryModeDirect {
				rangeItems = UnMarshalRange(ctx, rc.QuerySource)
				if len(rangeItems) == 0 {
					logkit.FromContext(ctx).Error("RecallConfiguration rangeConf parse error", logkit.Any("conf", rc))
					continue
				}
			} else {
				// 它必须是一个 string
				v := GetQueryData(ctx, traceInfo, recallName, rc.QuerySource, "string", rc.QueryMode)
				if len(v) != 1 {
					logkit.FromContext(ctx).Error("RecallConfiguration rangeConf parse error", logkit.Any("conf", rc))
					continue
				}
				rangeItems = UnMarshalRange(ctx, v[0].(string))
			}
			for _, r := range rangeItems {
				q := elastic.NewRangeQuery(boolQueryConf.EsField)
				if r.From != nil {
					if r.FromInclude {
						q.Gte(r.From)
					} else {
						q.Gt(r.From)
					}
				}
				if r.To != nil {
					if r.ToInclude {
						q.Lte(r.To)
					} else {
						q.Lt(r.To)
					}
				}
				queries = append(queries, q)
			}
		}
	case MatchTypePrefix:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		fields := strings.Split(boolQueryConf.EsField, ",")
		if len(fields) == 0 {
			logkit.FromContext(ctx).Error("RecallConfiguration prefix fields is nil", logkit.Any("field", boolQueryConf.EsField))
			break
		}
		for _, field := range fields {
			for _, val := range values {
				strVal, ok := val.(string)
				if !ok {
					logkit.FromContext(ctx).Error("RecallConfiguration prefix value failed", logkit.Any("queryConfig", boolQueryConf), logkit.Any("value", val))
					continue
				}
				prefixQuery := elastic.NewPrefixQuery(field, strVal)
				queries = append(queries, prefixQuery)
			}
		}
	}
	return queries
}

func GenQueryList(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, boolQueryConf *apollo.RecallQueryBoolItem) []elastic.Query {
	// 简单模式 vs 嵌套模式
	if len(boolQueryConf.OuterMatchType) == 0 {
		return GenBasicQueryList(ctx, traceInfo, recallConfig, boolQueryConf)
	} else {
		queries := make([]elastic.Query, 0)
		tmpQuery := GenBoolQuery(ctx, traceInfo, recallConfig, boolQueryConf)
		if tmpQuery != nil && len(tmpQuery) > 0 {
			queries = append(queries, tmpQuery...)
		}
		return queries
	}
}

func GenBoolQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, boolQueryConf *apollo.RecallQueryBoolItem) []elastic.Query {
	// 无嵌套模式 vs 嵌套模式
	if boolQueryConf.Queries == nil || len(boolQueryConf.Queries) == 0 {
		tmp := GenBoolQueryNoNest(ctx, traceInfo, recallConfig.RecallLogName, boolQueryConf)
		if tmp == nil {
			return nil
		} else {
			return []elastic.Query{tmp}
		}
	} else {
		tmp := GenBoolQueryNest(ctx, traceInfo, recallConfig, boolQueryConf)
		if tmp == nil {
			return nil
		} else {
			return tmp
		}
	}
}

func GenBoolQueryNoNest(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, boolQueryConf *apollo.RecallQueryBoolItem) *elastic.BoolQuery {
	// 判断condition
	if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &boolQueryConf.Condition); !isOk {
		return nil
	}

	// 判断 abTest
	if !isHitAbTest(ctx, traceInfo, boolQueryConf.AbKey) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenBoolQueryNoNest check abTestKey is false",
			logkit.String("recallName", recallName), logkit.String("AbKey", boolQueryConf.AbKey), logkit.Any("conf", boolQueryConf))
		return nil
	}

	boolQuery := elastic.NewBoolQuery()
	switch boolQueryConf.MatchType {
	case MatchTypeTerm:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			matchQuery := elastic.NewTermQuery(boolQueryConf.EsField, val)
			addQueryToBool(boolQueryConf, boolQuery, matchQuery)
		}
	case MatchTypeTerms:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		tmpValues := util2.ToInterfaceSlice(values)
		matchQuery := elastic.NewTermsQuery(boolQueryConf.EsField, tmpValues...)
		addQueryToBool(boolQueryConf, boolQuery, matchQuery)
	case MatchTypeMatch:
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			matchQuery := elastic.NewMatchQuery(boolQueryConf.EsField, val)
			handleMatchAttribute(boolQueryConf, matchQuery)
			addQueryToBool(boolQueryConf, boolQuery, matchQuery)
		}
	case MatchTypeMultiMatch:
		fields := strings.Split(boolQueryConf.EsField, ",")
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			matchQuery := elastic.NewMultiMatchQuery(val, fields...)
			handleMultiMatchQueryAttribute(boolQueryConf, matchQuery)
			addQueryToBool(boolQueryConf, boolQuery, matchQuery)
		}
	case MatchTypeMatchPhrase:
		fields := boolQueryConf.EsField
		values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
		if len(values) == 0 {
			return nil
		}
		for _, val := range values {
			matchQuery := elastic.NewMatchPhraseQuery(fields, val)
			handleMatchPhraseAttribute(boolQueryConf, matchQuery)
			addQueryToBool(boolQueryConf, boolQuery, matchQuery)
		}
	}

	handleShouldMinimumMatch(traceInfo, boolQueryConf, boolQuery)
	return boolQuery
}

func GenBoolQueryNest(ctx context.Context, traceInfo *traceinfo.TraceInfo,
	recallConfig *apollo.StoreRecallConfig, boolQueryConf *apollo.RecallQueryBoolItem) []elastic.Query {

	// 递归的边界: 遇到 function score
	if boolQueryConf.MatchType == MatchTypeFunctionScore {
		t := GenFunctionScoreQuery(ctx, traceInfo, recallConfig.RecallLogName, boolQueryConf)
		if t != nil {
			return []elastic.Query{t}
		} else {
			return nil
		}
	}

	// 递归的边界: 没有子集
	if len(boolQueryConf.Queries) == 0 {
		return GenBasicQueryList(ctx, traceInfo, recallConfig, boolQueryConf)
	}

	boolQuery := elastic.NewBoolQuery()
	isHit := false

	// 展开能自我复制的 bool
	totalCopiedQueries := make([]apollo.RecallQueryBoolItem, 0)
	for _, q := range boolQueryConf.Queries {
		if q.QueryMode == QueryModeCopy {
			copiedQueries := genCopyBoolQuery(ctx, traceInfo, &q)
			if len(copiedQueries) > 0 {
				totalCopiedQueries = append(totalCopiedQueries, copiedQueries...)
			}
		}
	}
	boolQueryConf.Queries = append(boolQueryConf.Queries, totalCopiedQueries...)
	for _, q := range boolQueryConf.Queries {
		// 被复制后的 bool 不再解析
		if q.QueryMode == QueryModeCopy {
			continue
		}
		t := GenBoolQueryNest(ctx, traceInfo, recallConfig, &q)
		if t != nil && len(t) > 0 {
			for _, i := range t {
				addQueryToBool(boolQueryConf, boolQuery, i)
				isHit = true
			}
		}
	}
	if isHit == false {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenBoolQueryNest bool is empty, return nil",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("boolQueryConf", boolQueryConf))
		return nil
	}

	handleShouldMinimumMatch(traceInfo, boolQueryConf, boolQuery)
	return []elastic.Query{boolQuery}
}

func addQueryToBool(boolQueryConf *apollo.RecallQueryBoolItem, boolQuery *elastic.BoolQuery, q elastic.Query) {

	switch boolQueryConf.OuterMatchType {
	case OuterMatchTypeMust:
		boolQuery.Must(q)
	case OuterMatchTypeShould:
		boolQuery.Should(q)
	case OuterMatchTypeMustNot:
		boolQuery.MustNot(q)
	case OuterMatchTypeFilter:
		boolQuery.Filter(q)
	}
}
