package parse

import (
	"context"
	"encoding/json"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func genCopyBoolQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, boolQueryConf *apollo.RecallQueryBoolItem) []apollo.RecallQueryBoolItem {
	if boolQueryConf == nil {
		return nil
	}

	// 初始化参数池
	params := make(map[string]interface{})
	initParamMap(ctx, traceInfo, boolQueryConf, params)

	// 获得参数池子的边界 = 解析出来的 n 个参数，比如Category_Level1 = [1,2,3], Category_Level2 = [a,b,c]， 那么边界就是 3
	minLen := 99999
	for _, v := range params {
		dataLen := GetDataLen(ctx, v.(*traceinfo.DataSourceItem))
		if minLen > dataLen {
			minLen = dataLen
		}
	}

	queries := make([]apollo.RecallQueryBoolItem, 0)
	for i := 0; i < minLen; i++ {
		tmpBoolConf := doBoolConfCopy(ctx, boolQueryConf)

		// 被复制过的 bool 就不再具备 copy 属性
		tmpBoolConf.QueryMode = ""

		// 填充参数
		fillParam(ctx, traceInfo, &tmpBoolConf, params, i)

		queries = append(queries, tmpBoolConf)
	}

	return queries
}

func initParamMap(ctx context.Context, traceInfo *traceinfo.TraceInfo, boolQueryConf *apollo.RecallQueryBoolItem, params map[string]interface{}) map[string]interface{} {
	if boolQueryConf == nil {
		return params
	}

	if len(boolQueryConf.QuerySource) > 0 {
		if _, isOk := params[boolQueryConf.QuerySource]; !isOk {
			v := GetQuerySource(ctx, traceInfo, boolQueryConf.QuerySource)
			if !IsDataSourceNil(ctx, traceInfo, "", v) {
				params[boolQueryConf.QuerySource] = v
			}
		}
	}
	if len(boolQueryConf.Queries) == 0 {
		return params
	}

	for _, q := range boolQueryConf.Queries {
		initParamMap(ctx, traceInfo, &q, params)
	}
	return params
}

func fillParam(ctx context.Context, traceInfo *traceinfo.TraceInfo, boolQueryConf *apollo.RecallQueryBoolItem, params map[string]interface{}, index int) {
	if boolQueryConf == nil {
		return
	}
	if len(boolQueryConf.QuerySource) > 0 {
		data := params[boolQueryConf.QuerySource]
		data2 := data.(*traceinfo.DataSourceItem)

		tmpQuerySource, tmpQuerySourceType := GetDataByIndex(ctx, data2, index)
		boolQueryConf.QuerySource = tmpQuerySource
		boolQueryConf.QuerySourceType = tmpQuerySourceType
		boolQueryConf.QueryMode = QueryModeDirect
	}
	if len(boolQueryConf.Queries) == 0 {
		return
	}

	for i := range boolQueryConf.Queries {
		fillParam(ctx, traceInfo, &boolQueryConf.Queries[i], params, index)
	}
}

func doBoolConfCopy(ctx context.Context, sourceBoolQueryConf *apollo.RecallQueryBoolItem) apollo.RecallQueryBoolItem {
	data, _ := json.Marshal(sourceBoolQueryConf)
	var p2 = apollo.RecallQueryBoolItem{}
	err := json.Unmarshal(data, &p2)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration doBoolConfCopy failed", logkit.Any("sourceBoolConf", sourceBoolQueryConf))
		return apollo.RecallQueryBoolItem{}
	}
	return p2
}
