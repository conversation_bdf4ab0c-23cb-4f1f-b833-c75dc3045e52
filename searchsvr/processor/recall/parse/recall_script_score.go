package parse

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GenScriptScoreQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, scriptScoreConf *apollo.ScriptScoreConf, recallCommon *apollo.RecallCommon, outerFilterConf *apollo.OuterFilterConf) *elastic.ScriptScoreQuery {
	if scriptScoreConf == nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration scriptScore condition is empty", logkit.String("recallName", recallConfig.RecallLogName))
		return nil
	}

	scriptScore := elastic.NewScriptScoreQuery(nil, nil)

	// handle script
	script := genScript(ctx, traceInfo, recallConfig.RecallLogName, &scriptScoreConf.Script)
	if script == nil {
		return nil
	}
	scriptScore.Script(script)

	// handle query
	if scriptScoreConf.Query != nil {
		mustQueries, shouldQueries, mustNotQueries, filterQueries, fsqQuery := GenSearchBoolQuery(ctx, traceInfo, recallConfig, scriptScoreConf.Query, outerFilterConf, recallCommon)
		if len(mustQueries) == 0 && len(shouldQueries) == 0 && len(mustNotQueries) == 0 && len(filterQueries) == 0 && fsqQuery == nil {
			logkit.FromContext(ctx).Error("RecallConfiguration GenScriptScoreQuery gen search failed case must and should empty, at least faq",
				logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("searchConf", scriptScoreConf.Query))
		} else {
			boolQ := elastic.NewBoolQuery()

			if len(mustQueries) > 0 {
				boolQ.Must(mustQueries...)
			}
			if len(mustNotQueries) > 0 {
				boolQ.MustNot(mustNotQueries...)
			}
			if len(shouldQueries) > 0 {
				boolQ.Should(shouldQueries...)
			}
			if len(filterQueries) > 0 {
				boolQ.Filter(filterQueries...)
			}

			if len(mustQueries) == 0 && len(mustNotQueries) == 0 && len(shouldQueries) > 0 {
				boolQ.MinimumShouldMatch("1")
			}
			if scriptScoreConf.Query.MinimumShouldMatchStr != nil && len(*scriptScoreConf.Query.MinimumShouldMatchStr) > 0 && len(shouldQueries) > 0 {
				boolQ.MinimumShouldMatch(*scriptScoreConf.Query.MinimumShouldMatchStr)
			}
			if fsqQuery != nil {
				query := fsqQuery.Query(boolQ)
				scriptScore.Query(query)
			} else {
				scriptScore.Query(boolQ)
			}
		}
	}

	return scriptScore
}

func genScript(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, scriptConfItem *apollo.ScriptItem) *elastic.Script {
	if scriptConfItem == nil || len(scriptConfItem.Source) == 0 {
		logkit.FromContext(ctx).Error("RecallConfiguration GenFunctionScoreQuery genScript failed, scriptConfItem is nil",
			logkit.String("recallName", recallName), logkit.Any("scriptConfItem", scriptConfItem))
		return nil
	}

	script := elastic.NewScript(scriptConfItem.Source)

	if scriptConfItem.Lang != nil && len(*scriptConfItem.Lang) > 0 {
		script.Lang(*scriptConfItem.Lang)
	}
	if scriptConfItem.Params != nil && len(*scriptConfItem.Params) > 0 {
		for _, p := range *scriptConfItem.Params {
			t := GetQueryData(ctx, traceInfo, recallName, p.QuerySource, p.QuerySourceType, p.QueryMode)
			if len(t) == 0 {
				logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenFunctionScoreQuery genScript get param failed",
					logkit.String("recallName", recallName), logkit.Any("param", p))
				continue
			}
			// key-value 结构，value 只有一个元素
			script.Param(p.ParamKey, t[0])

			// todo delete after diff
			if p.ParamKey == "lon" {
				longitude := float32(traceInfo.TraceRequest.Longitude)
				script.Param("lon", longitude)
			}
			if p.ParamKey == "log" {
				longitude := float32(traceInfo.TraceRequest.Longitude)
				script.Param("log", longitude)
			}
			if p.ParamKey == "lng" {
				longitude := float32(traceInfo.TraceRequest.Longitude)
				script.Param("lng", longitude)
			}
			if p.ParamKey == "lat" {
				latitude := float32(traceInfo.TraceRequest.Latitude)
				script.Param("lat", latitude)
			}
		}
	}

	return script
}
