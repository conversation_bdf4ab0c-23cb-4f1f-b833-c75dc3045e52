package parse

import (
	"context"
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func GenGeoBoundingQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, boolQueryConf *apollo.RecallQueryBoolItem) *elastic.GeoBoundingBoxQuery {

	values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
	if len(values) == 0 {
		return nil
	}
	distance, isOk := values[0].(float64)
	if isOk {
		topLeft, bottomRight := model2.CalBox(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, distance)
		t := elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed")

		if boolQueryConf.GeoBoundingBoxQueryType != nil && len(*boolQueryConf.GeoBoundingBoxQueryType) > 0 {
			t.Type(*boolQueryConf.GeoBoundingBoxQueryType)
		}
		return t
	} else {
		distance2, isOk2 := values[0].(uint32)
		if isOk2 {
			topLeft, bottomRight := model2.CalBox(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, float64(distance2))
			t := elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed")
			if boolQueryConf.GeoBoundingBoxQueryType != nil && len(*boolQueryConf.GeoBoundingBoxQueryType) > 0 {
				t.Type(*boolQueryConf.GeoBoundingBoxQueryType)
			}
			return t
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration GenFilterQuery geo_bounding_box tran failed, use default 20KM",
				logkit.Any("boolQueryConf", boolQueryConf))
			topLeft, bottomRight := model2.CalBox(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude, float64(20))
			t := elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed")
			if boolQueryConf.GeoBoundingBoxQueryType != nil && len(*boolQueryConf.GeoBoundingBoxQueryType) > 0 {
				t.Type(*boolQueryConf.GeoBoundingBoxQueryType)
			}
			return t
		}
	}
}

func GenGeoDistanceQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, boolQueryConf *apollo.RecallQueryBoolItem) *elastic.GeoDistanceQuery {

	values := GetQueryData(ctx, traceInfo, recallName, boolQueryConf.QuerySource, boolQueryConf.QuerySourceType, boolQueryConf.QueryMode)
	if len(values) == 0 {
		return nil
	}
	distance, isOk := values[0].(float64)
	if isOk {
		t := elastic.NewGeoDistanceQuery("location").Distance(fmt.Sprintf("%fm", distance)).
			Lon(traceInfo.TraceRequest.Longitude).Lat(traceInfo.TraceRequest.Latitude)
		if boolQueryConf.GeoDistanceType != nil && len(*boolQueryConf.GeoDistanceType) > 0 {
			t.DistanceType(*boolQueryConf.GeoDistanceType)
		}
		return t
	} else {
		distance2, isOk2 := values[0].(uint32)
		if isOk2 {
			t := elastic.NewGeoDistanceQuery("location").Distance(fmt.Sprintf("%dm", distance2)).
				Lon(traceInfo.TraceRequest.Longitude).Lat(traceInfo.TraceRequest.Latitude)
			if boolQueryConf.GeoDistanceType != nil && len(*boolQueryConf.GeoDistanceType) > 0 {
				t.DistanceType(*boolQueryConf.GeoDistanceType)
			}
			return t
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration GenFilterQuery geo_distance tran failed, use default 5KM",
				logkit.Any("dataValueItem", boolQueryConf))
			t := elastic.NewGeoDistanceQuery("location").Distance(fmt.Sprintf("%dm", 5)).
				Lon(traceInfo.TraceRequest.Longitude).Lat(traceInfo.TraceRequest.Latitude)
			if boolQueryConf.GeoDistanceType != nil && len(*boolQueryConf.GeoDistanceType) > 0 {
				t.DistanceType(*boolQueryConf.GeoDistanceType)
			}
			return t
		}
	}
}
