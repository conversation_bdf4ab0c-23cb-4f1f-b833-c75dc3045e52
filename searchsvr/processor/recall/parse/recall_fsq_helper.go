package parse

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GenFunctionScoreQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, boolQueryConf *apollo.RecallQueryBoolItem) *elastic.FunctionScoreQuery {
	// condition 判断
	if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &boolQueryConf.Condition); !isOk {
		return nil
	}
	// 判断 abTest
	if !isHitAbTest(ctx, traceInfo, boolQueryConf.AbKey) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenFunctionScoreQuery check abTestKey is false",
			logkit.String("recallName", recallName), logkit.String("AbKey", boolQueryConf.AbKey), logkit.Any("conf", boolQueryConf))
		return nil
	}

	if boolQueryConf.FunctionScore == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration GenFunctionScoreQuery FunctionScoreConf is nil", logkit.String("recallName", recallName))
		return nil
	}

	query := elastic.NewFunctionScoreQuery()

	if boolQueryConf.FunctionScore.Boost != nil {
		query.Boost(*boolQueryConf.FunctionScore.Boost)
	}
	if boolQueryConf.FunctionScore.BoostMode != nil && len(*boolQueryConf.FunctionScore.BoostMode) > 0 {
		query.BoostMode(*boolQueryConf.FunctionScore.BoostMode)
	}
	if boolQueryConf.FunctionScore.ScoreMode != nil && len(*boolQueryConf.FunctionScore.ScoreMode) > 0 {
		query.ScoreMode(*boolQueryConf.FunctionScore.ScoreMode)
	}

	if boolQueryConf.FunctionScore.Functions != nil && len(*boolQueryConf.FunctionScore.Functions) > 0 {
		for _, f := range *boolQueryConf.FunctionScore.Functions {
			// todo: function score 的全集是 ScriptScore , RandomScore, FieldValueFactor, Decay functions。目前只支持 ScriptScore
			if f.ScriptScore != nil && f.ScriptScore.ScriptItem != nil {
				script := genScript(ctx, traceInfo, recallName, f.ScriptScore.ScriptItem)
				scriptFunction := elastic.NewScriptFunction(script)
				if scriptFunction != nil {
					if f.Weight != nil {
						scriptFunction.Weight(*f.Weight)
					}
					query.AddScoreFunc(scriptFunction)
				}
			}
		}
	}
	return query
}
