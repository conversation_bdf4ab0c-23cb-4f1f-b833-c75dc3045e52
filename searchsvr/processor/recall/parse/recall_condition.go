package parse

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/Knetic/govaluate"
)

var functions = map[string]govaluate.ExpressionFunction{
	"bool": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return false, nil
		}
		return args[0].(bool), nil
	},
	"float64": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return false, nil
		}
		return args[0].(float64), nil
	},
	"strLen": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].(string))
		return (float64)(length), nil
	},
	"listLen": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]string))
		return (float64)(length), nil
	},
	"listStringLen": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]string))
		return (float64)(length), nil
	},
	"listStringListLen": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]string))
		return (float64)(length), nil
	},
	"listUint64Len": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]uint64))
		return (float64)(length), nil
	},
	"listUint32Len": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]uint32))
		return (float64)(length), nil
	},
	"listInt64Len": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]int64))
		return (float64)(length), nil
	},
	"listInt32Len": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		length := len(args[0].([]int32))
		return (float64)(length), nil
	},
	"strSplitLen": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return float64(0), nil
		}
		splitStr := strings.Split(args[0].(string), " ")
		return (float64)(len(splitStr)), nil
	},
	"strIsVn": func(args ...interface{}) (interface{}, error) {
		if args == nil || len(args) == 0 {
			return false, nil
		}
		str := args[0].(string)
		if util.IsAsciiTerms(str) {
			return false, nil
		} else {
			return true, nil
		}
	},
}

func CheckCondition(ctx context.Context, parameters map[string]interface{}, condition *string) (bool, error) {
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("RecallConfiguration CheckCondition panic error:", logkit.Any("condition", condition), logkit.Any("err", e), zap.Stack("stack trace"))
			fmt.Println(e)
		}
		return
	}()

	if condition == nil || len(*condition) == 0 {
		return true, nil
	}
	*condition = strings.TrimSpace(*condition)
	if len(*condition) == 0 {
		return true, nil
	}

	// 针对部分不能给默认值的进行特殊处理，减少 error 日志
	if strings.Contains(*condition, "RewriteNer_Store_0") {
		if _, ok := parameters["RewriteNer_Store_0"]; !ok {
			return false, nil
		}
	}

	expr, err := govaluate.NewEvaluableExpressionWithFunctions(*condition, functions)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration CheckCondition syntax error",
			logkit.String("condition", *condition))
		return false, err
	}

	result, err := expr.Evaluate(parameters)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration CheckCondition evaluate error",
			logkit.String("condition", *condition))
		return false, err
	}

	if val, ok := result.(bool); ok {
		return val, nil
	}
	return false, nil
}
