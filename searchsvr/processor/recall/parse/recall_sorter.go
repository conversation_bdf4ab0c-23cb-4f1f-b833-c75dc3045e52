package parse

import (
	"context"
	"errors"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	OrderDesc = "desc"
	OrderAsc  = "asc"
)

func GenSortQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, items []apollo.SortConfItem) ([]elastic.Sorter, error) {
	sorters := make([]elastic.Sorter, 0)

	for _, confItem := range items {
		// 判断condition
		if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &confItem.Condition); !isOk {
			continue
		}

		// 判断 abTest
		if !isHitAbTest(ctx, traceInfo, confItem.AbKey) {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration GenSortQuery check abTestKey is false",
				logkit.String("recallName", recallName), logkit.String("AbKey", confItem.AbKey), logkit.Any("conf", confItem))
			continue
		}

		// 处理 script
		if confItem.Script != nil {
			sorters = append(sorters, handleScriptSort(ctx, traceInfo, recallName, confItem.Script))
			continue
		}

		// 处理 distanceSort
		if confItem.DistanceSort != nil {
			sorters = append(sorters, handleDistanceSort(traceInfo, confItem.DistanceSort))
			continue
		}

		sortDirection := strings.ToLower(confItem.Order)
		if sortDirection == OrderDesc {
			if strings.ToLower(confItem.EsField) == "_geo_distance" {
				t := elastic.NewGeoDistanceSort("location").Point(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude).Desc()
				if confItem.GeoDistanceType != nil && len(*confItem.GeoDistanceType) > 0 {
					t.DistanceType(*confItem.GeoDistanceType)
				}
				sorters = append(sorters, t)
			} else {
				t := elastic.NewFieldSort(confItem.EsField).Desc()
				sorters = append(sorters, t)
			}
		} else if sortDirection == OrderAsc {
			if strings.ToLower(confItem.EsField) == "_geo_distance" {
				t := elastic.NewGeoDistanceSort("location").Point(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude).Asc()
				if confItem.GeoDistanceType != nil && len(*confItem.GeoDistanceType) > 0 {
					t.DistanceType(*confItem.GeoDistanceType)
				}
				sorters = append(sorters, t)
			} else {
				t := elastic.NewFieldSort(confItem.EsField).Asc()
				sorters = append(sorters, t)
			}
		} else {
			logkit.FromContext(ctx).Error("RecallConfiguration GenSortQuery error sort direction",
				logkit.String("recallName", recallName), logkit.String("order", confItem.Order), logkit.String("field", confItem.EsField))
			return sorters, errors.New("RecallConfiguration GenSortQuery error sort direction")
		}
	}
	return sorters, nil
}

func handleScriptSort(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallName string, item *apollo.ScriptItem) *elastic.ScriptSort {

	script := genScript(ctx, traceInfo, recallName, item)
	if item.Order != nil && *item.Order == OrderDesc {
		return elastic.NewScriptSort(script, item.ScriptOrderType).Desc()
	} else if item.Order != nil && *item.Order == OrderAsc {
		return elastic.NewScriptSort(script, item.ScriptOrderType).Asc()
	} else {
		logkit.FromContext(ctx).Error("RecallConfiguration GenSortQuery handleScript error sort direction",
			logkit.String("recallName", recallName), logkit.Any("order", item.Order), logkit.Any("conf", item))
		return elastic.NewScriptSort(script, item.ScriptOrderType).Asc()
	}
}

func handleDistanceSort(traceInfo *traceinfo.TraceInfo, item *apollo.DistanceSortItem) *elastic.GeoDistanceSort {
	return elastic.NewGeoDistanceSort(item.FieldName).DistanceType(item.DistanceType).Point(traceInfo.TraceRequest.Latitude, traceInfo.TraceRequest.Longitude)
}
