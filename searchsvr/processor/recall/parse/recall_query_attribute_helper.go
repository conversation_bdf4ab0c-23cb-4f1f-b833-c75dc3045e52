package parse

import (
	"strings"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

func handleMatchAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.MatchQuery) {
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.Analyzer != nil && len(*boolQueryConf.Analyzer) > 0 {
		q.Analyzer(*boolQueryConf.Analyzer)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}
	if boolQueryConf.Operator != nil && len(*boolQueryConf.Operator) > 0 {
		q.Operator(*boolQueryConf.Operator)
	}
	if boolQueryConf.MinimumShouldMatchStr != nil && len(*boolQueryConf.MinimumShouldMatchStr) > 0 {
		q.MinimumShouldMatch(*boolQueryConf.MinimumShouldMatchStr)
	}

	// 以下是模糊搜索
	if boolQueryConf.Fuzziness != nil && len(*boolQueryConf.Fuzziness) > 0 {
		q.Fuzziness(*boolQueryConf.Fuzziness)
	}
	if boolQueryConf.FuzzyRewrite != nil && len(*boolQueryConf.FuzzyRewrite) > 0 {
		q.FuzzyRewrite(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.CutoffFrequency != nil {
		q.CutoffFrequency(*boolQueryConf.CutoffFrequency)
	}
	if boolQueryConf.PrefixLength != nil {
		q.PrefixLength(*boolQueryConf.PrefixLength)
	}
	if boolQueryConf.Lenient != nil {
		q.Lenient(*boolQueryConf.Lenient)
	}
	if boolQueryConf.ZeroTermsQuery != nil && len(*boolQueryConf.ZeroTermsQuery) > 0 {
		q.ZeroTermsQuery(*boolQueryConf.ZeroTermsQuery)
	}
	if boolQueryConf.FuzzyTranspositions != nil {
		q.FuzzyTranspositions(*boolQueryConf.FuzzyTranspositions)
	}
	if boolQueryConf.MaxExpansions != nil {
		q.MaxExpansions(*boolQueryConf.MaxExpansions)
	}
}

func handleTermAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.TermQuery) {
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}
}

func handleTermsAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.TermsQuery) {
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}
}

func handleMultiMatchQueryAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.MultiMatchQuery) {
	if boolQueryConf.TieBreaker != nil {
		q.TieBreaker(*boolQueryConf.TieBreaker)
	}
	if boolQueryConf.Operator != nil && len(*boolQueryConf.Operator) > 0 {
		q.Operator(*boolQueryConf.Operator)
	}
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.Slop != nil {
		q.Slop(*boolQueryConf.Slop)
	}
	if boolQueryConf.MultiMatchType != nil && len(*boolQueryConf.MultiMatchType) > 0 {
		q.Type(*boolQueryConf.MultiMatchType)
	}
	if boolQueryConf.MinimumShouldMatchStr != nil && len(*boolQueryConf.MinimumShouldMatchStr) > 0 {
		q.MinimumShouldMatch(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}

	// 以下是模糊搜索
	if boolQueryConf.Fuzziness != nil && len(*boolQueryConf.Fuzziness) > 0 {
		q.Fuzziness(*boolQueryConf.Fuzziness)
	}
	if boolQueryConf.FuzzyRewrite != nil && len(*boolQueryConf.FuzzyRewrite) > 0 {
		q.FuzzyRewrite(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.CutoffFrequency != nil {
		q.CutoffFrequency(*boolQueryConf.CutoffFrequency)
	}
	if boolQueryConf.PrefixLength != nil {
		q.PrefixLength(*boolQueryConf.PrefixLength)
	}
	if boolQueryConf.Lenient != nil {
		q.Lenient(*boolQueryConf.Lenient)
	}
	if boolQueryConf.ZeroTermsQuery != nil && len(*boolQueryConf.ZeroTermsQuery) > 0 {
		q.ZeroTermsQuery(*boolQueryConf.ZeroTermsQuery)
	}
	if boolQueryConf.MaxExpansions != nil {
		q.MaxExpansions(*boolQueryConf.MaxExpansions)
	}
}

func handleMatchPhraseAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.MatchPhraseQuery) {
	if boolQueryConf.Slop != nil {
		q.Slop(*boolQueryConf.Slop)
	}
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.Analyzer != nil && len(*boolQueryConf.Analyzer) > 0 {
		q.Analyzer(*boolQueryConf.Analyzer)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}
	if boolQueryConf.ZeroTermsQuery != nil && len(*boolQueryConf.ZeroTermsQuery) > 0 {
		q.ZeroTermsQuery(*boolQueryConf.ZeroTermsQuery)
	}
}

func handleMatchBoolPrefixAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.MatchBoolPrefixQuery) {
	if boolQueryConf.Operator != nil && len(*boolQueryConf.Operator) > 0 {
		q.Operator(*boolQueryConf.Operator)
	}
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.MinimumShouldMatchStr != nil && len(*boolQueryConf.MinimumShouldMatchStr) > 0 {
		q.MinimumShouldMatch(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.Analyzer != nil && len(*boolQueryConf.Analyzer) > 0 {
		q.Analyzer(*boolQueryConf.Analyzer)
	}

	// 以下是模糊搜索
	if boolQueryConf.Fuzziness != nil && len(*boolQueryConf.Fuzziness) > 0 {
		q.Fuzziness(*boolQueryConf.Fuzziness)
	}
	if boolQueryConf.FuzzyRewrite != nil && len(*boolQueryConf.FuzzyRewrite) > 0 {
		q.FuzzyRewrite(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.PrefixLength != nil {
		q.PrefixLength(*boolQueryConf.PrefixLength)
	}
	if boolQueryConf.MaxExpansions != nil {
		q.MaxExpansions(*boolQueryConf.MaxExpansions)
	}
}

func handleShouldMinimumMatch(traceInfo *traceinfo.TraceInfo, boolQueryConf *apollo.RecallQueryBoolItem, boolQuery *elastic.BoolQuery) {
	if boolQueryConf.OuterMatchType == OuterMatchTypeShould {
		if boolQueryConf.MinimumShouldMatchStr != nil && len(*boolQueryConf.MinimumShouldMatchStr) > 0 {
			if strings.Contains(*boolQueryConf.MinimumShouldMatchStr, "{") && strings.Contains(*boolQueryConf.MinimumShouldMatchStr, "}") {
				// todo:  hardcode for vn, delete it when replace Apollo
				if traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
					boolQuery.MinimumShouldMatch("2")
				} else {
					boolQuery.MinimumShouldMatch("1")
				}
			} else {
				boolQuery.MinimumShouldMatch(*boolQueryConf.MinimumShouldMatchStr)
			}
		}
	}
}

func handleQueryStringAttribute(boolQueryConf *apollo.RecallQueryBoolItem, q *elastic.QueryStringQuery) {
	if boolQueryConf.Type != nil {
		q.Type(*boolQueryConf.Type)
	}
	if boolQueryConf.Boost != nil {
		q.Boost(*boolQueryConf.Boost)
	}
	if boolQueryConf.Analyzer != nil && len(*boolQueryConf.Analyzer) > 0 {
		q.Analyzer(*boolQueryConf.Analyzer)
	}
	if boolQueryConf.QueryName != nil && len(*boolQueryConf.QueryName) > 0 {
		q.QueryName(*boolQueryConf.QueryName)
	}
	if boolQueryConf.DefaultField != nil && len(*boolQueryConf.DefaultField) > 0 {
		q.DefaultField(*boolQueryConf.DefaultField)
	}
	if boolQueryConf.DefaultOperator != nil && len(*boolQueryConf.DefaultOperator) > 0 {
		q.DefaultOperator(*boolQueryConf.DefaultOperator)
	}

	// 以下是模糊搜索
	if boolQueryConf.Fuzziness != nil && len(*boolQueryConf.Fuzziness) > 0 {
		q.Fuzziness(*boolQueryConf.Fuzziness)
	}
	if boolQueryConf.FuzzyRewrite != nil && len(*boolQueryConf.FuzzyRewrite) > 0 {
		q.FuzzyRewrite(*boolQueryConf.MinimumShouldMatchStr)
	}
	if boolQueryConf.Lenient != nil {
		q.Lenient(*boolQueryConf.Lenient)
	}
}
