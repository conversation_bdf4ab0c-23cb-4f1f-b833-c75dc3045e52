package parse

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/olivere/elastic/v7"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GenSearchBoolQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, searchConf *apollo.SearchConf, outerFilterConf *apollo.OuterFilterConf, recallCommon *apollo.RecallCommon) ([]elastic.Query, []elastic.Query, []elastic.Query, []elastic.Query, *elastic.FunctionScoreQuery) {
	if searchConf == nil {
		logkit.FromContext(ctx).Error("RecallConfiguration GenSearchBoolQuery searchConf is nil", logkit.String("recallName", recallConfig.RecallLogName))
		return nil, nil, nil, nil, nil
	}

	mustQueries := make([]elastic.Query, 0)
	shouldQueries := make([]elastic.Query, 0)
	mustNotQueries := make([]elastic.Query, 0)
	filterQueries := make([]elastic.Query, 0)
	var fsqQuery *elastic.FunctionScoreQuery

	if searchConf.MustQueries != nil && len(searchConf.MustQueries) > 0 {
		handleQueries(ctx, traceInfo, recallConfig, &searchConf.MustQueries, &mustQueries)
	}
	if searchConf.ShouldQueries != nil && len(searchConf.ShouldQueries) > 0 {
		handleQueries(ctx, traceInfo, recallConfig, &searchConf.ShouldQueries, &shouldQueries)
	}
	if searchConf.MustNotQueries != nil && len(searchConf.MustNotQueries) > 0 {
		handleQueries(ctx, traceInfo, recallConfig, &searchConf.MustNotQueries, &mustNotQueries)
	}
	if searchConf.FilterQueries != nil && len(searchConf.FilterQueries) > 0 {
		handleQueries(ctx, traceInfo, recallConfig, &searchConf.FilterQueries, &filterQueries)
	}
	if searchConf.FunctionScoreConf != nil {
		fsqQuery, _ = GenFsqQuery(ctx, traceInfo, recallConfig, searchConf.FunctionScoreConf)
	}

	// 合并两个地方的 filter 为同一个 filter
	filters, err := GenFilterQuery(ctx, traceInfo, recallConfig, outerFilterConf)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("RecallConfiguration gen filters false",
			logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("filterConf", outerFilterConf))
	} else {
		filterQueries = append(filterQueries, filters...)
	}

	// 公共 filter
	commonFilters, err := GenFilterQueryByRecallCommon(ctx, traceInfo, recallConfig, recallCommon)
	if err == nil && len(commonFilters) > 0 {
		if filterQueries == nil {
			filterQueries = make([]elastic.Query, 0)
		}
		filterQueries = MergeFilters(ctx, filterQueries, commonFilters)
	}

	// 专项处理 my 的清真
	if env.GetCID() == cid.MY {
		req2 := model2.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
		if req2.FilterByHalalType {
			if req2.HalalType == model2.NonHalal {
				filterQueries = append(filterQueries, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if req2.HalalType == model2.Halal {
				filterQueries = append(filterQueries, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
		// 在MY站点,搜索词只要halal type 类型,直接召回对应halal type的门店.
		if req2.RecallByHalalType {
			mustQueries = make([]elastic.Query, 0)
			shouldQueries = make([]elastic.Query, 0)
		}
	}

	return mustQueries, shouldQueries, mustNotQueries, filterQueries, fsqQuery
}

func handleQueries(ctx context.Context, traceInfo *traceinfo.TraceInfo,
	recallConfig *apollo.StoreRecallConfig, items *[]apollo.RecallQueryBoolItem, queries *[]elastic.Query) {

	for _, queryItem := range *items {
		// condition 判断
		if isOk, _ := CheckCondition(ctx, traceInfo.RecallConfigurationDataParams, &queryItem.Condition); !isOk {
			continue
		}

		// 判断 abTest
		if !isHitAbTest(ctx, traceInfo, queryItem.AbKey) {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration handleQueries check abTestKey is false",
				logkit.String("recallName", recallConfig.RecallLogName), logkit.String("AbKey", queryItem.AbKey), logkit.Any("conf", queryItem))
			continue
		}

		// 每个 confItem 都可能解析出来多个 query
		tmpQuery := GenQueryList(ctx, traceInfo, recallConfig, &queryItem)
		if tmpQuery != nil && len(tmpQuery) > 0 {
			for _, q := range tmpQuery {
				if q != nil {
					*queries = append(*queries, q)
				} else {
					logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration handleQuery tmpQuery is nil",
						logkit.String("recallName", recallConfig.RecallLogName), logkit.Any("queryItem", queryItem))
				}
			}
		}
	}
}
