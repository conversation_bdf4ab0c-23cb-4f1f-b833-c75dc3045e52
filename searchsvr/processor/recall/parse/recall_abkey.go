package parse

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func isHitAbTest(ctx context.Context, traceInfo *traceinfo.TraceInfo, abKey string) bool {
	abKey = strings.TrimSpace(abKey)
	if len(abKey) == 0 {
		return true
	}

	if strings.Contains(abKey, "=") {
		// 如果是 key-value 模式，可以自定义召回层
		kv := strings.Split(abKey, "=")
		key := kv[0]
		value := kv[1]

		currentGroup := traceInfo.ABTestGroup.GetABTestGroupValue(key)
		if len(currentGroup) == 0 {
			currentGroup = "group_unknown"
		}
		groups := strings.Split(value, ",")
		if contains(groups, currentGroup) {
			return true
		} else {
			return false
		}
	} else {
		logkit.FromContext(ctx).Error("RecallConfiguration isHitAbTest must be key-value format", logkit.String("abKey", ab<PERSON><PERSON>))
	}
	return false
}

func contains(s []string, searchItem string) bool {
	for _, a := range s {
		if a == searchItem {
			return true
		}
	}
	return false
}
