package recall

import (
	"context"
	"strconv"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 查询菜品索引，并且转为model.DishInfo 返回
func SearchDishES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esBase *es.ESBase, esSearch *es.ESSearch, uid uint64) (uint64, model.DishInfos, error) {
	var cost int64
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseDishListingMultiRecallES)+"_Server_"+esSearch.RecallTypeName), time.Duration(cost)*time.Millisecond)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseDishListingMultiRecallES)+"_Client_"+esSearch.RecallTypeName), time.Since(pt))
	}()

	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreDishTimeOut
	if timeout <= 0 {
		timeout = 1000
	}

	// 如果有配置化定制了 timeout，已定制化的为主
	if esSearch.RecallTimeoutMs > 0 {
		timeout = esSearch.RecallTimeoutMs
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	totalHits, hits, err, cost := esBase.Search(ctx, esSearch, uid)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("SearchDishES esBase.Search failed", logkit.String("cost", time.Since(startTime).String()))
		return 0, nil, err
	}
	dishes := make([]*model.DishInfo, 0, len(hits))
	for _, hit := range hits {
		if hit == nil {
			continue
		}
		id, err := strconv.ParseInt(hit.Id, 10, 64)
		if err != nil {
			continue
		}
		score := float64(0)
		if hit.Score != nil {
			score = *hit.Score
		}
		if score == 0.0 && len(hit.Sort) != 0 {
			score = hit.Sort[0].(float64)
		}
		dish := &model.DishInfo{
			DishId:           uint64(id),
			Score:            score,
			ESScore:          score,
			DishRecallId:     esSearch.RecallId,
			ESMatchedQueries: hit.MatchedQueries,
		}
		// th,my 只有StoreWithDishField 这一路的门店会召回菜品，且需要菜品分数-1
		if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
			dish.Score -= 1.0
			dish.ESScore -= 1.0
		}
		dishes = append(dishes, dish)
	}
	return totalHits, dishes, nil
}
