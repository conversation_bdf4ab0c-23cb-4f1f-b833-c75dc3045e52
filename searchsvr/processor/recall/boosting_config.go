package recall

type BoostingConfig struct {
	<PERSON><PERSON>ieldFactor    float64
	BrandFieldFactor   float64
	KeywordFieldFactor float64
	DishFieldFactor    float64

	RawFactor         float64
	AsciiFactor       float64
	ShinglesFactor    float64
	TriShinglesFactor float64

	MainFieldsTieBreaker       float64
	DishFieldsTieBreaker       float64
	MainFieldsPrefixTieBreaker float64
	DishGroupTieBreaker        float64

	PrefixGroupBoost float64

	DishScoreScriptSource    string
	DishSumScoreScriptSource string

	CategoryFieldFactor                          float64
	LocationFieldFactor                          float64
	LocationTieBreaker                           float64
	LocationGroupBoost                           float64
	CategoryGroupBoost                           float64
	DishGroupBoost                               float64
	ClosedMerchantFactor                         float64
	BaseGroupBoost                               float64
	DishTieBreaker                               float64
	MainFieldsTermMinimumShouldMatch             string
	MainFieldsTermMinimumShouldMatchDishRecall   string
	RelevantSortMainFieldsTermMinimumShouldMatch string
}

func newBoostingConfig() *BoostingConfig {
	return &BoostingConfig{
		NameFieldFactor:    0.4,
		BrandFieldFactor:   0.2,
		KeywordFieldFactor: 0.001,
		DishFieldFactor:    0.32,

		RawFactor:         0.4,
		AsciiFactor:       1.2,
		ShinglesFactor:    1.0,
		TriShinglesFactor: 2.0,

		MainFieldsTieBreaker: 0.1,
		DishFieldsTieBreaker: 0.1,
		DishGroupTieBreaker:  0.2,

		PrefixGroupBoost: 1.0,

		MainFieldsTermMinimumShouldMatch:           "2<-40%",
		MainFieldsTermMinimumShouldMatchDishRecall: "100%",
		DishScoreScriptSource:                      "_score+((1+doc['store_sell_week'].value)/(doc['store_sell_week'].value+1000))",
		DishSumScoreScriptSource:                   "_score*(Math.sqrt(1+doc['store_sell_week'].value))/(Math.sqrt(1+doc['store_sell_week'].value)+1000)",
	}
}
