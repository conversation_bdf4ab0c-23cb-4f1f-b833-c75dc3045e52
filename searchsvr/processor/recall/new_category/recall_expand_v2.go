package new_category

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/gogo/protobuf/proto"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const (
	defaultDistance = 20 //km
)

// 门店新标签体系的扩召回-v2版本
func ToStoreCateExpandRecallRequestV2(r *model2.SearchRequest, traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	if traceInfo.PredictConfig.NewCateExpandRecallSize == 0 {
		logkit.Debug("SearchRequest.ToStoreCateExpandRecallRequestV2 store size = 0, no need expand recall")
		return nil
	}
	categoryTags := traceInfo.QPResult.ProbFilterCategoryIntentions
	if categoryTags == nil || len(categoryTags) == 0 {
		logkit.Debug("SearchRequest.ToStoreCateExpandRecallRequestV2 store has no categoryTags, no need expand recall")
		return nil
	}

	var must []elastic.Query
	var should []elastic.Query
	var fields = []string{"main_category.level2_name.keyword", "sub_category.level2_name.keyword"}
	for _, tag := range categoryTags {
		for _, filed := range fields {
			var filter = elastic.NewTermQuery(filed, tag.Level2Name)
			var constantScoreQuery = elastic.NewConstantScoreQuery(filter)
			constantScoreQuery.Boost(1)
			should = append(should, constantScoreQuery)
		}
	}

	// 组建 filter
	var distance = traceInfo.PredictConfig.NewCateExpandRecallV2DistanceKm
	if distance == 0.0 {
		distance = defaultDistance
	}
	topLeft, bottomRight := model2.CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}

	// 组建 sorter
	sort := []elastic.Sorter{
		// 匹配分>distance>销量>店铺id的优先级进行截断
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
		elastic.NewFieldSort("store_sell_week").Desc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	var store *es.ESSearch
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == model2.NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == model2.Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
		// 在MY站点,搜索词只要halal type 类型,直接召回对应halal type的门店.
		if r.RecallByHalalType {
			must = make([]elastic.Query, 0)
			should = make([]elastic.Query, 0)
		}
	}

	store = es.NewESSearch(es.WithMustQueries(must),
		es.WithSorters(sort),
		es.WithShouldQueries(should),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(uint64(traceInfo.PredictConfig.NewCateExpandRecallSize)),
		es.WithSourceInclude("grabfood_tags", "main_category", "sub_category"),
		es.WithRecallType("ToStoreCateExpandRecallRequestV2"),
		es.WithRecallId("10000"))

	logkit.Debug("SearchRequest.ToStoreCateExpandRecallRequestV2", zap.Any("store", store))
	return store
}
