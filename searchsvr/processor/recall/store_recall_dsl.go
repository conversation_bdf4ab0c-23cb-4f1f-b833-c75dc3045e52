package recall

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/new_category"

	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const (
	withExactTotal = true
)

func (r *EsRecallRequest) BuildRecallDSLByCodeVN(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	if traceInfo.IsDebug {
		traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreIndex")
	}

	defaultRecall := &StoreRecall{}
	defaultRecall.StoreQueries = make([]*es.ESSearch, 0)
	defaultRecall.StoreRecallConfigs = make([]*apollo.StoreRecallConfig, 0)

	// pageSize 和 recallSize 解耦，如果配置了 recallSize 则以 recallSize 为准
	configRecallSize := apollo.SearchApolloCfg.VNRecallSize
	recallSize := uint64(traceInfo.TraceRequest.PageSize)
	if configRecallSize > 0 {
		recallSize = uint64(configRecallSize)
	}

	group := traceInfo.ABTestGroup.GetABTestVal(apollo.SearchApolloCfg.RecallExpVariate)
	recallExpConfMap := apollo.GetRecallExpConf()
	hitRecallExp := false
	if val, ok := recallExpConfMap[group]; ok {
		hitRecallExp = true
		if mem, ok := val["RecallSize"]; ok {
			recallSize = uint64(mem)
		}
		logkit.Debug("hit recall exp", logkit.Any("val", val))
	}
	filterQuery := r.GetFilterQuery(traceInfo)
	mustQuery := make([]elastic.Query, 0)
	mainQuery := r.GetGeneralQuery(hitRecallExp, recallExpConfMap[group], traceInfo)
	if mainQuery != nil {
		mustQuery = append(mustQuery, mainQuery)
	}
	sorts := r.GetSorts(traceInfo)

	store := es.NewESSearch(es.WithMustQueries(mustQuery),
		es.WithFilters(filterQuery), es.WithSorters(sorts),
		es.WithExactTotal(proto.Bool(withExactTotal)),
		es.WithFrom(0), es.WithSize(recallSize),
	)
	store.FilterPath = []string{"hits.hits._id", "hits.hits._score", "hits.hits.sort", "took"}
	store.RecallType = "StoreIndex"
	store.RecallTypeName = "HardcodeRecallIdStoreIndex"
	logkit.Debug("EsRecallRequest.BuildStoreNormalRecallDSL", zap.Any("store", store))

	defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, store)
	timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreTimeOut)
	if timeout == 0 {
		timeout = 400
	}
	defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
		RecallId:            recallconstant.HardcodeRecallIdStoreIndex,
		RecallTypeStr:       "StoreIndex",
		RecallSize:          400,
		RecallTimeoutMs:     proto.Uint64(timeout),
		DishRecallCondition: "1==1",
	})
	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration BuildRecallDSLByCodeVN", logkit.Any("defaultRecall", defaultRecall))
	return defaultRecall
}

func (r *EsRecallRequest) BuildRecallDSLByCodeID(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	defaultRecall := &StoreRecall{}
	defaultRecall.StoreQueries = make([]*es.ESSearch, 0)
	defaultRecall.StoreRecallConfigs = make([]*apollo.StoreRecallConfig, 0)

	searchReq := model2.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
	var storeSearchReq, storeCateExpandReq, storeWithDishRecallSearchReq *es.ESSearch

	storeSearchReq = searchReq.ToStoreESFunctionScoreSearchV2NewCategory(traceInfo)
	storeWithDishRecallSearchReq = searchReq.ToStoreEsWithDishInfoSearchV2NewCategory(traceInfo)
	storeCateExpandReq = new_category.ToStoreCateExpandRecallRequestV2(searchReq, traceInfo)
	if storeSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreIndex")
		}
		storeSearchReq.RecallType = "StoreIndex"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreTimeOut)
		if timeout == 0 {
			timeout = 400
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdStoreIndex,
			RecallTypeStr:       "StoreIndex",
			RecallSize:          400,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "strLen(QPResult_QueryDishIntention) > 0 || bool(QPResult_IsNeedDishRecall) == true",
		})
	}
	if storeWithDishRecallSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreWithDishField")
		}
		storeWithDishRecallSearchReq.RecallType = "StoreWithDishField"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeWithDishRecallSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchDishTimeOut)
		if timeout == 0 {
			timeout = 400
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdStoreWithDishField,
			RecallTypeStr:       "StoreWithDishField",
			RecallSize:          400,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "1==1",
		})
	}
	if storeCateExpandReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreCateExpand")
		}
		storeCateExpandReq.RecallType = "StoreCateExpand"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeCateExpandReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreCateExpandTimeOut)
		if timeout == 0 {
			timeout = 400
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdStoreCateExpand,
			RecallTypeStr:       "StoreCateExpand",
			RecallSize:          200,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "strLen(QPResult_QueryDishIntention) > 0 || bool(QPResult_IsNeedDishRecall) == true",
		})
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration BuildRecallDSLByCodeID", logkit.Any("defaultRecall", defaultRecall))
	return defaultRecall
}

func (r *EsRecallRequest) BuildRecallDSLByCodeIDMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	defaultRecall := &StoreRecall{}
	defaultRecall.StoreQueries = make([]*es.ESSearch, 0)
	defaultRecall.StoreRecallConfigs = make([]*apollo.StoreRecallConfig, 0)

	searchReq := model2.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
	var storeSearchReq, storeWithDishRecallSearchReq *es.ESSearch

	storeSearchReq = searchReq.ToStoreESFunctionScoreSearchForMainSite(traceInfo)
	storeWithDishRecallSearchReq = searchReq.ToStoreEsWithDishInfoSearchMainSite(traceInfo)
	if storeSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdMainSiteStoreIndex")
		}
		storeSearchReq.RecallType = "StoreIndex"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreTimeOutMainSite)
		if timeout == 0 {
			timeout = 200
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdMainSiteStoreIndex,
			RecallTypeStr:       "StoreIndex",
			RecallType:          int32(foodalgo_search.RecallType_StoreIndex),
			RecallSize:          100,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "1==0", // 主站搜索仅需要StoreWithDishField召回菜品
		})
	}
	if storeWithDishRecallSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdMainSiteStoreWithDishField")
		}
		storeWithDishRecallSearchReq.RecallType = "StoreWithDishField"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeWithDishRecallSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreWithDishTimeOutMainSite)
		if timeout == 0 {
			timeout = 200
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdMainSiteStoreWithDishField,
			RecallTypeStr:       "StoreWithDishField",
			RecallType:          int32(foodalgo_search.RecallType_StoreWithDishField),
			RecallSize:          100,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "1==1", // 主站搜索仅需要StoreWithDishField召回菜品
		})
	}
	return defaultRecall
}

func (r *EsRecallRequest) BuildRecallDSLByCodeTHMY(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	defaultRecall := &StoreRecall{}
	defaultRecall.StoreQueries = make([]*es.ESSearch, 0)
	defaultRecall.StoreRecallConfigs = make([]*apollo.StoreRecallConfig, 0)

	searchReq := model2.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
	var storeSearchReq, storeWithDishRecallSearchReq *es.ESSearch

	storeSearchReq = searchReq.ToStoreESFunctionScoreSearchNew(traceInfo)
	storeWithDishRecallSearchReq = searchReq.ToStoreEsWithDishInfoSearchNew(traceInfo)
	if storeSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreIndex")
		}
		storeSearchReq.RecallType = "StoreIndex"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreTimeOut)
		if timeout == 0 {
			timeout = 400
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdStoreIndex,
			RecallTypeStr:       "StoreIndex",
			RecallSize:          400,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "strLen(QPResult_QueryDishIntention) > 0 || bool(QPResult_IsNeedDishRecall) == true",
		})
	}
	if storeWithDishRecallSearchReq != nil {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreFinal("HardcodeRecallIdStoreWithDishField")
		}
		storeWithDishRecallSearchReq.RecallType = "StoreWithDishField"
		defaultRecall.StoreQueries = append(defaultRecall.StoreQueries, storeWithDishRecallSearchReq)
		timeout := uint64(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchDishTimeOut)
		if timeout == 0 {
			timeout = 400
		}
		defaultRecall.StoreRecallConfigs = append(defaultRecall.StoreRecallConfigs, &apollo.StoreRecallConfig{
			RecallId:            recallconstant.HardcodeRecallIdStoreWithDishField,
			RecallTypeStr:       "StoreWithDishField",
			RecallSize:          400,
			RecallTimeoutMs:     proto.Uint64(timeout),
			DishRecallCondition: "1==1",
		})
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration BuildRecallDSLByCodeTHMY", logkit.Any("defaultRecall", defaultRecall))
	return defaultRecall
}

func (r *EsRecallRequest) BuildRecallDSLByConfig(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	if decision.IsSupportRecallConf(traceInfo.HandlerType, traceInfo.PipelineType) == false {
		return nil
	}
	recall := &StoreRecall{}
	recall = recall.BuildStoreRecall(ctx, traceInfo)
	if recall != nil && recall.StoreQueries != nil && len(recall.StoreQueries) > 0 {
		return recall
	}
	return nil
}

func (r *EsRecallRequest) GetGeneralQuery(hitRecallExp bool, recallConfMap map[string]float64, traceInfo *traceinfo.TraceInfo) elastic.Query {
	if len(traceInfo.QueryKeyword) == 0 {
		return nil
	}
	boostingConfig := *newBoostingConfig()

	if r.IsAsciiTerms {
		boostingConfig.BrandFieldFactor = 0.1
	}
	storeNameField := "store_name"
	branchNameGeneralFields := make([]string, 0)
	branchNameShinglesFields := make([]string, 0)
	branchName3ShinglesFields := make([]string, 0)

	branchNameGeneralFields = GetSearchFields("branch_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.BrandFieldFactor*0.5,
		true, false, false, true)

	branchNameShinglesFields = GetSearchFields("branch_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.BrandFieldFactor*0.5,
		false, true, false, true)

	branchName3ShinglesFields = GetSearchFields("branch_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.BrandFieldFactor*0.5,
		false, false, true, true)

	nameGeneralFields := GetSearchFields(storeNameField, r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.NameFieldFactor,
		true, false, false, true)

	brandNameGeneralFields := GetSearchFields("brand_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.BrandFieldFactor,
		true, false, false, true)

	nameShinglesFields := GetSearchFields(storeNameField, r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.NameFieldFactor,
		false, true, false, true)

	brandNameShinglesFields := GetSearchFields("brand_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.BrandFieldFactor,
		false, true, false, true)

	name3ShinglesFields := GetSearchFields(storeNameField, r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.NameFieldFactor,
		false, false, true, true)

	keywordGeneralFields := GetSearchFields("seo_keywords", r.TokenLen, r.IsAsciiTerms, boostingConfig, 0.0,
		true, false, false, false)

	dishNameFields := GetSearchFields("dish_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, boostingConfig.DishFieldFactor,
		true, false, false, false)

	multiMatchName := elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, CombineFields(nameGeneralFields, brandNameGeneralFields, branchNameGeneralFields)...).
		Type("best_fields").TieBreaker(boostingConfig.MainFieldsTieBreaker).MinimumShouldMatch(boostingConfig.MainFieldsTermMinimumShouldMatch)

	matchPhraseName := elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, CombineFields(nameGeneralFields, brandNameGeneralFields, branchNameGeneralFields)...).
		Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker)

	var multiMatchShingleName *elastic.MultiMatchQuery
	var multiMatch3ShingleName *elastic.MultiMatchQuery
	if len(nameShinglesFields) > 0 {
		multiMatchShingleName = elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, CombineFields(nameShinglesFields, brandNameShinglesFields, branchNameShinglesFields)...).
			Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker).Slop(1)
	}
	if len(name3ShinglesFields) > 0 {
		multiMatch3ShingleName = elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, CombineFields(name3ShinglesFields, branchName3ShinglesFields)...).
			Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker).Slop(2)
	}

	keywordMatchName := elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, CombineFields(keywordGeneralFields)...).
		Type("phrase").Slop(2)

	multiMathDishName := elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, dishNameFields...).Type("phrase").Slop(1).TieBreaker(0.1)
	dishScriptsFunc := elastic.NewScriptFunction(GetDishNameScoreScript())
	functionScoreDishName := elastic.NewFunctionScoreQuery().AddScoreFunc(dishScriptsFunc).Boost(1.2).BoostMode("multiply")
	//functionScoreDishName := elastic.NewFunctionScoreQuery().AddScoreFunc(elastic.NewFieldValueFactorFunction().Field("store_sell_week").Modifier("none").Factor(1.0)).Boost(1.2).BoostMode("multiply")
	dishQueries := []elastic.Query{multiMathDishName, functionScoreDishName}
	dishQueryMust := CombineQuery(dishQueries, "AND", 0)

	boolPrefixBoost := 1.0
	var matchBoolPrefixAsciiQuery *elastic.MatchBoolPrefixQuery
	if !r.IsAsciiTerms {
		boolPrefixBoost = 1.2
		matchBoolPrefixAsciiQuery = elastic.NewMatchBoolPrefixQuery(GetFieldName("store_name", 1.0), traceInfo.QueryKeyword).
			Operator("and").Boost(0.4)
	}

	keywordAscii := util2.ToAscii(traceInfo.QueryKeyword)
	matchBoolPrefix := elastic.NewMatchBoolPrefixQuery(GetFieldAsciiName("store_name", 1.0), keywordAscii).
		Operator("and").Boost(boolPrefixBoost)

	queries := []elastic.Query{multiMatchName, matchPhraseName}
	if multiMatchShingleName != nil {
		queries = append(queries, multiMatchShingleName)
	}
	if multiMatch3ShingleName != nil {
		queries = append(queries, multiMatch3ShingleName)
	}
	queries = append(queries, keywordMatchName)
	//queries = append(queries, multiMathDishName)
	queries = append(queries, dishQueryMust)
	if matchBoolPrefixAsciiQuery != nil {
		queries = append(queries, matchBoolPrefixAsciiQuery)
	}
	queries = append(queries, matchBoolPrefix)
	// 指定门店召回. 仅当IsJustFilterStores = true时，不将store_ids 用于指定召回。
	if traceInfo.TraceRequest.GetFilterType().GetIsJustFilterStores() == false {
		storeIds := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()))
		for _, c := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
			storeIds = append(storeIds, c)
		}
		if len(storeIds) > 0 {
			queries = append(queries, elastic.NewTermsQuery("id", storeIds...))
		}
	}
	minimumShouldMatch := 1
	if traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
		rankingScoreQuery := elastic.NewFunctionScoreQuery().AddScoreFunc(elastic.NewScriptFunction(GetRankingScoreScript(hitRecallExp, recallConfMap, traceInfo)))
		minimumShouldMatch += 1
		queries = append(queries, rankingScoreQuery)
	}
	return CombineQuery(queries, "OR", minimumShouldMatch)
}

func (r *EsRecallRequest) GetFilterQuery(traceInfo *traceinfo.TraceInfo) []elastic.Query {
	must := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
	}
	if traceInfo.TraceRequest.Longitude > 0 && traceInfo.TraceRequest.Latitude > 0 {
		must = append(must, elastic.NewGeoDistanceQuery("location").Distance(fmt.Sprintf("%dm", traceInfo.DistanceLimit)).
			Lon(traceInfo.TraceRequest.Longitude).Lat(traceInfo.TraceRequest.Latitude))
	}
	if len(traceInfo.TraceRequest.FilterType.GetCategoryType()) == 1 {
		must = append(must, elastic.NewTermQuery("category_type", traceInfo.TraceRequest.FilterType.GetCategoryType()[0]))
	}
	if len(traceInfo.TraceRequest.FilterType.GetCategoryType()) > 1 {
		var categoryTypeShould []elastic.Query
		for _, category := range traceInfo.TraceRequest.FilterType.GetCategoryType() {
			categoryTypeShould = append(categoryTypeShould, elastic.NewTermQuery("category_type", category))
		}
		must = append(must, elastic.NewBoolQuery().Should(categoryTypeShould...).MinimumShouldMatch("1"))
	}
	l1Categories := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetL1Categories()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetL1Categories() {
		l1Categories = append(l1Categories, c)
	}
	l2Categories := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetL2Categories()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetL2Categories() {
		l2Categories = append(l2Categories, c)
	}
	var categoriesShould []elastic.Query
	if len(l1Categories) > 0 {
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("main_category.level1_id", l1Categories...))
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("sub_category.level1_id", l1Categories...))
	}
	if len(l2Categories) > 0 {
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("main_category.level2_id", l2Categories...))
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("sub_category.level2_id", l2Categories...))
	}
	if len(categoriesShould) > 0 {
		must = append(must, elastic.NewBoolQuery().Should(categoriesShould...).MinimumShouldMatch("1"))
	}
	storeIds := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
		storeIds = append(storeIds, c)
	}
	if len(storeIds) > 0 {
		must = append(must, elastic.NewTermsQuery("id", storeIds...))
	}

	foodyServiceIds := make([]interface{}, 0, len(traceInfo.TraceRequest.FoodyServiceIds))
	for _, c := range traceInfo.TraceRequest.FoodyServiceIds {
		foodyServiceIds = append(foodyServiceIds, c)
	}
	if len(foodyServiceIds) == 1 {
		must = append(must, elastic.NewTermQuery("foody_service_id", foodyServiceIds[0]))
	} else if len(foodyServiceIds) > 1 {
		must = append(must, elastic.NewTermsQuery("foody_service_id", foodyServiceIds...))
	}

	if traceInfo.TraceRequest.FilterType.GetIsPreferredMerchant() {
		must = append(must, elastic.NewTermQuery("is_preferred_merchant", 1))
	}
	if traceInfo.TraceRequest.FilterType.GetIsPartnerMerchant() {
		must = append(must, elastic.NewTermQuery("is_partner_merchant", 1))
	}
	filter := elastic.NewBoolQuery().Must(must...)
	return []elastic.Query{filter}
}

func (r *EsRecallRequest) GetSorts(traceInfo *traceinfo.TraceInfo) []elastic.Sorter {
	var sort []elastic.Sorter
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchIdsWeb || traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_RankingScore {
		sort = append(sort, GetRankingScoreSorter(traceInfo.TraceRequest.Longitude, traceInfo.TraceRequest.Latitude))
		sort = append(sort, elastic.NewFieldSort("id"))
	}
	if traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Nearby {
		sort = append(sort, GetDistanceSorter(traceInfo.TraceRequest.Longitude, traceInfo.TraceRequest.Latitude))
		sort = append(sort, elastic.NewFieldSort("id"))
	}
	return sort
}

// sort = 1,3,4
func GetRankingScoreScript(hitRecallExp bool, recallConfMap map[string]float64, traceInfo *traceinfo.TraceInfo) *elastic.Script {
	if hitRecallExp {
		maxDist := recallConfMap["MaxDist"]
		disWeight := recallConfMap["DisWeight"]
		source := `
			double ranking_score = 0;
            double time_factor_score = 0;
            double distance_score = 0;
            if (doc['ranking_score'].size() > 0)
            	ranking_score = doc['ranking_score'].value;
            if (doc['time_factor_score'].size() > 0)
            	time_factor_score = doc['time_factor_score'].value;
			distance_score = (1.0-Math.min(doc['location'].arcDistance(params.lat,params.lon)/1000.0, params.max_dist)/params.max_dist);
			return (params.dist_weight*distance_score + ranking_score + time_factor_score)/(ranking_score + time_factor_score + params.ranking_pivot)
		`
		script := elastic.NewScript(source).Param("ranking_pivot", 12).
			Param("max_dist", maxDist).
			Param("dist_weight", disWeight).
			Param("lat", traceInfo.UserContext.Latitude).
			Param("lon", traceInfo.UserContext.Longitude)
		return script
	}

	source := `
			double ranking_score = 0;
			double time_factor_score = 0;
			if (doc['ranking_score'].size() > 0)
				ranking_score = doc['ranking_score'].value;
			if (doc['time_factor_score'].size() > 0)
				time_factor_score = doc['time_factor_score'].value;
			return (ranking_score + time_factor_score)/(ranking_score + time_factor_score + params.ranking_pivot)
	`
	script := elastic.NewScript(source).Param("ranking_pivot", 12)
	return script
}

// sort = 2
func GetDistanceSorter(lon, lat float64) elastic.Sorter {
	return elastic.NewGeoDistanceSort("location").DistanceType("arc").Point(lat, lon)
}

// sort = 5
func GetRankingScoreSorter(lon, lat float64) elastic.Sorter {
	source := `
			double ranking_score = 0;
			double time_factor_score = 0;
			int distance_score = 0;
			if (doc['ranking_score'].size() > 0)
				ranking_score = doc['ranking_score'].value;
			if (doc['time_factor_score'].size() > 0)
				time_factor_score = doc['time_factor_score'].value;
			if(doc['location'].size() > 0 && params.lat >= -90 && params.lat <= 90 && params.lon >= -180 && params.lon <= 180 && params.lat!=0 && params.lon!=0)
			{
				double distance=doc['location'].arcDistance(params.lat, params.lon);
				if(distance <= 1000)
					distance_score = 10;
				else if(distance <= 2000)
					distance_score = 9;
				else if(distance <= 3000)
					distance_score = 8;
				else if(distance <= 4000)
					distance_score = 7;
				else
					distance_score = 0;
			}
			return ranking_score + time_factor_score + (50 * distance_score);
	`
	script := elastic.NewScript(source).Lang("painless").Param("lat", lat).Param("lon", lon)
	return elastic.NewScriptSort(script, "number").Desc()
}

func GetDishNameScoreScript() *elastic.Script {
	source := `
			double store_sell_week = 0;
			if (doc['store_sell_week'].size() > 0)
				store_sell_week = doc['store_sell_week'].value;
			return (store_sell_week/(store_sell_week+1000))
	`
	script := elastic.NewScript(source)
	return script
}
