package recall

import (
	"context"
	"encoding/json"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 每一路召回查询订单索引，并且将SearchHits 解析为model.HistoryOrderInfo 返回，包含id+score
func SearchHistoryOrderES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esBase *es.ESBase, esSearch *es.ESSearch) (uint64, []*model.HistoryOrderInfo, error) {
	var cost int64
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseESRecallHistoryOrderES)+"_Server"), time.Duration(cost)*time.Millisecond)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseESRecallHistoryOrderES)+"_Client"), time.Since(pt))
	}()

	// 优先从召回配置化获取timeout
	timeout := esSearch.RecallTimeoutMs
	if timeout <= 0 {
		timeout = apollo.SearchApolloCfg.ClientTimeOutConfig.SearchHistoryOrderTimeOut
		if timeout <= 0 {
			timeout = 1000
		}
	}
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	totalHit, hits, err, cost := esBase.Search(ctx, esSearch, traceInfo.UserId)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("SearchHistoryOrderES esBase.Search failed", logkit.String("cost", time.Since(startTime).String()))
		return 0, nil, err
	}
	size := len(hits)
	orderSearch := make([]*model.HistoryOrderInfo, 0, size)
	for _, hit := range hits {
		if hit == nil {
			continue
		}

		score := float64(0)
		if hit.Score != nil {
			score = *hit.Score
		}
		if score == 0.0 && len(hit.Sort) != 0 {
			score = hit.Sort[0].(float64)
		}

		order := model.HistoryOrderInfo{
			ESScore: score,
		}

		// ES 召回相关字段赋值
		if len(hit.Source) != 0 {
			err = json.Unmarshal(hit.Source, &order)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("historyOrder Unmarshal failed")
			}
		}
		orderSearch = append(orderSearch, &order)
	}
	return totalHit, orderSearch, nil
}
