package recall

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
)

// 多个路径召回(es, redis) 且合并多个队列返回
func StoreRecallFromMultiSource(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	pt := time.Now()
	recallList := make([]*model.RecallStore, 0, 0)
	// 多路召回，未合并
	var esStores, redisStores, vecStores, fsStores []*model.RecallStore
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseStoreRecall, time.Since(pt))
	}()

	var esErr, redisErr, vecErr, fsErr error
	wg := &sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallFromES", func(params ...interface{}) {
		defer wg.Done()

		esStores, esErr = recallStoreFromES(ctx, traceInfo)
		if esErr != nil {
			logkit.FromContext(ctx).WithError(esErr).Error("recallFromES error")
		}
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallFromRedis", func(params ...interface{}) {
		defer wg.Done()

		redisStores, redisErr = recallStoreFromRedis(ctx, traceInfo)
		if redisErr != nil {
			logkit.FromContext(ctx).WithError(redisErr).Error("RecallFromRedis error")
		}
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallFromFS", func(params ...interface{}) {
		defer wg.Done()

		fsStores, fsErr = recallStoreFromFS(ctx, traceInfo)
		if fsErr != nil {
			logkit.FromContext(ctx).WithError(fsErr).Error("recallStoreFromFS error")
		}
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallFromVectorEngine", func(params ...interface{}) {
		defer wg.Done()

		vecStores, vecErr = recallStoreFromVec(ctx, traceInfo)
		if vecErr != nil {
			logkit.FromContext(ctx).WithError(vecErr).Error("RecallFromVectorEngine error")
		}
	})
	wg.Wait()

	if cid.IsVN() {
		if esErr != nil {
			return nil, errno.ErrESUnknown
		}
	} else {
		if esErr != nil && (redisErr != nil || len(redisStores) == 0) && (vecErr != nil || len(vecStores) == 0) && (fsErr != nil || len(fsStores) == 0) {
			return nil, errno.ErrRecallUnknown
		}
	}
	traceInfo.EmbeddingSuppressRule = make([]string, 0, 3)
	withoutVectorEngineRecallSize := 0
	synonymsRewriteRecallSize := 0
	for _, esStore := range esStores {
		if esStore != nil && len(esStore.Stores) > 0 {
			withoutVectorEngineRecallSize += len(esStore.Stores)
			recallList = append(recallList, esStore)
		}
		if esStore != nil && esStore.RecallTypeStr == foodalgo_search.RecallType_SynonymsRewriteRecall.String() {
			synonymsRewriteRecallSize = len(esStore.Stores)
		}
	}
	for _, redisStore := range redisStores {
		if redisStore != nil && len(redisStore.Stores) > 0 {
			withoutVectorEngineRecallSize += len(redisStore.Stores)
			recallList = append(recallList, redisStore)
		}
	}
	for _, fsStore := range fsStores {
		if fsStore != nil && len(fsStore.Stores) > 0 {
			withoutVectorEngineRecallSize += len(fsStore.Stores)
			recallList = append(recallList, fsStore)
		}
	}
	// 判断 是否需要打压向量召回
	vectorEngineRecallConfig := abtest.GetVectorEngineRecallConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	// 规则 3：如果除Embedding recall以外，所有召回都没有返回结果，则不返回Embedding recall结果。
	if vectorEngineRecallConfig.IsSuppressRule3 && withoutVectorEngineRecallSize == 0 {
		traceInfo.EmbeddingSuppressRule = append(traceInfo.EmbeddingSuppressRule, "3")
	}

	// 规则 2：如果Query的NER结果中只有UNKNOWN实体 且 Synonym recall无返回结果，则不返回Embedding recall结果。
	if vectorEngineRecallConfig.IsSuppressRule2 && traceInfo.QPResult.OnlyUnknownNer && synonymsRewriteRecallSize == 0 {
		traceInfo.EmbeddingSuppressRule = append(traceInfo.EmbeddingSuppressRule, "2")
	}

	for _, vcStore := range vecStores {
		//规则 1：如果Embedding recall的返回结果数目小于设定阈值，则不返回Embedding recall结果
		if vectorEngineRecallConfig.IsSuppressRule1 && vcStore != nil && len(vcStore.Stores) < vectorEngineRecallConfig.SuppressRecallResultThreshold {
			traceInfo.EmbeddingSuppressRule = append(traceInfo.EmbeddingSuppressRule, "1")
			continue
		}
	}
	if len(traceInfo.EmbeddingSuppressRule) == 0 {
		for _, vcStore := range vecStores {
			if vcStore != nil && len(vcStore.Stores) > 0 {
				recallList = append(recallList, vcStore)
			}
		}
	}
	return recallList, nil
}

func DishRecallWithStore(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) (model.StoreInfos, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishRecallV1, time.Since(pt))
	}()
	var dishInfos model.DishInfos
	if traceInfo.IsDebug {
		storeIds := make([]uint64, 0, len(storeInfos))
		for _, info := range storeInfos {
			storeIds = append(storeIds, info.StoreId)
		}
		logger.MyDebug(ctx, traceInfo.IsDebug, "before dish recall", zap.Any("store_ids", storeIds))
	}
	if isDishSearcher() {
		dishSearcherConfig := abtest.GetDishSearcherRecallConfig(traceInfo.AbParamClient)
		if dishSearcherConfig.IsNewDishRecall {
			dishInfos, _ = DishSearchWithDishAndCatalog(ctx, traceInfo, storeInfos, dishSearcherConfig)
		} else {
			dishInfos, _ = DishSearchWithQuery(ctx, traceInfo, storeInfos)
		}
	} else {
		dishes, _ := RecallDishFromES(ctx, traceInfo, storeInfos, true)
		if len(dishes) == 0 {
			return storeInfos, nil
		}
		dishInfos, _ = filling.DishMetaFilling(ctx, traceInfo, dishes)
	}
	storeInfos = storeMergeDish(storeInfos, dishInfos)
	return storeInfos, nil
}

func RecallDishFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos, isFromConfigurationInTHMY bool) (model.DishInfos, error) {
	pt := time.Now()
	dishes := make([]*model.DishInfo, 0)
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishRecallDishESIndex, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromES, len(dishes))
	}()
	storeIds := make([]uint64, 0, len(storeInfos))
	for _, storeInfo := range storeInfos {
		if storeInfo.IsNeedDishRecall && len(storeInfo.DishInfos) == 0 {
			storeIds = append(storeIds, storeInfo.StoreId) // 非VN地区看IsNeedDishRecall标识
		}
	}
	if len(storeIds) == 0 {
		return dishes, nil
	}
	batchSize := 150
	if apollo.SearchApolloCfg.NewDishRecallBatch != 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatch
	}
	total := len(storeIds) / batchSize
	if len(storeIds)%batchSize > 0 {
		total += 1
	}
	dishRecallList := make([]model.DishInfos, total, total)
	wg := sync.WaitGroup{}
	wg.Add(total)
	for i := 0; i < total; i++ {

		var tempStoreId []uint64
		if i+1 < total {
			tempStoreId = storeIds[i*batchSize : (i+1)*batchSize]
		} else {
			tempStoreId = storeIds[i*batchSize:]
		}

		goroutine.WithGo(ctx, "dishES.Search", func(params ...interface{}) {
			defer wg.Done()
			var err error
			param := params[0].([]interface{})
			index := param[0].(int)
			temp := param[1].([]uint64)
			if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMainSite {
				dishRecallList[index], err = searchMainSiteDish(ctx, traceInfo, temp)
			} else {
				dishRecallList[index], err = searchDish(ctx, traceInfo, temp, isFromConfigurationInTHMY)
			}

			if err != nil {
				traceInfo.AddErrorToTraceInfo(err)
			}
		}, i, tempStoreId)
	}
	wg.Wait()
	for _, dishList := range dishRecallList {
		if len(dishList) > 0 {
			dishes = append(dishes, dishList...)
		}
	}
	return dishes, nil
}

func searchDish(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64, isFromConfigurationInTHMY bool) (model.DishInfos, error) {
	var dishes model.DishInfos
	var err error

	hardCodeRecallName := fmt.Sprintf("%s_%s", "HardcodeRecallESDishIndex", recallconstant.HardcodeRecallIdESDishIndex)
	switch env.GetCID() {
	case cid.TH, cid.MY:
		if isFromConfigurationInTHMY && traceInfo.ABTestGroup.IsHitDishRecallConfiguration() {
			dishRecall := &DishRecall{}
			dishRecall = dishRecall.BuildDishRecall(ctx, traceInfo, storeIds)
			if dishRecall != nil && len(dishRecall.DishQueries) > 0 {
				// 约定只拿一个菜品召回
				dishReq := dishRecall.DishQueries[0]
				dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
				for _, d := range dishes {
					d.DishRecallId = "ConfigurationDishIndex"
				}
				return dishes, err
			}
		}
		// 如果配置化拿不到，就hardcode
		searchReq := model.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
		dishReq := searchReq.ToDishSecondaryRecallNew(traceInfo, storeIds)
		if dishReq != nil {
			if traceInfo.IsDebug {
				traceInfo.AppendRecallsDishFinal(hardCodeRecallName)
			}
		}
		dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
		for _, d := range dishes {
			d.DishRecallId = "OriginalDishIndex"
		}
		return dishes, err

	case cid.VN:
		// 召回配置化
		if decision.IsSupportRecallConf(traceInfo.HandlerType, traceInfo.PipelineType) && traceInfo.ABTestGroup.IsHitDishRecallConfiguration() {
			dishRecall := &DishRecall{}
			dishRecall = dishRecall.BuildDishRecall(ctx, traceInfo, storeIds)
			if dishRecall != nil && len(dishRecall.DishQueries) > 0 {
				// 约定只拿一个菜品召回
				dishReq := dishRecall.DishQueries[0]
				dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
				return dishes, err
			}
		}

		searchReq := NewESRecallRequest(traceInfo)
		dishReq := searchReq.BuildDishRecallDSL(traceInfo, storeIds)
		if dishReq != nil {
			if traceInfo.IsDebug {
				traceInfo.AppendRecallsDishFinal(hardCodeRecallName)
			}
		}
		dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
	default:
		// id dishsearcher 已经在召菜入口处拦截，不会走到这里
		if traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
			searchReqID := model.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
			dishReq := searchReqID.ToDishSecondaryRecall(traceInfo, storeIds)
			if dishReq != nil {
				if traceInfo.IsDebug {
					traceInfo.AppendRecallsDishFinal(hardCodeRecallName)
				}
			}
			dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
		} else if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
			searchReqID := model.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
			dishReq := searchReqID.ToDishSecondaryRecallForMainSite(traceInfo, storeIds)
			if dishReq != nil {
				if traceInfo.IsDebug {
					mainSiteHardCodeRecallName := fmt.Sprintf("%s_%s", "HardcodeRecallMainSiteDishIndex", recallconstant.HardcodeRecallIdMainSiteDishIndex)
					traceInfo.AppendRecallsDishFinal(mainSiteHardCodeRecallName)
				}
			}
			dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
		}
	}
	return dishes, err
}

func searchMainSiteDish(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	var err error
	dishRecall := &DishRecall{}
	dishRecall = dishRecall.BuildDishRecallV2(ctx, traceInfo, storeIds)
	if len(dishRecall.DishQueries) == 0 {
		searchReqID := model.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)
		dishReq := searchReqID.ToDishSecondaryRecallForMainSite(traceInfo, storeIds)
		if dishReq != nil {
			dishRecall.DishQueries = append(dishRecall.DishQueries, dishReq)
		}
	}

	// 多路菜品召回
	dishesList, err := RecallMultiDishesFromES(ctx, traceInfo, dishRecall.DishQueries)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("searchMainSiteDish failed")
		return nil, err
	}

	// 去重merge
	var dishes model.DishInfos
	dishMap := make(map[uint64]*model.DishInfo, 128)
	for _, _dishes := range dishesList {
		if _dishes == nil || len(_dishes.Dishes) == 0 {
			continue
		}
		for _, dishItem := range _dishes.Dishes {
			if dishItem == nil {
				continue
			}
			// 优先级赋值
			dishItem.RecallPriority = _dishes.RecallPriority
			dishItem.DishRecallPriorities = []int{_dishes.RecallPriority}
			// 召回类型赋值
			dishItem.DishRecallType = _dishes.RecallTypeStr

			// 如果es的子查询明确有name，就用这个name来做recallId
			if len(dishItem.ESMatchedQueries) > 0 {
				for _, matchedQuery := range dishItem.ESMatchedQueries {
					dishItem.DishRecallIds = append(dishItem.DishRecallIds, fmt.Sprintf("%s_%s", _dishes.RecallId, matchedQuery))
					dishItem.DishRecallTypes = append(dishItem.DishRecallTypes, fmt.Sprintf("%s_%s", _dishes.RecallTypeStr, matchedQuery))
				}
			} else if len(_dishes.RecallId) > 0 {
				// 否则用配置化的recallId来做recallId
				dishItem.DishRecallIds = append(dishItem.DishRecallIds, _dishes.RecallId)
				dishItem.DishRecallTypes = append(dishItem.DishRecallTypes, _dishes.RecallTypeStr)
			}

			// es内部去重，重复的会合并recallIds
			if _, exists := dishMap[dishItem.DishId]; !exists {
				dishMap[dishItem.DishId] = dishItem
				dishes = append(dishes, dishItem)
			} else {
				dishMap[dishItem.DishId].DishRecallIds = append(dishMap[dishItem.DishId].DishRecallIds, dishItem.DishRecallIds...)
				dishMap[dishItem.DishId].DishRecallTypes = append(dishMap[dishItem.DishId].DishRecallTypes, dishItem.DishRecallTypes...)
				dishMap[dishItem.DishId].DishRecallPriorities = append(dishMap[dishItem.DishId].DishRecallPriorities, dishItem.RecallPriority)

				// 优先级覆盖，越低越好
				if dishMap[dishItem.DishId].RecallPriority > dishItem.RecallPriority {
					dishMap[dishItem.DishId].RecallPriority = dishItem.RecallPriority
				}
			}
		}
	}
	return dishes, err
}

func isDishSearcher() bool {
	if env.GetCID() != cid.ID && env.GetCID() != cid.XX {
		return false
	}
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeDishSearcher) {
		reporter.ReportClientRequestError(1, "dishsearcher_downgrade", "true")
		return false
	}
	reporter.ReportClientRequestError(1, "dishsearcher_downgrade", "0")
	return true
}

func storeMergeDish(storeInfos model.StoreInfos, dishInfos model.DishInfos) model.StoreInfos {
	if len(dishInfos) == 0 {
		return storeInfos
	}
	// 合并
	storeMap := storeInfos.StoreInfoMap()
	dishMap := dishInfos.StoreDishInfosMap()
	for storeID, store := range storeMap {
		// todo: th，my delete， 2024年04月15日16:19:491，因为th，my会同时走老挂菜和新的二段式挂菜，这导致merge会有两个dishIndex，暂时加上这个if判断来控制只有一个dishIndex
		if len(store.DishInfos) > 0 {
			continue
		}
		if dishes, ok := dishMap[storeID]; ok && len(dishes) > 0 {
			store.DishInfos = dishes
			store.RecallTypes = append(store.RecallTypes, foodalgo_search.RecallType_DishIndex.String())
			store.RecallTypeAndIds = append(store.RecallTypeAndIds, fmt.Sprintf("%s_1", foodalgo_search.RecallType_DishIndex.String()))
			store.RecallScores = append(store.RecallScores, dishes[0].Score)
		}
	}
	return storeInfos
}

// vn mart门店下搜菜
func QueryStoreDishFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo) (model.DishInfos, error) {
	pt := time.Now()
	dishes := make([]*model.DishInfo, 0)
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreDishRecallDishes, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreDishLenRecallFromES, len(dishes))
	}()
	searchReq := NewESRecallRequest(traceInfo)
	dishReq := searchReq.BuildStoreDishesRecallDSL(traceInfo)
	dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
	return dishes, err
}

// vn mart门店下类目查询
func QueryL3CategoriesFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]uint32, error) {
	pt := time.Now()
	dishes := make([]*model.DishInfo, 0)
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreDishRecallCategories, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreCategoryLenRecallFromES, len(dishes))
	}()
	searchReq := NewESRecallRequest(traceInfo)
	categoryReq := searchReq.BuildStoreDishesL3CategoryRecallDSL(traceInfo)
	_, categories, err := RecallL3CategoryFromES(ctx, traceInfo, categoryReq)
	return categories, err
}

// 佣金门店搜索
func QueryStoresWithAffiliateFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo) (model.StoreInfos, error) {
	pt := time.Now()
	stores := make([]*model.StoreInfo, 0)
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseESRecallStoresAffiliate, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenRecallFromESAffiliate, len(stores))
	}()
	searchReq := NewESRecallRequest(traceInfo)
	storeReq := searchReq.BuildStoresAffiliateRecallDSL(traceInfo)
	stores, err = RecallStoreFromES(ctx, traceInfo, storeReq)
	return stores, err
}

// 佣金门店二次挂菜
func QueryDishesWithStoresWithAffiliateFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) (model.StoreInfos, error) {
	pt := time.Now()
	dishes := make([]*model.DishInfo, 0)
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseESRecallStoresAffiliate, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreDishLenRecallFromESAffiliate, len(dishes))
	}()
	storeIds := stores.StoreIDs()
	if len(storeIds) == 0 {
		return stores, nil
	}
	batchSize := 150
	if apollo.SearchApolloCfg.DishRecallAffiliateBatch != 0 {
		batchSize = apollo.SearchApolloCfg.DishRecallAffiliateBatch
	}
	total := len(stores) / batchSize
	if len(stores)%batchSize > 0 {
		total += 1
	}
	dishRecallList := make([]model.DishInfos, total, total)
	wg := sync.WaitGroup{}
	wg.Add(total)
	for i := 0; i < total; i++ {
		var tempStoreId []uint64
		if i+1 < total {
			tempStoreId = storeIds[i*batchSize : (i+1)*batchSize]
		} else {
			tempStoreId = storeIds[i*batchSize:]
		}
		goroutine.WithGo(ctx, "QueryDishesWithStoresWithAffiliateFromES", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			tempIds := param[1].([]uint64)
			searchReq := NewESRecallRequest(traceInfo)
			dishReq := searchReq.BuildDishesWithStoresAffiliateRecallDSL(traceInfo, tempIds)
			dishRecallList[index], _ = RecallDishesFromES(ctx, traceInfo, dishReq)
		}, i, tempStoreId)
	}
	wg.Wait()
	for _, dishList := range dishRecallList {
		if len(dishList) > 0 {
			dishes = append(dishes, dishList...)
		}
	}
	if len(dishes) == 0 {
		return stores, nil
	}
	dishInfos, _ := filling.DishMetaFilling(ctx, traceInfo, dishes)
	stores = storeMergeDish(stores, dishInfos)
	return stores, err
}

// 佣金门店同品牌聚合
func QuerySameBrandCountAffiliateFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo, brandIds []uint64) (model.StoreInfos, error) {
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseESRecallBrandCountAffiliate, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.BrandLenRecallFromESAffiliate, len(brandIds))
	}()
	nBrandIds := len(brandIds)
	if nBrandIds == 0 {
		return model.StoreInfos{}, nil
	}
	searchReq := NewESRecallRequest(traceInfo)
	brandCountReq := searchReq.BuildSameBrandStoresAffiliateRecallDSL(traceInfo, brandIds)
	storeInfos, err := RecallStoreFromES(ctx, traceInfo, brandCountReq)
	return storeInfos, err
}

// 佣金门店下搜菜
func QueryDishesWithAffiliateFromES(ctx context.Context, traceInfo *traceinfo.TraceInfo) (model.DishInfos, error) {
	pt := time.Now()
	dishes := make([]*model.DishInfo, 0)
	var err error
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseESRecallDishesAffiliate, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreDishLenRecallFromESAffiliate, len(dishes))
	}()
	searchReq := NewESRecallRequest(traceInfo)
	dishReq := searchReq.BuildDishesAffiliateRecallDSL(traceInfo)
	dishes, err = RecallDishesFromES(ctx, traceInfo, dishReq)
	return dishes, err
}
