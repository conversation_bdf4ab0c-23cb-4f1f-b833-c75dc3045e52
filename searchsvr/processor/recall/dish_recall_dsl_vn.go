package recall

import (
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const DishRecallSize = 2

func (r *EsRecallRequest) BuildDishRecallDSL(traceInfo *traceinfo.TraceInfo, storeIds []uint64) *model2.ESSearch {
	filterQuery := []elastic.Query{elastic.NewTermsQuery("store_id", util2.ConvertUint64ListToInterfaceSlice(storeIds)...)}

	mustQuery := make([]elastic.Query, 0)
	mainQuery := r.GetGeneralDishQuery(traceInfo)
	if mainQuery != nil {
		mustQuery = append(mustQuery, mainQuery)
	}
	agg := r.GetTermsAggregations(traceInfo)
	store := model2.NewESSearch(model2.WithTermsAggregation(agg), model2.WithMustQueries(mustQuery), model2.WithFilters(filterQuery),
		model2.WithExactTotal(proto.Bool(withExactTotal)), model2.WithFrom(0), model2.WithSize(0),
		model2.WithRecallType("HardCodeVnDishRecall"),
		model2.WithRecallId(recallconstant.HardcodeRecallIdESDishIndex),
	)
	logkit.Debug("EsRecallRequest.BuildIntentionSearch", zap.Any("store", store))
	return store
}

func (r *EsRecallRequest) GetGeneralDishQuery(traceInfo *traceinfo.TraceInfo) elastic.Query {
	if len(r.KeywordQuery) == 0 {
		return nil
	}
	boostingConfig := *newBoostingConfig()
	boostingConfig.AsciiFactor = 0.5
	if r.IsAsciiTerms {
		boostingConfig.BrandFieldFactor = 0.1
	}
	nameGeneralFields := GetSearchFields("dish_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, 10,
		true, false, false, true)

	nameShinglesFields := GetSearchFields("dish_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, 10,
		false, true, false, true)

	name3ShinglesFields := GetSearchFields("dish_name", r.TokenLen, r.IsAsciiTerms, boostingConfig, 10,
		false, false, true, true)

	multiMatchName := elastic.NewMultiMatchQuery(r.KeywordQuery, CombineFields(nameGeneralFields)...).
		Type("best_fields").TieBreaker(boostingConfig.MainFieldsTieBreaker).MinimumShouldMatch(boostingConfig.MainFieldsTermMinimumShouldMatchDishRecall)

	matchPhraseName := elastic.NewMultiMatchQuery(r.KeywordQuery, CombineFields(nameGeneralFields)...).
		Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker)

	var multiMatchShingleName *elastic.MultiMatchQuery
	var multiMatch3ShingleName *elastic.MultiMatchQuery
	if len(nameShinglesFields) > 0 {
		multiMatchShingleName = elastic.NewMultiMatchQuery(r.KeywordQuery, CombineFields(nameShinglesFields)...).
			Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker).Slop(1)
	}
	if len(name3ShinglesFields) > 0 {
		multiMatch3ShingleName = elastic.NewMultiMatchQuery(r.KeywordQuery, CombineFields(name3ShinglesFields)...).
			Type("phrase").TieBreaker(boostingConfig.MainFieldsTieBreaker).Slop(2)
	}

	boolPrefixBoost := 1.0
	var matchBoolPrefixAsciiQuery *elastic.MatchBoolPrefixQuery
	if !r.IsAsciiTerms {
		boolPrefixBoost = 0.5
		matchBoolPrefixAsciiQuery = elastic.NewMatchBoolPrefixQuery(GetFieldName("dish_name", 1.0), r.KeywordQuery).
			Operator("and").Boost(10)
	}
	matchBoolPrefix := elastic.NewMatchBoolPrefixQuery(GetFieldAsciiName("dish_name", 1.0), r.KeywordAscii).
		Operator("and").Boost(boolPrefixBoost)

	queries := []elastic.Query{multiMatchName, matchPhraseName}
	if multiMatchShingleName != nil {
		queries = append(queries, multiMatchShingleName)
	}
	if multiMatch3ShingleName != nil {
		queries = append(queries, multiMatch3ShingleName)
	}
	if matchBoolPrefixAsciiQuery != nil {
		queries = append(queries, matchBoolPrefixAsciiQuery)
	}
	queries = append(queries, matchBoolPrefix)
	rankingScoreQuery := elastic.NewFunctionScoreQuery().AddScoreFunc(elastic.NewScriptFunction(GetDishRecallRankingScoreScript()))
	queries = append(queries, rankingScoreQuery)

	// 原词：caffee 改写词： coffee , cafe, capi
	// 实验组要和新增的 rewrite recall 实验组保持一致
	if traceInfo.ABTestGroup.IsHitVNDishRecallWithRewrite() {
		for _, rewriteQuery := range r.RewriteKeywordList {
			rewriteNameGeneralFields := []string{"dish_name^3", "dish_name.ascii^1.5"}
			rewriteMultiMatchName := elastic.NewMultiMatchQuery(rewriteQuery, rewriteNameGeneralFields...).
				Type("best_fields").TieBreaker(boostingConfig.MainFieldsTieBreaker).MinimumShouldMatch(boostingConfig.MainFieldsTermMinimumShouldMatchDishRecall)
			queries = append(queries, rewriteMultiMatchName)
		}
	}

	return CombineQuery(queries, "OR", 2)
}

func (r *EsRecallRequest) GetTermsAggregations(traceInfo *traceinfo.TraceInfo) *elastic.TermsAggregation {
	topHitSize := DishRecallSize
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchIdsDishes {
		topHitSize = 10
	}
	aggTopHist := elastic.NewTopHitsAggregation().Size(topHitSize).FetchSource(false).
		Sort("_score", false).Sort("id", true)
	// 创建Terms桶聚合, top_dishes 这个聚合名称需要与结果解析时的json 保持一致，见 searchsvr/dao/es.go
	aggs := elastic.NewTermsAggregation().Field("store_id").Size(1000).SubAggregation("top_dishes", aggTopHist)
	return aggs
}

func GetDishRecallRankingScoreScript() *elastic.Script {
	source := `
            double sales_volume = 0;
            if (doc['sales_volume'].size() > 0)
                sales_volume = doc['sales_volume'].value;
            return Math.log(sales_volume+3)
    `
	script := elastic.NewScript(source)
	return script
}
