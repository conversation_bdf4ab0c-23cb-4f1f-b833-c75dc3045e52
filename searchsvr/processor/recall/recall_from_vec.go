package recall

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_vector_engine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/gogo/protobuf/proto"
)

func recallStoreFromVec(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	recallConfig := parse.GetStoreRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainStore, recallconstant.RecallDataSourceVectorEngine)
	if recallConfig == nil || len(recallConfig.StoreRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallStoreFromVec recall config is nil")
		}
		return nil, nil
	}

	recallConfig.StoreRecalls = parse.DoFilterCondition(ctx, traceInfo, recallConfig.StoreRecalls, recallconstant.RecallDomainStore)
	if len(recallConfig.StoreRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallStoreFromVec recall config is nil after filter conditions")
		}
		return nil, nil
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreRecallMultiSourceVectorEngine, time.Since(pt))
	}()

	parallel := len(recallConfig.StoreRecalls)
	if parallel == 0 {
		return []*model.RecallStore{}, nil
	}
	errs := make(chan error, parallel)
	recallList := make([]*model.RecallStore, parallel)

	if traceInfo.IsDebug {
		for _, x := range recallConfig.StoreRecalls {
			traceInfo.AppendRecallsStoreFinal(fmt.Sprintf("%s_%s", x.RecallName, x.RecallId))
		}
	}

	wg := &sync.WaitGroup{}
	wg.Add(parallel)
	for i, req := range recallConfig.StoreRecalls {
		go func(index int, recallConfig *apollo.StoreRecallConfig) {
			startTime := time.Now()
			recallStore := &model.RecallStore{}
			defer wg.Done()
			defer func() {
				traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(fmt.Sprintf("StoreVecRecall_%s_%s", recallConfig.RecallTypeStr, recallConfig.RecallId)), time.Since(startTime))
				if e := recover(); e != nil {
					logkit.Error("search vec panic", logkit.String("recall type", recallConfig.RecallTypeStr), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
					reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
						Key: "method",
						Val: "search-vec-store-" + recallConfig.RecallTypeStr,
					})
				}
			}()

			// 超时时间设置
			timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.SearchVectorEngineRecallTimeOut
			if recallConfig.RecallTimeoutMs != nil && *recallConfig.RecallTimeoutMs > 0 {
				timeout = int(*recallConfig.RecallTimeoutMs)
			}
			vecCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
			defer cancel()

			// 创建请求并调用
			tempReq := createReq(traceInfo, recallConfig)
			rsp, err := integrate.VectorEngineClient.Search(vecCtx, tempReq)
			if traceInfo.IsDebug {
				reqStr, _ := json.Marshal(tempReq)
				rspStr, _ := json.Marshal(rsp)
				logkit.FromContext(ctx).Info("recallStoreFromVec", logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
			}
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
				logkit.FromContext(ctx).Error("recallStoreFromVec errCode to get vector engine", logkit.Any("err", err))
				errs <- err
				return
			}

			// 包装rsp
			vecRecallStores := warpStoreInfos(recallConfig, rsp)
			recallStore = &model.RecallStore{
				Stores:        vecRecallStores,
				TotalHits:     uint64(len(vecRecallStores)),
				RecallTypeStr: recallConfig.RecallTypeStr,
				RecallId:      recallConfig.RecallId,
			}
			recallList[index] = recallStore
		}(i, req)
	}
	wg.Wait()
	if len(errs) == parallel {
		logkit.FromContext(ctx).Error("recallStoreFromVec all failed")
		return nil, <-errs
	}
	return recallList, nil
}

func warpStoreInfos(recallConfig *apollo.StoreRecallConfig, rsp *o2oalgo_vector_engine.RecallResponse) model.StoreInfos {
	// 如果设置了替换分数，则抛弃相似度分数，改为配置的分数作为召回score
	defaultReplaceScore := float64(recallConfig.VectorEngineRecallConfig.DefaultReplaceScore)

	// 如果设置了过滤项目：距离，相关性分数，需要同步埋点
	var filterByRelevanceScore float64
	var filterByDistance float64
	if recallConfig.FilterByRelevanceScoreSwitch {
		filterByRelevanceScore = recallConfig.FilterByRelevanceScore
	} else {
		filterByRelevanceScore = model.DefaultNonConfiguredPRelevanceScore
	}
	if recallConfig.FilterByDistanceSwitch {
		filterByDistance = recallConfig.FilterByDistance
	}

	vecRecallStores := make(model.StoreInfos, 0)
	for _, item := range rsp.GetResults().GetItems() {
		if item == nil {
			continue
		}
		score := float64(item.GetScore())
		if defaultReplaceScore > 0 {
			score = defaultReplaceScore
		}
		vecRecallStores = append(vecRecallStores, &model.StoreInfo{
			StoreId:                           item.GetItemId(),
			RecallTypes:                       []string{recallConfig.RecallTypeStr},
			RecallTypeAndIds:                  []string{fmt.Sprintf("%s_%s", recallConfig.RecallTypeStr, recallConfig.RecallId)},
			IsNeedDishRecall:                  true,
			ESScore:                           score,
			Score:                             score,
			RecallScores:                      []float64{score},
			FilterRecallConfigPRelevanceScore: filterByRelevanceScore,
			FilterRecallConfigDistance:        filterByDistance,
		})
	}
	return vecRecallStores
}

func createReq(traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig) *o2oalgo_vector_engine.RecallRequest {
	tempReq := &o2oalgo_vector_engine.RecallRequest{
		Scene:                   proto.String("search"),
		UserId:                  proto.Uint64(traceInfo.UserId),
		PublishId:               proto.String(traceInfo.TraceRequest.PublishId),
		Latitude:                proto.Float32(float32(traceInfo.TraceRequest.Latitude)),
		Longitude:               proto.Float32(float32(traceInfo.TraceRequest.Longitude)),
		ModelName:               proto.String(recallConfig.VectorEngineRecallConfig.ModelName),
		IndexName:               proto.String(recallConfig.VectorEngineRecallConfig.IndexName),
		Limit:                   proto.Int32(int32(recallConfig.RecallSize)),
		RecallFilters:           createWhereColumns(traceInfo, recallConfig.VectorEngineRecallConfig),
		IsDebug:                 proto.Bool(traceInfo.IsDebug),
		FeatureKey:              recallConfig.VectorEngineRecallConfig.ModelPlatformFeatureKeyList,
		UserCtx:                 traceInfo.ContextFeatureBytes,
		ScoreFilterThreshold:    proto.Float32(recallConfig.VectorEngineRecallConfig.ScoreFilterThreshold),
		DistanceFilterThreshold: proto.Float32(recallConfig.VectorEngineRecallConfig.DistanceFilterThreshold),
	}
	return tempReq
}

func createWhereColumns(traceInfo *traceinfo.TraceInfo, vectorEngineRecallConfig *apollo.VectorEngineConfig) []*o2oalgo_vector_engine.FilterColumn {
	eq := o2oalgo_vector_engine.ExpressionOp_EQ
	in := o2oalgo_vector_engine.ExpressionOp_IN
	interSect := o2oalgo_vector_engine.ExpressionOp_INTERSECT

	_int64 := o2oalgo_vector_engine.ColumnType_COLUMN_INT64
	_int32List := o2oalgo_vector_engine.ColumnType_COLUMN_INT32_LIST
	_geohash5 := o2oalgo_vector_engine.ColumnType_COLUMN_GEOHASH5

	whereColumns := []*o2oalgo_vector_engine.FilterColumn{
		{
			ColumnName: proto.String("store_status"),
			Action:     &eq,
			ColumnValue: &o2oalgo_vector_engine.FieldValue{
				ColumnType: &_int64,
				IntValue:   proto.Int64(int64(2)),
			},
		},
		{
			ColumnName: proto.String("store_dish_available"),
			Action:     &eq,
			ColumnValue: &o2oalgo_vector_engine.FieldValue{
				ColumnType: &_int64,
				IntValue:   proto.Int64(int64(1)),
			},
		},
		{
			ColumnName: proto.String("geohash5"),
			Action:     &in,
			ColumnValue: &o2oalgo_vector_engine.FieldValue{
				ColumnType: &_geohash5,
			},
		},
	}

	if vectorEngineRecallConfig.IsGeneralSubCateFilter && len(traceInfo.QPResult.QueryGeneralSubCateIds) > 0 {
		_queryGeneralSubCateIds := make([]int64, 0, 0)
		for _, i := range traceInfo.QPResult.QueryGeneralSubCateIds {
			_queryGeneralSubCateIds = append(_queryGeneralSubCateIds, int64(i))
		}

		whereColumns = append(whereColumns, &o2oalgo_vector_engine.FilterColumn{
			ColumnName: proto.String("category_level2_ids"),
			Action:     &interSect,
			ColumnValue: &o2oalgo_vector_engine.FieldValue{
				ColumnType: &_int32List,
				IntValues:  _queryGeneralSubCateIds,
			},
		})
	}

	return whereColumns
}
