package recall

import (
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const StoreDishRecallSize = 800

func (r *EsRecallRequest) BuildStoreDishesRecallDSL(traceInfo *traceinfo.TraceInfo) *model2.ESSearch {
	must, filters := buildFilter(traceInfo)
	sorts := []elastic.Sorter{
		elastic.NewFieldSort("sales_volume").Desc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	dish := es.NewESSearch(es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(traceInfo.From), es.WithSize(StoreDishRecallSize),
		es.WithSorters(sorts),
		es.WithRecallType("HardcodeESRecallDishIndex"),
		es.WithRecallId(recallconstant.HardcodeRecallIdESDishIndex),
	)
	logkit.Debug("EsRecallRequest.BuildStoreDishesRecallDSL", zap.Any("dish", dish))
	if traceInfo.IsDebug {
		traceInfo.AppendRecallsDishFinal("HardcodeESRecallDishIndex")
	}
	return dish
}

func (r *EsRecallRequest) BuildStoreDishesL3CategoryRecallDSL(traceInfo *traceinfo.TraceInfo) *model2.ESSearch {
	must, filters := buildFilter(traceInfo)
	agg := elastic.NewTermsAggregation().Field("l3_category").OrderByCountDesc().OrderByKeyAsc()

	category := es.NewESSearch(es.WithTermsAggregation(agg), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithRecallType("HardcodeESRecallDishL3Category"),
		es.WithRecallId(recallconstant.HardcodeRecallIdESDishL3Category),
	)
	logkit.Debug("EsRecallRequest.BuildStoreDishesL3CategoryRecallDSL", zap.Any("dish", category))
	if traceInfo.IsDebug {
		traceInfo.AppendRecallsDishFinal("HardcodeESRecallDishL3Category")
	}
	return category
}

func buildFilter(traceInfo *traceinfo.TraceInfo) ([]elastic.Query, []elastic.Query) {
	//query := regexp.QuoteMeta(traceInfo.QueryKeyword) // 元字符进行转义
	//reg := fmt.Sprintf(".*%s.*", query)
	must := []elastic.Query{
		//elastic.NewRegexpQuery("dish_name.ascii_raw", reg), // ascii_raw 是keyword 类型，转小写和去语掉, 性能太差，换成ngram 实现
	}
	filters := make([]elastic.Query, 0, 0)
	filters = append(filters, elastic.NewConstantScoreQuery(elastic.NewMatchPhraseQuery("dish_name.ascii_ngram", traceInfo.QueryKeyword)))
	filters = append(filters, elastic.NewTermQuery("available", 1))
	filters = append(filters, elastic.NewRangeQuery("price").Gt(0))
	if len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()) == 1 {
		filters = append(filters, elastic.NewTermQuery("store_id", traceInfo.TraceRequest.GetFilterType().GetStoreIds()[0]))
	}
	if len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()) > 1 {
		v := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()))
		for _, c := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
			v = append(v, c)
		}
		filters = append(filters, elastic.NewTermsQuery("store_id", v...))
	}
	if len(traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds()) == 1 {
		filters = append(filters, elastic.NewTermQuery("l3_category", traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds()[0]))
	}

	if len(traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds()) > 1 {
		v := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds()))
		for _, c := range traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds() {
			v = append(v, c)
		}
		filters = append(filters, elastic.NewTermsQuery("l3_category", v...))
	}
	if len(traceInfo.StoreCommPlans) > 1 {
		v := make([]interface{}, 0, len(traceInfo.StoreCommPlans))
		for _, c := range traceInfo.StoreCommPlans {
			v = append(v, c)
		}
		filters = append(filters, elastic.NewTermsQuery("store_commission_ids", v...))
	}
	if len(traceInfo.DishCommPlans) > 1 {
		v := make([]interface{}, 0, len(traceInfo.DishCommPlans))
		for _, c := range traceInfo.DishCommPlans {
			v = append(v, c)
		}
		filters = append(filters, elastic.NewTermsQuery("dish_commission_ids", v...))
	}

	return must, filters
}
