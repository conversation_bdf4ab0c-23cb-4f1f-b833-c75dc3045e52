package recall

import (
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const DishAffiliateRecallSize = 800
const StoreAffiliateRecallSize = 1000
const BrandStoreAffiliateRecallSize = 5000
const StoreWithDishAffiliateRecallSize = 3

func (r *EsRecallRequest) BuildStoresAffiliateRecallDSL(traceInfo *traceinfo.TraceInfo) *model2.ESSearch {
	storeNameField := "store_name"
	dishNameField := "dish_name"
	if cid.IsVN() {
		storeNameField = "store_name.ascii"
		dishNameField = "dish_name.ascii"
	}
	should := []elastic.Query{
		elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, storeNameField),
		elastic.NewMultiMatchQuery(traceInfo.QueryKeyword, dishNameField),
	}

	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
	}
	// vn 没有store_dish_available, 也没有类目召回
	if cid.IsVN() == false {
		// 佣金相关的都不需要store_dish_available 过滤
		//filters = append(filters, elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE))

		//categoryTags := traceInfo.QPResult.ProbFilterCategoryIntentions
		//for _, tag := range categoryTags {
		//	if tag != nil && tag.Level2Name != nil && len(*tag.Level2Name) > 0 {
		//		should = append(should, elastic.NewMultiMatchQuery(*tag.Level2Name, "main_category.level2_name.keyword", "sub_category.level2_name.keyword").Type("phrase"))
		//	}
		//}
	}
	if len(traceInfo.TraceRequest.City) > 0 {
		filters = append(filters, elastic.NewTermQuery("city", traceInfo.TraceRequest.City))
	}
	if len(traceInfo.TraceRequest.State) > 0 {
		filters = append(filters, elastic.NewTermQuery("state", traceInfo.TraceRequest.State))
	}
	if traceInfo.TraceRequest.GetFilterType().GetIsPreferredMerchant() {
		filters = append(filters, elastic.NewTermQuery("is_preferred_merchant", 1))
	}

	l1Categories := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetL1Categories()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetL1Categories() {
		l1Categories = append(l1Categories, c)
	}
	l2Categories := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetL2Categories()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetL2Categories() {
		l2Categories = append(l2Categories, c)
	}
	var categoriesShould []elastic.Query
	if len(l1Categories) > 0 {
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("main_category.level1_id", l1Categories...))
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("sub_category.level1_id", l1Categories...))
	}
	if len(l2Categories) > 0 {
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("main_category.level2_id", l2Categories...))
		categoriesShould = append(categoriesShould, elastic.NewTermsQuery("sub_category.level2_id", l2Categories...))
	}
	if len(categoriesShould) > 0 {
		filters = append(filters, elastic.NewBoolQuery().Should(categoriesShould...).MinimumShouldMatch("1"))
	}
	storeIds := make([]interface{}, 0, len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()))
	for _, c := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
		storeIds = append(storeIds, c)
	}
	if len(storeIds) > 0 {
		filters = append(filters, elastic.NewTermsQuery("id", storeIds...))
	}

	plansShould := make([]elastic.Query, 0, 2)
	if len(traceInfo.StoreCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.StoreCommPlans))
		for _, c := range traceInfo.StoreCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("store_commission_ids", v...))
	}
	if len(traceInfo.DishCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.DishCommPlans))
		for _, c := range traceInfo.DishCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("dish_commission_ids", v...))
	}
	if len(plansShould) > 0 {
		filters = append(filters, elastic.NewBoolQuery().Should(plansShould...).MinimumShouldMatch("1"))
	}

	sorts := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewFieldSort("max_commission_rate").Desc(),
		elastic.NewFieldSort("store_sell_week").Desc(),
		elastic.NewFieldSort("_id").Asc(),
	}

	store := es.NewESSearch(
		es.WithShouldQueries(should),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(StoreAffiliateRecallSize),
		es.WithSorters(sorts),
		es.WithRecallType("BuildStoresAffiliateRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildStoresAffiliateRecallDSL", zap.Any("store", store))
	return store
}

// 佣金门店二次挂菜
func (r *EsRecallRequest) BuildDishesWithStoresAffiliateRecallDSL(traceInfo *traceinfo.TraceInfo, storeIds []uint64) *model2.ESSearch {
	dishNameField := "dish_name"
	if cid.IsVN() {
		dishNameField = "dish_name.ascii"
	}
	must := []elastic.Query{
		elastic.NewMatchQuery(dishNameField, traceInfo.QueryKeyword),
	}
	filters := make([]elastic.Query, 0)
	//filters = append(filters, elastic.NewTermQuery("available", 1))  佣金相关菜品不考虑available字段
	filters = append(filters, elastic.NewTermQuery("listing_status", 1))
	filters = append(filters, elastic.NewTermsQuery("store_id", util2.ConvertUint64ListToInterfaceSlice(storeIds)...))

	plansShould := make([]elastic.Query, 0, 2)
	if len(traceInfo.StoreCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.StoreCommPlans))
		for _, c := range traceInfo.StoreCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("store_commission_ids", v...))
	}
	if len(traceInfo.DishCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.DishCommPlans))
		for _, c := range traceInfo.DishCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("dish_commission_ids", v...))
	}
	if len(plansShould) > 0 {
		filters = append(filters, elastic.NewBoolQuery().Should(plansShould...))
	}

	aggTopHist := elastic.NewTopHitsAggregation().Size(StoreWithDishAffiliateRecallSize).FetchSource(false).
		Sort("_score", false).Sort("max_commission_rate", false).Sort("sales_volume", false)
	aggs := elastic.NewTermsAggregation().Field("store_id").Size(StoreAffiliateRecallSize).SubAggregation("top_dishes", aggTopHist)

	sorts := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewFieldSort("store_id").Asc(),
	}
	dish := es.NewESSearch(
		es.WithTermsAggregation(aggs),
		es.WithMustQueries(must),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(StoreAffiliateRecallSize),
		es.WithSorters(sorts),
		es.WithRecallType("BuildDishesWithStoresAffiliateRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildDishesWithStoresAffiliateRecallDSL", zap.Any("dish", dish))
	return dish
}

// 统计同品牌门店数量
func (r *EsRecallRequest) BuildSameBrandStoresAffiliateRecallDSL(traceInfo *traceinfo.TraceInfo, brandIds []uint64) *model2.ESSearch {
	must := []elastic.Query{}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermsQuery("brand_id", util2.ConvertUint64ListToInterfaceSlice(brandIds)...),
	}
	//// vn 没有store_dish_available
	//if cid.IsVN() == false {
	//  佣金相关不用store_dish_available过滤
	//	filters = append(filters, elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE))
	//}
	if len(traceInfo.TraceRequest.City) > 0 {
		filters = append(filters, elastic.NewTermQuery("city", traceInfo.TraceRequest.City))
	}
	if len(traceInfo.TraceRequest.State) > 0 {
		filters = append(filters, elastic.NewTermQuery("state", traceInfo.TraceRequest.State))
	}
	plansShould := make([]elastic.Query, 0, 2)
	if len(traceInfo.StoreCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.StoreCommPlans))
		for _, c := range traceInfo.StoreCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("store_commission_ids", v...))
	}
	if len(traceInfo.DishCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.DishCommPlans))
		for _, c := range traceInfo.DishCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("dish_commission_ids", v...))
	}
	if len(plansShould) > 0 {
		filters = append(filters, elastic.NewBoolQuery().Should(plansShould...).MinimumShouldMatch("1"))
	}

	brands := es.NewESSearch(
		es.WithMustQueries(must),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(BrandStoreAffiliateRecallSize),
		es.WithRecallType("BuildSameBrandStoresAffiliateRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildSameBrandStoresAffiliateRecallDSL", zap.Any("brands", brands))
	return brands
}

func (r *EsRecallRequest) BuildDishesAffiliateRecallDSL(traceInfo *traceinfo.TraceInfo) *model2.ESSearch {
	//query := regexp.QuoteMeta(traceInfo.QueryKeyword) // 元字符进行转义
	//reg := fmt.Sprintf(".*%s.*", query)
	must := []elastic.Query{
		//elastic.NewRegexpQuery("dish_name.ascii_raw", reg), // ascii_raw 是keyword 类型，转小写和去语掉, 性能太差，换成ngram 实现
	}
	filters := make([]elastic.Query, 0)
	filters = append(filters, elastic.NewTermQuery("store_id", traceInfo.TraceRequest.StoreId))
	filters = append(filters, elastic.NewConstantScoreQuery(elastic.NewMatchPhraseQuery("dish_name.ascii_ngram", traceInfo.QueryKeyword)))
	filters = append(filters, elastic.NewTermQuery("listing_status", 1))
	//filters = append(filters, elastic.NewTermQuery("available", 1))
	filters = append(filters, elastic.NewRangeQuery("price").Gt(0))

	plansShould := make([]elastic.Query, 0, 2)
	if len(traceInfo.StoreCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.StoreCommPlans))
		for _, c := range traceInfo.StoreCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("store_commission_ids", v...))
	}
	if len(traceInfo.DishCommPlans) > 0 {
		v := make([]interface{}, 0, len(traceInfo.DishCommPlans))
		for _, c := range traceInfo.DishCommPlans {
			v = append(v, c)
		}
		plansShould = append(plansShould, elastic.NewTermsQuery("dish_commission_ids", v...))
	}
	if len(plansShould) > 0 {
		filters = append(filters, elastic.NewBoolQuery().Should(plansShould...))
	}

	sorts := []elastic.Sorter{
		elastic.NewFieldSort("max_commission_rate").Desc(),
		elastic.NewFieldSort("sales_volume").Desc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	dish := es.NewESSearch(
		es.WithMustQueries(must),
		es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)),
		es.WithSize(DishAffiliateRecallSize),
		es.WithSorters(sorts),
		es.WithRecallType("BuildDishesAffiliateRecallDSL"),
	)
	logkit.Debug("EsRecallRequest.BuildDishesAffiliateRecallDSL", zap.Any("dish", dish))
	return dish
}
