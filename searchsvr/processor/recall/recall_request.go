package recall

import (
	"fmt"
	"strconv"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"github.com/olivere/elastic/v7"
)

type EsRecallRequest struct {
	//OriginalRequest    *foodalgo_search.SearchRequest
	KeywordQuery       string
	KeywordAscii       string
	TokenLen           int
	IsAsciiTerms       bool
	RewriteKeywordList []string
}

func NewESRecallRequest(traceInfo *traceinfo.TraceInfo) *EsRecallRequest {
	if traceInfo == nil {
		return nil
	}
	sr := &EsRecallRequest{}
	sr.KeywordQuery = traceInfo.QueryKeyword
	sr.TokenLen = util2.GetTokenLen(traceInfo.QueryKeyword)
	sr.KeywordAscii = util2.ToAscii(traceInfo.QueryKeyword)
	sr.IsAsciiTerms = util2.IsAsciiTerms(traceInfo.QueryKeyword)

	// 初始化 rewrite 改写词组
	sr.RewriteKeywordList = make([]string, 0)
	for _, i := range traceInfo.QPResult.RewriteQuery {
		sr.RewriteKeywordList = append(sr.RewriteKeywordList, i)
	}

	return sr
}

func CombineQuery(queries []elastic.Query, conditionType string, minimumShouldMatch int) elastic.Query {
	// conditionType:  AND, OR
	if len(queries) == 0 {
		return nil
	}
	if len(queries) == 1 {
		return queries[0]
	}
	if conditionType == "AND" {
		return elastic.NewBoolQuery().Must(queries...)
	} else {
		return elastic.NewBoolQuery().Should(queries...).MinimumShouldMatch(strconv.Itoa(minimumShouldMatch))
	}
}

func CombineFields(fieldsList ...[]string) []string {
	var res []string
	for _, fields := range fieldsList {
		res = append(res, fields...)
	}
	return res
}

// boolBothName true 表示需要2种
func GetSearchFields(fieldBaseName string, tokenLength int, isAsciiTerms bool,
	boostingConfig BoostingConfig, fieldBaseBoostFactor float64,
	includeTermQuery bool, includeShinglesQuery bool, include3shinglesQuery bool, boolBothName bool) []string {

	if tokenLength == 0 {
		return nil
	}

	baseName := fieldBaseName
	asciiBoostFactor := boostingConfig.AsciiFactor //默认1.2
	if isAsciiTerms {
		asciiBoostFactor = 1.0
	}
	result := make([]string, 0)

	if includeTermQuery {
		if !isAsciiTerms {
			result = append(result, GetFieldName(baseName, fieldBaseBoostFactor))
		}
		if isAsciiTerms || boolBothName {
			result = append(result, GetFieldAsciiName(baseName, fieldBaseBoostFactor*asciiBoostFactor))
		}
	}

	if includeShinglesQuery && tokenLength > 1 {
		if !isAsciiTerms {
			result = append(result, GetFieldShinglesName(baseName, fieldBaseBoostFactor))
		}
		if isAsciiTerms || boolBothName {
			result = append(result, GetFieldShinglesAsciiName(baseName, fieldBaseBoostFactor*asciiBoostFactor))
		}
	}

	if include3shinglesQuery && tokenLength > 2 {
		if !isAsciiTerms {
			result = append(result, GetField3shinglesName(baseName, fieldBaseBoostFactor))
		}
		if isAsciiTerms || boolBothName {
			result = append(result, GetField3shinglesAsciiName(baseName, fieldBaseBoostFactor*asciiBoostFactor))

		}
	}
	return result

}

func GetField3shinglesName(baseName string, boost float64) string {
	result := fmt.Sprintf("%s.3shingles", baseName)
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}

func GetField3shinglesAsciiName(baseName string, boost float64) string {
	result := fmt.Sprintf("%s.ascii_3shingles", baseName)
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}

func GetFieldShinglesName(baseName string, boost float64) string {
	result := fmt.Sprintf("%s.shingles", baseName)
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}

func GetFieldShinglesAsciiName(baseName string, boost float64) string {
	result := fmt.Sprintf("%s.ascii_shingles", baseName)
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}

func GetFieldAsciiName(baseName string, boost float64) string {
	result := fmt.Sprintf("%s.ascii", baseName)
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}

func GetFieldName(baseName string, boost float64) string {
	result := baseName
	if boost != 1.0 {
		result = fmt.Sprintf("%s^%.2f", result, boost)
	}
	return result
}
