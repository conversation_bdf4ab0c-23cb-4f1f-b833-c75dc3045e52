package recall

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/predata"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type StoreRecall struct {
	StoreQueries       []*es.ESSearch
	StoreRecallConfigs []*apollo.StoreRecallConfig
	recallCommon       *apollo.RecallCommon
}

func (r *StoreRecall) initEveryConfig(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallConfig *apollo.StoreRecallConfig, common *apollo.RecallCommon) {
	// 初始化生成日志打印时的 recallName
	recallConfig.RecallLogName = recallConfig.RecallName + "_" + recallConfig.RecallId

	// 初始化超时时间
	if recallConfig.RecallTimeoutMs == nil && common.RecallTimeoutMs > 0 {
		recallConfig.RecallTimeoutMs = proto.Uint64(common.RecallTimeoutMs)
	}

	// 初始化是否降级
	if recallConfig.IsDownGrade == nil {
		recallConfig.IsDownGrade = proto.Bool(common.IsDownGrade)
	}

	// 召回比例
	if recallConfig.RecallSizeRate == nil && common.RecallSizeRate >= 0 && common.RecallSizeRate <= 1.0 {
		recallConfig.RecallSizeRate = proto.Float64(common.RecallSizeRate)
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration initEveryConfig", logkit.String("RecallLogName", recallConfig.RecallLogName), logkit.Any("RecallTimeoutMs", recallConfig.RecallTimeoutMs),
		logkit.Any("IsDownGrade", recallConfig.IsDownGrade), logkit.Any("RecallSizeRate", recallConfig.RecallSizeRate))
}

func (r *StoreRecall) BuildStoreRecall(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreRecall {
	var recallConfig *apollo.RecallConfig
	recallConfig = parse.GetStoreRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainStore, recallconstant.RecallDataSourceES)
	if recallConfig == nil || len(recallConfig.StoreRecalls) == 0 {
		logkit.FromContext(ctx).Error("RecallConfiguration RecallService BuildStoreRecall failed, get no recall config")
		return nil
	}

	recallConfigs := recallConfig.StoreRecalls
	r.StoreQueries = make([]*es.ESSearch, 0)
	r.StoreRecallConfigs = make([]*apollo.StoreRecallConfig, 0)
	r.recallCommon = recallConfig.Common
	traceInfo.RecallMaxSize = int(recallConfig.Common.RecallMaxSize) // 后续合并截断需要用到

	// 补充es数据源
	if traceInfo.RecallConfigurationDataSource != nil {
		predata.InitSupplementESDataSource(ctx, traceInfo, traceInfo.RecallConfigurationDataSource, recallConfig.StoreRecalls, recallConfig.Common)
		traceInfo.RecallConfigurationDataParams = parse.GetDataSourceParams(traceInfo.RecallConfigurationDataSource)
	}

	searchReq := model2.NewSearchRequest(traceInfo, traceInfo.QueryKeyword)

	// filter - condition
	filterByConditionRecalls := make([]*apollo.StoreRecallConfig, 0)
	filterByConditionQueries := make([]*es.ESSearch, 0)
	for i := range recallConfigs {
		r.initEveryConfig(ctx, traceInfo, recallConfigs[i], r.recallCommon)

		var q *es.ESSearch
		switch recallConfigs[i].RecallType {
		case 8:
			q = searchReq.ToStoreEsWithNer(traceInfo, "AND", model2.MustAndMatchType)
			upgradeStoreNer(ctx, traceInfo, q, recallConfigs[i], r.recallCommon)
		case 9:
			q = searchReq.ToStoreEsWithNer(traceInfo, "OR", model2.MustOrMatchType)
			upgradeStoreNer(ctx, traceInfo, q, recallConfigs[i], r.recallCommon)
		case 10:
			q = searchReq.ToStoreEsWithNer(traceInfo, "OR", model2.ShouldMatchType)
			upgradeStoreNer(ctx, traceInfo, q, recallConfigs[i], r.recallCommon)
		default:
			q = parse.GenRecallQuery(ctx, traceInfo, r.recallCommon, recallConfigs[i])
		}
		if q != nil {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration esQuery recallType", zap.String("recallName", recallConfigs[i].RecallLogName),
				zap.String("recallType", recallConfigs[i].RecallTypeStr), zap.String("esQueryRecallType", q.RecallType), zap.String("esQueryRecallTypeName", q.RecallTypeName))
			if traceInfo.IsDebug == false {
				q.FilterPath = []string{"hits.hits._id", "hits.hits._score", "hits.hits.sort", "took"}
				q.SourceInclude = []string{}
			}

			filterByConditionRecalls = append(filterByConditionRecalls, recallConfigs[i])
			filterByConditionQueries = append(filterByConditionQueries, q)
		} else {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration esQuery is nil, give up recall", zap.String("recallName", recallConfigs[i].RecallLogName),
				zap.String("recallType", recallConfigs[i].RecallTypeStr))
		}
	}

	// filter - downgrade
	filterByDowngradeRecalls := make([]*apollo.StoreRecallConfig, 0)
	filterByDowngradeQueries := make([]*es.ESSearch, 0)
	for i := range filterByConditionRecalls {
		if filterByConditionRecalls[i].IsDownGrade != nil && *filterByConditionRecalls[i].IsDownGrade == true {
			logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration downgrade, skip recall", logkit.String("recallName", filterByConditionRecalls[i].RecallLogName))
			continue
		}
		filterByDowngradeRecalls = append(filterByDowngradeRecalls, filterByConditionRecalls[i])
		filterByDowngradeQueries = append(filterByDowngradeQueries, filterByConditionQueries[i])
	}
	if len(filterByConditionRecalls) > 0 && len(filterByDowngradeRecalls) == 0 {
		// 全降级了，这种情况就是降级配置错误
		logger.MyDebug(ctx, traceInfo.IsDebug, "RecallConfiguration all downgrade, skip downgrade")
		r.StoreRecallConfigs = filterByConditionRecalls
		r.StoreQueries = filterByConditionQueries
	} else {
		r.StoreRecallConfigs = filterByDowngradeRecalls
		r.StoreQueries = filterByDowngradeQueries
	}

	if traceInfo.IsDebug {
		for _, x := range r.StoreRecallConfigs {
			traceInfo.AppendRecallsStoreFinal(fmt.Sprintf("%s_%s", x.RecallName, x.RecallId))
		}
	}
	return r
}

func upgradeStoreNer(ctx context.Context, traceInfo *traceinfo.TraceInfo, q *es.ESSearch, recallConfig *apollo.StoreRecallConfig, recallCommon *apollo.RecallCommon) {
	if q == nil {
		return
	}
	// 设置recallType
	q.RecallType = recallConfig.RecallTypeStr

	// 是否 explain
	q.IsNeedEsExplain = traceInfo.IsNeedEsExplain

	// 公共过滤器，如果不是标准的boolQ，就不支持公共 filter，比如 scriptScoreQuery 为主体的
	commonFilters, err := parse.GenFilterQueryByRecallCommon(ctx, traceInfo, recallConfig, recallCommon)
	if err == nil && len(commonFilters) > 0 {
		q.Filters = parse.MergeFilters(ctx, q.Filters, commonFilters)
	}
}
