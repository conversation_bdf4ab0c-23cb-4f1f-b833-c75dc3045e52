package recall

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func SearchStoreES(ctx context.Context, traceInfo *traceinfo.TraceInfo, esBase *es.ESBase, esSearch *es.ESSearch) (uint64, []*model.StoreInfo, error) {
	var cost int64
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseStoreRecallMultiSourceES)+"_Server_"+esSearch.RecallTypeName), time.Duration(cost)*time.Millisecond)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseStoreRecallMultiSourceES)+"_Client_"+esSearch.RecallTypeName), time.Since(pt))
	}()

	// 优先从召回配置化获取timeout
	timeout := esSearch.RecallTimeoutMs
	if timeout <= 0 {
		timeout = apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreTimeOut
		if timeout <= 0 {
			timeout = 500
		}
	}
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		timeout = 5000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	totalHit, hits, err, cost := esBase.Search(ctx, esSearch, traceInfo.UserId)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.MAIN_RECALL_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("SearchStoreES esBase.Search failed", logkit.String("cost", time.Since(startTime).String()), logkit.String("recallId", esSearch.RecallId), logkit.String("recallType", esSearch.RecallTypeName), logkit.String("dsl", esSearch.DslString(ctx)))
		return 0, nil, err
	}
	nStores := len(hits)
	storesSearch := make([]*model.StoreInfo, 0, nStores)
	storeInfos := make([]model.StoreInfo, nStores)
	var esStore *model.StoreSearchES
	var id, intNum uint64
	for index, hit := range hits {
		if hit == nil {
			continue
		}
		id, err = strconv.ParseUint(hit.Id, 10, 64)
		if err != nil {
			continue
		}
		score := float64(0)
		if hit.Score != nil {
			score = *hit.Score
		}
		if score == 0.0 && len(hit.Sort) != 0 {
			score = hit.Sort[0].(float64)
		}
		storeInfos[index].StoreId = id
		storeInfos[index].ESScore = score
		storeInfos[index].Score = score
		storeInfos[index].RecallQueries = []string{esSearch.Query}

		// es explain
		var explainStr string
		if traceInfo.IsNeedEsExplain && traceInfo.EsExplainStoreIds[id] == true && hit.Explanation != nil {
			explanationJSON, err1 := json.Marshal(hit.Explanation)
			if err1 != nil {
				fmt.Printf("Error marshaling es explanation to JSON: %s\n", err)
			} else {
				explainStr = string(explanationJSON)
				storeInfos[index].EsExplains = []string{explainStr}
			}
		}
		// ES 召回相关字段赋值
		if len(hit.Source) != 0 {
			esStore = &model.StoreSearchES{}
			err = json.Unmarshal(hit.Source, esStore)
			if err == nil {
				storeInfos[index].StoreIntents = esStore.GetGrabFoodTags() // StoreIntents 就是 grab food tags
			}
			intNum, err = strconv.ParseUint(esStore.GetBestSellingDishId(), 10, 64)
			if err == nil {
				storeInfos[index].BestSellingDishId = intNum // 主站搜索
			}
		}
		storesSearch = append(storesSearch, &storeInfos[index])
	}
	return totalHit, storesSearch, nil
}
