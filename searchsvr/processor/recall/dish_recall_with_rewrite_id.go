package recall

import (
	"context"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/env"
	"sort"
	"sync"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// query改写挂菜需求文档: https://confluence.shopee.io/pages/viewpage.action?pageId=1556056859
func DishSearchWithQuery(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) (model.DishInfos, error) {
	pt := time.Now()
	dishList := make(model.DishInfos, 0)
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishRecallDishSearcher, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromDishSearcher, len(dishList))
	}()

	storeIdsWithRawQuery := make([]uint64, 0)
	storeIdsWithCorQuery := make([]uint64, 0)
	for _, store := range stores {
		//多路召回里，只有通过菜品域或者某一实体是菜品类型，并且实体扩召回有返回结果的，才去选菜
		if store.IsNeedDishRecall {
			rewriteNer := 0
			others := 0
			storeCateExpand := 0
			for _, recallType := range store.RecallTypes {
				if recallType == foodalgo_search.RecallType_RewriteNer.String() {
					rewriteNer = 1
				} else if recallType == foodalgo_search.RecallType_StoreNerAndTag.String() ||
					recallType == foodalgo_search.RecallType_StoreNerOrTag.String() ||
					recallType == foodalgo_search.RecallType_StoreIndex.String() ||
					recallType == foodalgo_search.RecallType_StoreIntervention.String() ||
					recallType == foodalgo_search.RecallType_StoreWithDishField.String() ||
					recallType == foodalgo_search.RecallType_StoreNerReserveTag.String() ||
					recallType == foodalgo_search.RecallType_AnnRecall.String() ||
					recallType == foodalgo_search.RecallType_TextFieldRecallAND.String() ||
					recallType == foodalgo_search.RecallType_TextFieldRecallOR.String() ||
					recallType == foodalgo_search.RecallType_TextFieldRecallNearOR.String() {
					others += 1
				} else if recallType == foodalgo_search.RecallType_StoreCateExpand.String() {
					storeCateExpand = 1
				}
			}

			if rewriteNer > 0 && others == 0 && storeCateExpand == 0 {
				storeIdsWithCorQuery = append(storeIdsWithCorQuery, store.StoreId)
			} else if rewriteNer > 0 && others > 0 {
				storeIdsWithRawQuery = append(storeIdsWithRawQuery, store.StoreId)
			} else if rewriteNer > 0 && storeCateExpand > 0 {
				storeIdsWithCorQuery = append(storeIdsWithCorQuery, store.StoreId)
			} else {
				storeIdsWithRawQuery = append(storeIdsWithRawQuery, store.StoreId)
			}
		}
	}

	fineSegs := make([][]string, 0)
	coarseSegs := make([][]string, 0)
	for _, ner := range traceInfo.QPResult.RewriteNerResult {
		tmpFineSegs := make([]string, 0)
		tmpCoarseSegs := make([]string, 0)
		for _, ner2 := range ner {
			tmpFineSegs = append(tmpFineSegs, ner2.GetFineGrainedSeg()...)
			tmpCoarseSegs = append(tmpCoarseSegs, ner2.GetCoarseGrainedSeg()...)
		}
		fineSegs = append(fineSegs, tmpFineSegs)
		coarseSegs = append(coarseSegs, tmpCoarseSegs)
	}

	nLimit := apollo.SearchApolloCfg.DishRecallTermLimit
	if nLimit == 0 {
		nLimit = 5
	}
	nSegs := len(fineSegs)
	if nSegs > nLimit {
		nSegs = nLimit
		fineSegs = fineSegs[0:nSegs]
		coarseSegs = coarseSegs[0:nSegs]
	}
	group := traceInfo.ABTestGroup.GetABTestVal(apollo.SearchApolloCfg.DishRecallExpVariate)
	var dishIdsWithRawQuery model.DishInfos
	dishIdsWithCorQuery := make([]model.DishInfos, nSegs, nSegs)

	wg := sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "batchSearchDish-RawQuery", func(params ...interface{}) {
		defer wg.Done()
		if traceInfo.IsDebug {
			recallName := fmt.Sprintf("%s_%s", "HardcodeRecallDishSearcherDishRawQuery", recallconstant.HardcodeRecallIdDishSearcherDishRawQuery)
			traceInfo.AppendRecallsDishFinal(recallName)
		}
		dishIdsWithRawQuery = BatchSearchDish(ctx, traceInfo, storeIdsWithRawQuery, traceInfo.QPResult.Segments, traceInfo.QPResult.MaxWordSegments, group)
	})
	// 支持实验配置是否使用query改写词进行挂菜,没有配置默认使用
	if len(apollo.SearchApolloCfg.DishRecallCorExp) == 0 || traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.DishRecallCorExp) {
		if traceInfo.IsDebug {
			recallName := fmt.Sprintf("%s_%s", "HardcodeRecallDishSearcherDishRewrite", recallconstant.HardcodeRecallIdDishSearcherDishRewrite)
			traceInfo.AppendRecallsDishFinal(recallName)
		}
		for i := 0; i < nSegs; i++ {
			wg.Add(1)
			goroutine.WithGo(ctx, "batchSearchDish-CorQuery", func(params ...interface{}) {
				defer wg.Done()
				param := params[0].([]interface{})
				index := param[0].(int)
				dishIdsWithCorQuery[index] = BatchSearchDish(ctx, traceInfo, storeIdsWithCorQuery, fineSegs[index], coarseSegs[index], group)
			}, i)
		}
	} else {
		logkit.FromContext(ctx).Info("batchSearchDish do not hit any exp, skip")
	}
	wg.Wait()

	dishWithCorQueryMap := make(map[uint64]model.DishInfos, 0) //map key is store id, val are dishes
	repeatedDish := make(map[uint64]map[uint64]int, 0)         //map key is store id, val are the indexes of dishes
	for i := 0; i < nSegs; i++ {
		nList := len(dishIdsWithCorQuery[i])
		for j := 0; j < nList; j++ {
			if dishIdsWithCorQuery[i][j] != nil {
				sID := dishIdsWithCorQuery[i][j].StoreId
				if _, ok := dishWithCorQueryMap[dishIdsWithCorQuery[i][j].StoreId]; !ok {
					//if store id not in the map, then add the store id into the map
					dishWithCorQueryMap[sID] = append(dishWithCorQueryMap[sID], dishIdsWithCorQuery[i][j])
					repeatedDish[sID] = make(map[uint64]int, 0)
					repeatedDish[sID][dishIdsWithCorQuery[i][j].DishId] = len(dishWithCorQueryMap[sID]) - 1
				} else {
					// if the store id in the map ,then check whether the dish id in the map if map[store id]
					if index, exist := repeatedDish[sID][dishIdsWithCorQuery[i][j].DishId]; !exist {
						dishWithCorQueryMap[sID] = append(dishWithCorQueryMap[sID], dishIdsWithCorQuery[i][j])
						repeatedDish[sID][dishIdsWithCorQuery[i][j].DishId] = len(dishWithCorQueryMap[sID]) - 1
					} else {
						if dishIdsWithCorQuery[i][j].Score > dishWithCorQueryMap[sID][index].Score {
							dishWithCorQueryMap[sID][index].Score = dishIdsWithCorQuery[i][j].Score
						}
					}
				}
			}
		}
	}

	dishWithCorQueryList := make(model.DishInfos, 0)
	for _, val := range dishWithCorQueryMap {
		if len(val) > 2 {
			sort.Slice(val, func(i, j int) bool {
				if val[i].Score != val[j].Score {
					return val[i].Score > val[j].Score
				}
				return val[i].SalesVolume > val[j].SalesVolume
			})
			val = val[0:2]
		}
		dishWithCorQueryList = append(dishWithCorQueryList, val...)
	}

	dishList = append(dishList, dishIdsWithRawQuery...)
	dishList = append(dishList, dishWithCorQueryList...)

	return dishList, nil
}

func BatchSearchDish(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64, keyword []string, keywordSeg []string, group string) model.DishInfos {
	batchSize := 150
	if apollo.SearchApolloCfg.NewDishRecallBatch != 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatch
	}
	total := len(storeIds) / batchSize
	if len(storeIds)%batchSize > 0 {
		total += 1
	}
	dishRecallList := make([]model.DishInfos, total, total)
	wg2 := sync.WaitGroup{}
	wg2.Add(total)
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.DishSearcherTimeOut
	if timeout == 0 {
		timeout = 5000
	}
	if env.GetEnv() != "live" {
		timeout = 1000
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()

	recallConf := apollo.GetDishRecallConf()[group]
	for i := 0; i < total; i++ {
		var tempStoreId []uint64
		if i+1 < total {
			tempStoreId = storeIds[i*batchSize : (i+1)*batchSize]
		} else {
			tempStoreId = storeIds[i*batchSize:]
		}
		goroutine.WithGo(ctx, "DishSearcherSearch", func(params ...interface{}) {
			defer wg2.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			tempIds := param[1].([]uint64)
			searcherReq := o2oalgo_dishsearcher.SearcherRequest{
				Keyword:    keyword,
				StoreId:    tempIds,
				PublishId:  traceInfo.TraceRequest.PublishId,
				AbTest:     traceInfo.ABTestGroup.GetABTestString(),
				BuyerId:    traceInfo.UserId,
				IsDebug:    traceInfo.IsDebug,
				KeywordSeg: keywordSeg,
				RecallStrategy: &o2oalgo_dishsearcher.RecallStrategy{
					RecallStrategyType: int32(recallConf["DishRecallStg"]),
					TermCount:          int32(recallConf["TermCount"]),
					HitTermProportion:  recallConf["TermHitRatio"],
				},
			}
			logger.MyDebug(ctx, traceInfo.IsDebug, "dish searcher req", zap.Any("req", searcherReq))
			startTime := time.Now()
			resp, err := integrate.DishSearcherClient.DishSearcherSearch(ctx, &searcherReq)
			//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "DishSearcher"), zap.String("cost", time.Since(startTime).String()))
			metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "DishSearcher")
			logger.MyDebug(ctx, traceInfo.IsDebug, "diff debug log: DishSearcherSearch", zap.Any("req", searcherReq), zap.Any("response", resp), zap.Any("qp result", traceInfo.QPResult))
			if err != nil {
				traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
				traceInfo.AddErrorToTraceInfo(err)
				logkit.FromContext(ctx).Error("failed to Dish Search", logkit.String("cost", time.Since(startTime).String()), logkit.Any("err", err))
				metric_reporter.ReportClientRequestError(1, "DishSearcher", "failed")
				return
			}
			metric_reporter.ReportClientRequestError(1, "DishSearcher", "0")
			dishs := make([]*model.DishInfo, 0, len(resp.Stores)*2)
			for _, store := range resp.Stores {
				for _, d := range store.Dishs {
					dish := &model.DishInfo{
						DishId:  d.GetId(),
						Score:   float64(d.GetScore()),
						ESScore: float64(d.GetScore()),
						StoreId: store.GetId(),
						//DishName:       d.GetDishName(),
						//Price:          d.GetPrice(),
						//SalesVolume:    uint32(d.GetSalesVolume()),
						//Picture:        d.GetPicture(),
						//HasPicture:     d.GetHasPicture(),
						//Available:      foodalgo_search.Available_AVAILABLE,
						//ListingStatus:  foodalgo_search.DishListingStatus_ACTIVE
					}
					//salesTimeBytes, err1 := base64.StdEncoding.DecodeString(d.GetSalesTime())
					//if err1 != nil {
					//	traceInfo.AddErrorToTraceInfo(err1)
					//	logkit.FromContext(ctx).Error("Dish SalesTime base64 decode fail..", zap.Any("dish", d))
					//	continue
					//}
					//var salesDays o2oalgo.SalesDay
					//err1 = proto.Unmarshal(salesTimeBytes, &salesDays)
					//if err1 != nil {
					//	logkit.FromContext(ctx).Error("Dish SalesTime proto Unmarshal fail..", zap.Any("dish", d))
					//	continue
					//}
					//if req.GetIsDebug() {
					//	logger.MyDebug(ctx, traceInfo.IsDebug, "Dish_SalesDay", zap.Uint64("id", d.GetId()), zap.Any("SalesDay", salesDays))
					//}
					//dish.SalesTimeStruct = &salesDays
					dishs = append(dishs, dish)
				}
			}
			// 直接请求菜品正排：dish searcher 返回了部分菜品正排字段，但是没有新加的saleStatus & outOfStockFlag 这两个字段，需要再调用一次正排
			dishs, _ = filling.DishMetaFilling(ctx, traceInfo, dishs)
			dishRecallList[index] = dishs
		}, i, tempStoreId)
	}
	wg2.Wait()
	nDish := 0
	if len(dishRecallList) > 0 && len(dishRecallList[0]) > 0 {
		nDish = len(dishRecallList) * len(dishRecallList[0])
	}
	dishList := make([]*model.DishInfo, 0, nDish)
	for i := range dishRecallList {
		if dishRecallList[i] != nil {
			dishList = append(dishList, dishRecallList[i]...)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "dish recall result", zap.Any("keyword", keyword), zap.Any("storeIDs", storeIds), zap.Any("dish", dishList))
	return dishList
}
