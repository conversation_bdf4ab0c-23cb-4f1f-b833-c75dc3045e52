package recall

import (
	"context"
	"fmt"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/redis/go-redis/v9"
)

func recallStoreFromRedis(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*model.RecallStore, error) {
	// 仅ID 站内搜索有ann 召回, 且支持ab实验控制
	if traceInfo.PipelineType != traceinfo.PipelineTypeSearch {
		return []*model.RecallStore{}, nil
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreRecallMultiSourceRedis, time.Since(pt))
	}()

	var annStores, annStoresByDish []*model.RecallStore
	var annErr, annByDishErr error

	wg := &sync.WaitGroup{}
	if traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.AnnRecallExp) {
		wg.Add(1)
		goroutine.WithGo(ctx, "AnnRecallExp", func(params ...interface{}) {
			defer wg.Done()

			geoHash := traceInfo.UserContext.GeoHash5
			prefix := "ann"
			if traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.AnnRecallExpV2) {
				prefix = "annv2"
			}
			if traceInfo.ABTestGroup.IsHitAbTestMultiKey(apollo.SearchApolloCfg.AnnRecallExpV3) {
				prefix = "annv3"
			}
			key := prefix + "_" + traceInfo.QueryKeyword + "_" + geoHash + "_" + env.GetEnv() + "_" + env.GetCID()
			if traceInfo.IsDebug {
				traceInfo.AppendRecallsStoreFinal("AnnRecall" + "_" + key)
			}

			annStores, annErr = annRecallFromRedis(ctx, traceInfo, prefix, key, foodalgo_search.RecallType_AnnRecall.String())
			if annErr != nil {
				logkit.FromContext(ctx).WithError(annErr).Error("recallFromRedis AnnRecallExp error")
			}
		})
	} else {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreNotHitAbtest("AnnRecall")
		}
	}

	dishAnnRecallConfig := abtest.GetDishAnnRecallConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	if dishAnnRecallConfig != nil && len(dishAnnRecallConfig.RedisPrefix) > 0 && len(traceInfo.QPResult.QueryDishIntention) > 0 {
		wg.Add(1)
		goroutine.WithGo(ctx, "DishAnnRecallExp", func(params ...interface{}) {
			defer wg.Done()

			geoHash := traceInfo.UserContext.GeoHash5
			key := dishAnnRecallConfig.RedisPrefix + "_" + traceInfo.QueryKeyword + "_" + geoHash + "_" + env.GetEnv() + "_" + env.GetCID()
			if traceInfo.IsDebug {
				traceInfo.AppendRecallsStoreFinal("DishAnnRecallExp" + "_" + key)
			}
			annStoresByDish, annByDishErr = annRecallFromRedis(ctx, traceInfo, dishAnnRecallConfig.RedisPrefix, key, foodalgo_search.RecallType_DishAnnRecall.String())
			if annByDishErr != nil {
				logkit.FromContext(ctx).WithError(annByDishErr).Error("recallFromRedis DishAnnRecallExp error")
			}
		})
	} else {
		if traceInfo.IsDebug {
			traceInfo.AppendRecallsStoreNotHitAbtest("DishAnnRecallExp")
		}
	}
	wg.Wait()

	if annErr != nil && annByDishErr != nil {
		logkit.FromContext(ctx).WithError(annByDishErr).Error("recallFromRedis both failed")
		return nil, annErr
	}

	recallList := make([]*model.RecallStore, 0, 0)
	if len(annStores) > 0 && len(annStoresByDish) > 0 {
		recallList = append(annStores, annStoresByDish...)
		return recallList, nil
	}
	if len(annStores) > 0 {
		return annStores, nil
	}
	if len(annStoresByDish) > 0 {
		return annStoresByDish, nil
	}

	return recallList, nil
}

func annRecallFromRedis(ctx context.Context, traceInfo *traceinfo.TraceInfo, version string, key string, recallType string) ([]*model.RecallStore, error) {
	st := time.Now()
	annRecallStores := make([]*model.StoreInfo, 0, 0)
	errCode := "failed"

	defer func() {
		reportKey := fmt.Sprintf("ann-codis-%s", version)
		if e := recover(); e != nil {
			logkit.Error("search codis panic", logkit.String("recall type", key), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-codis-store-ann-" + version,
			})
		}

		metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeMiddleware, "", "", reportKey)
		//logkit.FromContext(ctx).Info("cost time log", zap.String("method", reportKey), zap.String("cost", time.Since(st).String()), zap.String("redisKey", key), zap.Int("size", len(annRecallStores)))
		reporter.ReportClientRequestError(1, reportKey, errCode)

		traceInfo.AddPhraseCostTime(ctx, traceinfo.CostPhrase(string(traceinfo.PhraseStoreRecallMultiSourceRedis)+"_"+version), time.Since(st))
		traceInfo.AddPhraseStoreLength(ctx, "StoreLenRecall_AnnFromCodis"+"_"+version, len(annRecallStores))
	}()

	bytes, err := integrate.RedisClient.Get(ctx, key).Bytes()

	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
		logkit.FromContext(ctx).Error("errCode to get redis vector data", logkit.String("cost", time.Since(st).String()), logkit.Any("err", err))
		return nil, err
	}

	var defaultAnnESScore = apollo.AlgoApolloCfg.DefaultAnnESScore
	var annESScoreGroup = apollo.AlgoApolloCfg.AnnESScoreGroup
	if len(annESScoreGroup) > 0 {
		groupList := strings.Split(annESScoreGroup, ";")
		for _, group := range groupList {
			kv := strings.Split(group, ",")
			if len(kv) != 2 {
				continue
			}
			k := kv[0]
			v, err := strconv.ParseFloat(kv[1], 64)
			if traceInfo.ABTestGroup.AnnRecallDefaultScore(k) && err == nil {
				defaultAnnESScore = v
			}
		}
	}
	i := 0
	for i+4 <= len(bytes) {
		storeID := util2.BytesToInt(bytes[i : i+4])
		annRecallStores = append(annRecallStores, &model.StoreInfo{
			StoreId:          uint64(storeID),
			RecallQueries:    []string{traceInfo.QueryKeyword},
			RecallTypes:      []string{recallType},
			RecallTypeAndIds: []string{fmt.Sprintf("%s_1", recallType)},
			RecallPosList:    []string{strconv.Itoa(i / 4)},
			IsNeedDishRecall: true,
			ESScore:          defaultAnnESScore,
			Score:            defaultAnnESScore,
			RecallScores:     []float64{defaultAnnESScore},
		})
		i += 4
	}
	recallStore := &model.RecallStore{
		Stores:        annRecallStores,
		TotalHits:     uint64(len(annRecallStores)),
		RecallTypeStr: recallType,
	}
	errCode = "0"
	return []*model.RecallStore{recallStore}, nil
}
