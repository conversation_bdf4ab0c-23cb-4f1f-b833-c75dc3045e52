package merge

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"math"
	"sort"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/Knetic/govaluate"
	"go.uber.org/zap"
)

// ner 3路需单独合并成1路后，再跟其他召回门店合并
func DoMergeStores(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallStores []*model.RecallStore) model.StoreInfos {
	pt := time.Now()
	var stores model.StoreInfos

	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreMerge, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenMerge, len(stores))
		traceInfo.AddPhraseStoreLength(ctx, "RecallCount", len(stores))
		// merge 之前记录下各路的召回数据
		if traceInfo.IsDebug {
			for _, recallStore := range recallStores {
				storeIdScores := make([]string, 0)
				for _, s := range recallStore.Stores {
					storeIdScores = append(storeIdScores, fmt.Sprintf("%d#%f", s.StoreId, s.ESScore))
				}
				if len(storeIdScores) == 0 {
					storeIdScores = append(storeIdScores, "无结果")
				}
				if len(recallStore.RecallTypeStr) == 0 {
					logkit.FromContext(ctx).Error("DoMergeStores recallStore.RecallTypeStr is empty", logkit.Any("RecallTypeStr", recallStore.RecallTypeStr), logkit.Any("RecallId", recallStore.RecallId))
				}
				traceInfo.AppendPhraseStoreInfos(recallStore.RecallTypeStr, storeIdScores)
			}
		}
	}()

	storeMap := make(map[uint64]*model.StoreInfo, 0)
	var dishStores model.StoreInfos
	for _, recallStore := range recallStores {
		// 记录size上报监控
		traceInfo.AddPhraseStoreLength(ctx, "StoreLenRecall"+recallStore.RecallTypeStr+"_"+recallStore.RecallId, len(recallStore.Stores))

		if len(recallStore.Stores) == 0 {
			continue
		}
		if isNer(recallStore.RecallTypeStr) {
			continue
		}
		if isI2i(recallStore.RecallTypeStr) {
			continue
		}
		if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
			if recallStore.RecallTypeStr == foodalgo_search.RecallType_StoreWithDishField.String() {
				dishStores = recallStore.Stores
				continue // th,my 菜品召回涉及dish召回以及分数替换，暂时跳过
			}
		}
		// 先合并除ner + i2i 6路之外的门店
		for _, store := range recallStore.Stores {
			mergeStore(storeMap, store)
		}
	}

	// th,my 菜品召回涉及dish召回以及分数替换，特殊召回菜品。后面不再召菜
	if len(dishStores) > 0 {
		for _, store := range dishStores {
			mergeDishStoreForTHMY(storeMap, store)
		}
	}

	// 单独合并3路ner成1路
	storeMap = doNerMerge(ctx, traceInfo, storeMap, recallStores)
	// 单独合并3路I2I成1路
	storeMap = doI2iMerge(ctx, traceInfo, storeMap, recallStores)

	stores = make([]*model.StoreInfo, 0, len(storeMap))
	for _, store := range storeMap {
		stores = append(stores, store)
	}

	// merge后是乱序的，先简单给一个顺序
	if decision.IsMockModelScore(ctx, traceInfo) {
		sort.Slice(stores, func(i, j int) bool {
			return stores[i].StoreId < stores[j].StoreId
		})
	}
	return stores
}

func isNer(recallType string) bool {
	if recallType == foodalgo_search.RecallType_StoreNerAndTag.String() {
		return true
	}
	if recallType == foodalgo_search.RecallType_StoreNerOrTag.String() {
		return true
	}
	if recallType == foodalgo_search.RecallType_StoreNerReserveTag.String() {
		return true
	}
	return false
}

func isI2i(recallType string) bool {
	if recallType == foodalgo_search.RecallType_I2IStoreCateExpand.String() {
		return true
	}
	if recallType == foodalgo_search.RecallType_I2IStoreIndex.String() {
		return true
	}
	if recallType == foodalgo_search.RecallType_I2IStoreWithDishField.String() {
		return true
	}
	return false
}

func mergeStore(storeMap map[uint64]*model.StoreInfo, store *model.StoreInfo) map[uint64]*model.StoreInfo {
	if _, ok := storeMap[store.StoreId]; !ok {
		storeMap[store.StoreId] = store
		return storeMap
	}
	storeId := store.StoreId
	storeMap[storeId].RecallQueries = append(storeMap[storeId].RecallQueries, store.RecallQueries...)
	storeMap[storeId].RecallTypes = append(storeMap[storeId].RecallTypes, store.RecallTypes...)
	storeMap[storeId].RecallTypeAndIds = append(storeMap[storeId].RecallTypeAndIds, store.RecallTypeAndIds...)
	storeMap[storeId].RecallScores = append(storeMap[storeId].RecallScores, store.RecallScores...)
	storeMap[storeId].RecallPosList = append(storeMap[storeId].RecallPosList, store.RecallPosList...)
	storeMap[storeId].EsExplains = append(storeMap[storeId].EsExplains, store.EsExplains...)
	// 相关性档位有重叠的时候，取max
	if store.RelLevelFromRecall > storeMap[storeId].RelLevelFromRecall {
		storeMap[storeId].RelLevelFromRecall = store.RelLevelFromRecall
	}
	// 合并时召菜要透传
	if store.IsNeedDishRecall {
		storeMap[storeId].IsNeedDishRecall = true
	}
	// 合并时取最大 score
	if store.Score > storeMap[storeId].Score {
		storeMap[storeId].Score = store.Score
		storeMap[storeId].ESScore = store.ESScore
	}
	// 赋值干预标识
	if store.StoreInterventionRecall == 1 {
		storeMap[storeId].StoreInterventionRecall = 1
	}
	if store.StoreInterventionWithMerchantIDRecall == 1 {
		storeMap[storeId].StoreInterventionWithMerchantIDRecall = 1
	}
	if store.BestSellingDishId > 0 {
		storeMap[storeId].BestSellingDishId = store.BestSellingDishId
	}
	// 打压类型标记，0-不进行打压，1-当门店仅从此路召回时进行标记，2-当门店有从此路召回时进行标记 两个同时有值，取max
	if store.RecallSuppressType > storeMap[storeId].RecallSuppressType {
		storeMap[storeId].RecallSuppressType = store.RecallSuppressType
	}

	// 相关性分数过滤标记: 宽松处理（分数越低，门店越容易留下）
	if storeMap[storeId].FilterRecallConfigPRelevanceScore != model.DefaultNonConfiguredPRelevanceScore && store.FilterRecallConfigPRelevanceScore != model.DefaultNonConfiguredPRelevanceScore {
		storeMap[storeId].FilterRecallConfigPRelevanceScore = math.Min(storeMap[storeId].FilterRecallConfigPRelevanceScore, store.FilterRecallConfigPRelevanceScore)
	} else if store.FilterRecallConfigPRelevanceScore == model.DefaultNonConfiguredPRelevanceScore {
		// 门店只要有其中一路召回不过滤相关性分数，那么这个门店就不应该被过滤相关性分数
		storeMap[storeId].FilterRecallConfigPRelevanceScore = model.DefaultNonConfiguredPRelevanceScore
	}

	// 距离过滤标记: 宽松处理（距离越大，门店越容易留下）
	if storeMap[storeId].FilterRecallConfigDistance != 0 && store.FilterRecallConfigDistance > 0 {
		storeMap[storeId].FilterRecallConfigDistance = math.Max(storeMap[storeId].FilterRecallConfigDistance, store.FilterRecallConfigDistance)
	} else if store.FilterRecallConfigDistance == 0 {
		// 门店只要有其中一路召回不过滤距离，那么这个门店就不应该被过滤距离
		storeMap[storeId].FilterRecallConfigDistance = 0
	}
	return storeMap
}

// ner 3路内部合并为1路
func doNerMerge(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeMap map[uint64]*model.StoreInfo, stores []*model.RecallStore) map[uint64]*model.StoreInfo {
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenMergeNer, len(storeMap))
	}()

	var storeNerAddTagRecall = model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreNerAndTag.String())
	var storeNerOrTagRecall = model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreNerOrTag.String())
	var storeNerReserveTagRecall = model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreNerReserveTag.String())

	storeNerMap := storeNerAddTagRecall.StoreInfoMap()
	for _, store := range storeNerOrTagRecall {
		mergeStore(storeNerMap, store)
	}
	for _, store := range storeNerReserveTagRecall {
		mergeStore(storeNerMap, store)
	}

	// 截断取前 500
	storeList := make(model.StoreInfos, 0, len(storeNerMap))
	for _, v := range storeNerMap {
		storeList = append(storeList, v)
	}
	sort.Slice(storeList, func(i, j int) bool {
		if storeList[i].Score == storeList[j].Score {
			return storeList[i].StoreId < storeList[j].StoreId
		}
		return storeList[i].Score > storeList[j].Score
	})
	nerSizeLimit := 500
	if traceInfo.PredictConfig != nil && traceInfo.PredictConfig.NerRecallTotal > 0 {
		nerSizeLimit = traceInfo.PredictConfig.NerRecallTotal
	}

	// ner 截断仅包含只有从ner3路召回的。 如果有其他路召回的，截断时不包括
	mergeCount := 0
	for _, store := range storeList {
		sid := store.StoreId
		if _, ok := storeMap[sid]; !ok {
			storeMap[sid] = store
			mergeCount += 1
			if mergeCount > nerSizeLimit {
				break
			}
		} else {
			mergeStore(storeMap, store)
		}
	}
	return storeMap
}

// doI2iMerge 处理 I2I 合并逻辑
func doI2iMerge(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeMap map[uint64]*model.StoreInfo, stores []*model.RecallStore) map[uint64]*model.StoreInfo {
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenMergeI2I, len(storeMap))
	}()

	i2iStoreRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_I2IStoreIndex.String())
	i2iDishRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_I2IStoreWithDishField.String())
	i2iCateRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_I2IStoreCateExpand.String())
	if len(i2iStoreRecallStores) == 0 && len(i2iDishRecallStores) == 0 && len(i2iCateRecallStores) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "doI2iMerge i2i recall is nil, give up merge")
		return storeMap
	}

	storeRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreIndex.String())
	dishRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreWithDishField.String())
	cateRecallStores := model.GetStoresByRecallType(stores, foodalgo_search.RecallType_StoreCateExpand.String())

	i2iCutStoreRecalls := doCutI2iStores(ctx, traceInfo, foodalgo_search.RecallType_I2IStoreIndex.String(), storeRecallStores, i2iStoreRecallStores, abtest.GetStoreIndexEsScorePercent, abtest.GetStoreIndexEsScoreThresholdFactor)
	i2iCutDishRecalls := doCutI2iStores(ctx, traceInfo, foodalgo_search.RecallType_I2IStoreWithDishField.String(), dishRecallStores, i2iDishRecallStores, abtest.GetStoreWithDishFieldEsScorePercent, abtest.GetStoreWithDishFieldEsScoreThresholdFactor)
	i2iCutCateRecalls := doCutI2iStores(ctx, traceInfo, foodalgo_search.RecallType_I2IStoreCateExpand.String(), cateRecallStores, i2iCateRecallStores, abtest.GetStoreCategoryEsScorePercent, abtest.GetStoreCategoryEsScoreThresholdFactor)

	// 合并 I2I 召回
	storeI2iMap := i2iCutStoreRecalls.StoreInfoMap()
	for _, store := range i2iCutDishRecalls {
		mergeStore(storeI2iMap, store)
	}
	for _, store := range i2iCutCateRecalls {
		mergeStore(storeI2iMap, store)
	}

	// sort
	storeList := make(model.StoreInfos, 0, len(storeI2iMap))
	for _, v := range storeI2iMap {
		storeList = append(storeList, v)
	}
	sort.Slice(storeList, func(i, j int) bool {
		if storeList[i].ESScore == storeList[j].ESScore {
			return storeList[i].StoreId < storeList[j].StoreId
		}
		return storeList[i].ESScore > storeList[j].ESScore
	})

	// 截断取前 n 个
	mergeCount := 0
	i2iSizeLimit := abtest.GetI2iMergeSize(traceInfo.AbParamClient) // 默认top=1000
	for _, store := range storeList {
		sid := store.StoreId
		if _, ok := storeMap[sid]; !ok {
			storeMap[sid] = store
			mergeCount++
			if mergeCount >= i2iSizeLimit {
				break
			}
		} else {
			mergeStore(storeMap, store)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "doI2iMerge final res", logkit.Int("i2iSizeLimit", i2iSizeLimit), logkit.Int("mergeI2ICount", mergeCount), logkit.Int("finalSize", len(storeMap)))
	return storeMap
}

func doCutI2iStores(ctx context.Context, traceInfo *traceinfo.TraceInfo, recallType string, originRecalls model.StoreInfos, i2iRecalls model.StoreInfos, percentFunction func(*abtest.SearchParamMultiClient) float64, thresholdFactorFunction func(*abtest.SearchParamMultiClient) float64) model.StoreInfos {
	if len(originRecalls) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "doI2iMerge doCutI2iStores origin recall is nil, return i2i recall", logkit.String("recallType", recallType), logkit.Int("i2iSize", len(i2iRecalls)))
		return i2iRecalls
	}

	// 提取原召回esScore并排序
	scores := make([]float64, len(originRecalls))
	for j, store := range originRecalls {
		scores[j] = store.ESScore
	}

	// 计算最小分数
	originMinScore := util.PercentileValue(scores, percentFunction(traceInfo.AbParamClient))
	minScore := originMinScore * thresholdFactorFunction(traceInfo.AbParamClient)

	// 过滤高于百分位的召回
	filtered := make(model.StoreInfos, 0)
	for _, store := range i2iRecalls {
		if store.ESScore >= minScore {
			filtered = append(filtered, store)
		}
	}

	originSize := len(originRecalls)
	beforeCutI2iSize := len(i2iRecalls)
	afterCutI2iSize := len(filtered)
	logger.MyDebug(ctx, traceInfo.IsDebug, "doI2iMerge doCutI2iStores", logkit.String("recallType", recallType), logkit.Int("originSize", originSize), logkit.Float64("originMinScore", originMinScore), logkit.Float64("minScore", minScore), logkit.Int("beforeCutI2iSize", beforeCutI2iSize), logkit.Int("afterCutI2iSize", afterCutI2iSize))
	return filtered

}

func DoStoresTruncate(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, recallStores []*model.RecallStore) model.StoreInfos {
	limit := 3000
	if traceInfo.RecallMaxSize > 0 {
		limit = traceInfo.RecallMaxSize
	}
	nStore := len(stores)
	if nStore <= limit {
		logger.MyDebug(ctx, traceInfo.IsDebug, "store length is less than max size, skip truncate", zap.Int("nStore", nStore), zap.Int("max size", limit))
		return stores
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseStoreMergeTruncate, time.Since(pt))
	}()
	mergeConfig := abtest.GetMergeConfig(ctx, traceInfo.IsDebug, traceInfo.AbParamClient)
	if len(mergeConfig.ExpString) == 0 {
		return truncateV1(ctx, traceInfo, stores, limit)
	} else {
		return truncateV2(ctx, traceInfo, mergeConfig, stores, limit, recallStores)
	}
}

// 按照各路召回保留、候补填充进行召回融合截断
func truncateV2(ctx context.Context, traceInfo *traceinfo.TraceInfo, mergeConfig *apollo.MergeConfig, stores []*model.StoreInfo, limit int, recallStores []*model.RecallStore) model.StoreInfos {
	// 保量门店标记
	minMergeSet := make(map[uint64]struct{}, len(stores))
	for _, recall := range recallStores {
		minMergeSize := mergeConfig.RecallMinSize[recall.RecallTypeStr]
		for i, store := range recall.Stores {
			if i >= minMergeSize {
				break
			}
			minMergeSet[store.StoreId] = struct{}{}
		}
	}
	// 保留填充+候补标记
	nStore := len(stores)
	nMinMerge := len(minMergeSet)
	resStores := make([]*model.StoreInfo, 0, nStore)
	remainStores := make(model.StoreInfos, 0, nStore-nMinMerge)
	for _, store := range stores {
		if _, exist := minMergeSet[store.StoreId]; exist {
			store.MergeType = 1 // 保留填充
			resStores = append(resStores, store)
		} else {
			remainStores = append(remainStores, store)
		}
	}
	// 排序截断
	if len(resStores) == limit {
		return resStores
	}
	// 当保量门店大于limit时，将所有保量门店进行统一排序后截断，剩余门店舍弃
	if len(resStores) > limit {
		for i, store := range remainStores {
			store.MergeType = 3 // 截断过滤
			index := limit + i
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeMultiMergeSizeLimit, index)
		}
		remainStores = resStores
		resStores = make([]*model.StoreInfo, 0, limit)
	}
	// 统一打分排序
	expression := util.BuildExpression(ctx, mergeConfig.ExpString)
	parameters := util.BuildExpParameters(mergeConfig.ExpParameters)
	for _, store := range remainStores {
		store.CategoryMatchScore = store.CountCategoryMatchScore(traceInfo.QPResult.ProbFilterCategoryIntentions) // 仅需要排序的计算，如果全部门店都计算，可以挪到filling
		store.MergeScore = countMergeScore(ctx, store, expression, parameters)
	}
	remainStores.SortByMergeScore()
	truncateSize := limit - len(resStores)
	for _, store := range remainStores[:truncateSize] {
		store.MergeType = 2 // 候补填充
		resStores = append(resStores, store)
	}
	for i, store := range remainStores[truncateSize:] {
		store.MergeType = 3 // 截断过滤
		index := limit + i
		traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeMultiMergeSizeLimit, index)
	}
	return resStores
}

// 直接合并门店，并按照距离、ID排序后进行截断
func truncateV1(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, limit int) []*model.StoreInfo {
	if len(stores) > limit {
		return stores
	}
	sort.Slice(stores, func(i, j int) bool {
		if stores[i].Distance == stores[j].Distance {
			return stores[i].StoreId > stores[j].StoreId
		}
		return stores[i].Distance < stores[j].Distance
	})
	for i, fs := range stores[limit:] {
		index := limit + i
		traceInfo.AddFilteredStore(fs.StoreId, traceinfo.FilteredTypeMultiMergeSizeLimit, index)
	}
	return stores[0:limit]
}

func countMergeScore(ctx context.Context, store *model.StoreInfo, expression *govaluate.EvaluableExpression, parameters map[string]interface{}) float64 {
	if expression == nil {
		return 0.0
	}
	parameters["dist_score"] = store.DistanceScore
	parameters["term_match_score"] = store.TermMatchScore
	parameters["category_match_score"] = store.CategoryMatchScore
	score, _ := util.EvaluateScore(ctx, expression, parameters)
	return score
}
