package merge

import (
	"fmt"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func mergeDishStoreForTHMY(storeMap map[uint64]*model.StoreInfo, store *model.StoreInfo) map[uint64]*model.StoreInfo {
	if existStore, ok := storeMap[store.StoreId]; ok && existStore != nil {
		existStore.DishInfos = store.DishInfos
		existStore.RecallTypes = append(existStore.RecallTypes, foodalgo_search.RecallType_DishIndex.String())
		existStore.RecallTypeAndIds = append(existStore.RecallTypeAndIds, fmt.Sprintf("%s_1", foodalgo_search.RecallType_DishIndex.String()))
		if len(store.DishInfos) > 0 {
			existStore.RecallScores = append(existStore.RecallScores, store.DishInfos[0].ESScore)
		}
		if store.FilterRecallConfigPRelevanceScore == model.DefaultNonConfiguredPRelevanceScore {
			existStore.FilterRecallConfigPRelevanceScore = model.DefaultNonConfiguredPRelevanceScore
		}
		return storeMap
	} else {
		storeMap[store.StoreId] = store
	}
	return storeMap
}

func TransDishStoreForTHMY(searchDishes model.DishInfos) []*model.StoreInfo {
	dishMap := searchDishes.StoreDishInfosMap()
	stores := make([]*model.StoreInfo, 0)
	for storeID, dishes := range dishMap {
		stores = append(stores, &model.StoreInfo{
			StoreId:                           storeID,
			DishInfos:                         dishes,
			ESScore:                           dishes[0].ESScore,
			Score:                             0.0, // master 代码没有对Score赋值，这里强制为0，否则会影响门店分数
			RecallTypes:                       []string{foodalgo_search.RecallType_DishIndex.String()},
			RecallTypeAndIds:                  []string{fmt.Sprintf("%s_1", foodalgo_search.RecallType_DishIndex.String())},
			RecallScores:                      []float64{dishes[0].ESScore},
			FilterRecallConfigPRelevanceScore: model.DefaultNonConfiguredPRelevanceScore,
		})
	}
	return stores
}
