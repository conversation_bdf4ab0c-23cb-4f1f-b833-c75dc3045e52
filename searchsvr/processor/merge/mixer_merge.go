package merge

import (
	"context"
	"encoding/json"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/protobuf/foodads_mixer"
	ads_o2oalgo "git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/protobuf/foodads_o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
)

func NormalAndAdsStoresMerge(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalStores, adsStores model.StoreInfos) (model.StoreInfos, error) {
	mixSize, adsSize := 0, 0
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenMixer, mixSize)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankMixer, time.Since(pt))
		metric_reporter2.ReportItemNumAndQPS(mixSize, metric_reporter2.SearchReportTypePhrase, traceInfo.HandlerType.String(), "", constants.MixerStoreNum)
		metric_reporter2.ReportItemNumAndQPS(adsSize, metric_reporter2.SearchReportTypePhrase, traceInfo.HandlerType.String(), "", constants.MixerAdsStoreNum)
	}()

	if len(adsStores) == 0 {
		mixSize = len(normalStores)
		return normalStores, nil
	}

	normalItemList := buildMixReqItemList(normalStores, ads_o2oalgo.ItemType_Normal)
	adsItemList := buildMixReqItemList(adsStores, ads_o2oalgo.ItemType_Ads)
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("SearchGlobal recall size info", zap.Int("list", len(normalItemList)), zap.Int("ads_list", len(adsItemList)))
		logkit.FromContext(ctx).Info("SearchGlobal recall info", zap.Any("list", normalItemList), zap.Any("ads_list", adsItemList))
	}
	queryType := ads_o2oalgo.QueryType_Unknown
	if traceInfo.QPResult != nil {
		if len(traceInfo.QPResult.QueryStoreIntention) != 0 {
			queryType = ads_o2oalgo.QueryType_Store
		} else if len(traceInfo.QPResult.QueryDishIntention) != 0 {
			queryType = ads_o2oalgo.QueryType_Dish
		}
	}
	var deviceId string
	if traceInfo.TraceRequest.ExtClientInfo != nil {
		deviceId = traceInfo.TraceRequest.ExtClientInfo.GetDeviceId()
	}
	mixReq := &foodads_mixer.MixerReq{
		QueryType: queryType,
		AbTest:    traceInfo.ABTestGroup.GetABTestString(),
		MixerReqBase: &foodads_mixer.AdsSearchMixReqBase{
			PubContextId: proto.String(traceInfo.TraceRequest.PublishId),
			UserId:       proto.Uint64(traceInfo.UserId),
			DeviceId:     proto.String(deviceId),
		},
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearch {
		if traceInfo.IsVnMart == true {
			mixReq.MartNormalList = normalItemList
			mixReq.MartAdsList = adsItemList
		} else {
			mixReq.NormalList = normalItemList
			mixReq.AdsList = adsItemList
		}
	}
	startTime := time.Now()
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.MixerTimeOut)*time.Millisecond)
	defer cancel()
	resp, err := integrate.MixerClient.MixRank(ctx, mixReq)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "Mixer"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "mixer")
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(mixReq)
		resStr, _ := json.Marshal(resp)
		logger.MyDebug(ctx, traceInfo.IsDebug, "diff debug log:MixRank", logkit.String("req", string(reqStr)), logkit.String("resp", string(resStr)))
	}
	if err != nil {
		traceInfo.AddErrorToTraceInfo(err)
		logkit.FromContext(ctx).WithError(err).Error("failed to mixer resp", zap.String("cost", time.Since(startTime).String()), zap.String("error", err.Error()))
		metric_reporter.ReportClientRequestError(1, "mixer", "failed")
		return normalStores, nil // mixer 失败时，记录APT和错误日志，返回搜索结果，且将错误内部吞掉
	}
	if len(resp.GetItemList()) == 0 && len(resp.GetMartItemList()) == 0 {
		traceInfo.AddErrorToTraceInfo(err)
		metric_reporter.ReportClientRequestError(1, "mixer", "failed")
		return normalStores, nil // mixer 返回空时，记录APT和错误日志，返回搜索结果，且将错误内部吞掉
	}
	metric_reporter.ReportClientRequestError(1, "mixer", "0")

	normalStoreMap := normalStores.StoreInfoMap()
	adsStoreMap := adsStores.StoreInfoMap()
	var resItemList []*ads_o2oalgo.RspItem
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.IsVnMart {
		resItemList = resp.GetMartItemList()
	} else {
		resItemList = resp.GetItemList()
	}
	mixerStores := make([]*model.StoreInfo, 0, len(resp.GetItemList()))
	for _, item := range resItemList {
		if item.GetItemType() == ads_o2oalgo.ItemType_Normal && normalStoreMap[item.GetStoreId()] != nil {
			mixerStores = append(mixerStores, normalStoreMap[item.GetStoreId()])
		} else if item.GetItemType() == ads_o2oalgo.ItemType_Ads && adsStoreMap[item.GetStoreId()] != nil {
			adsSize++
			store := adsStoreMap[item.GetStoreId()]
			if len(item.GetAdsExtraInfo()) > 0 {
				store.AdsExtraInfo = item.GetAdsExtraInfo()
			}
			if val, ok := normalStoreMap[item.GetStoreId()]; ok {
				store.L2CategoryId = val.L2CategoryId
			}
			mixerStores = append(mixerStores, store)
		}
	}

	mixSize = len(mixerStores)
	return mixerStores, nil
}

func buildMixReqItemList(stores model.StoreInfos, itemType ads_o2oalgo.ItemType) []*ads_o2oalgo.ReqItem {
	nStore := len(stores)
	itemList := make([]*ads_o2oalgo.ReqItem, 0, nStore)
	itemInfos := make([]ads_o2oalgo.ReqItem, nStore)
	for i, store := range stores {
		itemInfos[i].StoreId = store.StoreId
		itemInfos[i].ItemType = itemType
		itemInfos[i].BrandId = store.BrandId
		itemInfos[i].MerchantId = store.MerchantId
		if itemType == ads_o2oalgo.ItemType_Ads {
			itemInfos[i].RelevanceLevel = store.PredictRelevanceLevel
			itemInfos[i].RelevanceScore = store.PredictRelevance
			itemInfos[i].ExtInfo = store.AdsExtraInfoInternal
			itemInfos[i].AdsExtraInfo = store.AdsExtraInfo
		}
		itemList = append(itemList, &itemInfos[i])
	}
	return itemList
}
