package merge

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreMergeDish(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos, dishInfos model.DishInfos) model.StoreInfos {
	if len(dishInfos) == 0 || len(storeInfos) == 0 {
		return storeInfos
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMerge, time.Since(pt))
	}()
	storeDishMap := dishInfos.StoreDishInfosMap()
	for _, store := range storeInfos {
		if store == nil || store.StoreId == 0 {
			continue
		}
		dishes, ok := storeDishMap[store.StoreId]
		if !ok || len(dishes) == 0 {
			continue
		}
		if len(store.DishInfos) == 0 {
			store.DishInfos = make([]*model.DishInfo, 0)
		}
		store.DishInfos = append(store.DishInfos, dishes...)
	}
	return storeInfos
}
