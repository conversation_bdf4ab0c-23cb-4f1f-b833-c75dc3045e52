package processor

import (
	"context"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filling"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
)

// 搜索主站主流程：召回，正排填充，距离过滤，菜品填充，同品牌合并，排序
func MainSitePipeline(ctx context.Context, traceInfo *traceinfo.TraceInfo) (model.StoreInfos, model.StoreInfos, error) {
	// 每个pipeline进来要初始化数据源
	preprocess.TraceInfoReInitDataSource(ctx, traceInfo)

	pt := time.Now()
	var err error
	var storeFilling, storeInfos model.StoreInfos
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseSearchMainSitePipeline, time.Since(pt))
	}()

	// 门店多路召回
	recallStores, err := recall.StoreRecallFromMultiSource(ctx, traceInfo)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("PhraseSearchMainSitePipeline recall failed, return error")
		return nil, nil, err
	}

	// 门店多路合并
	mergeStores := merge.DoMergeStores(ctx, traceInfo, recallStores)
	// 正排信息填充. vn 需要填充导航距离
	storeFilling, err = filling.Filling(ctx, traceInfo, mergeStores)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("PhraseSearchMainSitePipeline filling meta data failed, return error")
		return nil, nil, err
	}

	// 召回融合截断
	storeTruncate := merge.DoStoresTruncate(ctx, traceInfo, storeFilling, recallStores)

	// 门店过滤
	storeInfos = filter.SearchMainSiteFilter(ctx, storeTruncate, traceInfo)

	// 门店挂菜
	storeInfos = MultiRecallDishesForMainSite(ctx, traceInfo, storeInfos)

	// 门店精排
	storeInfos = rank.MainSiteStoreRank(ctx, traceInfo, storeInfos)

	return storeFilling, storeInfos, nil
}

func MultiRecallDishesForMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) model.StoreInfos {
	if storeInfos == nil {
		return nil
	}

	// id 保持不动
	if cid.IsID() {
		storeInfos = recall.MultiRecallDishesForMainSite(ctx, traceInfo, storeInfos)
		return storeInfos
	}

	// th,my,vn 支持多路召回
	dishInfos, _ := recall2.MultiRecallDishes(ctx, traceInfo, storeInfos.StoreIDs())
	dishInfos, _ = filling.DishMetaFilling(ctx, traceInfo, dishInfos)
	dishInfos = filter.DishBatchFilter(ctx, traceInfo, dishInfos)
	storeInfos = merge.StoreMergeDish(ctx, traceInfo, storeInfos, dishInfos)

	// 菜品过滤
	storeInfos = filter.DishFilterMainSite(ctx, traceInfo, storeInfos)
	return storeInfos
}
