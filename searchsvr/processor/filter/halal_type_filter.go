package filter

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByHalalType(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if env.GetCID() != cid.MY {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	req := traceInfo.TraceRequest
	if req.GetFilterType() == nil || req.GetFilterType().GetHalalFilterList() == nil || len(req.GetFilterType().GetHalalFilterList()) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.FilterByHalalType, not need to filter", logkit.Int("len", len(stores)))
		return stores
	}
	reqHalalTypes := req.GetFilterType().GetHalalFilterList()
	nStores := len(stores)
	resStores := make(model2.StoreInfos, 0, nStores)
	for iStore := 0; iStore < nStores; iStore++ {
		bNeed := false
		for i := range reqHalalTypes {
			if int32(reqHalalTypes[i]) == int32(stores[iStore].HalalType) {
				bNeed = true
				break
			}
		}

		if bNeed {
			resStores = append(resStores, stores[iStore])
		} else {
			traceInfo.AddFilteredStore(stores[iStore].StoreId, traceinfo.FilterByHalalType, stores[iStore].HalalType)
		}
	}

	return resStores
}
