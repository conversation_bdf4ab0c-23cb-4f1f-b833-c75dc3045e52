package filter

import (
	"context"
	"sort"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 排序后进行同品牌过滤，原有顺序不能打乱。已下线，待删除
func DuplicateBrandFilterVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) []*model.StoreInfo {
	if len(stores) == 0 {
		return stores
	}
	resStores := make([]*model.StoreInfo, 0, len(stores))
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilterDuplicateBrand, len(resStores))
	}()
	var nonBrandStore model.StoreInfos
	brandStoreMap := make(map[uint64]model.StoreInfos)
	brandStoreIndex := make(map[uint64]int) // 标记同品牌下已替换的门店位置
	brandWithDish := make(map[uint64]bool)
	for _, store := range stores {
		brandID := store.GetBrandId()
		if brandID > 0 {
			brandStoreMap[brandID] = append(brandStoreMap[brandID], store)
			brandStoreIndex[brandID] = 0
			if len(store.DishInfos) > 0 {
				brandWithDish[brandID] = true
			}
		} else {
			nonBrandStore = append(nonBrandStore, store)
		}
	}
	filterIDs := make([]uint64, 0, len(stores))
	keywordNormal := util2.StringNormalize(traceInfo.QueryKeyword) // query 归一化处理
	for _, v := range brandStoreMap {
		if len(v) > 1 {
			isBranchTermMatch := IsBranchTermMatch(ctx, traceInfo, v, keywordNormal)
			// https://confluence.shopee.io/display/SPFOODY/%5BEpic%5D%5BVN%5Dbrand+complete+branch+name+search+improvement
			// 排序：场景1: 当Query为品牌名时。exp：Ga Ran KFC
			//            按照：status > match score > distance > store id合并
			//      场景2: 当Query为带有branch name的完整店名时。exp：Ga Ran KFC-Lê Lai
			//             按照：match score > distance > store id合并（不考虑status）
			// 1级排序：门店状态（open > pause > closed）
			// 2级排序：real name 相似度
			// 3级排序：距离（近 > 远）
			// 4级排序：storeID（storeID 升序）
			sort.Slice(v, func(i int, j int) bool {
				if !isBranchTermMatch {
					statusI := v[i].GetOpeningState()
					statusJ := v[j].GetOpeningState()
					if statusI != statusJ {
						// 状态不同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
						return statusI < statusJ
					}
				}
				if v[i].TermMatchScore != v[j].TermMatchScore {
					return v[i].TermMatchScore > v[j].TermMatchScore
				}
				if !acc.Equal(v[i].Distance, v[j].Distance) {
					return v[i].Distance < v[j].Distance
				}
				return v[i].StoreId < v[j].StoreId
			})
		}
	}
	for _, store := range stores {
		if store.BrandId <= 0 {
			resStores = append(resStores, store)
			continue
		}
		brandStores := brandStoreMap[store.BrandId]
		if len(brandStores) == 1 {
			resStores = append(resStores, store)
			continue
		}
		//只要有一个门店有菜品，全部分店保留
		//所有门店都没有菜品，按照brand聚合展示，展示排序第一的门店
		storeIndex := brandStoreIndex[store.BrandId]
		brandStore := brandStores[storeIndex]
		if brandStore == nil {
			continue
		}
		if brandWithDish[store.BrandId] == true || storeIndex == 0 {
			resStores = append(resStores, brandStore)
		} else {
			filterIDs = append(filterIDs, brandStore.StoreId) //品牌下门店无菜品且第二个门店，直接过滤
			traceInfo.AddFilteredStore(brandStore.StoreId, traceinfo.FilteredTypeDuplicateBrand, store.BrandId)
		}
		brandStoreIndex[store.BrandId]++
	}
	logger.MyDebug(ctx, traceInfo.IsDebug,
		"SearchService.filterDuplicateBrand",
		zap.Int("total length before filterDuplicateBrand", len(stores)),
		zap.Int("total length after filterDuplicateBrand", len(resStores)),
		zap.Any("filterByDuplicateBrandIDs", filterIDs))
	return resStores
}

// 品牌合并PRD:https://confluence.shopee.io/pages/viewpage.action?spaceKey=SPFOODY&title=%5BEpic%5D%5BVN%5D+Brand+Merge+Logic+Improvement
func DuplicateBrandMergeVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, normalStores, adsStores model.StoreInfos) []*model.StoreInfo {
	if len(normalStores) == 0 {
		return normalStores
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchGlobalV1 {
		return normalStores
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchIdsDishes {
		return normalStores
	}
	resStores := make([]*model.StoreInfo, 0, len(normalStores))
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilterDuplicateBrand, len(resStores))
	}()
	brandStoreMap := make(map[uint64]model.StoreInfos)
	skipMergeBrandMap := make(map[uint64]bool)

	// 1.归集同品牌门店  2.标记该品牌是否有门店属于广告、干预门店. 3.命中品牌保护的请求，也需要品牌合并
	adsStoreMap := adsStores.StoreInfoMap()
	for _, store := range normalStores {
		brandID := store.GetBrandId()
		if brandID <= 0 {
			resStores = append(resStores, store) // 无品牌ID的门店直接填充
			continue
		}
		brandStoreMap[brandID] = append(brandStoreMap[brandID], store)
		if _, exist := adsStoreMap[store.StoreId]; exist {
			skipMergeBrandMap[brandID] = true
			continue
		}
		if store.StoreInterventionRecall == 1 || store.StoreInterventionWithMerchantIDRecall == 1 {
			skipMergeBrandMap[brandID] = true
			continue
		}
	}
	keywordNormal := util2.StringNormalize(traceInfo.QueryKeyword) // query 归一化处理
	for brandID, v := range brandStoreMap {
		if len(v) <= 1 || skipMergeBrandMap[brandID] == true {
			resStores = append(resStores, v...) // 品牌只有1个门店，或者不需要有广告、干预门店，直接填充
			continue
		}
		isBranchTermMatch := IsBranchTermMatch(ctx, traceInfo, v, keywordNormal) // 是否精准分店
		//当查询精准分店,排序逻辑： match score > fusion score > distance > store id
		//当未命中精准店铺，排序逻辑：store status > fusion score > distance > store id
		sort.Slice(v, func(i int, j int) bool {
			if isBranchTermMatch {
				if v[i].TermMatchScore != v[j].TermMatchScore {
					return v[i].TermMatchScore > v[j].TermMatchScore
				}
			} else {
				statusI := v[i].GetOpeningState()
				statusJ := v[j].GetOpeningState()
				if statusI != statusJ {
					// 状态不同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
					return statusI < statusJ
				}
			}
			if !acc.Equal(v[i].ReRankScore, v[j].ReRankScore) {
				return v[i].ReRankScore > v[j].ReRankScore
			}
			if !acc.Equal(v[i].Distance, v[j].Distance) {
				return v[i].Distance < v[j].Distance
			}
			return v[i].StoreId < v[j].StoreId
		})
		resStores = append(resStores, v[0])
		for _, s := range v[1:] {
			traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateBrand, v[0].StoreId) // 记录保留下来的同品牌门店ID
			if traceInfo.IsDebug {
				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateBrandDetails, s) // 记录被过滤掉的的同品牌门店info
			}
		}
	}
	return resStores
}

func IsBranchTermMatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos, keyword string) bool {
	//keyword = util2.StringNormalize(keyword)   在逻辑上游归一化一次传入
	for _, s := range stores {
		storeNameNor := util2.StringNormalize(s.StoreName)
		if storeNameNor == keyword {
			return true
		}
		branchNameNor := util2.StringNormalize(s.BranchName)
		termMatchCount := util2.CountTermMatchScore(keyword, branchNameNor)
		if termMatchCount == 0 {
			continue
		}
		thread := util2.CountTermMatchThread(branchNameNor)
		if traceInfo.IsDebug {
			logger.MyDebug(ctx, traceInfo.IsDebug, "IsBranchTermMatch", zap.String("branch name", branchNameNor),
				zap.Uint32("match count", termMatchCount), zap.Uint32("thread", thread))
		}
		// 不用考虑query term 的个数小于 thread
		if termMatchCount >= thread {
			return true
		}
	}
	return false
}
