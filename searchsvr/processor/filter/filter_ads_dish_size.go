package filter

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func AdsDishSizeLimitFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos, limitSize int) model2.StoreInfos {
	if len(storeInfos) == 0 {
		return storeInfos
	}
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("AdsDishSizeLimitFilter", logkit.Int("limitSize", limitSize))
	}

	for _, store := range storeInfos {
		if store.DishInfos == nil || len(store.DishInfos) == 0 {
			continue
		}
		if len(store.DishInfos) > limitSize {
			store.DishInfos = store.DishInfos[:limitSize]
		}
	}
	return storeInfos
}

func AdsDishNotShowSizeLimitFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	if len(storeInfos) == 0 {
		return storeInfos
	}

	adsDishNotShowSize := abtest.GetDishNotShowSize(traceInfo.AbParamClient)
	for _, store := range storeInfos {
		if store.DishInfos == nil || len(store.DishInfos) == 0 {
			continue
		}
		if len(store.DishInfos) < adsDishNotShowSize {
			store.DishInfos = nil
		}
	}
	return storeInfos
}
