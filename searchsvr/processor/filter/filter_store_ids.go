package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoreIdsFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(traceInfo.TraceRequest.GetFilterType().GetStoreIds()) > 0 {
		storeIds := traceInfo.TraceRequest.GetFilterType().GetStoreIds()
		storeMap := make(map[uint32]bool, 0)
		for _, id := range storeIds {
			storeMap[id] = true
		}
		ss := make(model.StoreInfos, 0)
		for _, s := range stores {
			if storeMap[uint32(s.StoreId)] {
				ss = append(ss, s)
			} else {
				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilterByStoreIds, nil)
			}
		}
		stores = ss
	}
	return stores
}
