package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const (
	ratingTotalThreshold = 10
	ratingScore50        = 4.95
	ratingScore45        = 4.45
	ratingScore40        = 3.95
)

// 店铺评分筛选
func RatingScoreFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	filter := traceInfo.TraceRequest.GetFilterType()
	if filter == nil || filter.RatingFilter == nil || filter.GetRatingFilter() == foodalgo_search.SearchRequest_NotRating {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.filterByRating, not need to filter", zap.Int("len", len(stores)))
		return stores
	}
	for _, store := range stores {
		switch filter.GetRatingFilter() {
		case foodalgo_search.SearchRequest_Rating_50:
			if store.RatingTotal >= ratingTotalThreshold && store.RatingScore >= ratingScore50 {
				resStoreIDs = append(resStoreIDs, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeRatingScore, store.RatingScore)
			}
		case foodalgo_search.SearchRequest_Rating_45:
			if store.RatingTotal >= ratingTotalThreshold && store.RatingScore >= ratingScore45 {
				resStoreIDs = append(resStoreIDs, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeRatingScore, store.RatingScore)
			}
		case foodalgo_search.SearchRequest_Rating_40:
			if store.RatingTotal >= ratingTotalThreshold && store.RatingScore >= ratingScore40 {
				resStoreIDs = append(resStoreIDs, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeRatingScore, store.RatingScore)
			}
		}
	}
	return resStoreIDs
}
