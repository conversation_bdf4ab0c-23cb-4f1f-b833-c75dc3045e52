package filter

import (
	"context"
	"sort"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	common "git.garena.com/shopee/o2o-intelligence/common/common-lib/math"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

var acc common.Accuracy = func() float64 { return 0.000001 }

func DuplicateBrandFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchCollection {
		if !traceInfo.TraceRequest.GetFilterType().GetNeedGroupByBrand() {
			logkit.FromContext(ctx).Debug("DuplicateBrandFilter SearchCollection not need brand merge")
			return stores
		}
	}

	resStores := make([]*model2.StoreInfo, 0, len(stores))
	brandStoreMap := make(map[uint64]model2.StoreInfos)
	// 命中门店意图的brandID集合
	storeIntentionBrandIdMap := make(map[uint64]bool)
	for _, store := range stores {
		brandID := store.GetBrandId()
		if brandID != 0 {
			brandStoreMap[brandID] = append(brandStoreMap[brandID], store)
			if traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 && store.StoreInterventionRecall == 1 {
				storeIntentionBrandIdMap[brandID] = true
				logger.MyDebug(ctx, traceInfo.IsDebug, "DuplicateBrandFilter hit brand", zap.Any("storeId", store.StoreId), zap.Any("brandId", store.BrandId), zap.Any("store", store))
			}
		}
	}
	for _, store := range stores {
		brandID := store.GetBrandId()
		if brandID != 0 {
			if _, ok := storeIntentionBrandIdMap[brandID]; ok && traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 {
				// 命中门店意图，该品牌的所有门店都需要置为true. 新排序可能将非门店意图召回的门店排第一位
				logger.MyDebug(ctx, traceInfo.IsDebug, "DuplicateBrandFilter same brand, store.StoreInterventionRecall = 1", zap.Any("storeId", store.StoreId), zap.Any("brandId", store.BrandId))
				store.StoreInterventionRecall = 1
			}
		} else {
			resStores = append(resStores, store)
		}
	}

	// 品牌店铺取排序最前的第一个
	for _, v := range brandStoreMap {
		if len(v) > 1 {
			// 1级排序：门店状态（open > pause > closed）
			// 2级排序：距离（近 > 远）
			// 3级排序：storeID（storeID 升序）
			sort.Slice(v, func(i int, j int) bool {
				statusI := v[i].GetOpeningState()
				statusJ := v[j].GetOpeningState()
				if statusI != statusJ {
					// 状态不同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
					return statusI < statusJ
				}
				if !acc.Equal(v[i].Distance, v[j].Distance) {
					return v[i].Distance < v[j].Distance
				}
				return v[i].StoreId < v[j].StoreId
			})
		}
		resStores = append(resStores, v[0])
		if len(v) > 1 {
			for _, s := range v[1:] {
				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateBrand, v[0].StoreId) // 记录保留下来的同品牌门店ID
			}
		}
	}
	return resStores
}
