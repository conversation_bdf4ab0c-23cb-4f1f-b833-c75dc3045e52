package filter

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func IsPreferredMerchantFilter(traceInfo *traceinfo.TraceInfo, filter *foodalgo_search.SearchRequest_FilterType, stores model2.StoreInfos) model2.StoreInfos {
	if filter == nil {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if filter.GetIsPreferredMerchant() {
		resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
		for _, store := range stores {
			if store.IsPreferredMerchant == 1 {
				resStoreIDs = append(resStoreIDs, store)
			}
		}
		return resStoreIDs
	} else {
		return stores
	}
}
