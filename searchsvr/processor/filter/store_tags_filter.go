package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByStoreTags(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedStoreTags(traceInfo) == false {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	nStores := len(stores)
	reqStoreTagOuterList := traceInfo.TraceRequest.GetFilterType().GetStoreTags()
	nReqStoreTagOuterList := len(reqStoreTagOuterList)
	resStores := make(model2.StoreInfos, 0, nStores)

	for _, store := range stores {
		storeTags := store.StoreTags
		if len(storeTags) == 0 {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreTags, store.StoreTags)
			continue
		}
		nOuterMatches := 0
		for iOuter := range reqStoreTagOuterList {
			nInnerMatches := 0
			userTagsList := reqStoreTagOuterList[iOuter].GetStoreTags()
			logicalLink := reqStoreTagOuterList[iOuter].GetStoreTagsLogic()
			for idx := range userTagsList {
				if logicalLink == 1 { //or: at least match one tag that user selected
					if _, ok := storeTags[userTagsList[idx]]; ok {
						nInnerMatches += 1
						break
					}
				} else { // and: must match all tags that user selected
					if _, ok := storeTags[userTagsList[idx]]; ok {
						nInnerMatches += 1
					}
				}
			}
			if logicalLink == 1 {
				if nInnerMatches > 0 {
					nOuterMatches += 1
				}
			} else {
				if nInnerMatches == len(userTagsList) {
					nOuterMatches += 1
				}
			}
		}
		if nOuterMatches == nReqStoreTagOuterList {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreTags, store.StoreTags)
		}
	}
	return resStores
}
