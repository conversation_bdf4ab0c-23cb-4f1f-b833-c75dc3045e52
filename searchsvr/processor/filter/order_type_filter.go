package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByOrderType(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 || traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList() == nil || len(traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList()) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	nStores := len(stores)
	reqOrderTypeList := traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList()
	resStores := make(model2.StoreInfos, 0, nStores)

	for _, store := range stores {
		bNeed := false
		for iOrderType := range reqOrderTypeList {
			if int32(reqOrderTypeList[iOrderType]) == int32(store.IsSelfPickUp) {
				bNeed = true
				break
			}
		}

		if bNeed {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByOrderType, store.IsSelfPickUp)
		}
	}
	return resStores
}
