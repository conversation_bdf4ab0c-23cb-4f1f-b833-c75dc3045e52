package filter

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const DishLimit = 2 // 每个店铺下仅展示2个菜品
const DishForMainSiteLimit = 1

// ID,TH,MY 站内菜品过滤
func DishFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	if cid.IsVN() {
		return storeInfos
	}
	if traceInfo.PipelineType != traceinfo.PipelineTypeSearch || len(storeInfos) == 0 {
		return storeInfos
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFilterDish, time.Since(pt))
	}()

	// 是否斋月过滤
	isFilterNonHalal := decision.IsFilterNonHalal(ctx, traceInfo)

	// 是否零价格过滤
	zeroPriceFilterSwitch := abtest.GetDishZeroPriceFilterSwitch(traceInfo.AbParamClient)

	resStoreIDs := make([]*model2.StoreInfo, 0, len(storeInfos))
	for _, store := range storeInfos { // 此处只能用list类型的storeInfos,不能用map类型的storeMap,否则门店顺序会被打乱,
		dishes := store.DishInfos
		if len(dishes) == 0 {
			resStoreIDs = append(resStoreIDs, store) // 没有菜品，直接保留，不处理
			continue
		}
		// 菜品过滤
		if traceInfo.IsDowngradeDataServer == false {
			// On SaleTime 的过滤再正排 saleStatus 字段已经算过了
			//dishes = NotOnSaleTimeDishesFilter(traceInfo, traceInfo.TraceRequest.SearchTime, dishes, store.Timezone)
			dishes = OutOfStockAndSpecialSaleTimeFilter(traceInfo, dishes)
			dishes = FilterNonHalalDishWhenRamadan(ctx, traceInfo, dishes, isFilterNonHalal) // 斋月过滤
			// 零价格过滤
			if zeroPriceFilterSwitch {
				dishes = FilterDishByZeroPrice(ctx, traceInfo, dishes) // 价格为0的菜品过滤
			}
		}
		// 原本有菜品，但是被上面全部过滤了
		if len(dishes) == 0 {
			// th,my 只会保留一个 DishIndex
			if len(store.RecallTypes) == 1 &&
				store.RecallTypes[0] == foodalgo_search.RecallType_DishIndex.String() {
				//过滤掉仅通过dish信息召回的门店
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeDishSaleTime, nil)
				continue
			} else if len(store.RecallTypes) == 2 &&
				store.RecallTypes[0] == foodalgo_search.RecallType_StoreWithDishField.String() &&
				store.RecallTypes[1] == foodalgo_search.RecallType_DishIndex.String() {
				//过滤掉仅通过dish信息召回的门店
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeDishSaleTime, nil)
				continue
			} else {
				// 门店&菜品都有召回，且菜品都被过滤，需要去掉菜品，保留门店. 但是主站不需要保留此门店
				store.DishInfos = nil
				resStoreIDs = append(resStoreIDs, store)
				continue
			}
		} else {
			// 根据 score 对 dishes 排序
			dishes = dishes.Sort()
			dishes = dishes.Truncate(DishLimit)
			store.DishInfos = dishes
			//ID 地区取 store 和 dish 最大的 score 作为店铺的 score. TH,MY 不会走这里
			if env.GetCID() != cid.VN && store.Score < dishes[0].Score {
				store.Score = dishes[0].Score
				store.ESScore = dishes[0].Score
			}
			resStoreIDs = append(resStoreIDs, store)
		}
	}
	return resStoreIDs
}

// ID,TH,MY,VN 新挂菜的菜品过滤，未排序
func DishFilterV2(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	if len(storeInfos) == 0 {
		return storeInfos
	}
	totalESCnt, totalFSCnt := 0, 0
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingFilter, time.Since(pt))
		metric_reporter2.ReportHitRateTotalRequest(totalESCnt, "FinalDishFromES")
		metric_reporter2.ReportHitRateTotalRequest(totalFSCnt, "FinalDishFromFS")
	}()

	// 是否斋月过滤
	isFilterNonHalal := decision.IsFilterNonHalal(ctx, traceInfo)

	// 是否零价格过滤
	zeroPriceFilterSwitch := abtest.GetDishZeroPriceFilterSwitch(traceInfo.AbParamClient)

	for _, store := range storeInfos { // 此处只能用list类型的storeInfos,不能用map类型的storeMap,否则门店顺序会被打乱,
		dishes := store.DishInfos
		// 没有菜品，直接保留，不处理
		if len(dishes) == 0 {
			continue
		}

		// 增加过滤前的菜品信息
		if traceInfo.IsDebug {
			store.DishInfosBeforeFilter = dishes.DeepClone()
			sort.Slice(store.DishInfosBeforeFilter, func(i, j int) bool {
				return store.DishInfosBeforeFilter[i].DishId < store.DishInfosBeforeFilter[j].DishId
			})
		}

		totalESCnt += dishes.DishRecallFromESCount()
		totalFSCnt += dishes.DishRecallFromFSCount()
		// 菜品过滤
		if traceInfo.IsDowngradeDataServer == false {
			if cid.IsVN() {
				dishes = OutOfStockAndSpecialSaleTimeFilter(traceInfo, dishes)
			} else {
				dishes = OutOfStockAndSpecialSaleTimeFilter(traceInfo, dishes)
				dishes = FilterNonHalalDishWhenRamadan(ctx, traceInfo, dishes, isFilterNonHalal) // 斋月过滤
			}
			// 零价格过滤
			if zeroPriceFilterSwitch {
				dishes = FilterDishByZeroPrice(ctx, traceInfo, dishes) // 价格为0的菜品过滤
			}
		}
		store.DishInfos = dishes
	}
	return storeInfos
}

// 菜品数量截断，已排序
func DishTruncate(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	if len(storeInfos) == 0 {
		return storeInfos
	}
	pt := time.Now()
	finalESCnt, finalFSCnt := 0, 0
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingTruncate, time.Since(pt))
		metric_reporter2.ReportHitRateHitRequest(finalESCnt, "FinalDishFromES")
		metric_reporter2.ReportHitRateHitRequest(finalESCnt, "FinalDishFromFS")
	}()
	var maxDishSize = abtest.GetDishListingRecallSize(traceInfo.AbParamClient)
	flashSaleShowSize := abtest.GetFlashSaleDishShowSize(traceInfo.AbParamClient)

	// PRD需求，如果菜品少于 n 个，则置为空
	dishNotShowSize := abtest.GetDishNotShowSize(traceInfo.AbParamClient)

	for _, store := range storeInfos { // 此处只能用list类型的storeInfos,不能用map类型的storeMap,否则门店顺序会被打乱,
		if len(store.DishInfos) == 0 {
			continue
		}

		if len(store.DishInfos) < dishNotShowSize {
			if traceInfo.IsDebug {
				for _, d := range store.DishInfos {
					traceInfo.AddFilteredDish(d.DishId, traceinfo.FilteredDishByLowerNotShowSize, store.StoreId)
				}
			}
			store.DishInfos = nil
		}

		// PRD要求：flash sale只要折扣最大的top 2
		store.DishInfos = truncateFlashSaleDishes(traceInfo, flashSaleShowSize, store.StoreId, store.DishInfos)

		// PRD 需求，如果 > 6个菜，则只保留6个
		if maxDishSize > 0 && len(store.DishInfos) > maxDishSize {
			if traceInfo.IsDebug {
				for _, d := range store.DishInfos[maxDishSize:] {
					traceInfo.AddFilteredDish(d.DishId, traceinfo.FilteredDishByMaxSizeCut, fmt.Sprintf("storeId:%d,dishScore:%f,priority:%d", store.StoreId, d.Score, d.RecallPriority))
				}
			}
			store.DishInfos = store.DishInfos[:maxDishSize]
		}
		finalESCnt += store.DishInfos.DishRecallFromESCount()
		finalFSCnt += store.DishInfos.DishRecallFromFSCount()
	}
	return storeInfos
}

func truncateFlashSaleDishes(traceInfo *traceinfo.TraceInfo, flashSaleShowSize int, storeId uint64, dishInfos model2.DishInfos) model2.DishInfos {
	flashSaleDishInfos := make([]*model2.DishInfo, 0)
	for _, dish := range dishInfos {
		if dish.IsFlashSale && dish.IsFlashSaleValid {
			flashSaleDishInfos = append(flashSaleDishInfos, dish)
		}
	}
	// 如果flash sale菜品数量不超过显示限制，无需处理
	if len(flashSaleDishInfos) <= flashSaleShowSize {
		return dishInfos
	}

	// 对flashSaleDishInfos排序，折扣百分比越大越好，如果折扣相等，dishId降序
	sort.Slice(flashSaleDishInfos, func(i, j int) bool {
		if flashSaleDishInfos[i].FlashSaleDiscountPercentage != flashSaleDishInfos[j].FlashSaleDiscountPercentage {
			return flashSaleDishInfos[i].FlashSaleDiscountPercentage > flashSaleDishInfos[j].FlashSaleDiscountPercentage
		}
		return flashSaleDishInfos[i].DishId > flashSaleDishInfos[j].DishId
	})

	// 创建需要删除的菜品ID集合，使用map提高查找效率
	needDeleteFlashSaleDishIds := make(map[uint64]struct{}, len(flashSaleDishInfos)-flashSaleShowSize)
	for i := flashSaleShowSize; i < len(flashSaleDishInfos); i++ {
		needDeleteFlashSaleDishIds[flashSaleDishInfos[i].DishId] = struct{}{}
	}

	// 创建新的菜品列表，过滤掉需要删除的flash sale菜品
	newDishInfos := make([]*model2.DishInfo, 0, len(dishInfos))
	for _, d := range dishInfos {
		if d.IsFlashSale && d.IsFlashSaleValid {
			if _, needDelete := needDeleteFlashSaleDishIds[d.DishId]; needDelete {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredDish(d.DishId, traceinfo.FilteredDishByFlashSaleShowSize, storeId)
				}
				continue
			}
		}
		newDishInfos = append(newDishInfos, d)
	}
	return newDishInfos
}

// 主站菜品过滤
func DishFilterMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	resStoreIDs := make([]*model2.StoreInfo, 0, len(storeInfos))

	// 是否斋月过滤
	isFilterNonHalal := decision.IsFilterNonHalal(ctx, traceInfo)

	// 是否零价格过滤
	zeroPriceFilterSwitch := abtest.GetDishZeroPriceFilterSwitch(traceInfo.AbParamClient)

	for _, store := range storeInfos { // 此处只能用list类型的storeInfos,不能用map类型的storeMap,否则门店顺序会被打乱,
		dishes := store.DishInfos
		if len(dishes) == 0 {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeNoDishFilterForMainSite, nil)
			}
			continue // 主站直接过滤掉无菜品召回的(前面已经补充好best selling dish)
		}
		// 菜品过滤
		if traceInfo.IsDowngradeDataServer == false {
			dishes = NotOnSaleTimeAndNoImageDishesFilter(traceInfo, traceInfo.TraceRequest.SearchTime, dishes, store.Timezone)
			dishes = OutOfStockAndSpecialSaleTimeFilter(traceInfo, dishes)
			dishes = FilterNonHalalDishWhenRamadan(ctx, traceInfo, dishes, isFilterNonHalal) // 斋月过滤
			// 零价格过滤
			if zeroPriceFilterSwitch {
				dishes = FilterDishByZeroPrice(ctx, traceInfo, dishes) // 价格为0的菜品过滤
			}
		}
		// 原本有菜品，但是被上面全部过滤了
		if len(dishes) == 0 {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeAllDishFilterForMainSite, nil)
			continue
		} else {
			// 算法需求，对 dish score 重新打分
			reCalcDishesScore(ctx, traceInfo, dishes)

			// 根据 score 对 dishes 排序后截断
			dishes = dishes.Sort()
			dishes = dishes.Truncate(DishForMainSiteLimit)
			store.DishInfos = dishes

			store.Score = dishes[0].Score
			store.ESScore = dishes[0].Score
			store.CreateTime = dishes[0].CreateTime
			store.Price = dishes[0].Price
			store.SalesVolume = dishes[0].SalesVolume
			resStoreIDs = append(resStoreIDs, store)
		}
	}
	return resStoreIDs
}

func OutOfStockAndSpecialSaleTimeFilter(traceInfo *traceinfo.TraceInfo, dishes model2.DishInfos) model2.DishInfos {
	if dishes == nil || len(dishes) <= 0 {
		return nil
	}
	var filterDishes model2.DishInfos

	for _, dish := range dishes {
		if dish.OutOfStockFlag == o2oalgo.DishOutOfStock_DISH_OUT_OF_STOCK {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByOutOfStockAndSpecialSaleTime, dish)
			}
			continue
		}
		if dish.SaleStatus == o2oalgo.DishSale_DISH_SALE_NOT_FOR_SALE {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByOutOfStockAndSpecialSaleTime, dish)
			}
			continue
		}
		filterDishes = append(filterDishes, dish)
	}
	return filterDishes
}

func NotOnSaleTimeAndNoImageDishesFilter(traceInfo *traceinfo.TraceInfo, searchTime uint64, dishes model2.DishInfos, timezone string) model2.DishInfos {
	if dishes == nil {
		return nil
	}
	var filterDishes model2.DishInfos
	for _, dish := range dishes {
		if dish == nil {
			continue
		}
		if cid.IsVN() {
			if dish.Picture == "" && dish.MmsImage == "" {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByNotOnSaleTimeAndNoImageDishes, dish)
				}
				continue
			}
			if dish.SaleStatus == o2oalgo.DishSale_DISH_SALE_NOT_FOR_SALE {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByNotOnSaleTimeAndNoImageDishes, dish)
				}
				continue
			}
		} else {
			if dish.Picture == "" {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByNotOnSaleTimeAndNoImageDishes, dish)
				}
				continue
			}
			if dish.SaleStatus == o2oalgo.DishSale_DISH_SALE_NOT_FOR_SALE {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByNotOnSaleTimeAndNoImageDishes, dish)
				}
				continue
			}
		}
		filterDishes = append(filterDishes, dish)
	}
	return filterDishes
}

// 预约单搜菜，需要将不可售卖菜品过滤
func MartSearchDishesFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model2.DishInfos, reserveTime uint64) model2.DishInfos {
	if dishes == nil || len(dishes) <= 0 {
		return nil
	}
	isReserve := false
	if reserveTime > 0 {
		isReserve = true
	}
	var resDishes model2.DishInfos
	for _, dish := range dishes {
		isActive := true
		if isReserve && dish.OutOfStockFlag == o2oalgo.DishOutOfStock_DISH_OUT_OF_STOCK {
			isActive = false
		}
		if isReserve && dish.SaleStatus == o2oalgo.DishSale_DISH_SALE_NOT_FOR_SALE {
			isActive = false
		}
		if dish.Price <= 0 {
			isActive = false
		}
		if dish.HideFlagForVn {
			isActive = false
		}
		if isActive {
			resDishes = append(resDishes, dish)
		} else {
			traceInfo.AddFilteredStore(dish.DishId, traceinfo.FilterByDishFilter, dish)
		}
	}
	return resDishes
}

// 算法需求，对菜品重新打分
func reCalcDishesScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes []*model2.DishInfo) {
	if len(apollo.SearchApolloCfg.MainSiteDishScoreExpression) == 0 {
		logkit.FromContext(ctx).Error("reCalcDishesScore failed without expression")
		return
	}
	if len(apollo.SearchApolloCfg.MainSiteDishScoreWeightMap) == 0 {
		logkit.FromContext(ctx).Error("reCalcDishesScore failed without weightMap")
		return
	}

	//is_store_intention*(wa1 * dish_text_match_score + wa2*has_pic + wa3*sales_volume_score) +
	//is_dish_intention*(wb1 * dish_text_match_score + wb2*has_pic + wb3*sales_volume_score)
	expression := util.BuildExpression(ctx, apollo.SearchApolloCfg.MainSiteDishScoreExpression)

	// 公共权重
	parameters := util.BuildExpParameters(apollo.SearchApolloCfg.MainSiteDishScoreWeightMap)
	parametersSize := len(parameters)

	// 当不是门店意图、菜品意图的时候，都改为 0.5
	isStoreIntention := util.BoolToFloat(len(traceInfo.QPResult.QueryStoreIntention) > 0)
	isDishIntention := util.BoolToFloat(len(traceInfo.QPResult.QueryDishIntention) > 0)
	if isStoreIntention == 0.0 && isDishIntention == 0.0 {
		isStoreIntention = 0.5
		isDishIntention = 0.5
	}

	// 公共参数
	parameters["is_store_intention"] = isStoreIntention
	parameters["is_dish_intention"] = isDishIntention

	// 主站 dish score 重新打分
	for _, dish := range dishes {
		// 每次都要重新升成一个 params，防止数据污染
		currentParameters := make(map[string]interface{}, 2*parametersSize)
		for k, v := range parameters {
			currentParameters[k] = v
		}
		currentParameters["dish_text_match_score"] = dish.DishTextMatchScore
		currentParameters["has_pic"] = util.BoolToFloat(dish.HasPicture)
		currentParameters["sales_volume_score"] = dish.GetSalesVolumeScore()

		if traceInfo.IsDebug {
			currentParametersStr, _ := json.MarshalIndent(currentParameters, "", "  ")
			dish.ScoreCalc = string(currentParametersStr)
		}

		score, err := util.EvaluateScore(ctx, expression, currentParameters)
		if err == nil {
			dish.Score = score
		}
	}
}

func FilterNonHalalDishWhenRamadan(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model2.DishInfos, isFilterNonHalal bool) model2.DishInfos {

	if !isFilterNonHalal {
		logkit.FromContext(ctx).Debug("FilterNonHalalDishWhenRamadan no need to filter nonHalal stores")
		return dishes
	}
	if traceInfo.IsDowngradeDataServer {
		return dishes
	}
	resDishIDs := make([]*model2.DishInfo, 0, len(dishes))
	for _, dish := range dishes {
		if !dish.SearchNonHalalFlag {
			resDishIDs = append(resDishIDs, dish)
		} else {
			logkit.FromContext(ctx).Debug("FilterNonHalalDishWhenRamadan filter nonHalal dish", logkit.Uint64("dishId", dish.DishId))
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByNonHalalWhenRamadan, nil)
			}
		}
	}

	logkit.FromContext(ctx).Debug("FilterNonHalalDishWhenRamadan success", logkit.Int("beforeSize", len(dishes)), logkit.Int("afterSize", len(resDishIDs)))
	return resDishIDs
}

func FilterDishByZeroPrice(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model2.DishInfos) model2.DishInfos {
	if traceInfo.IsDowngradeDataServer {
		return dishes
	}
	resDishIDs := make([]*model2.DishInfo, 0, len(dishes))
	for _, dish := range dishes {
		if dish.Price == 0 {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilterDishByZeroPrice, nil)
			}
		} else {
			resDishIDs = append(resDishIDs, dish)
		}
	}
	return resDishIDs
}
