package filter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterStoreByRecallConfigDistance(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}

	var resStores []*model.StoreInfo
	for _, store := range stores {
		if store.FilterRecallConfigDistance == 0 {
			resStores = append(resStores, store)
		} else {
			if store.Distance <= store.FilterRecallConfigDistance {
				resStores = append(resStores, store)
			} else {
				if traceInfo.IsDebug {
					traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreCauseOutOfRecallConfigDistance, fmt.Sprintf("distance:%f##limitDistance:%f", store.Distance, store.FilterRecallConfigDistance))
				}
			}
		}
	}
	return resStores
}
