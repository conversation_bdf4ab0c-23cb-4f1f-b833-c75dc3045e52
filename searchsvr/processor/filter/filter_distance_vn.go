package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func DistanceFilterVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, distance uint32) []*model.StoreInfo {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
		return stores
	}
	resStores := make([]*model.StoreInfo, 0, len(stores))
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilterDistanceVN, len(resStores))
	}()
	for _, store := range stores {
		if isStoreDistanceValid(traceInfo, store, distance) {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDistance, store.Distance)
		}
	}
	return resStores
}

func DistanceFilterVNMainSite(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, distance uint32) []*model.StoreInfo {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
		return stores
	}
	resStores := make([]*model.StoreInfo, 0, len(stores))
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFilterDistanceVNMainSite, len(resStores))
	}()
	for _, store := range stores {
		if store.Distance <= float64(distance) {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.StoreLenFilterDistanceVNMainSite, store.Distance)
		}
	}
	return resStores
}

// https://confluence.shopee.io/pages/viewpage.action?pageId=2778235523
func isStoreDistanceValid(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo, distance uint32) bool {
	// 自配送商家
	if store.IsFoodyDelivery == 0 {
		if store.OriginalDeliveryDistance > 0 {
			// 自配送商家有设置配置距离, 直接使用门店原始配置的delivery distance，且不受peakmode 影响
			if store.Distance <= float64(store.OriginalDeliveryDistance) {
				return true
			} else {
				return false
			}
		} else {
			//自配送商家没有设置配置距离, 继续用使用召回距离，且不受peakmode 影响
			if store.Distance <= float64(distance) {
				return true
			} else {
				return false
			}
		}
	}
	// 非自配送商家
	if store.DeliveryDistance > 0 {
		// DeliveryDistance有两层:正排会优先用peakmode距离，peakmode没有，则是门店原始配置的delivery distance距离
		// 非自配送商家，直接用DeliveryDistance过滤(peakmode或者原始配置的delivery distance)
		if store.Distance <= float64(store.DeliveryDistance) {
			return true
		} else {
			return false
		}
	} else {
		// 非自配送商家且peakmode没有返回距离且原始配置的delivery distance==0，用召回距离兜底
		if store.Distance <= float64(distance) {
			return true
		} else {
			return false
		}
	}
}
