package filter

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func ShopTypeFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if env.GetCID() != cid.MY {
		return stores
	}
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	filter := traceInfo.TraceRequest.GetFilterType()
	if filter == nil || filter.ShopFilter == nil || filter.GetShopFilter() == foodalgo_search.SearchRequest_Unkown {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.filterByShopType, not need to filter", zap.Int("len", len(stores)))
		return stores
	}
	if filter.GetShopFilter() == foodalgo_search.SearchRequest_Halal {
		resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
		for _, store := range stores {
			if store.HalalType == foodalgo_search.HalalType_CERTIFIED_HALAL {
				resStoreIDs = append(resStoreIDs, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeShopType, store.HalalType)
			}
		}
		return resStoreIDs
	}
	return stores
}
