package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func FilterByCategoryType(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if cid.IsVN() == false {
		return stores
	}
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	req := traceInfo.TraceRequest
	if len(req.GetFilterType().GetCategoryType()) != 1 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.FilterByCategoryType, not need to filter", zap.Int("len", len(stores)))
		return stores
	}
	cate := req.GetFilterType().GetCategoryType()[0]
	categoryType := o2oalgo.CategoryType_CATEGORY_TYPE_FOOD
	if cate == 1002 {
		categoryType = o2oalgo.CategoryType_CATEGORY_TYPE_MART
	}
	resStores := make(model2.StoreInfos, 0, len(stores))
	for _, store := range stores {
		if store.CategoryType == categoryType {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByCategoryType, store.CategoryType)
		}
	}
	return resStores
}

func FilterByL2Category(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	req := traceInfo.TraceRequest
	if req.GetFilterType() == nil || req.GetFilterType().GetL2Categories() == nil || len(req.GetFilterType().GetL2Categories()) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.FilterByL2Category, not need to filter", zap.Int("len", len(stores)))
		return stores
	}
	reqL2Categories := req.GetFilterType().GetL2Categories()
	nStores := len(stores)
	resStores := make(model2.StoreInfos, 0, nStores)
	for iStore := 0; iStore < nStores; iStore++ {
		hasFlag := 0
		for i := range reqL2Categories {
			if _, ok := stores[iStore].L2CategoryId[reqL2Categories[i]]; ok {
				resStores = append(resStores, stores[iStore])
				hasFlag = 1
				break
			}
		}
		if hasFlag == 0 {
			traceInfo.AddFilteredStore(stores[iStore].StoreId, traceinfo.FilterByL2CategoryId, stores[iStore].L2CategoryId)
		}
	}
	return resStores
}
