package filter

import (
	"context"
	"sort"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// DuplicateMerchantFilter
// 通过merchant id 召回 => 按照merchant 维度排序后 ，第一个直接保留。剩余门店：
//
//	仅通过merchant id 召回  => 直接过滤掉
//	通过merchant id 召回同时，也有其他类型召回。  => 直接保留，但是将 StoreInterventionWithMerchantIDRecall 置为0
//
// 不是merchant id 召回的   => 直接保留，不根据merchant id去重
func DuplicateMerchantFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	resStores := make([]*model2.StoreInfo, 0, len(stores))
	multiRecallStoreSet := make(map[uint64]bool) // 标记该门店是否多路召回
	merchantStoreMap := make(map[uint64]model2.StoreInfos)
	for _, store := range stores {
		merchantID := store.GetMerchantId()
		if store.StoreInterventionWithMerchantIDRecall == 1 && merchantID != 0 {
			merchantStoreMap[merchantID] = append(merchantStoreMap[merchantID], store)
			if len(store.RecallTypes) > 1 {
				multiRecallStoreSet[store.StoreId] = true
			}
		} else {
			resStores = append(resStores, store)
		}
	}
	// 商户铺取排序最前的第一个
	for k, v := range merchantStoreMap {
		if len(v) > 1 {
			// 1级排序：门店状态（open > pause > closed）
			// 2级排序：距离（近 > 远）
			// 3级排序：storeID（storeID 升序）
			sort.Slice(v, func(i int, j int) bool {
				statusI := v[i].GetOpeningState()
				statusJ := v[j].GetOpeningState()
				if statusI != statusJ {
					// 状态不同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
					return statusI < statusJ
				}
				if !acc.Equal(v[i].Distance, v[j].Distance) {
					return v[i].Distance < v[j].Distance
				}
				return v[i].StoreId < v[j].StoreId
			})
		}
		resStores = append(resStores, v[0]) // 商户铺取排序最前的第一个
		if len(v) > 1 {
			for _, s := range v[1:] {
				// 多路召回的，需要保留，且将StoreInterventionWithMerchantIDRecall标识1改为0
				if _, exists := multiRecallStoreSet[s.StoreId]; exists {
					s.StoreInterventionWithMerchantIDRecall = 0
					resStores = append(resStores, s)
				} else {
					traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateMerchant, k)
				}
			}
		}
	}
	return resStores
}
