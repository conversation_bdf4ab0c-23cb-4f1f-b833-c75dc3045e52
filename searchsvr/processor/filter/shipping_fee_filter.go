package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByShippingFee(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedShippingFee(traceInfo) == false {
		return stores
	}
	result := make(model2.StoreInfos, 0, len(stores))
	if traceInfo.TraceRequest.GetFilterType().GetShippingFee() > 0 {
		targetFee := traceInfo.TraceRequest.GetFilterType().GetShippingFee()
		for _, store := range stores {
			if store.ShippingFee <= targetFee {
				result = append(result, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByShippingFee, store.ShippingFee)
			}
		}
	}
	if traceInfo.TraceRequest.GetFilterType().GetSlashedShippingFee() > 0 {
		targetFee := traceInfo.TraceRequest.GetFilterType().GetSlashedShippingFee()
		for _, store := range stores {
			if (store.ShippingFee < store.MaxShippingFeeDiscount) || (store.ShippingFee-store.MaxShippingFeeDiscount <= targetFee) {
				result = append(result, store)
			} else {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterBySlashedShippingFee, store.ShippingFee)
			}
		}
	}
	return result
}
