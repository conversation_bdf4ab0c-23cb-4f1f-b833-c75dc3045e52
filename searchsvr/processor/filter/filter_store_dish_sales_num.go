package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func DishSalesNumFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos) model2.StoreInfos {
	if decision.IsNeedStoreDishNumFilter(traceInfo) == false || len(storeInfos) == 0 {
		return storeInfos
	}
	if traceInfo.IsDowngradeDataServer {
		return storeInfos
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(storeInfos))
	for _, store := range storeInfos {
		if store.StoreAvailableDishCnt <= 0 {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStoreDishSalesNum, store.StoreAvailableDishCnt)
			logger.MyDebug(ctx, traceInfo.IsDebug, "DishSalesNumFilter: storeID filtered by DishSalesNum <= 0", zap.Any("storeID", store.StoreId))
			continue
		}
		resStoreIDs = append(resStoreIDs, store)
	}
	return resStoreIDs
}
