package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const (
	promotionShopFlag         = 1
	promotionFreeFreeShipping = 2
	promotionDishFlag         = 4
)

func PromotionFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if decision.IsNeedPromotion(traceInfo) == false || len(stores) == 0 {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	filter := traceInfo.TraceRequest.GetFilterType()
	// 开始promotion过滤
	for _, store := range stores {
		if store.Promotion == nil {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypePromotionNil, nil)
			continue
		}
		filterFlag := 0
		hasFlag := 0
		for _, filterType := range filter.PromotionFilter {
			switch filterType {
			case foodalgo_search.SearchRequest_ShopDiscount:
				filterFlag = filterFlag | promotionShopFlag
				if store.Promotion.HasShopPromotion {
					hasFlag = hasFlag | promotionShopFlag
				}
			case foodalgo_search.SearchRequest_FreeShipping:
				filterFlag = filterFlag | promotionFreeFreeShipping
				if store.Promotion.HasShippingFeePromotion {
					hasFlag = hasFlag | promotionFreeFreeShipping
				}
			case foodalgo_search.SearchRequest_DishPromotion:
				filterFlag = filterFlag | promotionDishFlag
				if store.Promotion.HasDishPromotion {
					hasFlag = hasFlag | promotionDishFlag
				}
			}
		}
		if filterFlag == hasFlag {
			resStoreIDs = append(resStoreIDs, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypePromotion, store.Promotion)
		}
	}
	return resStoreIDs
}
