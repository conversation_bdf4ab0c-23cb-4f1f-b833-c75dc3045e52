package filter

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterNonHalalStoreWhenRamadan(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model2.StoreInfo) []*model2.StoreInfo {

	if !decision.IsFilterNonHalal(ctx, traceInfo) {
		logkit.FromContext(ctx).Debug("FilterNonHalalStoreWhenRamadan no need to filter nonHalal stores")
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	for _, store := range stores {
		if !store.SearchNonHalalFlag {
			resStoreIDs = append(resStoreIDs, store)
		} else {
			logkit.FromContext(ctx).Debug("FilterNonHalalStoreWhenRamadan filter nonHalal store", logkit.Uint64("storeId", store.StoreId))
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByNonHalalWhenRamadan, nil)
		}
	}

	logkit.FromContext(ctx).Debug("FilterNonHalalStoreWhenRamadan success", logkit.Int("beforeSize", len(stores)), logkit.Int("afterSize", len(resStoreIDs)))
	return resStoreIDs
}
