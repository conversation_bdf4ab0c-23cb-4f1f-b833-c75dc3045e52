package filter

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StatusFilter(traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	for _, store := range stores {
		if store.StoreStatus == o2oalgo.StoreStatus_STORE_STATUS_ACTIVE {
			resStoreIDs = append(resStoreIDs, store)
		}
	}
	return resStoreIDs
}
