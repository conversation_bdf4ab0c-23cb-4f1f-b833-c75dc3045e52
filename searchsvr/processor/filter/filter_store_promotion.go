package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/3rdparty/foody_promotion"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByStorePromotion(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if decision.IsNeedStorePromotion(traceInfo) == false || len(stores) == 0 {
		return stores
	}
	resStoreIDs := make(model2.StoreInfos, 0, len(stores))
	promoTypesFilterList := traceInfo.TraceRequest.GetFilterType().GetPromotionTypeFilters()
	nFilters := len(promoTypesFilterList)
	nMatches := 0
	for _, store := range stores {
		if store.StorePromotion == nil {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStorePromotionNil, store.StorePromotion)
			continue
		}
		nMatches = 0
		for jFilter := 0; jFilter < nFilters; jFilter++ {
			reqPromos := promoTypesFilterList[jFilter].GetPromotionTypes()
			nPromos := len(reqPromos)
			bFilter := false
			for jPromo := 0; jPromo < nPromos; jPromo++ {
				promoType := foody_promotion.PromotionBriefType(reqPromos[jPromo])
				switch promoType {
				case foody_promotion.PromotionBriefType_SHIPPING_FEE_VOUCHER:
					if store.StorePromotion.HasShippingFeeVoucher {
						bFilter = true
					}
				case foody_promotion.PromotionBriefType_COIN_CASHBACK_VOUCHER:
					if store.StorePromotion.HasCoinCashBackVoucher {
						bFilter = true
					}
				case foody_promotion.PromotionBriefType_FOOD_DISCOUNT_VOUCHER:
					if store.StorePromotion.HasFoodDiscountVoucher {
						bFilter = true
					}
				case foody_promotion.PromotionBriefType_SHIPPING_FEE_DIRECT_DISCOUNT:
					if store.StorePromotion.HasShippingFeeDirectDiscount {
						bFilter = true
					}
				case foody_promotion.PromotionBriefType_ORDER_DIRECT_DISCOUNT:
					if store.StorePromotion.HasOrderDirectDiscount {
						bFilter = true
					}
				case foody_promotion.PromotionBriefType_FOOD_DIRECT_DISCOUNT:
					if store.StorePromotion.HasFoodDirectDiscount {
						bFilter = true
					}
				}
				if bFilter {
					break
				}
			}
			if bFilter {
				nMatches += 1
			}
		}
		if nMatches == nFilters {
			resStoreIDs = append(resStoreIDs, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeStorePromotion, store.StorePromotion)
		}
	}
	return resStoreIDs
}
