package filter

import (
	"strconv"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// 仅 vn SearchGlobalV1 生效
func CityFilter(traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos, cityId uint32) model2.StoreInfos {
	if decision.IsNeedVNCityFilter(traceInfo) == false {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	for _, store := range stores {
		sc, _ := strconv.Atoi(store.City)
		if sc > 0 && cityId > 0 && uint32(sc) != cityId {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByCityId, store.City)
		} else {
			resStoreIDs = append(resStoreIDs, store)
		}
	}
	return resStoreIDs
}

// 仅 vn SearchGlobalV1 生效
func DistrictFilter(traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos, districtIds []uint32) model2.StoreInfos {
	if decision.IsNeedVNDistrictFilter(traceInfo) == false {
		return stores
	}
	districtIdsMap := make(map[uint32]bool, len(traceInfo.TraceRequest.DistrictIds))
	for _, id := range traceInfo.TraceRequest.DistrictIds {
		if id > 0 {
			districtIdsMap[id] = true
		}
	}
	if len(districtIdsMap) == 0 {
		return stores
	}
	resStoreIDs := make([]*model2.StoreInfo, 0, len(stores))
	for _, store := range stores {
		sd, _ := strconv.Atoi(store.District)
		if sd > 0 && districtIdsMap[uint32(sd)] == false {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByDistrictIds, store.District)
		} else {
			resStoreIDs = append(resStoreIDs, store)
		}
	}
	return resStoreIDs
}
