package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func OpeningFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model2.StoreInfo) []*model2.StoreInfo {
	if decision.IsNeedOpeningFilter(traceInfo) == false || len(stores) == 0 {
		return stores
	}
	resStores := make([]*model2.StoreInfo, 0, len(stores))
	// 开始promotion过滤
	for _, store := range stores {
		if store.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypeOpening, store.DisplayOpeningStatus)
		}
	}
	return resStores
}
