package filter

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func RecallPriorityFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) model.StoreInfos {
	// 门店数量小于限制，不需要过滤截断
	if traceInfo.RecallPriorityConfig == nil || traceInfo.RecallPriorityConfig.LimitSize >= len(stores) {
		return stores
	}

	useESScore1 := traceInfo.AbParamClient.GetParamWithInt("Search.RecallPriority.RankWithEsScore", 0)
	useESScore2 := traceInfo.AbParamClient.GetParamWithInt("Search.RecallPriorityV2.RankWithEsScore", 0)
	if useESScore1 == 1 || useESScore2 == 1 {
		stores = model.SortByRelevanceVNWithEsScore(ctx, stores, traceInfo)
	}
	resStores := make([]*model.StoreInfo, 0, traceInfo.RecallPriorityConfig.LimitSize)
	leftStores := make([]*model.StoreInfo, 0, len(stores))
	for _, store := range stores {
		// exact match优先级最高
		if store.ExactMatch > 0 {
			resStores = append(resStores, store)
		} else {
			leftStores = append(leftStores, store)
		}
	}
	priorityStoreCount := make(map[int]int) // 每个优先级的门店数量
	for _, store := range leftStores {
		priorityStoreCount[store.RecallPriority]++
	}

	priorityStoreLimit := make(map[int]int) // 每个优先级保留下来的门店数量
	limitSize := traceInfo.RecallPriorityConfig.LimitSize
	for p := 0; p <= traceInfo.RecallPriorityConfig.MinPriority; p++ {
		if limitSize <= 0 {
			break
		}
		if limitSize >= priorityStoreCount[p] {
			priorityStoreLimit[p] = priorityStoreCount[p]
			limitSize = limitSize - priorityStoreCount[p]
		} else {
			priorityStoreLimit[p] = limitSize
			limitSize = 0
		}
	}

	for _, store := range leftStores {
		p := store.RecallPriority
		if priorityStoreLimit[p] > 0 {
			resStores = append(resStores, store)
			priorityStoreLimit[p] = priorityStoreLimit[p] - 1
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByPriorityMerge, p)
		}
	}
	return resStores
}
