package filter

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

func FilterByShippingDistance(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if env.GetCID() != cid.ID {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if len(stores) == 0 {
		return stores
	}
	req := traceInfo.TraceRequest
	if req.GetFilterType() == nil || req.GetFilterType().GetShippingDistance() <= 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchService.FilterByShippingDistance, not need to filter", zap.Int("len", len(stores)))
		return stores
	}
	shippingDistance := req.GetFilterType().GetShippingDistance()
	nStores := len(stores)
	resStores := make(model2.StoreInfos, 0, nStores)
	for iStore := 0; iStore < nStores; iStore++ {
		if stores[iStore].Distance < float64(shippingDistance) {
			resStores = append(resStores, stores[iStore])
		} else {
			traceInfo.AddFilteredStore(stores[iStore].StoreId, traceinfo.FilterByShippingDistance, stores[iStore].Distance)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "FilterByShippingDistance", zap.Any("filtered result:", resStores))
	return resStores
}
