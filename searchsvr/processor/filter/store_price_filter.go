package filter

import (
	"context"
	"math"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterByStorePrice(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if decision.IsNeedStorePrice(traceInfo) == false {
		return stores
	}
	nStores := len(stores)
	resStores := make(model2.StoreInfos, 0, nStores)
	priceRangeList := traceInfo.TraceRequest.GetFilterType().GetPriceRange()
	var price uint64
	for _, store := range stores {
		price = store.StorePrice
		bNeed := false
		for _, priceRange := range priceRangeList {
			if priceRange.GetFloor() == 0 || priceRange.GetCeil() == math.MaxUint64 {
				if priceRange.GetFloor() < price && price < priceRange.GetCeil() {
					bNeed = true
					break
				}
			} else {
				if priceRange.GetFloor() <= price && price <= priceRange.GetCeil() {
					bNeed = true
					break
				}
			}
		}
		if bNeed {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByPriceRange, store.StorePrice)
		}
	}
	return resStores
}
