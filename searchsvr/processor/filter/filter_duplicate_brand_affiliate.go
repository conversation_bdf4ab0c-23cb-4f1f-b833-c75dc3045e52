package filter

//func DuplicateBrandFilterForAffiliate(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
//	if len(stores) == 0 {
//		return stores
//	}
//	if traceInfo.IsDowngradeDataServer {
//		return stores
//	}
//
//	resStores := make([]*model2.StoreInfo, 0, len(stores))
//	brandStoreMap := make(map[uint64]model2.StoreInfos)
//	// 命中门店意图的brandID集合
//	for _, store := range stores {
//		brandID := store.GetBrandId()
//		if brandID != 0 {
//			brandStoreMap[brandID] = append(brandStoreMap[brandID], store)
//		} else {
//			resStores = append(resStores, store)
//		}
//	}
//	// 品牌店铺取排序最前的第一个
//	for _, v := range brandStoreMap {
//		if len(v) > 1 {
//			v = rank.StoresAffiliateRank(ctx, traceInfo, v)
//		}
//		resStores = append(resStores, v[0])
//		if len(v) > 1 {
//			for _, s := range v[1:] {
//				traceInfo.AddFilteredStore(s.StoreId, traceinfo.FilteredTypeDuplicateBrand, v[0].StoreId) // 记录保留下来的同品牌门店ID
//			}
//		}
//	}
//	return resStores
//}
