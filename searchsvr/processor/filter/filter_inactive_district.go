package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func InactiveDistrictFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model2.StoreInfos) model2.StoreInfos {
	if len(stores) == 0 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	searchStores := make([]*model2.StoreInfo, 0, len(stores))
	for _, ss := range stores {
		if ss.DisplayDistrictStatus == o2oalgo.DisplayDistrictStatus_DISPLAY_DISTRICT_STATUS_INACTIVE {
			traceInfo.AddFilteredStore(ss.StoreId, traceinfo.FilteredTypeInactiveDistrict, ss.DisplayDistrictStatus)
			continue
		}
		searchStores = append(searchStores, ss)
	}
	return searchStores
}
