package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func PromotionFilterVN(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model2.StoreInfo) []*model2.StoreInfo {
	if decision.IsNeedVNPromotion(traceInfo) == false || len(stores) == 0 {
		return stores
	}
	resStores := make([]*model2.StoreInfo, 0, len(stores))
	// 开始promotion过滤
	promotionFilter := traceInfo.TraceRequest.GetFilterType().GetPromotionFilter()
	for _, store := range stores {
		okFlag := true
		promotionData := store.Promotion
		if promotionData == nil {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypePromotionNil, store.Promotion) // 当门店没有营销信息时，将其过滤掉
			continue
		}
		for _, filterType := range promotionFilter {
			if !okFlag {
				break
			}
			switch filterType {
			case foodalgo_search.SearchRequest_FreeShipping:
				if !promotionData.HasShippingFeePromotion {
					okFlag = false
					break
				}
			case foodalgo_search.SearchRequest_StorePromotion:
				if !promotionData.HasShopPromotion {
					okFlag = false
					break
				}
			}
		}
		if okFlag {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilteredTypePromotion, promotionData)
		}
	}
	return resStores
}
