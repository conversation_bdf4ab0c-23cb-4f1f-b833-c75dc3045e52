package filter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
	"strings"
	"sync"
	"time"
)

func DishBatchFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) model.DishInfos {
	if len(dishes) == 0 {
		return dishes
	}

	validDishes := make(model.DishInfos, 0, len(dishes))
	for _, dish := range dishes {
		// 抛弃无效的菜品
		if dish == nil {
			continue
		}
		// 如果菜品正排降级了，那就不做任何过滤
		if traceInfo.IsDowngradeDataServer == true && dish.DishId > 0 && dish.StoreId > 0 {
			validDishes = append(validDishes, dish)
			continue
		}

		// 抛弃无效状态的菜品
		if dish.Available != foodalgo_search.Available_AVAILABLE || dish.ListingStatus != foodalgo_search.DishListingStatus_ACTIVE {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilteredDishByInvalidStatus, fmt.Sprintf("dish.Available:%v##dish.ListingStatus:%v", dish.Available, dish.ListingStatus))
			}
			continue
		}

		// 抛弃无效的flash sale菜品
		if dish.IsFlashSale == true && len(dish.DishRecallIds) == 1 && dish.IsFlashSaleValid == false {
			if traceInfo.IsDebug {
				traceInfo.AddFilteredDish(dish.DishId, traceinfo.FilteredDishByFlashSaleInvalidStatus, fmt.Sprintf("recallTypes:%v##flashSaleIds:%v", dish.DishRecallTypes, dish.FlashSaleIds))
			}
			continue
		}

		validDishes = append(validDishes, dish)
	}

	// todo: 特殊的，给es的召回打标 recallPriority
	BatchTagESRecallDishes(ctx, traceInfo, validDishes)

	return validDishes
}

func BatchTagESRecallDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) {
	if len(dishes) == 0 {
		return
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseFillingDishESRecallPriority, time.Since(pt))
	}()

	batchSize := 500
	if apollo.SearchApolloCfg.ESDishRecallFillingTagsBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.ESDishRecallFillingTagsBatchSize)
	}

	dishLen := len(dishes)
	totalBatch := (dishLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return
	}

	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "BatchTagESRecallDishes", func(params ...interface{}) {
			defer wg.Done()

			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > dishLen {
				end = dishLen
			}
			batchDishIds := dishes[begin:end]
			doTagESRecallDishes(batchDishIds, traceInfo.QueryKeyword)
		}, i)
	}
	wg.Wait()
}

func doTagESRecallDishes(dishes model.DishInfos, keywordLower string) {
	keywordLower = strings.ToLower(keywordLower)
	keywordTokens := util.SplitToWords(keywordLower)
	keywordTokenLen := len(keywordTokens)
	for _, dish := range dishes {
		if dish == nil {
			continue
		}
		if dish.IsFromES == false {
			continue
		}
		isNeedTag := false
		for _, dishRecallPriority := range dish.DishRecallPriorities {
			if dishRecallPriority == recallconstant.MaxRecallPriority {
				isNeedTag = true
				break
			}
		}
		if !isNeedTag {
			continue
		}

		dishNameLower := strings.ToLower(dish.DishName)
		// 完全匹配，最高优先
		if strings.Contains(dishNameLower, keywordLower) {
			if dish.RecallPriority > 1 {
				dish.RecallPriority = 1
				dish.DishRecallType = "CombineDishRecallExact"
			}
			dish.DishRecallPriorities = append(dish.DishRecallPriorities, 1)
			dish.DishRecallTypes = append(dish.DishRecallTypes, "CombineDishRecallExact")
			continue
		}

		// 多词情况下，对keyword和dishName都拆分
		dishNameTokens := util.SplitToWords(dishNameLower)

		// 词组都存在，给1
		if util.CountOverlap(keywordTokens, dishNameTokens) == keywordTokenLen {
			if dish.RecallPriority > 1 {
				dish.RecallPriority = 1
				dish.DishRecallType = "CombineDishRecallExact"
			}
			dish.DishRecallPriorities = append(dish.DishRecallPriorities, 1)
			dish.DishRecallTypes = append(dish.DishRecallTypes, "CombineDishRecallExact")
			continue
		}

		// prefix = 2
		for _, dishToken := range dishNameTokens {
			if strings.HasPrefix(dishToken, keywordLower) {
				if dish.RecallPriority > 2 {
					dish.RecallPriority = 2
					dish.DishRecallType = "CombineDishRecallPrefix"
				}
				dish.DishRecallPriorities = append(dish.DishRecallPriorities, 2)
				dish.DishRecallTypes = append(dish.DishRecallTypes, "CombineDishRecallPrefix")
				continue
			}
		}

		// partial = 3
		if dish.RecallPriority > 3 {
			dish.RecallPriority = 3
			dish.DishRecallType = "CombineDishRecallPartial"
		}
		dish.DishRecallPriorities = append(dish.DishRecallPriorities, 3)
		dish.DishRecallTypes = append(dish.DishRecallTypes, "CombineDishRecallPartial")
	}
}
