package filter

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// ID 挂菜补充后调用, 去掉Top K 之后的门店的补充挂菜
func SupplementDishFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeInfos model2.StoreInfos, config *apollo.DishSupplementRecallConfig) model2.StoreInfos {
	for i := config.StoreLimit; i < len(storeInfos); i++ {
		store := storeInfos[i]
		if store.IsSupplementDishRecall {
			store.DishInfos = nil
		}
	}
	return storeInfos
}
