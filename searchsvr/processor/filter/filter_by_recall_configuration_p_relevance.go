package filter

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

// PRD: https://confluence.shopee.io/pages/viewpage.action?pageId=2754180910&focusedCommentId=2832892836&refresh=1752748347896
func FilterStoreByRecallConfigPRelevanceScore(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if len(stores) == 0 {
		return stores
	}

	// 算法要求：当filterByRelevanceScoreSwitch打开时，p_relevance_score!=Search.MultiFactor.DefaultPredictRelevanceScore & p_relevance_score < filterByRelevanceScore才生效过滤功能
	defaultPredictRelevanceScore := abtest.GetDefaultPredictRelevanceScore(traceInfo.AbParamClient)

	var resStores []*model.StoreInfo
	for _, store := range stores {
		if store.FilterRecallConfigPRelevanceScore == model.DefaultNonConfiguredPRelevanceScore {
			resStores = append(resStores, store)
		} else {
			if store.PRelevanceScore >= store.FilterRecallConfigPRelevanceScore || store.PRelevanceScore == defaultPredictRelevanceScore {
				// 保留门店
				resStores = append(resStores, store)
				if traceInfo.IsDebug {
					traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreRemainingAfterRecallConfigPRelevanceScore, fmt.Sprintf("%v##PRel:%f##LimitPRel:%f##defaultPredictRelevanceScore:%f", store.RecallTypes, store.PRelevanceScore, store.FilterRecallConfigPRelevanceScore, defaultPredictRelevanceScore))
				}
			} else {
				// 过滤掉该门店
				store.IsFilterByPRelevanceScore = 1
				if traceInfo.IsDebug {
					traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreCauseLowerRecallConfigPRelevanceScore, fmt.Sprintf("%v##PRel:%f##LimitPRel:%f##defaultPredictRelevanceScore:%f", store.RecallTypes, store.PRelevanceScore, store.FilterRecallConfigPRelevanceScore, defaultPredictRelevanceScore))
				}
			}
		}
	}
	return resStores
}
