package filter

import (
	"context"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func CommissionRateFilter(traceInfo *traceinfo.TraceInfo, filter *foodalgo_search.SearchRequest_FilterType, stores model.StoreInfos) model.StoreInfos {
	if len(stores) < 1 {
		return stores
	}
	if traceInfo.IsDowngradeDataServer {
		return stores
	}
	if filter.GetCommRate().GetMaxValue() <= 0 && filter.GetCommRate().GetMinValue() <= 0 {
		return stores
	}
	max := filter.GetCommRate().GetMaxValue()
	min := filter.GetCommRate().GetMinValue()
	resStores := make([]*model.StoreInfo, 0, len(stores))
	for _, store := range stores {
		if max > 0 {
			if store.BaseCommissionRate > max {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByCommissionRate, store.BaseCommissionRate)
				continue
			}
		}
		if min > 0 {
			if store.BaseCommissionRate <= min {
				traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByCommissionRate, store.BaseCommissionRate)
				continue
			}
		}
		resStores = append(resStores, store)
	}
	return resStores
}

func DishesWithoutCommissionFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes model.DishInfos) model.DishInfos {
	if dishes == nil || len(dishes) <= 0 {
		return dishes
	}
	var resDishes model.DishInfos
	for _, dish := range dishes {
		if dish.BaseCommissionRate > 0 {
			resDishes = append(resDishes, dish)
		} else {
			traceInfo.AddFilteredStore(dish.DishId, traceinfo.FilterByStoreWithoutCommission, dish)
		}
	}
	return resDishes
}

func StoresWithoutCommissionFilter(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores model.StoreInfos) model.StoreInfos {
	if stores == nil || len(stores) <= 0 {
		return nil
	}
	var resStores model.StoreInfos
	for _, store := range stores {
		if store.BaseCommissionRate > 0 {
			resStores = append(resStores, store)
		} else {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByDishWithoutCommission, store)
		}
	}
	return resStores
}
