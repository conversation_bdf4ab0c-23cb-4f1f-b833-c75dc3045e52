package filter

import (
	"context"
	"fmt"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/store_blacklist"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func FilterStoreBlacklist(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model2.StoreInfo) []*model2.StoreInfo {
	if decision.IsNeedStoreBlacklistFilter(traceInfo) == false || len(stores) == 0 {
		return stores
	}
	resStores := make([]*model2.StoreInfo, 0, len(stores))
	for _, store := range stores {
		if store_blacklist.IsStoreInCache(store.StoreId, uint64(store_blacklist.ShopeeFoodSearchResultPage)) {
			traceInfo.AddFilteredStore(store.StoreId, traceinfo.FilterByStoreBlacklist, fmt.Sprintf("%d_%d", store.StoreId, uint64(store_blacklist.ShopeeFoodSearchResultPage)))
			continue
		}
		resStores = append(resStores, store)
	}
	return resStores
}
