package recall2

import (
	"context"
	"runtime/debug"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func MultiRecallDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	if len(storeIds) == 0 {
		return nil, nil
	}

	pt := time.Now()
	recallList := make(model.DishInfos, 0)

	defer func() {
		if e := recover(); e != nil {
			logkit.Error("MultiRecallDishes panic", logkit.String("recall type", "MultiRecallDishes"), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-dish-MultiRecallDishes",
			})
		}

		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecall, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecall, len(recallList))
	}()

	// 多路召回，未合并
	var esDishes, fsDishes model.DishInfos
	var esErr, fsErr error
	wg := &sync.WaitGroup{}
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallDishFromES", func(params ...interface{}) {
		defer wg.Done()

		esDishes, esErr = recallDishFromES(ctx, traceInfo, storeIds)
		if esErr != nil {
			logkit.FromContext(ctx).WithError(esErr).Error("recallDishFromES error")
		}
	})
	wg.Add(1)
	goroutine.WithGo(ctx, "RecallDishFromFS", func(params ...interface{}) {
		defer wg.Done()

		fsDishes, fsErr = recallDishFromFS(ctx, traceInfo, storeIds)
		if fsErr != nil {
			logkit.FromContext(ctx).WithError(fsErr).Error("recallDishFromFS error")
		}
	})
	wg.Wait()

	for _, esDish := range esDishes {
		esDish.IsFromES = true
		recallList = append(recallList, esDish)
	}
	for _, fsDish := range fsDishes {
		fsDish.IsFromFS = true
		recallList = append(recallList, fsDish)
	}

	dishInfos := dishMerge(recallList)
	return dishInfos, nil
}
func dishMerge(recallList model.DishInfos) model.DishInfos {
	dishMap := make(map[uint64]*model.DishInfo)
	for _, dish := range recallList {
		if existingDish, exists := dishMap[dish.DishId]; exists {
			// 合并切片（以 DishRecallIds 为例，其他类似）
			existingDish.DishRecallIds = mergeSlices(existingDish.DishRecallIds, dish.DishRecallIds)
			existingDish.DishRecallTypes = mergeSlices(existingDish.DishRecallTypes, dish.DishRecallTypes)
			existingDish.DishRecallPriorities = mergeSlices(existingDish.DishRecallPriorities, dish.DishRecallPriorities)
			existingDish.FlashSaleIds = mergeSlices(existingDish.FlashSaleIds, dish.FlashSaleIds)

			if dish.RecallPriority < existingDish.RecallPriority {
				existingDish.RecallPriority = dish.RecallPriority
			}
			if dish.IsFlashSale {
				existingDish.IsFlashSale = true
			}
			if dish.IsFromES {
				existingDish.IsFromES = true
			}
			if dish.IsFromFS {
				existingDish.IsFromFS = true
			}
		} else {
			dishMap[dish.DishId] = dish
		}
	}

	// 预分配切片，直接按长度初始化
	dishInfos := make([]*model.DishInfo, len(dishMap))
	index := 0
	for _, dish := range dishMap {
		dishInfos[index] = dish
		index++
	}

	return dishInfos
}

// 泛型版 mergeSlices，支持任意切片类型（如 []uint64、[]int、[]string 等）
func mergeSlices[T any](dst, src []T) []T {
	newLen := len(dst) + len(src)
	newSlice := make([]T, newLen)
	// 拷贝原 dst 数据
	copy(newSlice, dst)
	// 拷贝原 src 数据到 dst 后面
	copy(newSlice[len(dst):], src)
	return newSlice
}
