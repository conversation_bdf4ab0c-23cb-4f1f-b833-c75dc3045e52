package recall2

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	featurepb "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/feature-server"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall/parse"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"
)

func recallDishFromFS(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (model.DishInfos, error) {
	if len(storeIds) == 0 {
		return nil, nil
	}

	recallConfig := parse.GetDishRecallConfigByPipelineType(ctx, traceInfo, recallconstant.RecallDomainDish, recallconstant.RecallDataSourceFS)
	if recallConfig == nil || len(recallConfig.DishRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallDishFromFS recall config is nil")
		}
		return nil, nil
	}

	// 过滤recall
	recallConfig.DishRecalls = parse.DoFilterCondition(ctx, traceInfo, recallConfig.DishRecalls, recallconstant.RecallDomainDish)
	if len(recallConfig.DishRecalls) == 0 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("recallDishFromFS recall config is nil after filter")
		}
		return nil, nil
	}

	pt := time.Now()
	var dishInfos model.DishInfos
	totalLen := len(storeIds)
	hitLen := totalLen
	defer func() {
		if e := recover(); e != nil {
			logkit.Error("recallDishFromFS panic", logkit.String("recall type", "recallDishFromFS"), logkit.Any("err", e), logkit.String("stack", util.ByteToString(debug.Stack())))
			reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "search-dish-fs",
			})
		}

		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallFS, time.Since(pt))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.DishLenRecallFromFS, len(dishInfos))
		metric_reporter2.ReportHitRateRequest(totalLen, hitLen, "DishFromFSV2FreeCache")

		if traceInfo.IsDebug {
			for _, recall := range recallConfig.DishRecalls {
				logkit.FromContext(ctx).Info("recallDishFromFS hit recall", logkit.String("recallName", recall.RecallName), logkit.String("recallId", recall.RecallId), logkit.String("RecallTypeStr", recall.RecallTypeStr))
				traceInfo.AppendRecallsDishFinal(fmt.Sprintf("%s_%s", recall.RecallName, recall.RecallId))
			}
		}
	}()

	// 构造cache 前缀
	cachePrefix := ""
	for _, recall := range recallConfig.DishRecalls {
		cachePrefix += recall.RecallId + "|"
	}

	var noCacheIds []string
	totalDishRecallMap := make(map[string]model.DishInfos, len(storeIds))

	// 看debug请求放弃缓存
	if decision.IsSkipDishRecallCache(ctx, traceInfo) {
		for _, storeId := range storeIds {
			noCacheIds = append(noCacheIds, fmt.Sprintf("%s_%d", cachePrefix, storeId))
		}
	} else {
		checkCacheKeys := make([]string, 0, len(storeIds))
		for _, storeId := range storeIds {
			checkCacheKeys = append(checkCacheKeys, fmt.Sprintf("%s_%d", cachePrefix, storeId))
		}
		pt1 := time.Now()
		noCacheIds = localcache.StoreCacheSysInstance.MGetDishesFromFSCache(ctx, checkCacheKeys, totalDishRecallMap)
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallFSGetCache, time.Since(pt1))
	}

	if len(noCacheIds) > 0 {
		hitLen = totalLen - len(noCacheIds)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish fs recall with rpc", logkit.Any("noCacheIds", noCacheIds))
		pt2 := time.Now()
		rpcStoreDishesMap, err := doRecallFSBatch(ctx, traceInfo, noCacheIds, cachePrefix, recallConfig)
		logger.MyDebug(ctx, traceInfo.IsDebug, "dish fs recall with rpc", logkit.Any("rpcStoreDishesMap", rpcStoreDishesMap))
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallFSRPCV2, time.Since(pt2))

		if err != nil {
			logkit.Error("MultiRecallDishes doRecallFSBatch error", logkit.String("recall type", "MultiRecallDishes"), logkit.Any("err", err))
		} else {
			// 更新到总的数据集中
			for cacheKey, storeDishes := range rpcStoreDishesMap {
				totalDishRecallMap[cacheKey] = storeDishes
			}
			// 批量写入缓存
			goroutine.WithGo(ctx, "MSetDishRecallCache", func(params ...interface{}) {
				pt3 := time.Now()
				localcache.StoreCacheSysInstance.MSetDishesFromFSCache(ctx, rpcStoreDishesMap, 1800)
				traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseDishListingMultiRecallFSSetCache, time.Since(pt3))
			})
		}
	}

	// 调试专用
	if traceInfo.IsDebug {
		storeIdsStr := abtest.GetPrintDishRecallCache(traceInfo.AbParamClient)
		if len(storeIdsStr) > 0 {
			storeIdsStrSplits := strings.Split(storeIdsStr, ",")
			for _, storeIdStr := range storeIdsStrSplits {
				key := fmt.Sprintf("%s_%s", cachePrefix, storeIdStr)
				if value, ok := totalDishRecallMap[key]; ok {
					valueStr, _ := json.Marshal(value)
					logkit.FromContext(ctx).Info("recallDishFromFS cache hit", logkit.String("key", key), logkit.String("valueStr", string(valueStr)))
				} else {
					logkit.FromContext(ctx).Info("recallDishFromFS cache not hit", logkit.String("key", key))
				}
			}
		}
	}

	for _, batchDishInfos := range totalDishRecallMap {
		if len(batchDishInfos) == 0 {
			continue
		}
		dishInfos = append(dishInfos, batchDishInfos...)
	}
	return dishInfos, nil
}

func doRecallFSBatch(ctx context.Context, traceInfo *traceinfo.TraceInfo, cacheKeys []string, cachePrefix string, recallConfig *apollo.RecallConfig) (map[string]model.DishInfos, error) {
	batchSize := 50
	if apollo.SearchApolloCfg.NewDishRecallBatchForFS > 0 {
		batchSize = apollo.SearchApolloCfg.NewDishRecallBatchForFS
	}

	storesLen := len(cacheKeys)
	totalBatch := (storesLen + batchSize - 1) / batchSize
	var wg sync.WaitGroup
	errs := make(chan error, totalBatch)
	batchRecallList := make([]map[string]model.DishInfos, totalBatch)
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "recallDishFromFS", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			batchIndex := param[0].(int)
			begin := batchIndex * batchSize
			end := begin + batchSize
			if end > storesLen {
				end = storesLen
			}
			subKeys := cacheKeys[begin:end]
			batchStoreIds := make([]uint64, 0, len(subKeys))
			for _, key := range subKeys {
				storeIdStr := strings.Split(key, "_")[1]
				storeId, _ := strconv.ParseUint(storeIdStr, 10, 64)
				batchStoreIds = append(batchStoreIds, storeId)
			}
			tmpDishInfosMap, err := doRecallFs(batchStoreIds, ctx, traceInfo, cachePrefix, recallConfig)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("recallDishFromFS batch recall failed", logkit.Int("batch", batchIndex))
				errs <- err
				return
			}
			batchRecallList[batchIndex] = tmpDishInfosMap
		}, i)
	}
	wg.Wait()
	if len(errs) == totalBatch {
		logkit.FromContext(ctx).Error("recallDishFromFS all failed")
		return nil, <-errs
	}

	// 预分配内存空间，提升性能
	rsp := make(map[string]model.DishInfos, 4096)
	for _, tmpDishInfosMap := range batchRecallList {
		for k, v := range tmpDishInfosMap {
			rsp[k] = v
		}
	}
	return rsp, nil
}
func doRecallFs(batchStoreIds []uint64, ctx context.Context, traceInfo *traceinfo.TraceInfo, cachePrefix string, recallConfig *apollo.RecallConfig) (map[string]model.DishInfos, error) {
	req := &featurepb.ReadReq{
		IntKeys:      batchStoreIds,
		FeatureGroup: 199,
	}

	// 收集所有 tags
	tags := make([]uint32, 0, len(recallConfig.DishRecalls)*6) // 预估每个 recall 有多个 tag
	for _, recall := range recallConfig.DishRecalls {
		tags = append(tags, recall.FsRecallConfig.Tags...)
	}
	req.Tags = tags

	// 调用 feature server
	rsp, err := mlplatform.ReadFeatureFromFeatureServer(ctx, req)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("doRecallFs error", logkit.Uint64s("storeIds", batchStoreIds))
		traceInfo.SlaCode.UpdateErrorCode(sla_code.OTHER_RECALL_ERROR)
		return nil, err
	}
	if rsp == nil || len(rsp.Feas) == 0 {
		logkit.FromContext(ctx).Error("doRecallFs rsp is nil")
		return nil, nil
	}
	if len(rsp.Feas) != len(batchStoreIds) {
		logkit.FromContext(ctx).Error("doRecallFs not match size error",
			logkit.Int("featureSize", len(rsp.Feas)),
			logkit.Int("batchStoreIdSize", len(batchStoreIds)))
		return nil, nil
	}

	// 预分配容量
	rspStoreDishes := make(map[string]model.DishInfos, len(batchStoreIds))

	for storeIdx, feaList := range rsp.Feas {
		storeId := batchStoreIds[storeIdx]
		storeDishInfos := make(model.DishInfos, 0, 16) // 预估每店返回十几个菜

		for _, recall := range recallConfig.DishRecalls {
			sType := recall.FsRecallConfig.SpecialProcessType
			switch sType {
			case "none":
				storeDishInfos = append(storeDishInfos, handleNormalDishes(ctx, storeId, feaList, recall)...)
			case "sku":
				storeDishInfos = append(storeDishInfos, handleSkuDishes(ctx, traceInfo, storeId, feaList, recall)...)
			case "flash_sale":
				storeDishInfos = append(storeDishInfos, handleFlashSaleDishes(ctx, storeId, feaList, recall)...)
			}
		}

		// merge 去重
		storeDishInfos = dishMerge(storeDishInfos)

		// 避免 fmt.Sprintf
		key := cacheKey(cachePrefix, storeId)
		rspStoreDishes[key] = storeDishInfos
	}

	return rspStoreDishes, nil
}

// cacheKey 构造 key，替代 fmt.Sprintf
func cacheKey(prefix string, storeId uint64) string {
	var b strings.Builder
	b.Grow(len(prefix) + 21) // 21 足够容纳 uint64 最大值
	b.WriteString(prefix)
	b.WriteByte('_')
	b.WriteString(strconv.FormatUint(storeId, 10))
	return b.String()
}

func handleNormalDishes(ctx context.Context, storeId uint64, features *featurepb.FeatureList, recall *apollo.StoreRecallConfig) model.DishInfos {
	if features == nil || len(features.Features) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleNormalDishes failed, features is nil", logkit.String("recallName", recall.RecallName))
		return nil
	}
	if recall.FsRecallConfig.Tags == nil || len(recall.FsRecallConfig.Tags) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleNormalDishes failed, no tags", logkit.String("recallName", recall.RecallName))
		return nil
	}

	// 普通的fs dish 召回，只要拿到一个tag即可
	targetTag := recall.FsRecallConfig.Tags[0]

	// 提前分配好内存
	dishInfos := make(model.DishInfos, 0, recall.RecallSize)

	// 可复用字段
	recallIds := []string{recall.RecallId}
	recallTypes := []string{recall.RecallTypeStr}
	recallPriorities := []int{recall.RecallPriority}

	count := 0
	for _, fea := range features.Features {
		if fea == nil {
			continue
		}
		if uint32(fea.Tag) == targetTag {
			dishIds := fea.GetInt64Value()
			for j, dishId := range dishIds {
				// 召回数截断
				if uint64(j) >= recall.RecallSize {
					break
				}
				dishInfos = append(dishInfos, &model.DishInfo{
					StoreId:              storeId,
					DishId:               uint64(dishId),
					DishRecallId:         recall.RecallId,
					DishRecallIds:        recallIds,
					DishRecallType:       recall.RecallTypeStr,
					DishRecallTypes:      recallTypes,
					RecallPriority:       recall.RecallPriority,
					DishRecallPriorities: recallPriorities,
					IsFromFS:             true,
				})
				count += 1
			}
		}
		// 召回数截断
		if uint64(count) >= recall.RecallSize {
			break
		}
	}
	return dishInfos
}

func handleSkuDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeId uint64, features *featurepb.FeatureList, recall *apollo.StoreRecallConfig) model.DishInfos {
	if features == nil || len(features.Features) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleNormalDishes failed, features is nil", logkit.String("recallName", recall.RecallName))
		return nil
	}
	if recall.FsRecallConfig.Tags == nil || len(recall.FsRecallConfig.Tags) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleNormalDishes failed, no tags", logkit.String("recallName", recall.RecallName))
		return nil
	}

	var skuCateValues []int64
	var skuLenValues []int64
	var skuTotalDishValues []int64

	for _, fea := range features.Features {
		if fea == nil {
			continue
		}
		if fea.Tag == mlplatform.SKUCate {
			skuCateValues = fea.GetInt64Value()
		} else if fea.Tag == mlplatform.SKULen {
			skuLenValues = fea.GetInt64Value()
		} else if fea.Tag == mlplatform.SKUTotalList {
			skuTotalDishValues = fea.GetInt64Value()
		}
	}

	var specialDishes model.DishInfos
	queryCateIds := make([]uint64, 0)
	for _, sku := range traceInfo.QPResult.CategoryIntentions {
		queryCateIds = append(queryCateIds, uint64(sku.GetLevel2ID()))
	}

	skuDishIds := getDishListByCateIds(skuCateValues, skuLenValues, skuTotalDishValues, queryCateIds)
	for _, skuDishId := range skuDishIds {
		specialDishes = append(specialDishes, &model.DishInfo{
			StoreId:              storeId,
			DishId:               uint64(skuDishId),
			DishRecallId:         recall.RecallId,
			DishRecallIds:        []string{recall.RecallId},
			DishRecallType:       recall.RecallTypeStr,
			DishRecallTypes:      []string{recall.RecallTypeStr},
			RecallPriority:       recall.RecallPriority,
			DishRecallPriorities: []int{recall.RecallPriority},
			IsFromFS:             true,
		})
	}

	// recall size 截断
	if len(skuDishIds) > int(recall.RecallSize) {
		skuDishIds = skuDishIds[:int(recall.RecallSize)]
	}

	return specialDishes
}
func handleFlashSaleDishes(ctx context.Context, storeId uint64, features *featurepb.FeatureList, recall *apollo.StoreRecallConfig) model.DishInfos {
	if features == nil || len(features.Features) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleFlashSaleDishes failed, features is nil", logkit.String("recallName", recall.RecallName))
		return nil
	}
	if recall.FsRecallConfig.Tags == nil || len(recall.FsRecallConfig.Tags) == 0 {
		logkit.FromContext(ctx).Error("recallDishFromFS handleFlashSaleDishes failed, no tags", logkit.String("recallName", recall.RecallName))
		return nil
	}

	var dishIds, flashSaleIds []uint64

	for _, fea := range features.Features {
		if fea == nil {
			continue
		}
		switch fea.Tag {
		case mlplatform.FlashSaleDishIdsList:
			values := fea.GetInt64Value()
			if len(values) > 0 {
				dishIds = append(dishIds, int64SliceToUint64Slice(values)...)
			}
		case mlplatform.FlashSaleIdsList:
			values := fea.GetInt64Value()
			if len(values) > 0 {
				flashSaleIds = append(flashSaleIds, int64SliceToUint64Slice(values)...)
			}
		}
	}

	if len(dishIds) != len(flashSaleIds) {
		logkit.FromContext(ctx).Error("recallDishFromFS handleFlashSaleDishes failed, dishIds and flashSaleIds length not equal",
			logkit.String("recallName", recall.RecallName),
			logkit.Int("dishIdsLength", len(dishIds)),
			logkit.Int("flashSaleIdsLength", len(flashSaleIds)),
		)
		return nil
	}

	// 在循环前提取这些共享字段
	recallId := recall.RecallId
	recallType := recall.RecallTypeStr
	recallPriority := recall.RecallPriority

	recallIdList := []string{recallId}
	recallTypeList := []string{recallType}
	recallPriorityList := []int{recallPriority}

	// 尽量提前预估容量
	dishMap := make(map[uint64]*model.DishInfo, len(dishIds))
	for i := range dishIds {
		dishId := dishIds[i]
		flashSaleId := flashSaleIds[i]

		if dish, ok := dishMap[dishId]; ok {
			// 判断是否已存在此 flashSaleId
			exist := false
			for _, id := range dish.FlashSaleIds {
				if id == flashSaleId {
					exist = true
					break
				}
			}
			if !exist {
				dish.FlashSaleIds = append(dish.FlashSaleIds, flashSaleId)
			}
		} else {
			dishMap[dishId] = &model.DishInfo{
				StoreId:              storeId,
				DishId:               dishId,
				DishRecallId:         recallId,
				DishRecallIds:        recallIdList,
				DishRecallType:       recallType,
				DishRecallTypes:      recallTypeList,
				RecallPriority:       recallPriority,
				DishRecallPriorities: recallPriorityList,
				FlashSaleIds:         []uint64{flashSaleId},
				IsFlashSale:          true,
				IsFromFS:             true,
			}
		}
	}

	dishInfos := make(model.DishInfos, 0, len(dishMap))
	for _, dish := range dishMap {
		dishInfos = append(dishInfos, dish)
	}

	return dishInfos
}

func int64SliceToUint64Slice(src []int64) []uint64 {
	dst := make([]uint64, len(src))
	for i, v := range src {
		dst[i] = uint64(v)
	}
	return dst
}

func getDishListByCateIds(skuRecallCateL2IdList, skuRecallCateL2DishCntList, skuRecallCateL2DishIdList []int64, cateIdList []uint64) []int64 {
	var result []int64

	for _, cateID := range cateIdList {
		// 查找 cateID 在 skuRecallCateL2IdList 中的索引
		index := -1
		for i, id := range skuRecallCateL2IdList {
			if id == int64(cateID) {
				index = i
				break
			}
		}
		if index == -1 {
			continue // cateID 不在规则列表中
		}

		// 计算菜品的起始和结束索引
		startIdx := int64(0)
		for i := int64(0); i < int64(index); i++ {
			startIdx += skuRecallCateL2DishCntList[i]
		}
		endIdx := startIdx + skuRecallCateL2DishCntList[index]

		// 添加菜品 ID 到结果列表
		if startIdx < int64(len(skuRecallCateL2DishIdList)) {
			if endIdx > int64(len(skuRecallCateL2DishIdList)) {
				endIdx = int64(len(skuRecallCateL2DishIdList))
			}
			result = append(result, skuRecallCateL2DishIdList[startIdx:endIdx]...)
		}
	}

	return result
}
