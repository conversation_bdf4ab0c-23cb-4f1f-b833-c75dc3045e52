package rerank

import (
	"context"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

func StoresReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) []*model.StoreInfo {
	pt := time.Now()
	defer func() {
		traceInfo.AddMainPhraseCostTime(ctx, traceinfo.PhraseReRank, time.Since(pt))
	}()
	// 干预重排
	stores = Intervention(ctx, traceInfo, stores)
	// Ltr
	stores = LtrReRank(ctx, traceInfo, stores)
	// top K 门店相关性低的打压
	stores = DeboostReRank(ctx, traceInfo, stores, DeboostRelevance)
	// top K 门店 category跟 query 不相关的打压
	stores = DeboostReRank(ctx, traceInfo, stores, DeboostCategory)

	return stores
}
