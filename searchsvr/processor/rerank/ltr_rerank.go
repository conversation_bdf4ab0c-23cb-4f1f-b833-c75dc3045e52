package rerank

import (
	"context"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"go.uber.org/zap"
)

func LtrReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, mixStores []*model.StoreInfo) []*model.StoreInfo {
	if !traceInfo.PredictConfig.UseLtr {
		return mixStores
	}
	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankLTR, time.Since(pt))
	}()
	normalStores := make(model.StoreInfos, 0, len(mixStores))
	normalNum := 0
	for _, store := range mixStores {
		if store.BusinessType != foodalgo_search.BusinessType_Ads {
			normalNum++
			normalStores = append(normalStores, store)
		}
	}
	normalStores = rank.CalculateAfterLtrAndRankByRelevance(ctx, traceInfo, normalStores)

	normalStores = normalStores.SortByRelevanceAfterLtr(ctx, traceInfo)
	normalIdx, idx := 0, 0
	for ; idx < len(mixStores); idx++ {
		if mixStores[idx].BusinessType == foodalgo_search.BusinessType_Ads {
			continue
		}
		if normalIdx < len(normalStores) {
			mixStores[idx] = normalStores[normalIdx]
			normalIdx++
		}
	}
	if idx != len(mixStores) {
		logkit.FromContext(ctx).Error("LtrReRank error, normalStore list end but store list not end", zap.Int("normal store len", len(normalStores)),
			zap.Int("store list len", len(mixStores)))
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "after ltr", zap.Any("normal", normalStores), zap.Any("after ltr", mixStores))
	return mixStores
}
