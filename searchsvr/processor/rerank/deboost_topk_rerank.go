package rerank

import (
	"context"
	"sort"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"go.uber.org/zap"
)

const (
	DeboostRelevance = 1
	DeboostCategory  = 2
)

func DeboostReRank(ctx context.Context, traceInfo *traceinfo.TraceInfo, mixStores []*model.StoreInfo, deboostType int) []*model.StoreInfo {
	// relevance Tab 页下生效
	if traceInfo.TraceRequest.GetSortType() != foodalgo_search.SearchRequest_Relevance {
		return mixStores
	}
	config := getConfig(traceInfo, deboostType)
	logger.MyDebug(ctx, traceInfo.IsDebug, "DeboostReRank", zap.Int("deboost type", deboostType), zap.Any("config", config), zap.Any("mixStores list len", len(mixStores)),
		zap.Any("NewStrategy", traceInfo.PredictConfig.NewStrategy), zap.Any("RelModelInfo", traceInfo.RelModelInfo.ModelName))

	if !checkConfig(traceInfo, config, deboostType) {
		return mixStores
	}

	topK := config.TopK
	topN := config.TopN

	if len(mixStores) <= topK {
		return mixStores
	}

	pt := time.Now()
	defer func() {
		traceInfo.AddPhraseCostTime(ctx, traceinfo.PhraseReRankTopKBoost, time.Since(pt))
	}()
	var inTopK, outTopK []*model.StoreInfo
	if len(mixStores) > topK {
		inTopK = mixStores[:topK]
		outTopK = mixStores[topK:]
	}
	// TopK 内查找需要打压降权的门店
	inTopKLeft := make(model.StoreInfos, 0, topK)
	deboostList := make(model.StoreInfos, 0, topK)
	var checkScoreFunc func(store *model.StoreInfo) bool
	if deboostType == DeboostRelevance {
		checkScoreFunc = func(store *model.StoreInfo) bool {
			return store.PRelevanceScore >= config.DeboostThreshold
		}
	} else {
		checkScoreFunc = func(store *model.StoreInfo) bool {
			return store.DeboostCategoryScore > 0 && store.DeboostCategoryScore >= config.DeboostThreshold
		}
	}

	for i, store := range inTopK {
		if NotNeedDeboostOrBoost(store) {
			continue
		}
		if checkScoreFunc(store) || i+1 < topN {
			inTopKLeft = append(inTopKLeft, store)
		} else {
			deboostList = append(deboostList, store)
		}
	}
	// TopK 外查找可以加权的门店
	outTopKLeft := make(model.StoreInfos, 0, len(mixStores)-topK)
	boostList := make(model.StoreInfos, 0, topK)
	for _, store := range outTopK {
		if NotNeedDeboostOrBoost(store) {
			continue
		}
		var score float64
		var threshold float64
		if deboostType == DeboostRelevance {
			score = store.PRelevanceScore
			threshold = config.DeboostThreshold
		} else {
			score = store.DeboostCategoryScore
			threshold = config.BoostThreshold
		}
		if score < threshold || store.DisplayOpeningStatus != o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
			outTopKLeft = append(outTopKLeft, store)
		} else {
			boostList = append(boostList, store)
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "DeboostReRank", zap.Any("Deboost list", deboostList), zap.Any("boost list", boostList))
	deboostSize := len(deboostList)
	boostSize := len(boostList)
	if deboostSize == 0 || boostSize == 0 {
		return mixStores
	}

	if deboostSize > boostSize {
		// 打压降权的门店多余加权的门店, 打压门店按相关性分数排序后选分数低的
		if deboostType == DeboostRelevance {
			sort.Slice(deboostList, func(i, j int) bool {
				return deboostList[i].PRelevanceScore < deboostList[j].PRelevanceScore
			})
		} else {
			sort.Slice(deboostList, func(i, j int) bool {
				return deboostList[i].DeboostCategoryScore < deboostList[j].DeboostCategoryScore
			})
		}
		deboostListLeft := deboostList[boostSize:]
		deboostList = deboostList[:boostSize]
		inTopKLeft = append(inTopKLeft, deboostListLeft...)
	} else if deboostSize < boostSize {
		boostListLeft := boostList[deboostSize:]
		boostList = boostList[:deboostSize]
		outTopKLeft = append(outTopKLeft, boostListLeft...)
	}

	if len(deboostList) == len(boostList) {
		for _, info := range deboostList {
			info.DeboostType = 1
		}
		for _, info := range boostList {
			info.DeboostType = 2
		}
		// 打压降权门店放到 top K 外
		outTopKLeft = append(outTopKLeft, deboostList...)
		// 加权门店放到 top K 内
		inTopKLeft = append(inTopKLeft, boostList...)
		// 各自重新排序
		inTopKLeft.SortByRelevance(ctx, traceInfo)
		outTopKLeft.SortByRelevance(ctx, traceInfo)
		afterDeboostList := append(inTopKLeft, outTopKLeft...)
		afterDeboostIdx, idx := 0, 0
		for ; idx < len(mixStores); idx++ {
			if NotNeedDeboostOrBoost(mixStores[idx]) {
				continue
			}
			if afterDeboostIdx < len(afterDeboostList) {
				mixStores[idx] = afterDeboostList[afterDeboostIdx]
				afterDeboostIdx++
			}
		}
		if idx != len(mixStores) {
			logkit.Error("DeboostReRank error, afterDeboost list end but store list not end", zap.Int("afterDeboost store len", len(afterDeboostList)),
				zap.Int("store list len", len(mixStores)))
			_ = metric_reporter.ReportAbtestConfigError(1, "DeboostReRank.finalMerge error")
		}
	} else {
		logkit.Error("DeboostReRank deboostSize != boostSize", zap.Int("deboost", len(deboostList)),
			zap.Int("boost", len(boostList)))
	}
	return mixStores
}

func NotNeedDeboostOrBoost(store *model.StoreInfo) bool {
	if store.BusinessType == foodalgo_search.BusinessType_Ads || store.IsInterForSort > 0 || store.ExactMatch > 0 {
		return true
	}
	return false
}

func getConfig(traceInfo *traceinfo.TraceInfo, deboostType int) *abtest.DeboostConfig {
	if deboostType == DeboostRelevance {
		return abtest.GetDeboostConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	}
	return abtest.GetDeboostCategoryConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
}

func checkConfig(traceInfo *traceinfo.TraceInfo, config *abtest.DeboostConfig, deboostType int) bool {
	if config == nil || config.TopK <= 0 || config.DeboostThreshold <= 0 {
		return false
	}

	if config.TopK > 20 {
		_ = metric_reporter.ReportAbtestConfigError(1, "DeboostReRank.topK>20")
		return false
	}
	if deboostType == DeboostRelevance {
		// 判断是否有相关性模型的配置
		if !(traceInfo.PredictConfig.NewStrategy == 4 && len(traceInfo.RelModelInfo.ModelName) > 0) {
			_ = metric_reporter.ReportAbtestConfigError(1, "DeboostReRank.RelevanceScore=0")
			return false
		}
	}
	return true
}

func DeboostReRankInGenFormula(ctx context.Context, traceInfo *traceinfo.TraceInfo, listwiseStores model.ListWiseItems, adsOrIntentStoreNum int, deboostType int) model.ListWiseItems {
	config := getConfig(traceInfo, deboostType)

	if !checkConfig(traceInfo, config, deboostType) {
		return listwiseStores
	}

	topK := config.TopK
	topN := config.TopN
	// list wise 的 list 不包含广告和干预结果, 因此 topK 需要减去TopN-TopK 内广告和干预结果的数量
	topK -= adsOrIntentStoreNum

	if len(listwiseStores) <= topK || topK <= 0 {
		return listwiseStores
	}

	var inTopK, outTopK model.ListWiseItems
	if len(listwiseStores) > topK {
		inTopK = listwiseStores[:topK]
		outTopK = listwiseStores[topK:]
	}

	var checkScoreFunc func(store *model.ListWiseItem) bool
	if deboostType == DeboostRelevance {
		checkScoreFunc = func(store *model.ListWiseItem) bool {
			return store.PRelevanceScore >= config.DeboostThreshold
		}
	} else {
		checkScoreFunc = func(store *model.ListWiseItem) bool {
			return store.DeboostCategoryScore > 0 && store.DeboostCategoryScore >= config.DeboostThreshold
		}
	}

	// TopK 内查找需要打压降权的门店
	inTopKLeft := make(model.ListWiseItems, 0, topK)
	deboostList := make(model.ListWiseItems, 0, topK)
	for i, store := range inTopK {
		if checkScoreFunc(store) || i+1 < topN {
			inTopKLeft = append(inTopKLeft, store)
		} else {
			deboostList = append(deboostList, store)
		}
	}

	// TopK 外查找可以加权的门店
	outTopKLeft := make(model.ListWiseItems, 0, len(listwiseStores)-topK)
	boostList := make(model.ListWiseItems, 0, len(listwiseStores)-topK)
	for _, store := range outTopK {
		var score float64
		var threshold float64
		if deboostType == DeboostRelevance {
			score = store.PRelevanceScore
			threshold = config.DeboostThreshold
		} else {
			score = store.DeboostCategoryScore
			threshold = config.BoostThreshold
		}
		if score < threshold || store.DisplayOpeningStatus != o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
			outTopKLeft = append(outTopKLeft, store)
		} else {
			boostList = append(boostList, store)
		}
	}

	deboostSize := len(deboostList)
	boostSize := len(boostList)
	if deboostSize == 0 || boostSize == 0 {
		return listwiseStores
	}

	if deboostSize > boostSize {
		// 打压降权的门店多余加权的门店, 打压门店按相关性分数排序后选分数低的
		if deboostType == DeboostRelevance {
			sort.Slice(deboostList, func(i, j int) bool {
				return deboostList[i].PRelevanceScore < deboostList[j].PRelevanceScore
			})
		} else {
			sort.Slice(deboostList, func(i, j int) bool {
				return deboostList[i].DeboostCategoryScore < deboostList[j].DeboostCategoryScore
			})
		}
		deboostListLeft := deboostList[boostSize:]
		deboostList = deboostList[:boostSize]
		inTopKLeft = append(inTopKLeft, deboostListLeft...)
	} else if deboostSize < boostSize {
		boostListLeft := boostList[deboostSize:]
		boostList = boostList[:deboostSize]
		outTopKLeft = append(outTopKLeft, boostListLeft...)
	}

	if len(deboostList) == len(boostList) {
		for _, info := range deboostList {
			info.DeboostType = 1
		}
		for _, info := range boostList {
			info.DeboostType = 2
		}
		// 打压降权门店放到 top K 外
		outTopKLeft = append(outTopKLeft, deboostList...)
		// 加权门店放到 top K 内
		inTopKLeft = append(inTopKLeft, boostList...)
		// 各自重新排序
		inTopKLeft.SortByGeneratorScore()
		outTopKLeft.SortByGeneratorScore()
		afterDeboostList := append(inTopKLeft, outTopKLeft...)
		return afterDeboostList
	} else {
		logkit.Error("DeboostReRank deboostSize != boostSize", zap.Int("deboost", len(deboostList)),
			zap.Int("boost", len(boostList)))
	}
	return listwiseStores
}
