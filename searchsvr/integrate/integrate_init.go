package integrate

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/abtest/params_v3"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	data_management_client "git.garena.com/shopee/o2o-intelligence/common/common-lib/data-management"
	esclient "git.garena.com/shopee/o2o-intelligence/common/common-lib/elastic/client"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_peakmode"
	searchFS "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_ml_search_feature"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_vector_engine"
	client2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/client"
	config2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/config"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	spex "git.garena.com/shopee/o2o-intelligence/common/common-lib/spex_client_gray"
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/protobuf/foodads_mixer"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/abtest"
	affiliate_client "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/affiliate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/geo_service"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/httpclient"
	search_limiter "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/limiter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/query_normalize"
	foodalgo_shippingfee "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/shipping_fee"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/store_tags/foody_storetag"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/udp_client"
	"github.com/redis/go-redis/v9"

	food_env "git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/ads"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/dish_searcher"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/rediscache"
)

var RedisClient *redis.Client
var MixerClient foodads_mixer.Client
var MixSearchResultCache *rediscache.MixSearchResultCache
var SearchDishesResultCache *rediscache.SearchDishesResultCache
var SearchStoreCategoriesResultCache *rediscache.SearchStoreCategoriesResultCache
var SearchAffiliateResultCache *rediscache.SearchAffiliateResultCache

var PromotionCacheRedis *rediscache.PromotionCacheRedis
var AdsSearchService *ads.AdsSearchService
var DishSearcherClient *dish_searcher.DishSearcherService
var DataManageServiceClient *data_management_client.DataManagementClient
var S3Client s3.S3Service
var StoreTagClient foody_storetag.Client
var ShippingFeeClient foodalgo_shippingfee.Client
var VectorEngineClient o2oalgo_vector_engine.Client
var PeakModeClient foodalgo_peakmode.Client
var SearchRateLimiter *search_limiter.RateLimiterService

func InitIntegrate(config *config.ShopConfig, serviceName string) {
	InitRedis(&config.Redis)
	InitCodisCache()
	InitQPService()
	mlplatform.InitPredictService(&config.MicroClientConfig)
	InitESClient(config)
	InitStoreTagClient()
	InitShippingFeeClient()
	AdsSearchService = ads.NewAdsSearchService()
	MixerClient = InitMixerClient()
	DishSearcherClient = dish_searcher.NewDishSearcherService(&config.MicroClientConfig)
	InitFeatureServerSpexClient()
	InitDataManageServiceClient()
	VectorEngineClient, _ = o2oalgo_vector_engine.NewClient(spexcommon.DefaultClientOptions...)
	udp_client.InitUdpClient()
	S3Client = s3.NewS3Service(config.S3Config)
	query_normalize.InitQueryNormalize(S3Client)
	geo_service.GeoServiceClient = geo_service.NewGeoService(config.GeoConfig)
	httpclient.Init(&config.VnHttpConfig)
	InitAbTest(serviceName)
	InitPeakModeClient()
	affiliate_client.InitAffiliateClient()

	SearchRateLimiter = search_limiter.InitRateLimitConfig("food")
}

func InitAbTest(serviceName string) {
	// 初始化ab参数平台
	err := params_v3.InitParamsConfig("ab_params_config", food_env.Region(), []string{traceinfo.FoodSearchAbtestProject, traceinfo.CommonAbtestProject})
	if err != nil {
		logkit.Fatal("failed to init abtest params config", logkit.Err(err))
	}
	abtest.BuildABTestService(serviceName)
}

func InitRedis(redisConfig *config2.Redis) {
	RedisClient = client2.NewV9(redisConfig)
}

func InitESClient(config *config.ShopConfig) {
	es.ESClient1 = esclient.New(&config.Elastic)
	es.ESClient2 = esclient.New(&config.ReplicaElastic)
}

func InitMixerClient() foodads_mixer.Client {
	var client foodads_mixer.Client
	var err error
	if cid.IsVN() {
		client, err = foodads_mixer.NewMixerClient(spexcommon.GetDefaultClientOptions()...)
		if err != nil {
			logkit.Fatal("new ads mixer client error", logkit.String("err", err.Error()))
		}
	} else {
		client, err = foodads_mixer.NewClient(spexcommon.GetDefaultClientOptions()...)
		if err != nil {
			logkit.Fatal("new ads mixer client error", logkit.String("err", err.Error()))
		}
	}
	return client
}

func InitCodisCache() {
	MixSearchResultCache = rediscache.NewMixSearchResultCache(RedisClient)
	PromotionCacheRedis = rediscache.NewPromotionCacheRedis(RedisClient)
	SearchDishesResultCache = rediscache.NewSearchDishesResultCache(RedisClient)
	SearchStoreCategoriesResultCache = rediscache.NewSearchStoreCategoriesResultCache(RedisClient)
	SearchAffiliateResultCache = rediscache.NewSearchAffiliateResultCache(RedisClient)
}

func InitQPService() {
	qpResultRedis := rediscache.NewQPResultRedis(RedisClient)
	qp.NewQPService(qpResultRedis)
}

func InitStoreTagClient() error {
	var err error
	foody_storetag.SetGreySwitch(spex.DefaultSwitchFunc)
	StoreTagClient, err = foody_storetag.NewSpexClient(spexcommon.DefaultClientOptions...)
	if err != nil {
		return err
	}
	return nil
}

func InitShippingFeeClient() error {
	var err error
	foodalgo_shippingfee.SetGreySwitch(spex.DefaultSwitchFunc)
	ShippingFeeClient, err = foodalgo_shippingfee.NewSpexClient(spexcommon.DefaultClientOptions...)
	if err != nil {
		return err
	}
	return nil
}

func InitFeatureServerSpexClient() {
	if cid.IsVN() {
		// 2025年04月25日17:21:30 by xinbo: 只有live 会访问  o2oalgo.ml_common.fsfoodysearchgovn
		if env.GetEnv() == "test" || env.GetEnv() == "uat" {
			mlplatform.FeatureServerSpexClient, _ = searchFS.NewClient(spexcommon.DefaultClientOptions...)
		} else {
			mlplatform.FeatureServerSpexClient, _ = searchFS.NewVnClient(spexcommon.DefaultClientOptions...)
		}
	} else {
		mlplatform.FeatureServerSpexClient, _ = searchFS.NewClient(spexcommon.DefaultClientOptions...)
	}
}

func InitDataManageServiceClient() {
	dishOpt := data_management_client.UseDishFreeCache(apollo.SearchApolloCfg.DishFreeCacheSize)
	storeOpt := data_management_client.UseStoreFreeCache(33554432)
	commOpt := data_management_client.UseItemCommissionPlansFreeCach(134217728) // 128M

	dishFlashSaleSize := 134217728 // 128M
	if apollo.SearchApolloCfg.DishFlashSaleFreeCacheSize > 0 {
		dishFlashSaleSize = apollo.SearchApolloCfg.DishFlashSaleFreeCacheSize
	}
	dishFlashSaleOpt := data_management_client.UseFlashSaleDishDiscountFreeCach(dishFlashSaleSize)
	DataManageServiceClient = data_management_client.NewDataManagementClient(dishOpt, storeOpt, commOpt, dishFlashSaleOpt)
}

func InitPeakModeClient() {
	pmClient, err := foodalgo_peakmode.NewClient()
	if err != nil {
		logkit.Error("init foodalgo_peakmode client fail, check spexcommon.InitClient(), panic now!")
		logkit.Fatal("panic!")
	}
	PeakModeClient = pmClient
}
