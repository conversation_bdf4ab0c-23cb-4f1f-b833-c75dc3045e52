package promotion

import (
	"context"
	"encoding/json"
	"go.uber.org/zap"
	"time"

	"git.garena.com/shopee/foody/service/pb/foody_base"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	promotion "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/3rdparty/foody_promotion"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"github.com/golang/protobuf/proto"
)

// 调用promotion.MGetStorePromotionBrief 接口查询优惠，同时转化为3个类型
func GetPromotionBrief(ctx context.Context, buyerId uint64, storeIds []uint64, isSelfPickup bool) (map[uint64]*model.PromotionData, error) {
	promotionBriefTypes := make([]promotion.PromotionBriefType, 0, 6)

	promotionBriefTypes = append(promotionBriefTypes, promotion.PromotionBriefType_SHIPPING_FEE_VOUCHER,
		promotion.PromotionBriefType_COIN_CASHBACK_VOUCHER,
		promotion.PromotionBriefType_FOOD_DISCOUNT_VOUCHER,
		promotion.PromotionBriefType_SHIPPING_FEE_DIRECT_DISCOUNT,
		promotion.PromotionBriefType_ORDER_DIRECT_DISCOUNT,
		promotion.PromotionBriefType_FOOD_DIRECT_DISCOUNT)

	req := &promotion.MGetStorePromotionBriefRequest{BuyerId: proto.Uint64(buyerId), StoreIds: storeIds,
		PromotionBriefTypes: promotionBriefTypes, DeliveryType: proto.Int32(1)}
	if isSelfPickup {
		req.DeliveryType = proto.Int32(2)
	}
	//promotion.SetGreySwitch(spex.DefaultSwitchFunc)
	promotionClient, err := promotion.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to init promotion client", logkit.Any("err", err))
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchPromotionTimeOut)*time.Millisecond)
	defer cancel()
	resp, err := promotionClient.MGetStorePromotionBrief(ctx, req)
	if err != nil {
		return nil, err
	}

	retMap := make(map[uint64]*model.PromotionData)
	for _, val := range resp.PromotionBriefs {
		item := &model.PromotionData{
			HasShippingFeePromotion: val.GetHasShippingFeeVoucher() || val.GetHasShippingFeeDirectDiscount(),
			HasShopPromotion:        val.GetHasCoinCashbackVoucher() || val.GetHasOrderDirectDiscount() || val.GetHasFoodDiscountVoucher(),
			HasDishPromotion:        val.GetHasFoodDirectDiscount(),
		}
		if !item.HasShopPromotion && !item.HasDishPromotion && !item.HasShippingFeePromotion {
			continue
		}
		retMap[val.GetStoreId()] = item
	}

	return retMap, nil
}

// 调用promotion.MGetStorePromotionBrief 接口查询优惠，不转化，直接使用原始优化信息
func GetStorePromotionBrief(ctx context.Context, buyerId uint64, storeIds []uint64, isSelfPickup bool) (map[uint64]*model.StorePromotionData, error) {
	promotionBriefTypes := make([]promotion.PromotionBriefType, 0, 6)

	promotionBriefTypes = append(promotionBriefTypes, promotion.PromotionBriefType_SHIPPING_FEE_VOUCHER,
		promotion.PromotionBriefType_COIN_CASHBACK_VOUCHER,
		promotion.PromotionBriefType_FOOD_DISCOUNT_VOUCHER,
		promotion.PromotionBriefType_SHIPPING_FEE_DIRECT_DISCOUNT,
		promotion.PromotionBriefType_ORDER_DIRECT_DISCOUNT,
		promotion.PromotionBriefType_FOOD_DIRECT_DISCOUNT)

	req := &promotion.MGetStorePromotionBriefRequest{BuyerId: proto.Uint64(buyerId), StoreIds: storeIds,
		PromotionBriefTypes: promotionBriefTypes, DeliveryType: proto.Int32(1)}
	if isSelfPickup {
		req.DeliveryType = proto.Int32(2)
	}
	//promotion.SetGreySwitch(spex.DefaultSwitchFunc)
	promotionClient, err := promotion.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to init promotion client", logkit.Any("err", err))
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchPromotionTimeOut)*time.Millisecond)
	defer cancel()
	st := time.Now()
	resp, err := promotionClient.MGetStorePromotionBrief(ctx, req)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "MGetStorePromotionBrief"), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "MGetStorePromotionBrief")
	if err != nil {
		reporter.ReportClientRequestError(1, "MGetStorePromotionBrief", "failed")
		logkit.FromContext(ctx).Error("failed to MGetStorePromotionBrief", logkit.String("cost", time.Since(st).String()), logkit.Any("err", err))
		return nil, err
	}
	reporter.ReportClientRequestError(1, "MGetStorePromotionBrief", "0")
	retMap := make(map[uint64]*model.StorePromotionData)
	for _, val := range resp.PromotionBriefs {
		retMap[val.GetStoreId()] = &model.StorePromotionData{
			HasShippingFeeVoucher:        val.GetHasShippingFeeVoucher(),
			HasCoinCashBackVoucher:       val.GetHasCoinCashbackVoucher(),
			HasFoodDiscountVoucher:       val.GetHasFoodDiscountVoucher(),
			HasShippingFeeDirectDiscount: val.GetHasShippingFeeDirectDiscount(),
			HasOrderDirectDiscount:       val.GetHasOrderDirectDiscount(),
			HasFoodDirectDiscount:        val.GetHasFoodDirectDiscount(),
		}
	}

	return retMap, nil
}

// 调用promotion.MGetStorePromotion 接口查询运费最多扣减金额，不转化，直接使用原始优化信息
func GetStorePromotion(ctx context.Context, buyerId uint64, storeInfos []*model.StoreInfo) (map[uint64]uint64, error) {
	operationType := promotion.ItemDiscountOperationType_BUYER_STORE_PROMOTION_VIEW_OPERATION
	promotionClass := []promotion.PromotionClass{promotion.PromotionClass_VOUCHER}

	totalBatches := make([]*promotion.MGetStorePromotionRequest_Pair, 0, len(storeInfos))
	for _, store := range storeInfos {
		tmpPromotionPair := &promotion.MGetStorePromotionRequest_Pair{}
		tmpPromotionPair.StoreId = proto.Uint64(store.StoreId)
		partnerType := foody_base.PartnerType(store.PartnerType)
		tmpPromotionPair.PartnerType = &(partnerType)
		totalBatches = append(totalBatches, tmpPromotionPair)
	}

	req := &promotion.MGetStorePromotionRequest{
		BuyerId:          proto.Uint64(buyerId),
		Pairs:            totalBatches,
		OperationType:    &operationType,
		PromotionClasses: promotionClass,
	}
	promotionClient, err := promotion.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to init promotion client", logkit.Any("err", err))
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.SearchPromotionTimeOut)*time.Millisecond)
	defer cancel()
	st := time.Now()
	resp, err := promotionClient.MGetStorePromotion(ctx, req)
	reqStr, _ := json.Marshal(req)
	resStr, _ := json.Marshal(resp)
	logkit.FromContext(ctx).Info("cost time log", zap.String("method", "MGetStorePromotion"), zap.String("cost", time.Since(st).String()), zap.String("req", string(reqStr)), zap.String("resp", string(resStr)))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "MGetStorePromotionBrief")
	if err != nil {
		reporter.ReportClientRequestError(1, "MGetStorePromotion", "failed")
		logkit.FromContext(ctx).Error("failed to MGetStorePromotion", logkit.String("cost", time.Since(st).String()), logkit.Any("err", err))
		return nil, err
	}
	reporter.ReportClientRequestError(1, "MGetStorePromotion", "0")
	retMap := make(map[uint64]uint64)
	for _, val := range resp.Promotions {
		retMap[val.GetStoreId()] = val.GetMaxedShippingFeeDiscountAmount()
	}
	return retMap, nil
}
