package commissionbass

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/client"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
)

type Client interface {
	CheckAmsFeatureToggle(ctx context.Context, in *CheckAMSFeatureToggleRequest, opts ...callopt.Option) (*CheckAMSFeatureToggleResponse, error)

	CheckItemIdList(ctx context.Context, in *CheckItemIdListRequest, opts ...callopt.Option) (*CheckItemIdListResponse, error)

	CheckSellerHasCreatedCampaign(ctx context.Context, in *CheckSellerHasCreatedCampaignRequest, opts ...callopt.Option) (*CheckSellerHasCreatedCampaignResponse, error)

	Echo(ctx context.Context, in *EchoRequest, opts ...callopt.Option) (*EchoResponse, error)

	EchoError(ctx context.Context, in *EchoRequest, opts ...callopt.Option) (*EchoResponse, error)

	// api doc:https://confluence.shopee.io/display/MKT/%5BTD%5DTo+Support+setup+AMS+commission+in+Seller+Center
	GetAccountTc(ctx context.Context, in *GetAccountTcRequest, opts ...callopt.Option) (*GetAccountTcResponse, error)

	GetAmsRecommendationInSellerPlatform(ctx context.Context, in *GetRecommendationRequest, opts ...callopt.Option) (*GetRecommendationResponse, error)

	// get_ams_seller get AMS account of seller
	GetAmsSeller(ctx context.Context, in *GetAMSSellerRequest, opts ...callopt.Option) (*GetAMSSellerResponse, error)

	// get_campaigns_number_for_seller_mission get number of campaigns which match requirement of seller mission
	GetCampaignsNumberForSellerMission(ctx context.Context, in *GetCampaignsNumberForSellerMissionRequest, opts ...callopt.Option) (*GetCampaignsNumberForSellerMissionResponse, error)

	// 根据shopee user_id查询满足条件的所有佣金计划
	// 请求中需要指定tenant
	GetCommissionPlansByAffiliateInfo(ctx context.Context, in *GetCommissionPlansByAffiliateInfoRequest, opts ...callopt.Option) (*GetCommissionPlansByAffiliateInfoResponse, error)

	GetItemsOpenCampaign(ctx context.Context, in *GetItemsOpenCampaignRequest, opts ...callopt.Option) (*GetItemsOpenCampaignResponse, error)

	GetRcmdCommissionRate(ctx context.Context, in *GetRcmdCommissionRateRequest, opts ...callopt.Option) (*GetRcmdCommissionRateResponse, error)

	OpenCampaignSuggestion(ctx context.Context, in *OpenCampaignSuggestionRequest, opts ...callopt.Option) (*OpenCampaignSuggestionResponse, error)

	SetAccountTc(ctx context.Context, in *SetAccountTcRequest, opts ...callopt.Option) (*SetAccountTcResponse, error)

	SetOpenCampaigns(ctx context.Context, in *SetOpenCampaignsRequest, opts ...callopt.Option) (*SetOpenCampaignsResponse, error)
}

func NewClient(opts ...client.Option) (Client, error) {
	namespace := "affiliateplatform.commissionbass"

	client, err := client.NewClient(append([]client.Option{
		client.WithInterceptor(ClientVersionReport(version, serviceName)),
	}, opts...)...)
	if err != nil {
		return nil, err
	}

	cli := &commissionbassClient{
		c:         client,
		namespace: namespace,
	}

	return cli, nil
}

type commissionbassClient struct {
	c         client.Client
	namespace string
}

func (client *commissionbassClient) CheckAmsFeatureToggle(ctx context.Context, in *CheckAMSFeatureToggleRequest, opts ...callopt.Option) (*CheckAMSFeatureToggleResponse, error) {
	out := new(CheckAMSFeatureToggleResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"check_ams_feature_toggle", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) CheckItemIdList(ctx context.Context, in *CheckItemIdListRequest, opts ...callopt.Option) (*CheckItemIdListResponse, error) {
	out := new(CheckItemIdListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"check_item_id_list", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) CheckSellerHasCreatedCampaign(ctx context.Context, in *CheckSellerHasCreatedCampaignRequest, opts ...callopt.Option) (*CheckSellerHasCreatedCampaignResponse, error) {
	out := new(CheckSellerHasCreatedCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"check_seller_has_created_campaign", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) Echo(ctx context.Context, in *EchoRequest, opts ...callopt.Option) (*EchoResponse, error) {
	out := new(EchoResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"echo", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) EchoError(ctx context.Context, in *EchoRequest, opts ...callopt.Option) (*EchoResponse, error) {
	out := new(EchoResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"echo_error", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetAccountTc(ctx context.Context, in *GetAccountTcRequest, opts ...callopt.Option) (*GetAccountTcResponse, error) {
	out := new(GetAccountTcResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_account_tc", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetAmsRecommendationInSellerPlatform(ctx context.Context, in *GetRecommendationRequest, opts ...callopt.Option) (*GetRecommendationResponse, error) {
	out := new(GetRecommendationResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_ams_recommendation_in_seller_platform", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetAmsSeller(ctx context.Context, in *GetAMSSellerRequest, opts ...callopt.Option) (*GetAMSSellerResponse, error) {
	out := new(GetAMSSellerResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_ams_seller", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetCampaignsNumberForSellerMission(ctx context.Context, in *GetCampaignsNumberForSellerMissionRequest, opts ...callopt.Option) (*GetCampaignsNumberForSellerMissionResponse, error) {
	out := new(GetCampaignsNumberForSellerMissionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_campaigns_number_for_seller_mission", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetCommissionPlansByAffiliateInfo(ctx context.Context, in *GetCommissionPlansByAffiliateInfoRequest, opts ...callopt.Option) (*GetCommissionPlansByAffiliateInfoResponse, error) {
	out := new(GetCommissionPlansByAffiliateInfoResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_commission_plans_by_affiliate_info", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetItemsOpenCampaign(ctx context.Context, in *GetItemsOpenCampaignRequest, opts ...callopt.Option) (*GetItemsOpenCampaignResponse, error) {
	out := new(GetItemsOpenCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_items_open_campaign", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) GetRcmdCommissionRate(ctx context.Context, in *GetRcmdCommissionRateRequest, opts ...callopt.Option) (*GetRcmdCommissionRateResponse, error) {
	out := new(GetRcmdCommissionRateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_rcmd_commission_rate", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) OpenCampaignSuggestion(ctx context.Context, in *OpenCampaignSuggestionRequest, opts ...callopt.Option) (*OpenCampaignSuggestionResponse, error) {
	out := new(OpenCampaignSuggestionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"open_campaign_suggestion", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) SetAccountTc(ctx context.Context, in *SetAccountTcRequest, opts ...callopt.Option) (*SetAccountTcResponse, error) {
	out := new(SetAccountTcResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"set_account_tc", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}

func (client *commissionbassClient) SetOpenCampaigns(ctx context.Context, in *SetOpenCampaignsRequest, opts ...callopt.Option) (*SetOpenCampaignsResponse, error) {
	out := new(SetOpenCampaignsResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"set_open_campaigns", in, out, opts...)
	if err != nil {
		return out, err
	}

	return out, nil
}
