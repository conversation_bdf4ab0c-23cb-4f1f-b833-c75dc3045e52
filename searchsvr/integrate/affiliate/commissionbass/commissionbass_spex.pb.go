package commissionbass

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/desc"
	sp_errors "git.garena.com/shopee/platform/golang_splib/errors"
	sp_common "git.garena.com/shopee/sp_protocol/golang/common.pb"
)

type Handler interface {
	CheckAmsFeatureToggle(context.Context, *CheckAMSFeatureToggleRequest) (*CheckAMSFeatureToggleResponse, error)
	CheckItemIdList(context.Context, *CheckItemIdListRequest) (*CheckItemIdListResponse, error)
	CheckSellerHasCreatedCampaign(context.Context, *CheckSellerHasCreatedCampaignRequest) (*CheckSellerHasCreatedCampaignResponse, error)
	Echo(context.Context, *EchoRequest) (*EchoResponse, error)
	EchoError(context.Context, *EchoRequest) (*EchoResponse, error)
	// api doc:https://confluence.shopee.io/display/MKT/%5BTD%5DTo+Support+setup+AMS+commission+in+Seller+Center
	GetAccountTc(context.Context, *GetAccountTcRequest) (*GetAccountTcResponse, error)
	GetAmsRecommendationInSellerPlatform(context.Context, *GetRecommendationRequest) (*GetRecommendationResponse, error)
	// get_ams_seller get AMS account of seller
	GetAmsSeller(context.Context, *GetAMSSellerRequest) (*GetAMSSellerResponse, error)
	// get_campaigns_number_for_seller_mission get number of campaigns which match requirement of seller mission
	GetCampaignsNumberForSellerMission(context.Context, *GetCampaignsNumberForSellerMissionRequest) (*GetCampaignsNumberForSellerMissionResponse, error)
	// 根据shopee user_id查询满足条件的所有佣金计划
	// 请求中需要指定tenant
	GetCommissionPlansByAffiliateInfo(context.Context, *GetCommissionPlansByAffiliateInfoRequest) (*GetCommissionPlansByAffiliateInfoResponse, error)
	GetItemsOpenCampaign(context.Context, *GetItemsOpenCampaignRequest) (*GetItemsOpenCampaignResponse, error)
	GetRcmdCommissionRate(context.Context, *GetRcmdCommissionRateRequest) (*GetRcmdCommissionRateResponse, error)
	OpenCampaignSuggestion(context.Context, *OpenCampaignSuggestionRequest) (*OpenCampaignSuggestionResponse, error)
	SetAccountTc(context.Context, *SetAccountTcRequest) (*SetAccountTcResponse, error)
	SetOpenCampaigns(context.Context, *SetOpenCampaignsRequest) (*SetOpenCampaignsResponse, error)
}

type commissionbassServiceImpl struct {
	methods []desc.Method
	handler Handler
}

func (s *commissionbassServiceImpl) Name() string {
	return "commissionbass"
}

func (s *commissionbassServiceImpl) Methods() []desc.Method {
	return s.methods
}

func (s *commissionbassServiceImpl) Backend() desc.Backend {
	return desc.SPEX
}

type method struct {
	handler      desc.Handler
	command      string
	requestType  interface{}
	responseType interface{}
}

func newMethod(command string, handler desc.Handler, reqType interface{}, respType interface{}) *method {
	return &method{
		command:      command,
		handler:      handler,
		requestType:  reqType,
		responseType: respType,
	}
}

func (m *method) Command() string {
	return m.command
}

func (m *method) RequestType() interface{} {
	return m.requestType
}

func (m *method) ResponseType() interface{} {
	return m.responseType
}

func (m *method) Handler() desc.Handler {
	return m.handler
}

func newService(handler Handler) desc.Service {
	namespace := "affiliateplatform.commissionbass"

	hs := commissionbassServiceImpl{
		handler: handler,
	}
	hs.methods = append(hs.methods,
		newMethod(namespace+"."+"check_ams_feature_toggle", hs.CheckAmsFeatureToggle, &CheckAMSFeatureToggleRequest{}, &CheckAMSFeatureToggleResponse{}),
		newMethod(namespace+"."+"check_item_id_list", hs.CheckItemIdList, &CheckItemIdListRequest{}, &CheckItemIdListResponse{}),
		newMethod(namespace+"."+"check_seller_has_created_campaign", hs.CheckSellerHasCreatedCampaign, &CheckSellerHasCreatedCampaignRequest{}, &CheckSellerHasCreatedCampaignResponse{}),
		newMethod(namespace+"."+"echo", hs.Echo, &EchoRequest{}, &EchoResponse{}),
		newMethod(namespace+"."+"echo_error", hs.EchoError, &EchoRequest{}, &EchoResponse{}),
		newMethod(namespace+"."+"get_account_tc", hs.GetAccountTc, &GetAccountTcRequest{}, &GetAccountTcResponse{}),
		newMethod(namespace+"."+"get_ams_recommendation_in_seller_platform", hs.GetAmsRecommendationInSellerPlatform, &GetRecommendationRequest{}, &GetRecommendationResponse{}),
		newMethod(namespace+"."+"get_ams_seller", hs.GetAmsSeller, &GetAMSSellerRequest{}, &GetAMSSellerResponse{}),
		newMethod(namespace+"."+"get_campaigns_number_for_seller_mission", hs.GetCampaignsNumberForSellerMission, &GetCampaignsNumberForSellerMissionRequest{}, &GetCampaignsNumberForSellerMissionResponse{}),
		newMethod(namespace+"."+"get_commission_plans_by_affiliate_info", hs.GetCommissionPlansByAffiliateInfo, &GetCommissionPlansByAffiliateInfoRequest{}, &GetCommissionPlansByAffiliateInfoResponse{}),
		newMethod(namespace+"."+"get_items_open_campaign", hs.GetItemsOpenCampaign, &GetItemsOpenCampaignRequest{}, &GetItemsOpenCampaignResponse{}),
		newMethod(namespace+"."+"get_rcmd_commission_rate", hs.GetRcmdCommissionRate, &GetRcmdCommissionRateRequest{}, &GetRcmdCommissionRateResponse{}),
		newMethod(namespace+"."+"open_campaign_suggestion", hs.OpenCampaignSuggestion, &OpenCampaignSuggestionRequest{}, &OpenCampaignSuggestionResponse{}),
		newMethod(namespace+"."+"set_account_tc", hs.SetAccountTc, &SetAccountTcRequest{}, &SetAccountTcResponse{}),
		newMethod(namespace+"."+"set_open_campaigns", hs.SetOpenCampaigns, &SetOpenCampaignsRequest{}, &SetOpenCampaignsResponse{}),
	)

	return &hs
}

func (s *commissionbassServiceImpl) CheckAmsFeatureToggle(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CheckAMSFeatureToggleRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.CheckAmsFeatureToggle(ctx, req)
}

func (s *commissionbassServiceImpl) CheckItemIdList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CheckItemIdListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.CheckItemIdList(ctx, req)
}

func (s *commissionbassServiceImpl) CheckSellerHasCreatedCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CheckSellerHasCreatedCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.CheckSellerHasCreatedCampaign(ctx, req)
}

func (s *commissionbassServiceImpl) Echo(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*EchoRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.Echo(ctx, req)
}

func (s *commissionbassServiceImpl) EchoError(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*EchoRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.EchoError(ctx, req)
}

func (s *commissionbassServiceImpl) GetAccountTc(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetAccountTcRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetAccountTc(ctx, req)
}

func (s *commissionbassServiceImpl) GetAmsRecommendationInSellerPlatform(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetRecommendationRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetAmsRecommendationInSellerPlatform(ctx, req)
}

func (s *commissionbassServiceImpl) GetAmsSeller(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetAMSSellerRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetAmsSeller(ctx, req)
}

func (s *commissionbassServiceImpl) GetCampaignsNumberForSellerMission(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetCampaignsNumberForSellerMissionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetCampaignsNumberForSellerMission(ctx, req)
}

func (s *commissionbassServiceImpl) GetCommissionPlansByAffiliateInfo(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetCommissionPlansByAffiliateInfoRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetCommissionPlansByAffiliateInfo(ctx, req)
}

func (s *commissionbassServiceImpl) GetItemsOpenCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetItemsOpenCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetItemsOpenCampaign(ctx, req)
}

func (s *commissionbassServiceImpl) GetRcmdCommissionRate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetRcmdCommissionRateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.GetRcmdCommissionRate(ctx, req)
}

func (s *commissionbassServiceImpl) OpenCampaignSuggestion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*OpenCampaignSuggestionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.OpenCampaignSuggestion(ctx, req)
}

func (s *commissionbassServiceImpl) SetAccountTc(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SetAccountTcRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.SetAccountTc(ctx, req)
}

func (s *commissionbassServiceImpl) SetOpenCampaigns(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SetOpenCampaignsRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	return s.handler.SetOpenCampaigns(ctx, req)
}
