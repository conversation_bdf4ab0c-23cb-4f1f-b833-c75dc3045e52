// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: commissionbass/commissionbass.proto

package commissionbass

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Constant_ErrorCode int32

const (
	Constant_ERROR_SUCCESS                        Constant_ErrorCode = 1601800000
	Constant_ERROR_RECORD_NOT_FOUND               Constant_ErrorCode = 1601800001
	Constant_ERROR_INVALID_COMMISSION_RATE        Constant_ErrorCode = 1601801001
	Constant_ERROR_BLACK_CATEGORY                 Constant_ErrorCode = 1601801002
	Constant_ERROR_BLACK_ITEM                     Constant_ErrorCode = 1601801003
	Constant_ERROR_BLACK_SHOP                     Constant_ErrorCode = 1601801004
	Constant_ERROR_INVALID_PARAM                  Constant_ErrorCode = 1601801005
	Constant_ERROR_SYSTEM_ERROR                   Constant_ErrorCode = 1601801006
	Constant_ERROR_NOT_TC_SHOP                    Constant_ErrorCode = 1601801007
	Constant_ERROR_BLACK_ATTRIBUTE                Constant_ErrorCode = 1601801008
	Constant_ERROR_HAS_VALID_HIGHER_OPEN_CAMPAIGN Constant_ErrorCode = 1601801009
	Constant_ERROR_HAS_TERMINATING_OPEN_CAMPAIGN  Constant_ErrorCode = 1601801010
	Constant_ERROR_COMMISSION_RATE_LOWER_LIMIT    Constant_ErrorCode = 1601801011
	Constant_ERROR_COMMISSION_RATE_HIGHER_LIMIT   Constant_ErrorCode = 1601801012
	Constant_ERROR_NOT_AMS_WHITE_SHOP             Constant_ErrorCode = 1601801013
	Constant_ERROR_NO_RCMD_COMMISSION_RATE        Constant_ErrorCode = 1601801014
	Constant_ERROR_SELLER_COMMISSION_OP_IS_FROZEN Constant_ErrorCode = 1601801015
)

var Constant_ErrorCode_name = map[int32]string{
	1601800000: "ERROR_SUCCESS",
	1601800001: "ERROR_RECORD_NOT_FOUND",
	1601801001: "ERROR_INVALID_COMMISSION_RATE",
	1601801002: "ERROR_BLACK_CATEGORY",
	1601801003: "ERROR_BLACK_ITEM",
	1601801004: "ERROR_BLACK_SHOP",
	1601801005: "ERROR_INVALID_PARAM",
	1601801006: "ERROR_SYSTEM_ERROR",
	1601801007: "ERROR_NOT_TC_SHOP",
	1601801008: "ERROR_BLACK_ATTRIBUTE",
	1601801009: "ERROR_HAS_VALID_HIGHER_OPEN_CAMPAIGN",
	1601801010: "ERROR_HAS_TERMINATING_OPEN_CAMPAIGN",
	1601801011: "ERROR_COMMISSION_RATE_LOWER_LIMIT",
	1601801012: "ERROR_COMMISSION_RATE_HIGHER_LIMIT",
	1601801013: "ERROR_NOT_AMS_WHITE_SHOP",
	1601801014: "ERROR_NO_RCMD_COMMISSION_RATE",
	1601801015: "ERROR_SELLER_COMMISSION_OP_IS_FROZEN",
}

var Constant_ErrorCode_value = map[string]int32{
	"ERROR_SUCCESS":                        1601800000,
	"ERROR_RECORD_NOT_FOUND":               1601800001,
	"ERROR_INVALID_COMMISSION_RATE":        1601801001,
	"ERROR_BLACK_CATEGORY":                 1601801002,
	"ERROR_BLACK_ITEM":                     1601801003,
	"ERROR_BLACK_SHOP":                     1601801004,
	"ERROR_INVALID_PARAM":                  1601801005,
	"ERROR_SYSTEM_ERROR":                   1601801006,
	"ERROR_NOT_TC_SHOP":                    1601801007,
	"ERROR_BLACK_ATTRIBUTE":                1601801008,
	"ERROR_HAS_VALID_HIGHER_OPEN_CAMPAIGN": 1601801009,
	"ERROR_HAS_TERMINATING_OPEN_CAMPAIGN":  1601801010,
	"ERROR_COMMISSION_RATE_LOWER_LIMIT":    1601801011,
	"ERROR_COMMISSION_RATE_HIGHER_LIMIT":   1601801012,
	"ERROR_NOT_AMS_WHITE_SHOP":             1601801013,
	"ERROR_NO_RCMD_COMMISSION_RATE":        1601801014,
	"ERROR_SELLER_COMMISSION_OP_IS_FROZEN": 1601801015,
}

func (x Constant_ErrorCode) Enum() *Constant_ErrorCode {
	p := new(Constant_ErrorCode)
	*p = x
	return p
}

func (x Constant_ErrorCode) String() string {
	return proto.EnumName(Constant_ErrorCode_name, int32(x))
}

func (x *Constant_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_ErrorCode_value, data, "Constant_ErrorCode")
	if err != nil {
		return err
	}
	*x = Constant_ErrorCode(value)
	return nil
}

func (Constant_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 0}
}

type Constant_AMSSellerStatus int32

const (
	Constant_AMS_SELLER_STATUS_NORMAL Constant_AMSSellerStatus = 1
	Constant_AMS_SELLER_STATUS_FROZEN Constant_AMSSellerStatus = 2
)

var Constant_AMSSellerStatus_name = map[int32]string{
	1: "AMS_SELLER_STATUS_NORMAL",
	2: "AMS_SELLER_STATUS_FROZEN",
}

var Constant_AMSSellerStatus_value = map[string]int32{
	"AMS_SELLER_STATUS_NORMAL": 1,
	"AMS_SELLER_STATUS_FROZEN": 2,
}

func (x Constant_AMSSellerStatus) Enum() *Constant_AMSSellerStatus {
	p := new(Constant_AMSSellerStatus)
	*p = x
	return p
}

func (x Constant_AMSSellerStatus) String() string {
	return proto.EnumName(Constant_AMSSellerStatus_name, int32(x))
}

func (x *Constant_AMSSellerStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_AMSSellerStatus_value, data, "Constant_AMSSellerStatus")
	if err != nil {
		return err
	}
	*x = Constant_AMSSellerStatus(value)
	return nil
}

func (Constant_AMSSellerStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 1}
}

type Constant_CampaignGroup int32

const (
	Constant_CAMPAIGN_GROUP_SHOP_CAMPAIGN   Constant_CampaignGroup = 1
	Constant_CAMPAIGN_GROUP_TARGET_CAMPAIGN Constant_CampaignGroup = 2
	Constant_CAMPAIGN_GROUP_OPEN_CAMPAIGN   Constant_CampaignGroup = 3
)

var Constant_CampaignGroup_name = map[int32]string{
	1: "CAMPAIGN_GROUP_SHOP_CAMPAIGN",
	2: "CAMPAIGN_GROUP_TARGET_CAMPAIGN",
	3: "CAMPAIGN_GROUP_OPEN_CAMPAIGN",
}

var Constant_CampaignGroup_value = map[string]int32{
	"CAMPAIGN_GROUP_SHOP_CAMPAIGN":   1,
	"CAMPAIGN_GROUP_TARGET_CAMPAIGN": 2,
	"CAMPAIGN_GROUP_OPEN_CAMPAIGN":   3,
}

func (x Constant_CampaignGroup) Enum() *Constant_CampaignGroup {
	p := new(Constant_CampaignGroup)
	*p = x
	return p
}

func (x Constant_CampaignGroup) String() string {
	return proto.EnumName(Constant_CampaignGroup_name, int32(x))
}

func (x *Constant_CampaignGroup) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_CampaignGroup_value, data, "Constant_CampaignGroup")
	if err != nil {
		return err
	}
	*x = Constant_CampaignGroup(value)
	return nil
}

func (Constant_CampaignGroup) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 2}
}

type Constant_UserTypeV2 int32

const (
	Constant_New      Constant_UserTypeV2 = 1
	Constant_Existing Constant_UserTypeV2 = 2
)

var Constant_UserTypeV2_name = map[int32]string{
	1: "New",
	2: "Existing",
}

var Constant_UserTypeV2_value = map[string]int32{
	"New":      1,
	"Existing": 2,
}

func (x Constant_UserTypeV2) Enum() *Constant_UserTypeV2 {
	p := new(Constant_UserTypeV2)
	*p = x
	return p
}

func (x Constant_UserTypeV2) String() string {
	return proto.EnumName(Constant_UserTypeV2_name, int32(x))
}

func (x *Constant_UserTypeV2) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_UserTypeV2_value, data, "Constant_UserTypeV2")
	if err != nil {
		return err
	}
	*x = Constant_UserTypeV2(value)
	return nil
}

func (Constant_UserTypeV2) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 3}
}

type Constant_TrackingChannelSource int32

const (
	Constant_ChannelSocialMedia   Constant_TrackingChannelSource = 1
	Constant_ChannelShopeeVideo   Constant_TrackingChannelSource = 2
	Constant_ChannelLiveStreaming Constant_TrackingChannelSource = 3
)

var Constant_TrackingChannelSource_name = map[int32]string{
	1: "ChannelSocialMedia",
	2: "ChannelShopeeVideo",
	3: "ChannelLiveStreaming",
}

var Constant_TrackingChannelSource_value = map[string]int32{
	"ChannelSocialMedia":   1,
	"ChannelShopeeVideo":   2,
	"ChannelLiveStreaming": 3,
}

func (x Constant_TrackingChannelSource) Enum() *Constant_TrackingChannelSource {
	p := new(Constant_TrackingChannelSource)
	*p = x
	return p
}

func (x Constant_TrackingChannelSource) String() string {
	return proto.EnumName(Constant_TrackingChannelSource_name, int32(x))
}

func (x *Constant_TrackingChannelSource) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_TrackingChannelSource_value, data, "Constant_TrackingChannelSource")
	if err != nil {
		return err
	}
	*x = Constant_TrackingChannelSource(value)
	return nil
}

func (Constant_TrackingChannelSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 4}
}

type Constant_OrderAttributionModel int32

const (
	Constant_AttrModelIndirectCheckout Constant_OrderAttributionModel = 1
	Constant_AttrModelDirectShop       Constant_OrderAttributionModel = 2
	Constant_AttrModelDirectItem       Constant_OrderAttributionModel = 3
)

var Constant_OrderAttributionModel_name = map[int32]string{
	1: "AttrModelIndirectCheckout",
	2: "AttrModelDirectShop",
	3: "AttrModelDirectItem",
}

var Constant_OrderAttributionModel_value = map[string]int32{
	"AttrModelIndirectCheckout": 1,
	"AttrModelDirectShop":       2,
	"AttrModelDirectItem":       3,
}

func (x Constant_OrderAttributionModel) Enum() *Constant_OrderAttributionModel {
	p := new(Constant_OrderAttributionModel)
	*p = x
	return p
}

func (x Constant_OrderAttributionModel) String() string {
	return proto.EnumName(Constant_OrderAttributionModel_name, int32(x))
}

func (x *Constant_OrderAttributionModel) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_OrderAttributionModel_value, data, "Constant_OrderAttributionModel")
	if err != nil {
		return err
	}
	*x = Constant_OrderAttributionModel(value)
	return nil
}

func (Constant_OrderAttributionModel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 5}
}

type Constant_CommissionType int32

const (
	Constant_COMMISSION_TYPE_UNKNOWN                      Constant_CommissionType = 0
	Constant_COMMISSION_TYPE_CATEGORY                     Constant_CommissionType = 1
	Constant_COMMISSION_TYPE_ITEM                         Constant_CommissionType = 2
	Constant_COMMISSION_TYPE_SHOP                         Constant_CommissionType = 3
	Constant_COMMISSION_TYPE_PRODUCT_LABEL_O_R_COLLECTION Constant_CommissionType = 4
	Constant_COMMISSION_TYPE_SHOP_TAG                     Constant_CommissionType = 5
	Constant_COMMISSION_TYPE_ITEM_TAG                     Constant_CommissionType = 6
)

var Constant_CommissionType_name = map[int32]string{
	0: "COMMISSION_TYPE_UNKNOWN",
	1: "COMMISSION_TYPE_CATEGORY",
	2: "COMMISSION_TYPE_ITEM",
	3: "COMMISSION_TYPE_SHOP",
	4: "COMMISSION_TYPE_PRODUCT_LABEL_O_R_COLLECTION",
	5: "COMMISSION_TYPE_SHOP_TAG",
	6: "COMMISSION_TYPE_ITEM_TAG",
}

var Constant_CommissionType_value = map[string]int32{
	"COMMISSION_TYPE_UNKNOWN":                      0,
	"COMMISSION_TYPE_CATEGORY":                     1,
	"COMMISSION_TYPE_ITEM":                         2,
	"COMMISSION_TYPE_SHOP":                         3,
	"COMMISSION_TYPE_PRODUCT_LABEL_O_R_COLLECTION": 4,
	"COMMISSION_TYPE_SHOP_TAG":                     5,
	"COMMISSION_TYPE_ITEM_TAG":                     6,
}

func (x Constant_CommissionType) Enum() *Constant_CommissionType {
	p := new(Constant_CommissionType)
	*p = x
	return p
}

func (x Constant_CommissionType) String() string {
	return proto.EnumName(Constant_CommissionType_name, int32(x))
}

func (x *Constant_CommissionType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_CommissionType_value, data, "Constant_CommissionType")
	if err != nil {
		return err
	}
	*x = Constant_CommissionType(value)
	return nil
}

func (Constant_CommissionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 6}
}

// TODO(xiangqian.li) feature/span-49892
type Constant_AppType int32

const (
	Constant_Shopee Constant_AppType = 1
	Constant_Now    Constant_AppType = 2
)

var Constant_AppType_name = map[int32]string{
	1: "Shopee",
	2: "Now",
}

var Constant_AppType_value = map[string]int32{
	"Shopee": 1,
	"Now":    2,
}

func (x Constant_AppType) Enum() *Constant_AppType {
	p := new(Constant_AppType)
	*p = x
	return p
}

func (x Constant_AppType) String() string {
	return proto.EnumName(Constant_AppType_name, int32(x))
}

func (x *Constant_AppType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_AppType_value, data, "Constant_AppType")
	if err != nil {
		return err
	}
	*x = Constant_AppType(value)
	return nil
}

func (Constant_AppType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 7}
}

// TODO(xiangqian.li) feature/span-49892
type Constant_Tenant int32

const (
	Constant_TenantShopee Constant_Tenant = 1
	Constant_TenantFood   Constant_Tenant = 2
)

var Constant_Tenant_name = map[int32]string{
	1: "TenantShopee",
	2: "TenantFood",
}

var Constant_Tenant_value = map[string]int32{
	"TenantShopee": 1,
	"TenantFood":   2,
}

func (x Constant_Tenant) Enum() *Constant_Tenant {
	p := new(Constant_Tenant)
	*p = x
	return p
}

func (x Constant_Tenant) String() string {
	return proto.EnumName(Constant_Tenant_name, int32(x))
}

func (x *Constant_Tenant) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_Tenant_value, data, "Constant_Tenant")
	if err != nil {
		return err
	}
	*x = Constant_Tenant(value)
	return nil
}

func (Constant_Tenant) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 8}
}

type Constant_CapOrderType int32

const (
	Constant_CapOrderTypeCheckout Constant_CapOrderType = 1
	Constant_CapOrderTypeOrder    Constant_CapOrderType = 2
)

var Constant_CapOrderType_name = map[int32]string{
	1: "CapOrderTypeCheckout",
	2: "CapOrderTypeOrder",
}

var Constant_CapOrderType_value = map[string]int32{
	"CapOrderTypeCheckout": 1,
	"CapOrderTypeOrder":    2,
}

func (x Constant_CapOrderType) Enum() *Constant_CapOrderType {
	p := new(Constant_CapOrderType)
	*p = x
	return p
}

func (x Constant_CapOrderType) String() string {
	return proto.EnumName(Constant_CapOrderType_name, int32(x))
}

func (x *Constant_CapOrderType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_CapOrderType_value, data, "Constant_CapOrderType")
	if err != nil {
		return err
	}
	*x = Constant_CapOrderType(value)
	return nil
}

func (Constant_CapOrderType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0, 9}
}

type Constant struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Constant) Reset()         { *m = Constant{} }
func (m *Constant) String() string { return proto.CompactTextString(m) }
func (*Constant) ProtoMessage()    {}
func (*Constant) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{0}
}
func (m *Constant) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Constant) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Constant.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Constant) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Constant.Merge(m, src)
}
func (m *Constant) XXX_Size() int {
	return m.Size()
}
func (m *Constant) XXX_DiscardUnknown() {
	xxx_messageInfo_Constant.DiscardUnknown(m)
}

var xxx_messageInfo_Constant proto.InternalMessageInfo

type EchoRequest struct {
	Data                 *string  `protobuf:"bytes,2,opt,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EchoRequest) Reset()         { *m = EchoRequest{} }
func (m *EchoRequest) String() string { return proto.CompactTextString(m) }
func (*EchoRequest) ProtoMessage()    {}
func (*EchoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{1}
}
func (m *EchoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EchoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EchoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EchoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EchoRequest.Merge(m, src)
}
func (m *EchoRequest) XXX_Size() int {
	return m.Size()
}
func (m *EchoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EchoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EchoRequest proto.InternalMessageInfo

func (m *EchoRequest) GetData() string {
	if m != nil && m.Data != nil {
		return *m.Data
	}
	return ""
}

type EchoResponse struct {
	Data                 *string  `protobuf:"bytes,2,opt,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EchoResponse) Reset()         { *m = EchoResponse{} }
func (m *EchoResponse) String() string { return proto.CompactTextString(m) }
func (*EchoResponse) ProtoMessage()    {}
func (*EchoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{2}
}
func (m *EchoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *EchoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_EchoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *EchoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EchoResponse.Merge(m, src)
}
func (m *EchoResponse) XXX_Size() int {
	return m.Size()
}
func (m *EchoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EchoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EchoResponse proto.InternalMessageInfo

func (m *EchoResponse) GetData() string {
	if m != nil && m.Data != nil {
		return *m.Data
	}
	return ""
}

type GetAMSSellerRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	Region               *string  `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAMSSellerRequest) Reset()         { *m = GetAMSSellerRequest{} }
func (m *GetAMSSellerRequest) String() string { return proto.CompactTextString(m) }
func (*GetAMSSellerRequest) ProtoMessage()    {}
func (*GetAMSSellerRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{3}
}
func (m *GetAMSSellerRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAMSSellerRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAMSSellerRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAMSSellerRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAMSSellerRequest.Merge(m, src)
}
func (m *GetAMSSellerRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAMSSellerRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAMSSellerRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAMSSellerRequest proto.InternalMessageInfo

func (m *GetAMSSellerRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *GetAMSSellerRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

type GetAMSSellerResponse struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	Status               *int32   `protobuf:"varint,2,opt,name=status" json:"status,omitempty"`
	CreateTime           *int64   `protobuf:"varint,3,opt,name=create_time,json=createTime" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAMSSellerResponse) Reset()         { *m = GetAMSSellerResponse{} }
func (m *GetAMSSellerResponse) String() string { return proto.CompactTextString(m) }
func (*GetAMSSellerResponse) ProtoMessage()    {}
func (*GetAMSSellerResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{4}
}
func (m *GetAMSSellerResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAMSSellerResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAMSSellerResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAMSSellerResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAMSSellerResponse.Merge(m, src)
}
func (m *GetAMSSellerResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAMSSellerResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAMSSellerResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAMSSellerResponse proto.InternalMessageInfo

func (m *GetAMSSellerResponse) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *GetAMSSellerResponse) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *GetAMSSellerResponse) GetCreateTime() int64 {
	if m != nil && m.CreateTime != nil {
		return *m.CreateTime
	}
	return 0
}

type GetCampaignsNumberForSellerMissionRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	Region               *string  `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	CampaignGroup        *int32   `protobuf:"varint,3,opt,name=campaign_group,json=campaignGroup" json:"campaign_group,omitempty"`
	LeastPromotionDays   *int32   `protobuf:"varint,4,opt,name=least_promotion_days,json=leastPromotionDays" json:"least_promotion_days,omitempty"`
	LeastCommissionRate  *int32   `protobuf:"varint,5,opt,name=least_commission_rate,json=leastCommissionRate" json:"least_commission_rate,omitempty"`
	RequiredNumber       *int64   `protobuf:"varint,6,opt,name=required_number,json=requiredNumber" json:"required_number,omitempty"`
	StartTime            *int64   `protobuf:"varint,7,opt,name=start_time,json=startTime" json:"start_time,omitempty"`
	EndTime              *int64   `protobuf:"varint,8,opt,name=end_time,json=endTime" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCampaignsNumberForSellerMissionRequest) Reset() {
	*m = GetCampaignsNumberForSellerMissionRequest{}
}
func (m *GetCampaignsNumberForSellerMissionRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetCampaignsNumberForSellerMissionRequest) ProtoMessage() {}
func (*GetCampaignsNumberForSellerMissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{5}
}
func (m *GetCampaignsNumberForSellerMissionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetCampaignsNumberForSellerMissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetCampaignsNumberForSellerMissionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetCampaignsNumberForSellerMissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCampaignsNumberForSellerMissionRequest.Merge(m, src)
}
func (m *GetCampaignsNumberForSellerMissionRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetCampaignsNumberForSellerMissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCampaignsNumberForSellerMissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCampaignsNumberForSellerMissionRequest proto.InternalMessageInfo

func (m *GetCampaignsNumberForSellerMissionRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetCampaignGroup() int32 {
	if m != nil && m.CampaignGroup != nil {
		return *m.CampaignGroup
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetLeastPromotionDays() int32 {
	if m != nil && m.LeastPromotionDays != nil {
		return *m.LeastPromotionDays
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetLeastCommissionRate() int32 {
	if m != nil && m.LeastCommissionRate != nil {
		return *m.LeastCommissionRate
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetRequiredNumber() int64 {
	if m != nil && m.RequiredNumber != nil {
		return *m.RequiredNumber
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *GetCampaignsNumberForSellerMissionRequest) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type GetCampaignsNumberForSellerMissionResponse struct {
	Number               *int64   `protobuf:"varint,1,opt,name=number" json:"number,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCampaignsNumberForSellerMissionResponse) Reset() {
	*m = GetCampaignsNumberForSellerMissionResponse{}
}
func (m *GetCampaignsNumberForSellerMissionResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetCampaignsNumberForSellerMissionResponse) ProtoMessage() {}
func (*GetCampaignsNumberForSellerMissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{6}
}
func (m *GetCampaignsNumberForSellerMissionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetCampaignsNumberForSellerMissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetCampaignsNumberForSellerMissionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetCampaignsNumberForSellerMissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCampaignsNumberForSellerMissionResponse.Merge(m, src)
}
func (m *GetCampaignsNumberForSellerMissionResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetCampaignsNumberForSellerMissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCampaignsNumberForSellerMissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCampaignsNumberForSellerMissionResponse proto.InternalMessageInfo

func (m *GetCampaignsNumberForSellerMissionResponse) GetNumber() int64 {
	if m != nil && m.Number != nil {
		return *m.Number
	}
	return 0
}

type GetRecommendationRequest struct {
	Shopid               *int64            `protobuf:"varint,1,opt,name=shopid" json:"shopid,omitempty"`
	Region               *string           `protobuf:"bytes,2,opt,name=region" json:"region,omitempty"`
	Language             *string           `protobuf:"bytes,3,opt,name=language" json:"language,omitempty"`
	ExtraData            map[string]string `protobuf:"bytes,5,rep,name=extra_data,json=extraData" json:"extra_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRecommendationRequest) Reset()         { *m = GetRecommendationRequest{} }
func (m *GetRecommendationRequest) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationRequest) ProtoMessage()    {}
func (*GetRecommendationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{7}
}
func (m *GetRecommendationRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRecommendationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRecommendationRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRecommendationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationRequest.Merge(m, src)
}
func (m *GetRecommendationRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRecommendationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationRequest proto.InternalMessageInfo

func (m *GetRecommendationRequest) GetShopid() int64 {
	if m != nil && m.Shopid != nil {
		return *m.Shopid
	}
	return 0
}

func (m *GetRecommendationRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *GetRecommendationRequest) GetLanguage() string {
	if m != nil && m.Language != nil {
		return *m.Language
	}
	return ""
}

func (m *GetRecommendationRequest) GetExtraData() map[string]string {
	if m != nil {
		return m.ExtraData
	}
	return nil
}

type GetRecommendationResponse struct {
	Code                 *int32              `protobuf:"varint,1,opt,name=code" json:"code,omitempty"`
	Msg                  *string             `protobuf:"bytes,2,opt,name=msg" json:"msg,omitempty"`
	Data                 *RecommendationInfo `protobuf:"bytes,3,opt,name=data" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetRecommendationResponse) Reset()         { *m = GetRecommendationResponse{} }
func (m *GetRecommendationResponse) String() string { return proto.CompactTextString(m) }
func (*GetRecommendationResponse) ProtoMessage()    {}
func (*GetRecommendationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{8}
}
func (m *GetRecommendationResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRecommendationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRecommendationResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRecommendationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendationResponse.Merge(m, src)
}
func (m *GetRecommendationResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRecommendationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendationResponse proto.InternalMessageInfo

func (m *GetRecommendationResponse) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *GetRecommendationResponse) GetMsg() string {
	if m != nil && m.Msg != nil {
		return *m.Msg
	}
	return ""
}

func (m *GetRecommendationResponse) GetData() *RecommendationInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type RecommendationInfo struct {
	TitleKey             *string           `protobuf:"bytes,1,opt,name=title_key,json=titleKey" json:"title_key,omitempty"`
	DescriptionKey       *string           `protobuf:"bytes,2,opt,name=description_key,json=descriptionKey" json:"description_key,omitempty"`
	DescriptionVariable  map[string]string `protobuf:"bytes,3,rep,name=description_variable,json=descriptionVariable" json:"description_variable,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	LabelKey             *string           `protobuf:"bytes,4,opt,name=label_key,json=labelKey" json:"label_key,omitempty"`
	LabelVariable        map[string]string `protobuf:"bytes,5,rep,name=label_variable,json=labelVariable" json:"label_variable,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ButtonKey            *string           `protobuf:"bytes,6,opt,name=button_key,json=buttonKey" json:"button_key,omitempty"`
	RedirectionUrl       *string           `protobuf:"bytes,7,opt,name=redirection_url,json=redirectionUrl" json:"redirection_url,omitempty"`
	IconUrl              *string           `protobuf:"bytes,8,opt,name=icon_url,json=iconUrl" json:"icon_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RecommendationInfo) Reset()         { *m = RecommendationInfo{} }
func (m *RecommendationInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendationInfo) ProtoMessage()    {}
func (*RecommendationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{9}
}
func (m *RecommendationInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RecommendationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RecommendationInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RecommendationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendationInfo.Merge(m, src)
}
func (m *RecommendationInfo) XXX_Size() int {
	return m.Size()
}
func (m *RecommendationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendationInfo proto.InternalMessageInfo

func (m *RecommendationInfo) GetTitleKey() string {
	if m != nil && m.TitleKey != nil {
		return *m.TitleKey
	}
	return ""
}

func (m *RecommendationInfo) GetDescriptionKey() string {
	if m != nil && m.DescriptionKey != nil {
		return *m.DescriptionKey
	}
	return ""
}

func (m *RecommendationInfo) GetDescriptionVariable() map[string]string {
	if m != nil {
		return m.DescriptionVariable
	}
	return nil
}

func (m *RecommendationInfo) GetLabelKey() string {
	if m != nil && m.LabelKey != nil {
		return *m.LabelKey
	}
	return ""
}

func (m *RecommendationInfo) GetLabelVariable() map[string]string {
	if m != nil {
		return m.LabelVariable
	}
	return nil
}

func (m *RecommendationInfo) GetButtonKey() string {
	if m != nil && m.ButtonKey != nil {
		return *m.ButtonKey
	}
	return ""
}

func (m *RecommendationInfo) GetRedirectionUrl() string {
	if m != nil && m.RedirectionUrl != nil {
		return *m.RedirectionUrl
	}
	return ""
}

func (m *RecommendationInfo) GetIconUrl() string {
	if m != nil && m.IconUrl != nil {
		return *m.IconUrl
	}
	return ""
}

type CheckSellerHasCreatedCampaignRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shopId" json:"shopId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSellerHasCreatedCampaignRequest) Reset()         { *m = CheckSellerHasCreatedCampaignRequest{} }
func (m *CheckSellerHasCreatedCampaignRequest) String() string { return proto.CompactTextString(m) }
func (*CheckSellerHasCreatedCampaignRequest) ProtoMessage()    {}
func (*CheckSellerHasCreatedCampaignRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{10}
}
func (m *CheckSellerHasCreatedCampaignRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckSellerHasCreatedCampaignRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckSellerHasCreatedCampaignRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckSellerHasCreatedCampaignRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSellerHasCreatedCampaignRequest.Merge(m, src)
}
func (m *CheckSellerHasCreatedCampaignRequest) XXX_Size() int {
	return m.Size()
}
func (m *CheckSellerHasCreatedCampaignRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSellerHasCreatedCampaignRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSellerHasCreatedCampaignRequest proto.InternalMessageInfo

func (m *CheckSellerHasCreatedCampaignRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type CheckSellerHasCreatedCampaignResponse struct {
	HasCreatedCampaign   *bool    `protobuf:"varint,1,opt,name=hasCreatedCampaign" json:"hasCreatedCampaign,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSellerHasCreatedCampaignResponse) Reset()         { *m = CheckSellerHasCreatedCampaignResponse{} }
func (m *CheckSellerHasCreatedCampaignResponse) String() string { return proto.CompactTextString(m) }
func (*CheckSellerHasCreatedCampaignResponse) ProtoMessage()    {}
func (*CheckSellerHasCreatedCampaignResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{11}
}
func (m *CheckSellerHasCreatedCampaignResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckSellerHasCreatedCampaignResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckSellerHasCreatedCampaignResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckSellerHasCreatedCampaignResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSellerHasCreatedCampaignResponse.Merge(m, src)
}
func (m *CheckSellerHasCreatedCampaignResponse) XXX_Size() int {
	return m.Size()
}
func (m *CheckSellerHasCreatedCampaignResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSellerHasCreatedCampaignResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSellerHasCreatedCampaignResponse proto.InternalMessageInfo

func (m *CheckSellerHasCreatedCampaignResponse) GetHasCreatedCampaign() bool {
	if m != nil && m.HasCreatedCampaign != nil {
		return *m.HasCreatedCampaign
	}
	return false
}

type GetAccountTcRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccountTcRequest) Reset()         { *m = GetAccountTcRequest{} }
func (m *GetAccountTcRequest) String() string { return proto.CompactTextString(m) }
func (*GetAccountTcRequest) ProtoMessage()    {}
func (*GetAccountTcRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{12}
}
func (m *GetAccountTcRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAccountTcRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAccountTcRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAccountTcRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccountTcRequest.Merge(m, src)
}
func (m *GetAccountTcRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAccountTcRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccountTcRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccountTcRequest proto.InternalMessageInfo

func (m *GetAccountTcRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type GetAccountTcResponse struct {
	IsNew                *bool    `protobuf:"varint,1,opt,name=is_new,json=isNew" json:"is_new,omitempty"`
	Status               *int32   `protobuf:"varint,2,opt,name=status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAccountTcResponse) Reset()         { *m = GetAccountTcResponse{} }
func (m *GetAccountTcResponse) String() string { return proto.CompactTextString(m) }
func (*GetAccountTcResponse) ProtoMessage()    {}
func (*GetAccountTcResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{13}
}
func (m *GetAccountTcResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAccountTcResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAccountTcResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAccountTcResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAccountTcResponse.Merge(m, src)
}
func (m *GetAccountTcResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAccountTcResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAccountTcResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAccountTcResponse proto.InternalMessageInfo

func (m *GetAccountTcResponse) GetIsNew() bool {
	if m != nil && m.IsNew != nil {
		return *m.IsNew
	}
	return false
}

func (m *GetAccountTcResponse) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

type SetAccountTcRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	Source               *string  `protobuf:"bytes,2,opt,name=source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAccountTcRequest) Reset()         { *m = SetAccountTcRequest{} }
func (m *SetAccountTcRequest) String() string { return proto.CompactTextString(m) }
func (*SetAccountTcRequest) ProtoMessage()    {}
func (*SetAccountTcRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{14}
}
func (m *SetAccountTcRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetAccountTcRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetAccountTcRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetAccountTcRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAccountTcRequest.Merge(m, src)
}
func (m *SetAccountTcRequest) XXX_Size() int {
	return m.Size()
}
func (m *SetAccountTcRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAccountTcRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetAccountTcRequest proto.InternalMessageInfo

func (m *SetAccountTcRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *SetAccountTcRequest) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

type SetAccountTcResponse struct {
	Result               *bool    `protobuf:"varint,1,opt,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAccountTcResponse) Reset()         { *m = SetAccountTcResponse{} }
func (m *SetAccountTcResponse) String() string { return proto.CompactTextString(m) }
func (*SetAccountTcResponse) ProtoMessage()    {}
func (*SetAccountTcResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{15}
}
func (m *SetAccountTcResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetAccountTcResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetAccountTcResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetAccountTcResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAccountTcResponse.Merge(m, src)
}
func (m *SetAccountTcResponse) XXX_Size() int {
	return m.Size()
}
func (m *SetAccountTcResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAccountTcResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetAccountTcResponse proto.InternalMessageInfo

func (m *SetAccountTcResponse) GetResult() bool {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return false
}

type CheckAMSFeatureToggleRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAMSFeatureToggleRequest) Reset()         { *m = CheckAMSFeatureToggleRequest{} }
func (m *CheckAMSFeatureToggleRequest) String() string { return proto.CompactTextString(m) }
func (*CheckAMSFeatureToggleRequest) ProtoMessage()    {}
func (*CheckAMSFeatureToggleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{16}
}
func (m *CheckAMSFeatureToggleRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckAMSFeatureToggleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckAMSFeatureToggleRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckAMSFeatureToggleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAMSFeatureToggleRequest.Merge(m, src)
}
func (m *CheckAMSFeatureToggleRequest) XXX_Size() int {
	return m.Size()
}
func (m *CheckAMSFeatureToggleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAMSFeatureToggleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAMSFeatureToggleRequest proto.InternalMessageInfo

func (m *CheckAMSFeatureToggleRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type CheckAMSFeatureToggleResponse struct {
	Result               *bool    `protobuf:"varint,1,opt,name=result" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckAMSFeatureToggleResponse) Reset()         { *m = CheckAMSFeatureToggleResponse{} }
func (m *CheckAMSFeatureToggleResponse) String() string { return proto.CompactTextString(m) }
func (*CheckAMSFeatureToggleResponse) ProtoMessage()    {}
func (*CheckAMSFeatureToggleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{17}
}
func (m *CheckAMSFeatureToggleResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckAMSFeatureToggleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckAMSFeatureToggleResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckAMSFeatureToggleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckAMSFeatureToggleResponse.Merge(m, src)
}
func (m *CheckAMSFeatureToggleResponse) XXX_Size() int {
	return m.Size()
}
func (m *CheckAMSFeatureToggleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckAMSFeatureToggleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckAMSFeatureToggleResponse proto.InternalMessageInfo

func (m *CheckAMSFeatureToggleResponse) GetResult() bool {
	if m != nil && m.Result != nil {
		return *m.Result
	}
	return false
}

type GetRcmdCommissionRateRequest struct {
	ItemId               *int64   `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	ShopId               *int64   `protobuf:"varint,2,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRcmdCommissionRateRequest) Reset()         { *m = GetRcmdCommissionRateRequest{} }
func (m *GetRcmdCommissionRateRequest) String() string { return proto.CompactTextString(m) }
func (*GetRcmdCommissionRateRequest) ProtoMessage()    {}
func (*GetRcmdCommissionRateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{18}
}
func (m *GetRcmdCommissionRateRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRcmdCommissionRateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRcmdCommissionRateRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRcmdCommissionRateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdCommissionRateRequest.Merge(m, src)
}
func (m *GetRcmdCommissionRateRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetRcmdCommissionRateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdCommissionRateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdCommissionRateRequest proto.InternalMessageInfo

func (m *GetRcmdCommissionRateRequest) GetItemId() int64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *GetRcmdCommissionRateRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type GetRcmdCommissionRateResponse struct {
	Lv1GlobalBeCategoryId *int64   `protobuf:"varint,1,opt,name=lv1_global_be_category_id,json=lv1GlobalBeCategoryId" json:"lv1_global_be_category_id,omitempty"`
	Lv2GlobalBeCategoryId *int64   `protobuf:"varint,2,opt,name=lv2_global_be_category_id,json=lv2GlobalBeCategoryId" json:"lv2_global_be_category_id,omitempty"`
	MinRate               *int32   `protobuf:"varint,3,opt,name=min_rate,json=minRate" json:"min_rate,omitempty"`
	MaxRate               *int32   `protobuf:"varint,4,opt,name=max_rate,json=maxRate" json:"max_rate,omitempty"`
	PrefillRate           *int32   `protobuf:"varint,5,opt,name=prefill_rate,json=prefillRate" json:"prefill_rate,omitempty"`
	FromLvx               *int32   `protobuf:"varint,6,opt,name=from_lvx,json=fromLvx" json:"from_lvx,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetRcmdCommissionRateResponse) Reset()         { *m = GetRcmdCommissionRateResponse{} }
func (m *GetRcmdCommissionRateResponse) String() string { return proto.CompactTextString(m) }
func (*GetRcmdCommissionRateResponse) ProtoMessage()    {}
func (*GetRcmdCommissionRateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{19}
}
func (m *GetRcmdCommissionRateResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetRcmdCommissionRateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetRcmdCommissionRateResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetRcmdCommissionRateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRcmdCommissionRateResponse.Merge(m, src)
}
func (m *GetRcmdCommissionRateResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetRcmdCommissionRateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRcmdCommissionRateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetRcmdCommissionRateResponse proto.InternalMessageInfo

func (m *GetRcmdCommissionRateResponse) GetLv1GlobalBeCategoryId() int64 {
	if m != nil && m.Lv1GlobalBeCategoryId != nil {
		return *m.Lv1GlobalBeCategoryId
	}
	return 0
}

func (m *GetRcmdCommissionRateResponse) GetLv2GlobalBeCategoryId() int64 {
	if m != nil && m.Lv2GlobalBeCategoryId != nil {
		return *m.Lv2GlobalBeCategoryId
	}
	return 0
}

func (m *GetRcmdCommissionRateResponse) GetMinRate() int32 {
	if m != nil && m.MinRate != nil {
		return *m.MinRate
	}
	return 0
}

func (m *GetRcmdCommissionRateResponse) GetMaxRate() int32 {
	if m != nil && m.MaxRate != nil {
		return *m.MaxRate
	}
	return 0
}

func (m *GetRcmdCommissionRateResponse) GetPrefillRate() int32 {
	if m != nil && m.PrefillRate != nil {
		return *m.PrefillRate
	}
	return 0
}

func (m *GetRcmdCommissionRateResponse) GetFromLvx() int32 {
	if m != nil && m.FromLvx != nil {
		return *m.FromLvx
	}
	return 0
}

type GetItemsOpenCampaignRequest struct {
	ItemIds              []int64  `protobuf:"varint,1,rep,name=item_ids,json=itemIds" json:"item_ids,omitempty"`
	ShopId               *int64   `protobuf:"varint,2,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetItemsOpenCampaignRequest) Reset()         { *m = GetItemsOpenCampaignRequest{} }
func (m *GetItemsOpenCampaignRequest) String() string { return proto.CompactTextString(m) }
func (*GetItemsOpenCampaignRequest) ProtoMessage()    {}
func (*GetItemsOpenCampaignRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{20}
}
func (m *GetItemsOpenCampaignRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetItemsOpenCampaignRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetItemsOpenCampaignRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetItemsOpenCampaignRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemsOpenCampaignRequest.Merge(m, src)
}
func (m *GetItemsOpenCampaignRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetItemsOpenCampaignRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemsOpenCampaignRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemsOpenCampaignRequest proto.InternalMessageInfo

func (m *GetItemsOpenCampaignRequest) GetItemIds() []int64 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

func (m *GetItemsOpenCampaignRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

type GetItemsOpenCampaignResponse struct {
	ItemList             []*OpenCampaignAddedProduct `protobuf:"bytes,1,rep,name=item_list,json=itemList" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetItemsOpenCampaignResponse) Reset()         { *m = GetItemsOpenCampaignResponse{} }
func (m *GetItemsOpenCampaignResponse) String() string { return proto.CompactTextString(m) }
func (*GetItemsOpenCampaignResponse) ProtoMessage()    {}
func (*GetItemsOpenCampaignResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{21}
}
func (m *GetItemsOpenCampaignResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetItemsOpenCampaignResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetItemsOpenCampaignResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetItemsOpenCampaignResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetItemsOpenCampaignResponse.Merge(m, src)
}
func (m *GetItemsOpenCampaignResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetItemsOpenCampaignResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetItemsOpenCampaignResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetItemsOpenCampaignResponse proto.InternalMessageInfo

func (m *GetItemsOpenCampaignResponse) GetItemList() []*OpenCampaignAddedProduct {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type OpenCampaignAddedProduct struct {
	ItemId               *int64   `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	ItemName             *string  `protobuf:"bytes,2,opt,name=item_name,json=itemName" json:"item_name,omitempty"`
	CommissionId         *int64   `protobuf:"varint,3,opt,name=commission_id,json=commissionId" json:"commission_id,omitempty"`
	CommissionStatus     *int32   `protobuf:"varint,4,opt,name=commission_status,json=commissionStatus" json:"commission_status,omitempty"`
	CommissionRate       *int32   `protobuf:"varint,5,opt,name=commission_rate,json=commissionRate" json:"commission_rate,omitempty"`
	PeriodStartTime      *int64   `protobuf:"varint,6,opt,name=period_start_time,json=periodStartTime" json:"period_start_time,omitempty"`
	PeriodEndTime        *int64   `protobuf:"varint,7,opt,name=period_end_time,json=periodEndTime" json:"period_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OpenCampaignAddedProduct) Reset()         { *m = OpenCampaignAddedProduct{} }
func (m *OpenCampaignAddedProduct) String() string { return proto.CompactTextString(m) }
func (*OpenCampaignAddedProduct) ProtoMessage()    {}
func (*OpenCampaignAddedProduct) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{22}
}
func (m *OpenCampaignAddedProduct) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenCampaignAddedProduct) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenCampaignAddedProduct.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenCampaignAddedProduct) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCampaignAddedProduct.Merge(m, src)
}
func (m *OpenCampaignAddedProduct) XXX_Size() int {
	return m.Size()
}
func (m *OpenCampaignAddedProduct) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCampaignAddedProduct.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCampaignAddedProduct proto.InternalMessageInfo

func (m *OpenCampaignAddedProduct) GetItemId() int64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *OpenCampaignAddedProduct) GetItemName() string {
	if m != nil && m.ItemName != nil {
		return *m.ItemName
	}
	return ""
}

func (m *OpenCampaignAddedProduct) GetCommissionId() int64 {
	if m != nil && m.CommissionId != nil {
		return *m.CommissionId
	}
	return 0
}

func (m *OpenCampaignAddedProduct) GetCommissionStatus() int32 {
	if m != nil && m.CommissionStatus != nil {
		return *m.CommissionStatus
	}
	return 0
}

func (m *OpenCampaignAddedProduct) GetCommissionRate() int32 {
	if m != nil && m.CommissionRate != nil {
		return *m.CommissionRate
	}
	return 0
}

func (m *OpenCampaignAddedProduct) GetPeriodStartTime() int64 {
	if m != nil && m.PeriodStartTime != nil {
		return *m.PeriodStartTime
	}
	return 0
}

func (m *OpenCampaignAddedProduct) GetPeriodEndTime() int64 {
	if m != nil && m.PeriodEndTime != nil {
		return *m.PeriodEndTime
	}
	return 0
}

type SetOpenCampaignsRequest struct {
	Items                []*SetOpenCampaignItem `protobuf:"bytes,1,rep,name=items" json:"items,omitempty"`
	ShopId               *int64                 `protobuf:"varint,2,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	CommissionRate       *int32                 `protobuf:"varint,3,opt,name=commission_rate,json=commissionRate" json:"commission_rate,omitempty"`
	Operator             *string                `protobuf:"bytes,4,opt,name=operator" json:"operator,omitempty"`
	Source               *string                `protobuf:"bytes,5,opt,name=source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SetOpenCampaignsRequest) Reset()         { *m = SetOpenCampaignsRequest{} }
func (m *SetOpenCampaignsRequest) String() string { return proto.CompactTextString(m) }
func (*SetOpenCampaignsRequest) ProtoMessage()    {}
func (*SetOpenCampaignsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{23}
}
func (m *SetOpenCampaignsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetOpenCampaignsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetOpenCampaignsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetOpenCampaignsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOpenCampaignsRequest.Merge(m, src)
}
func (m *SetOpenCampaignsRequest) XXX_Size() int {
	return m.Size()
}
func (m *SetOpenCampaignsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOpenCampaignsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetOpenCampaignsRequest proto.InternalMessageInfo

func (m *SetOpenCampaignsRequest) GetItems() []*SetOpenCampaignItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *SetOpenCampaignsRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *SetOpenCampaignsRequest) GetCommissionRate() int32 {
	if m != nil && m.CommissionRate != nil {
		return *m.CommissionRate
	}
	return 0
}

func (m *SetOpenCampaignsRequest) GetOperator() string {
	if m != nil && m.Operator != nil {
		return *m.Operator
	}
	return ""
}

func (m *SetOpenCampaignsRequest) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

type SetOpenCampaignItem struct {
	ItemId               *int64   `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	ItemName             *string  `protobuf:"bytes,2,opt,name=item_name,json=itemName" json:"item_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOpenCampaignItem) Reset()         { *m = SetOpenCampaignItem{} }
func (m *SetOpenCampaignItem) String() string { return proto.CompactTextString(m) }
func (*SetOpenCampaignItem) ProtoMessage()    {}
func (*SetOpenCampaignItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{24}
}
func (m *SetOpenCampaignItem) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetOpenCampaignItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetOpenCampaignItem.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetOpenCampaignItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOpenCampaignItem.Merge(m, src)
}
func (m *SetOpenCampaignItem) XXX_Size() int {
	return m.Size()
}
func (m *SetOpenCampaignItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOpenCampaignItem.DiscardUnknown(m)
}

var xxx_messageInfo_SetOpenCampaignItem proto.InternalMessageInfo

func (m *SetOpenCampaignItem) GetItemId() int64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *SetOpenCampaignItem) GetItemName() string {
	if m != nil && m.ItemName != nil {
		return *m.ItemName
	}
	return ""
}

type SetOpenCampaignsResponse struct {
	IsAllSuccess         *bool                    `protobuf:"varint,1,opt,name=is_all_success,json=isAllSuccess" json:"is_all_success,omitempty"`
	Results              []*SetOpenCampaignResult `protobuf:"bytes,2,rep,name=results" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *SetOpenCampaignsResponse) Reset()         { *m = SetOpenCampaignsResponse{} }
func (m *SetOpenCampaignsResponse) String() string { return proto.CompactTextString(m) }
func (*SetOpenCampaignsResponse) ProtoMessage()    {}
func (*SetOpenCampaignsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{25}
}
func (m *SetOpenCampaignsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetOpenCampaignsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetOpenCampaignsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetOpenCampaignsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOpenCampaignsResponse.Merge(m, src)
}
func (m *SetOpenCampaignsResponse) XXX_Size() int {
	return m.Size()
}
func (m *SetOpenCampaignsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOpenCampaignsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetOpenCampaignsResponse proto.InternalMessageInfo

func (m *SetOpenCampaignsResponse) GetIsAllSuccess() bool {
	if m != nil && m.IsAllSuccess != nil {
		return *m.IsAllSuccess
	}
	return false
}

func (m *SetOpenCampaignsResponse) GetResults() []*SetOpenCampaignResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type SetOpenCampaignResult struct {
	ItemId               *int64   `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	ErrCode              *int32   `protobuf:"varint,2,opt,name=err_code,json=errCode" json:"err_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOpenCampaignResult) Reset()         { *m = SetOpenCampaignResult{} }
func (m *SetOpenCampaignResult) String() string { return proto.CompactTextString(m) }
func (*SetOpenCampaignResult) ProtoMessage()    {}
func (*SetOpenCampaignResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{26}
}
func (m *SetOpenCampaignResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SetOpenCampaignResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SetOpenCampaignResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SetOpenCampaignResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOpenCampaignResult.Merge(m, src)
}
func (m *SetOpenCampaignResult) XXX_Size() int {
	return m.Size()
}
func (m *SetOpenCampaignResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOpenCampaignResult.DiscardUnknown(m)
}

var xxx_messageInfo_SetOpenCampaignResult proto.InternalMessageInfo

func (m *SetOpenCampaignResult) GetItemId() int64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *SetOpenCampaignResult) GetErrCode() int32 {
	if m != nil && m.ErrCode != nil {
		return *m.ErrCode
	}
	return 0
}

type CheckItemResult struct {
	ItemId               *int64   `protobuf:"varint,1,opt,name=item_id,json=itemId" json:"item_id,omitempty"`
	IsPass               *bool    `protobuf:"varint,2,opt,name=is_pass,json=isPass" json:"is_pass,omitempty"`
	ErrCode              *int32   `protobuf:"varint,3,opt,name=err_code,json=errCode" json:"err_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckItemResult) Reset()         { *m = CheckItemResult{} }
func (m *CheckItemResult) String() string { return proto.CompactTextString(m) }
func (*CheckItemResult) ProtoMessage()    {}
func (*CheckItemResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{27}
}
func (m *CheckItemResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckItemResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckItemResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckItemResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckItemResult.Merge(m, src)
}
func (m *CheckItemResult) XXX_Size() int {
	return m.Size()
}
func (m *CheckItemResult) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckItemResult.DiscardUnknown(m)
}

var xxx_messageInfo_CheckItemResult proto.InternalMessageInfo

func (m *CheckItemResult) GetItemId() int64 {
	if m != nil && m.ItemId != nil {
		return *m.ItemId
	}
	return 0
}

func (m *CheckItemResult) GetIsPass() bool {
	if m != nil && m.IsPass != nil {
		return *m.IsPass
	}
	return false
}

func (m *CheckItemResult) GetErrCode() int32 {
	if m != nil && m.ErrCode != nil {
		return *m.ErrCode
	}
	return 0
}

type CheckItemIdListRequest struct {
	ShopId               *int64   `protobuf:"varint,1,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	ItemIds              []int64  `protobuf:"varint,2,rep,name=item_ids,json=itemIds" json:"item_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckItemIdListRequest) Reset()         { *m = CheckItemIdListRequest{} }
func (m *CheckItemIdListRequest) String() string { return proto.CompactTextString(m) }
func (*CheckItemIdListRequest) ProtoMessage()    {}
func (*CheckItemIdListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{28}
}
func (m *CheckItemIdListRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckItemIdListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckItemIdListRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckItemIdListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckItemIdListRequest.Merge(m, src)
}
func (m *CheckItemIdListRequest) XXX_Size() int {
	return m.Size()
}
func (m *CheckItemIdListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckItemIdListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckItemIdListRequest proto.InternalMessageInfo

func (m *CheckItemIdListRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *CheckItemIdListRequest) GetItemIds() []int64 {
	if m != nil {
		return m.ItemIds
	}
	return nil
}

type CheckItemIdListResponse struct {
	Items                []*CheckItemResult `protobuf:"bytes,1,rep,name=items" json:"items,omitempty"`
	ErrCode              *int32             `protobuf:"varint,2,opt,name=err_code,json=errCode" json:"err_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CheckItemIdListResponse) Reset()         { *m = CheckItemIdListResponse{} }
func (m *CheckItemIdListResponse) String() string { return proto.CompactTextString(m) }
func (*CheckItemIdListResponse) ProtoMessage()    {}
func (*CheckItemIdListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{29}
}
func (m *CheckItemIdListResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CheckItemIdListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CheckItemIdListResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CheckItemIdListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckItemIdListResponse.Merge(m, src)
}
func (m *CheckItemIdListResponse) XXX_Size() int {
	return m.Size()
}
func (m *CheckItemIdListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckItemIdListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckItemIdListResponse proto.InternalMessageInfo

func (m *CheckItemIdListResponse) GetItems() []*CheckItemResult {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *CheckItemIdListResponse) GetErrCode() int32 {
	if m != nil && m.ErrCode != nil {
		return *m.ErrCode
	}
	return 0
}

type OpenCampaignSuggestionRequest struct {
	MerchantId           *int64            `protobuf:"varint,1,opt,name=merchant_id,json=merchantId" json:"merchant_id,omitempty"`
	UserId               *int64            `protobuf:"varint,2,opt,name=user_id,json=userId" json:"user_id,omitempty"`
	ShopId               *int64            `protobuf:"varint,3,opt,name=shop_id,json=shopId" json:"shop_id,omitempty"`
	DetailMap            map[string]string `protobuf:"bytes,4,rep,name=detail_map,json=detailMap" json:"detail_map,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *OpenCampaignSuggestionRequest) Reset()         { *m = OpenCampaignSuggestionRequest{} }
func (m *OpenCampaignSuggestionRequest) String() string { return proto.CompactTextString(m) }
func (*OpenCampaignSuggestionRequest) ProtoMessage()    {}
func (*OpenCampaignSuggestionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{30}
}
func (m *OpenCampaignSuggestionRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenCampaignSuggestionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenCampaignSuggestionRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenCampaignSuggestionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCampaignSuggestionRequest.Merge(m, src)
}
func (m *OpenCampaignSuggestionRequest) XXX_Size() int {
	return m.Size()
}
func (m *OpenCampaignSuggestionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCampaignSuggestionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCampaignSuggestionRequest proto.InternalMessageInfo

func (m *OpenCampaignSuggestionRequest) GetMerchantId() int64 {
	if m != nil && m.MerchantId != nil {
		return *m.MerchantId
	}
	return 0
}

func (m *OpenCampaignSuggestionRequest) GetUserId() int64 {
	if m != nil && m.UserId != nil {
		return *m.UserId
	}
	return 0
}

func (m *OpenCampaignSuggestionRequest) GetShopId() int64 {
	if m != nil && m.ShopId != nil {
		return *m.ShopId
	}
	return 0
}

func (m *OpenCampaignSuggestionRequest) GetDetailMap() map[string]string {
	if m != nil {
		return m.DetailMap
	}
	return nil
}

type OpenCampaignSuggestionResponse struct {
	HitCondition                  *bool             `protobuf:"varint,1,opt,name=hit_condition,json=hitCondition" json:"hit_condition,omitempty"`
	TitleMapping                  map[string]string `protobuf:"bytes,2,rep,name=title_mapping,json=titleMapping" json:"title_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	DescriptionMapping            map[string]string `protobuf:"bytes,3,rep,name=description_mapping,json=descriptionMapping" json:"description_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TitleTransifyKeyMapping       map[string]string `protobuf:"bytes,4,rep,name=title_transify_key_mapping,json=titleTransifyKeyMapping" json:"title_transify_key_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	DescriptionTransifyKeyMapping map[string]string `protobuf:"bytes,5,rep,name=description_transify_key_mapping,json=descriptionTransifyKeyMapping" json:"description_transify_key_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ButtonLinkMapping             map[string]string `protobuf:"bytes,6,rep,name=button_link_mapping,json=buttonLinkMapping" json:"button_link_mapping,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral          struct{}          `json:"-"`
	XXX_unrecognized              []byte            `json:"-"`
	XXX_sizecache                 int32             `json:"-"`
}

func (m *OpenCampaignSuggestionResponse) Reset()         { *m = OpenCampaignSuggestionResponse{} }
func (m *OpenCampaignSuggestionResponse) String() string { return proto.CompactTextString(m) }
func (*OpenCampaignSuggestionResponse) ProtoMessage()    {}
func (*OpenCampaignSuggestionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{31}
}
func (m *OpenCampaignSuggestionResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OpenCampaignSuggestionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OpenCampaignSuggestionResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OpenCampaignSuggestionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OpenCampaignSuggestionResponse.Merge(m, src)
}
func (m *OpenCampaignSuggestionResponse) XXX_Size() int {
	return m.Size()
}
func (m *OpenCampaignSuggestionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OpenCampaignSuggestionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OpenCampaignSuggestionResponse proto.InternalMessageInfo

func (m *OpenCampaignSuggestionResponse) GetHitCondition() bool {
	if m != nil && m.HitCondition != nil {
		return *m.HitCondition
	}
	return false
}

func (m *OpenCampaignSuggestionResponse) GetTitleMapping() map[string]string {
	if m != nil {
		return m.TitleMapping
	}
	return nil
}

func (m *OpenCampaignSuggestionResponse) GetDescriptionMapping() map[string]string {
	if m != nil {
		return m.DescriptionMapping
	}
	return nil
}

func (m *OpenCampaignSuggestionResponse) GetTitleTransifyKeyMapping() map[string]string {
	if m != nil {
		return m.TitleTransifyKeyMapping
	}
	return nil
}

func (m *OpenCampaignSuggestionResponse) GetDescriptionTransifyKeyMapping() map[string]string {
	if m != nil {
		return m.DescriptionTransifyKeyMapping
	}
	return nil
}

func (m *OpenCampaignSuggestionResponse) GetButtonLinkMapping() map[string]string {
	if m != nil {
		return m.ButtonLinkMapping
	}
	return nil
}

type GetCommissionPlansByAffiliateInfoRequest struct {
	Tenant                    *uint32  `protobuf:"varint,1,opt,name=tenant" json:"tenant,omitempty"`
	AffiliateId               *uint64  `protobuf:"varint,2,opt,name=affiliate_id,json=affiliateId" json:"affiliate_id,omitempty"`
	NeedOrderCapNewBuyer      *bool    `protobuf:"varint,3,opt,name=need_order_cap_new_buyer,json=needOrderCapNewBuyer" json:"need_order_cap_new_buyer,omitempty"`
	NeedOrderCapExistingBuyer *bool    `protobuf:"varint,4,opt,name=need_order_cap_existing_buyer,json=needOrderCapExistingBuyer" json:"need_order_cap_existing_buyer,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *GetCommissionPlansByAffiliateInfoRequest) Reset() {
	*m = GetCommissionPlansByAffiliateInfoRequest{}
}
func (m *GetCommissionPlansByAffiliateInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCommissionPlansByAffiliateInfoRequest) ProtoMessage()    {}
func (*GetCommissionPlansByAffiliateInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{32}
}
func (m *GetCommissionPlansByAffiliateInfoRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetCommissionPlansByAffiliateInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetCommissionPlansByAffiliateInfoRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetCommissionPlansByAffiliateInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommissionPlansByAffiliateInfoRequest.Merge(m, src)
}
func (m *GetCommissionPlansByAffiliateInfoRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetCommissionPlansByAffiliateInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommissionPlansByAffiliateInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommissionPlansByAffiliateInfoRequest proto.InternalMessageInfo

func (m *GetCommissionPlansByAffiliateInfoRequest) GetTenant() uint32 {
	if m != nil && m.Tenant != nil {
		return *m.Tenant
	}
	return 0
}

func (m *GetCommissionPlansByAffiliateInfoRequest) GetAffiliateId() uint64 {
	if m != nil && m.AffiliateId != nil {
		return *m.AffiliateId
	}
	return 0
}

func (m *GetCommissionPlansByAffiliateInfoRequest) GetNeedOrderCapNewBuyer() bool {
	if m != nil && m.NeedOrderCapNewBuyer != nil {
		return *m.NeedOrderCapNewBuyer
	}
	return false
}

func (m *GetCommissionPlansByAffiliateInfoRequest) GetNeedOrderCapExistingBuyer() bool {
	if m != nil && m.NeedOrderCapExistingBuyer != nil {
		return *m.NeedOrderCapExistingBuyer
	}
	return false
}

type GetCommissionPlansByAffiliateInfoResponse struct {
	CommissionPlans       []*CommissionPlanDetail `protobuf:"bytes,1,rep,name=commission_plans,json=commissionPlans" json:"commission_plans,omitempty"`
	OrderCapNewBuyer      *OrderCap               `protobuf:"bytes,2,opt,name=order_cap_new_buyer,json=orderCapNewBuyer" json:"order_cap_new_buyer,omitempty"`
	OrderCapExistingBuyer *OrderCap               `protobuf:"bytes,3,opt,name=order_cap_existing_buyer,json=orderCapExistingBuyer" json:"order_cap_existing_buyer,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *GetCommissionPlansByAffiliateInfoResponse) Reset() {
	*m = GetCommissionPlansByAffiliateInfoResponse{}
}
func (m *GetCommissionPlansByAffiliateInfoResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetCommissionPlansByAffiliateInfoResponse) ProtoMessage() {}
func (*GetCommissionPlansByAffiliateInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{33}
}
func (m *GetCommissionPlansByAffiliateInfoResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetCommissionPlansByAffiliateInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetCommissionPlansByAffiliateInfoResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetCommissionPlansByAffiliateInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCommissionPlansByAffiliateInfoResponse.Merge(m, src)
}
func (m *GetCommissionPlansByAffiliateInfoResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetCommissionPlansByAffiliateInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCommissionPlansByAffiliateInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCommissionPlansByAffiliateInfoResponse proto.InternalMessageInfo

func (m *GetCommissionPlansByAffiliateInfoResponse) GetCommissionPlans() []*CommissionPlanDetail {
	if m != nil {
		return m.CommissionPlans
	}
	return nil
}

func (m *GetCommissionPlansByAffiliateInfoResponse) GetOrderCapNewBuyer() *OrderCap {
	if m != nil {
		return m.OrderCapNewBuyer
	}
	return nil
}

func (m *GetCommissionPlansByAffiliateInfoResponse) GetOrderCapExistingBuyer() *OrderCap {
	if m != nil {
		return m.OrderCapExistingBuyer
	}
	return nil
}

type CommissionPlanDetail struct {
	CommissionId         *uint64                 `protobuf:"varint,1,opt,name=commission_id,json=commissionId" json:"commission_id,omitempty"`
	CommissionType       *uint32                 `protobuf:"varint,2,opt,name=commission_type,json=commissionType" json:"commission_type,omitempty"`
	PeriodStartTime      *uint64                 `protobuf:"varint,3,opt,name=period_start_time,json=periodStartTime" json:"period_start_time,omitempty"`
	PeriodEndTime        *uint64                 `protobuf:"varint,4,opt,name=period_end_time,json=periodEndTime" json:"period_end_time,omitempty"`
	CommissionRates      []*CommissionRateDetail `protobuf:"bytes,5,rep,name=commission_rates,json=commissionRates" json:"commission_rates,omitempty"`
	Version              *uint32                 `protobuf:"varint,6,opt,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *CommissionPlanDetail) Reset()         { *m = CommissionPlanDetail{} }
func (m *CommissionPlanDetail) String() string { return proto.CompactTextString(m) }
func (*CommissionPlanDetail) ProtoMessage()    {}
func (*CommissionPlanDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{34}
}
func (m *CommissionPlanDetail) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommissionPlanDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommissionPlanDetail.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommissionPlanDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionPlanDetail.Merge(m, src)
}
func (m *CommissionPlanDetail) XXX_Size() int {
	return m.Size()
}
func (m *CommissionPlanDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionPlanDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionPlanDetail proto.InternalMessageInfo

func (m *CommissionPlanDetail) GetCommissionId() uint64 {
	if m != nil && m.CommissionId != nil {
		return *m.CommissionId
	}
	return 0
}

func (m *CommissionPlanDetail) GetCommissionType() uint32 {
	if m != nil && m.CommissionType != nil {
		return *m.CommissionType
	}
	return 0
}

func (m *CommissionPlanDetail) GetPeriodStartTime() uint64 {
	if m != nil && m.PeriodStartTime != nil {
		return *m.PeriodStartTime
	}
	return 0
}

func (m *CommissionPlanDetail) GetPeriodEndTime() uint64 {
	if m != nil && m.PeriodEndTime != nil {
		return *m.PeriodEndTime
	}
	return 0
}

func (m *CommissionPlanDetail) GetCommissionRates() []*CommissionRateDetail {
	if m != nil {
		return m.CommissionRates
	}
	return nil
}

func (m *CommissionPlanDetail) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

type CommissionRateDetail struct {
	ChannelSource        *uint32  `protobuf:"varint,1,opt,name=channel_source,json=channelSource" json:"channel_source,omitempty"`
	AttributionModel     *uint32  `protobuf:"varint,2,opt,name=attribution_model,json=attributionModel" json:"attribution_model,omitempty"`
	UserTypeV2           *uint32  `protobuf:"varint,3,opt,name=user_type_v2,json=userTypeV2" json:"user_type_v2,omitempty"`
	AppType              *uint32  `protobuf:"varint,4,opt,name=app_type,json=appType" json:"app_type,omitempty"`
	CommissionRate       *uint32  `protobuf:"varint,5,opt,name=commission_rate,json=commissionRate" json:"commission_rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CommissionRateDetail) Reset()         { *m = CommissionRateDetail{} }
func (m *CommissionRateDetail) String() string { return proto.CompactTextString(m) }
func (*CommissionRateDetail) ProtoMessage()    {}
func (*CommissionRateDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{35}
}
func (m *CommissionRateDetail) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CommissionRateDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CommissionRateDetail.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CommissionRateDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CommissionRateDetail.Merge(m, src)
}
func (m *CommissionRateDetail) XXX_Size() int {
	return m.Size()
}
func (m *CommissionRateDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CommissionRateDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CommissionRateDetail proto.InternalMessageInfo

func (m *CommissionRateDetail) GetChannelSource() uint32 {
	if m != nil && m.ChannelSource != nil {
		return *m.ChannelSource
	}
	return 0
}

func (m *CommissionRateDetail) GetAttributionModel() uint32 {
	if m != nil && m.AttributionModel != nil {
		return *m.AttributionModel
	}
	return 0
}

func (m *CommissionRateDetail) GetUserTypeV2() uint32 {
	if m != nil && m.UserTypeV2 != nil {
		return *m.UserTypeV2
	}
	return 0
}

func (m *CommissionRateDetail) GetAppType() uint32 {
	if m != nil && m.AppType != nil {
		return *m.AppType
	}
	return 0
}

func (m *CommissionRateDetail) GetCommissionRate() uint32 {
	if m != nil && m.CommissionRate != nil {
		return *m.CommissionRate
	}
	return 0
}

// 2023-05-08之后cap在order上生效
type OrderCap struct {
	CapId                *uint64  `protobuf:"varint,1,opt,name=cap_id,json=capId" json:"cap_id,omitempty"`
	CapValue             *uint64  `protobuf:"varint,2,opt,name=cap_value,json=capValue" json:"cap_value,omitempty"`
	CapType              *uint32  `protobuf:"varint,3,opt,name=cap_type,json=capType" json:"cap_type,omitempty"`
	UserType             *uint32  `protobuf:"varint,4,opt,name=user_type,json=userType" json:"user_type,omitempty"`
	TagId                *uint64  `protobuf:"varint,5,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Version              *uint32  `protobuf:"varint,6,opt,name=version" json:"version,omitempty"`
	CapOrderType         *uint32  `protobuf:"varint,7,opt,name=cap_order_type,json=capOrderType" json:"cap_order_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderCap) Reset()         { *m = OrderCap{} }
func (m *OrderCap) String() string { return proto.CompactTextString(m) }
func (*OrderCap) ProtoMessage()    {}
func (*OrderCap) Descriptor() ([]byte, []int) {
	return fileDescriptor_5c3460d3de3051d2, []int{36}
}
func (m *OrderCap) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OrderCap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OrderCap.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OrderCap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderCap.Merge(m, src)
}
func (m *OrderCap) XXX_Size() int {
	return m.Size()
}
func (m *OrderCap) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderCap.DiscardUnknown(m)
}

var xxx_messageInfo_OrderCap proto.InternalMessageInfo

func (m *OrderCap) GetCapId() uint64 {
	if m != nil && m.CapId != nil {
		return *m.CapId
	}
	return 0
}

func (m *OrderCap) GetCapValue() uint64 {
	if m != nil && m.CapValue != nil {
		return *m.CapValue
	}
	return 0
}

func (m *OrderCap) GetCapType() uint32 {
	if m != nil && m.CapType != nil {
		return *m.CapType
	}
	return 0
}

func (m *OrderCap) GetUserType() uint32 {
	if m != nil && m.UserType != nil {
		return *m.UserType
	}
	return 0
}

func (m *OrderCap) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *OrderCap) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

func (m *OrderCap) GetCapOrderType() uint32 {
	if m != nil && m.CapOrderType != nil {
		return *m.CapOrderType
	}
	return 0
}

func init() {
	proto.RegisterEnum("commissionbass.Constant_ErrorCode", Constant_ErrorCode_name, Constant_ErrorCode_value)
	proto.RegisterEnum("commissionbass.Constant_AMSSellerStatus", Constant_AMSSellerStatus_name, Constant_AMSSellerStatus_value)
	proto.RegisterEnum("commissionbass.Constant_CampaignGroup", Constant_CampaignGroup_name, Constant_CampaignGroup_value)
	proto.RegisterEnum("commissionbass.Constant_UserTypeV2", Constant_UserTypeV2_name, Constant_UserTypeV2_value)
	proto.RegisterEnum("commissionbass.Constant_TrackingChannelSource", Constant_TrackingChannelSource_name, Constant_TrackingChannelSource_value)
	proto.RegisterEnum("commissionbass.Constant_OrderAttributionModel", Constant_OrderAttributionModel_name, Constant_OrderAttributionModel_value)
	proto.RegisterEnum("commissionbass.Constant_CommissionType", Constant_CommissionType_name, Constant_CommissionType_value)
	proto.RegisterEnum("commissionbass.Constant_AppType", Constant_AppType_name, Constant_AppType_value)
	proto.RegisterEnum("commissionbass.Constant_Tenant", Constant_Tenant_name, Constant_Tenant_value)
	proto.RegisterEnum("commissionbass.Constant_CapOrderType", Constant_CapOrderType_name, Constant_CapOrderType_value)
	proto.RegisterType((*Constant)(nil), "commissionbass.Constant")
	proto.RegisterType((*EchoRequest)(nil), "commissionbass.EchoRequest")
	proto.RegisterType((*EchoResponse)(nil), "commissionbass.EchoResponse")
	proto.RegisterType((*GetAMSSellerRequest)(nil), "commissionbass.GetAMSSellerRequest")
	proto.RegisterType((*GetAMSSellerResponse)(nil), "commissionbass.GetAMSSellerResponse")
	proto.RegisterType((*GetCampaignsNumberForSellerMissionRequest)(nil), "commissionbass.GetCampaignsNumberForSellerMissionRequest")
	proto.RegisterType((*GetCampaignsNumberForSellerMissionResponse)(nil), "commissionbass.GetCampaignsNumberForSellerMissionResponse")
	proto.RegisterType((*GetRecommendationRequest)(nil), "commissionbass.GetRecommendationRequest")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.GetRecommendationRequest.ExtraDataEntry")
	proto.RegisterType((*GetRecommendationResponse)(nil), "commissionbass.GetRecommendationResponse")
	proto.RegisterType((*RecommendationInfo)(nil), "commissionbass.RecommendationInfo")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.RecommendationInfo.DescriptionVariableEntry")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.RecommendationInfo.LabelVariableEntry")
	proto.RegisterType((*CheckSellerHasCreatedCampaignRequest)(nil), "commissionbass.CheckSellerHasCreatedCampaignRequest")
	proto.RegisterType((*CheckSellerHasCreatedCampaignResponse)(nil), "commissionbass.CheckSellerHasCreatedCampaignResponse")
	proto.RegisterType((*GetAccountTcRequest)(nil), "commissionbass.GetAccountTcRequest")
	proto.RegisterType((*GetAccountTcResponse)(nil), "commissionbass.GetAccountTcResponse")
	proto.RegisterType((*SetAccountTcRequest)(nil), "commissionbass.SetAccountTcRequest")
	proto.RegisterType((*SetAccountTcResponse)(nil), "commissionbass.SetAccountTcResponse")
	proto.RegisterType((*CheckAMSFeatureToggleRequest)(nil), "commissionbass.CheckAMSFeatureToggleRequest")
	proto.RegisterType((*CheckAMSFeatureToggleResponse)(nil), "commissionbass.CheckAMSFeatureToggleResponse")
	proto.RegisterType((*GetRcmdCommissionRateRequest)(nil), "commissionbass.GetRcmdCommissionRateRequest")
	proto.RegisterType((*GetRcmdCommissionRateResponse)(nil), "commissionbass.GetRcmdCommissionRateResponse")
	proto.RegisterType((*GetItemsOpenCampaignRequest)(nil), "commissionbass.GetItemsOpenCampaignRequest")
	proto.RegisterType((*GetItemsOpenCampaignResponse)(nil), "commissionbass.GetItemsOpenCampaignResponse")
	proto.RegisterType((*OpenCampaignAddedProduct)(nil), "commissionbass.OpenCampaignAddedProduct")
	proto.RegisterType((*SetOpenCampaignsRequest)(nil), "commissionbass.SetOpenCampaignsRequest")
	proto.RegisterType((*SetOpenCampaignItem)(nil), "commissionbass.SetOpenCampaignItem")
	proto.RegisterType((*SetOpenCampaignsResponse)(nil), "commissionbass.SetOpenCampaignsResponse")
	proto.RegisterType((*SetOpenCampaignResult)(nil), "commissionbass.SetOpenCampaignResult")
	proto.RegisterType((*CheckItemResult)(nil), "commissionbass.CheckItemResult")
	proto.RegisterType((*CheckItemIdListRequest)(nil), "commissionbass.CheckItemIdListRequest")
	proto.RegisterType((*CheckItemIdListResponse)(nil), "commissionbass.CheckItemIdListResponse")
	proto.RegisterType((*OpenCampaignSuggestionRequest)(nil), "commissionbass.OpenCampaignSuggestionRequest")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionRequest.DetailMapEntry")
	proto.RegisterType((*OpenCampaignSuggestionResponse)(nil), "commissionbass.OpenCampaignSuggestionResponse")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionResponse.ButtonLinkMappingEntry")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionResponse.DescriptionMappingEntry")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionResponse.DescriptionTransifyKeyMappingEntry")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionResponse.TitleMappingEntry")
	proto.RegisterMapType((map[string]string)(nil), "commissionbass.OpenCampaignSuggestionResponse.TitleTransifyKeyMappingEntry")
	proto.RegisterType((*GetCommissionPlansByAffiliateInfoRequest)(nil), "commissionbass.GetCommissionPlansByAffiliateInfoRequest")
	proto.RegisterType((*GetCommissionPlansByAffiliateInfoResponse)(nil), "commissionbass.GetCommissionPlansByAffiliateInfoResponse")
	proto.RegisterType((*CommissionPlanDetail)(nil), "commissionbass.CommissionPlanDetail")
	proto.RegisterType((*CommissionRateDetail)(nil), "commissionbass.CommissionRateDetail")
	proto.RegisterType((*OrderCap)(nil), "commissionbass.OrderCap")
}

func init() {
	proto.RegisterFile("commissionbass/commissionbass.proto", fileDescriptor_5c3460d3de3051d2)
}

var fileDescriptor_5c3460d3de3051d2 = []byte{
	// 3071 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x39, 0x4b, 0x73, 0xdb, 0xd6,
	0xd5, 0x26, 0xa9, 0x07, 0x75, 0x24, 0xca, 0xf0, 0xb5, 0x64, 0x53, 0xb4, 0xe4, 0xc8, 0xb0, 0x1d,
	0x3b, 0xb6, 0xa3, 0x2f, 0xd1, 0x7c, 0x79, 0x7e, 0x99, 0x2f, 0xa1, 0x28, 0x88, 0x66, 0xcd, 0x57,
	0x40, 0xca, 0x9e, 0x24, 0x9d, 0x62, 0xae, 0x80, 0x6b, 0x0a, 0x63, 0x10, 0x40, 0x00, 0x50, 0xb6,
	0x16, 0x5d, 0x74, 0xd5, 0x6e, 0x9a, 0x65, 0xa6, 0xd3, 0x4e, 0xa7, 0x8b, 0xae, 0xfa, 0x4e, 0xd3,
	0xd7, 0xb6, 0xdd, 0x75, 0x3a, 0xed, 0x4c, 0x76, 0x5d, 0xb6, 0x93, 0x45, 0x77, 0xfd, 0x05, 0xed,
	0xa2, 0x73, 0x1f, 0x20, 0x01, 0x10, 0x14, 0x25, 0x77, 0x87, 0x7b, 0xde, 0x8f, 0x7b, 0xcf, 0x3d,
	0xf7, 0x00, 0xae, 0xeb, 0x4e, 0xbf, 0x6f, 0xfa, 0xbe, 0xe9, 0xd8, 0x07, 0xd8, 0xf7, 0xff, 0x27,
	0xbe, 0xdc, 0x72, 0x3d, 0x27, 0x70, 0xd0, 0x72, 0x1c, 0x2a, 0x7f, 0x6f, 0x11, 0xf2, 0x15, 0xc7,
	0xf6, 0x03, 0x6c, 0x07, 0xf2, 0x5f, 0x67, 0x60, 0x41, 0xf1, 0x3c, 0xc7, 0xab, 0x38, 0x06, 0x41,
	0xab, 0x50, 0x50, 0x54, 0xb5, 0xa5, 0x6a, 0x9d, 0xfd, 0x4a, 0x45, 0xe9, 0x74, 0xa4, 0xdf, 0x7f,
	0xf2, 0x8f, 0x7f, 0xcd, 0xa2, 0xab, 0x70, 0x89, 0x83, 0x55, 0xa5, 0xd2, 0x52, 0x77, 0xb5, 0x66,
	0xab, 0xab, 0xed, 0xb5, 0xf6, 0x9b, 0xbb, 0xd2, 0x1f, 0x18, 0xfe, 0x26, 0x6c, 0x70, 0x7c, 0xad,
	0xf9, 0xb0, 0x5c, 0xaf, 0xed, 0x6a, 0x95, 0x56, 0xa3, 0x51, 0xeb, 0x74, 0x6a, 0xad, 0xa6, 0xa6,
	0x96, 0xbb, 0x8a, 0xf4, 0xa3, 0x4f, 0x29, 0xd9, 0x3a, 0xac, 0x70, 0xb2, 0x9d, 0x7a, 0xb9, 0xf2,
	0x40, 0xab, 0x94, 0xbb, 0x4a, 0xb5, 0xa5, 0x7e, 0x20, 0xfd, 0x98, 0x61, 0x8b, 0x20, 0x45, 0xb1,
	0xb5, 0xae, 0xd2, 0x90, 0x7e, 0x92, 0x86, 0xe9, 0xdc, 0x6f, 0xb5, 0xa5, 0x9f, 0x32, 0xcc, 0x15,
	0xb8, 0x18, 0x57, 0xdc, 0x2e, 0xab, 0xe5, 0x86, 0xf4, 0x33, 0x86, 0x2c, 0x01, 0x12, 0xce, 0x7c,
	0xd0, 0xe9, 0x2a, 0x0d, 0x8d, 0x2d, 0xa4, 0x9f, 0x33, 0xdc, 0x1a, 0x5c, 0xe0, 0x38, 0xea, 0x4a,
	0xb7, 0xc2, 0x65, 0xfe, 0x82, 0xa1, 0x36, 0x60, 0x35, 0xaa, 0xad, 0xdc, 0xed, 0xaa, 0xb5, 0x9d,
	0xfd, 0xae, 0x22, 0x7d, 0xc6, 0xd0, 0xf7, 0xe0, 0x06, 0x47, 0xdf, 0x2f, 0x77, 0x34, 0xae, 0xf4,
	0x7e, 0xad, 0x7a, 0x5f, 0x51, 0xb5, 0x56, 0x5b, 0x69, 0x6a, 0x95, 0x72, 0xa3, 0x5d, 0xae, 0x55,
	0x9b, 0xd2, 0x2f, 0x19, 0xf5, 0x5d, 0xb8, 0x3e, 0xa2, 0xee, 0x2a, 0x6a, 0xa3, 0xd6, 0x2c, 0x77,
	0x6b, 0xcd, 0x6a, 0x82, 0xf8, 0x73, 0x46, 0xfc, 0x12, 0x5c, 0xe3, 0xc4, 0x89, 0xf0, 0x69, 0xf5,
	0xd6, 0x23, 0x45, 0xd5, 0xea, 0xb5, 0x46, 0xad, 0x2b, 0xfd, 0x8a, 0x91, 0xde, 0x01, 0x39, 0x9d,
	0x54, 0xd8, 0xc2, 0x69, 0x7f, 0xcd, 0x68, 0x37, 0xa1, 0x38, 0xf2, 0xb5, 0xdc, 0xe8, 0x68, 0x8f,
	0xee, 0xd7, 0xba, 0x0a, 0x77, 0xf9, 0x37, 0x9f, 0xc6, 0xf2, 0xd7, 0x6c, 0x69, 0x6a, 0xa5, 0x31,
	0x9e, 0xbf, 0xdf, 0xc6, 0x5d, 0xef, 0x28, 0xf5, 0xba, 0x12, 0xd3, 0xdd, 0x6a, 0x6b, 0xb5, 0x8e,
	0xb6, 0xa7, 0xb6, 0x3e, 0x54, 0x9a, 0xd2, 0xef, 0x28, 0xb5, 0xdc, 0x80, 0xf3, 0xe5, 0x46, 0xa7,
	0x43, 0x2c, 0x8b, 0x78, 0x9d, 0x00, 0x07, 0x03, 0x1f, 0xad, 0x43, 0x91, 0xea, 0x17, 0xec, 0x9d,
	0x6e, 0xb9, 0xbb, 0xdf, 0xd1, 0x9a, 0x2d, 0xb5, 0x51, 0xae, 0x4b, 0x99, 0x74, 0xac, 0x10, 0x99,
	0x95, 0x9f, 0x42, 0xa1, 0x82, 0xfb, 0x2e, 0x36, 0x7b, 0x76, 0xd5, 0x73, 0x06, 0x2e, 0xda, 0x84,
	0xf5, 0x30, 0x7e, 0x5a, 0x55, 0x6d, 0xed, 0xb7, 0x99, 0x47, 0xa3, 0x98, 0x66, 0x90, 0x0c, 0x57,
	0x13, 0x14, 0xdd, 0xb2, 0x5a, 0x55, 0xba, 0x23, 0x9a, 0x6c, 0x8a, 0x94, 0x78, 0x66, 0x72, 0xf2,
	0x75, 0x80, 0x7d, 0x9f, 0x78, 0xdd, 0x63, 0x97, 0x3c, 0xdc, 0x46, 0xf3, 0x90, 0x6b, 0x92, 0xa7,
	0x52, 0x06, 0x2d, 0x41, 0x5e, 0x79, 0x66, 0xfa, 0x81, 0x69, 0xf7, 0xa4, 0xac, 0x8c, 0x61, 0xb5,
	0xeb, 0x61, 0xfd, 0x89, 0x69, 0xf7, 0x2a, 0x87, 0xd8, 0xb6, 0x89, 0xd5, 0x71, 0x06, 0x9e, 0x4e,
	0xd0, 0x25, 0x40, 0x43, 0x80, 0x6e, 0x62, 0xab, 0x41, 0x0c, 0x13, 0x4b, 0x99, 0x28, 0xfc, 0xd0,
	0x71, 0x09, 0x79, 0x68, 0x1a, 0xc4, 0x91, 0xb2, 0xa8, 0x08, 0x2b, 0x02, 0x5e, 0x37, 0x8f, 0x48,
	0x27, 0xf0, 0x08, 0xee, 0x53, 0x15, 0x39, 0xf9, 0x10, 0x56, 0x5b, 0x9e, 0x41, 0xbc, 0x72, 0x10,
	0x78, 0xe6, 0xc1, 0x20, 0x30, 0x1d, 0xbb, 0xe1, 0x18, 0xc4, 0x42, 0x1b, 0xb0, 0x46, 0x61, 0x6c,
	0x51, 0xb3, 0x0d, 0xd3, 0x23, 0x7a, 0x50, 0x39, 0x24, 0xfa, 0x13, 0x67, 0x10, 0x48, 0x19, 0x74,
	0x19, 0x2e, 0x0e, 0xd1, 0xbb, 0x0c, 0x49, 0x35, 0x4a, 0xd9, 0x14, 0x44, 0x2d, 0x20, 0x7d, 0x29,
	0x27, 0xff, 0x33, 0x03, 0xcb, 0x95, 0x61, 0xcd, 0xa0, 0x8e, 0xa3, 0x2b, 0x70, 0x39, 0x92, 0xed,
	0xee, 0x07, 0x6d, 0x45, 0xdb, 0x6f, 0x3e, 0x68, 0xb6, 0x1e, 0x35, 0xa5, 0x73, 0x34, 0x71, 0x49,
	0xe4, 0xf0, 0x6c, 0x67, 0x98, 0x47, 0x09, 0x2c, 0x3b, 0xdb, 0xd9, 0x34, 0x0c, 0xdb, 0x94, 0x39,
	0xf4, 0x0a, 0xdc, 0x4b, 0x62, 0xda, 0x6a, 0x6b, 0x77, 0xbf, 0xd2, 0xd5, 0xea, 0xe5, 0x1d, 0xa5,
	0xae, 0xb5, 0x34, 0xba, 0xfd, 0xea, 0x75, 0xa5, 0xd2, 0xad, 0xb5, 0x9a, 0xd2, 0x4c, 0x9a, 0x0d,
	0x6c, 0x3b, 0x74, 0xcb, 0x55, 0x69, 0x36, 0x0d, 0x4b, 0x6d, 0x60, 0xd8, 0x39, 0xf9, 0x2a, 0xcc,
	0x97, 0x5d, 0x97, 0xf9, 0x09, 0x30, 0xc7, 0xf3, 0x21, 0x65, 0x58, 0xaa, 0x9d, 0xa7, 0x52, 0x56,
	0xbe, 0x03, 0x73, 0x5d, 0x62, 0x63, 0x3b, 0x40, 0x12, 0x2c, 0xf1, 0xaf, 0x21, 0xd1, 0x32, 0x00,
	0x87, 0xec, 0x39, 0x8e, 0x21, 0x65, 0xe5, 0x77, 0x61, 0xa9, 0x82, 0x5d, 0x96, 0x28, 0x26, 0x90,
	0xfa, 0x18, 0x59, 0x47, 0xf2, 0xb2, 0x0a, 0x17, 0xa2, 0x18, 0xf6, 0x21, 0x65, 0xe5, 0x6b, 0xb0,
	0xa8, 0xe8, 0x87, 0x8e, 0x4a, 0x3e, 0x1e, 0x10, 0x3f, 0x40, 0x08, 0x66, 0x0c, 0x1c, 0xe0, 0x62,
	0x76, 0x33, 0x73, 0x7b, 0x41, 0x65, 0xdf, 0xb2, 0x0c, 0x4b, 0x9c, 0xc4, 0x77, 0x1d, 0xdb, 0x27,
	0xa9, 0x34, 0x7b, 0x70, 0xb1, 0x4a, 0x82, 0xe1, 0x01, 0x0c, 0xc5, 0x5d, 0x86, 0x79, 0xff, 0xd0,
	0x71, 0x35, 0xd3, 0x28, 0x66, 0x36, 0x33, 0xb7, 0x73, 0xea, 0x1c, 0x5d, 0xd6, 0x0c, 0x74, 0x09,
	0xe6, 0x3c, 0xd2, 0x33, 0x1d, 0x5b, 0x48, 0x11, 0x2b, 0xf9, 0x10, 0x56, 0xe2, 0x72, 0x84, 0xce,
	0x93, 0x04, 0xf9, 0xec, 0xb4, 0x33, 0x41, 0xb3, 0xaa, 0x58, 0xa1, 0x17, 0x60, 0x51, 0xf7, 0x08,
	0x0e, 0x88, 0x16, 0x98, 0x7d, 0x52, 0xcc, 0x31, 0x26, 0xe0, 0xa0, 0xae, 0xd9, 0x27, 0xf2, 0x9f,
	0xb3, 0xf0, 0x52, 0x95, 0x04, 0xe1, 0x21, 0xf7, 0x9b, 0x83, 0xfe, 0x01, 0xf1, 0xf6, 0x1c, 0x8f,
	0x6b, 0x6e, 0xf0, 0xdd, 0xf8, 0xbc, 0x8e, 0xa0, 0x9b, 0xb0, 0xac, 0x0b, 0xd1, 0x5a, 0x8f, 0x16,
	0x10, 0x66, 0xc2, 0xac, 0x5a, 0xd0, 0x63, 0x55, 0xe5, 0x15, 0x58, 0xb1, 0x08, 0xf6, 0x03, 0xcd,
	0xf5, 0x9c, 0xbe, 0x43, 0x0f, 0x99, 0x66, 0xe0, 0x63, 0xbf, 0x38, 0xc3, 0x88, 0x11, 0xc3, 0xb5,
	0x43, 0xd4, 0x2e, 0x3e, 0xf6, 0xd1, 0x36, 0xac, 0x72, 0x8e, 0xd1, 0x35, 0xab, 0x79, 0x38, 0x20,
	0xc5, 0x59, 0xc6, 0x72, 0x91, 0x21, 0x47, 0xc7, 0x49, 0xc5, 0x01, 0x41, 0xb7, 0xe0, 0xbc, 0x47,
	0x3e, 0x1e, 0x98, 0x1e, 0x31, 0x34, 0x9b, 0xf9, 0x59, 0x9c, 0x63, 0x5e, 0x2c, 0x87, 0x60, 0xee,
	0x3d, 0xda, 0x00, 0xf0, 0x03, 0xec, 0x05, 0x3c, 0x68, 0xf3, 0x8c, 0x66, 0x81, 0x41, 0x68, 0xcc,
	0xd0, 0x1a, 0xe4, 0x89, 0x6d, 0x70, 0x64, 0x9e, 0x21, 0xe7, 0x89, 0x6d, 0xb0, 0x70, 0xee, 0xc2,
	0x9d, 0xd3, 0x44, 0x53, 0xa4, 0xf3, 0x12, 0xcc, 0x09, 0x3b, 0x44, 0x34, 0xf9, 0x4a, 0xfe, 0x77,
	0x06, 0x8a, 0x55, 0x12, 0xa8, 0x84, 0x3a, 0x47, 0x6c, 0x03, 0x07, 0x91, 0x1c, 0xd0, 0x54, 0x1f,
	0x3a, 0x6e, 0x3c, 0x05, 0xe6, 0xe4, 0x14, 0x94, 0x20, 0x6f, 0x61, 0xbb, 0x37, 0xc0, 0x3d, 0x9e,
	0xff, 0x05, 0x75, 0xb8, 0x46, 0x0f, 0x01, 0xc8, 0xb3, 0xc0, 0xc3, 0x1a, 0xdb, 0xc9, 0xb3, 0x9b,
	0xb9, 0xdb, 0x8b, 0xdb, 0x6f, 0x6c, 0x25, 0xfa, 0x99, 0x49, 0x96, 0x6c, 0x29, 0x94, 0x75, 0x17,
	0x07, 0x58, 0xb1, 0x03, 0xef, 0x58, 0x5d, 0x20, 0xe1, 0xba, 0xf4, 0x0e, 0x2c, 0xc7, 0x91, 0x48,
	0x82, 0xdc, 0x13, 0x72, 0xcc, 0x4c, 0x5e, 0x50, 0xe9, 0x27, 0x5a, 0x81, 0xd9, 0x23, 0x6c, 0x0d,
	0x88, 0x30, 0x97, 0x2f, 0xde, 0xce, 0xbe, 0x99, 0x91, 0x8f, 0x61, 0x2d, 0x45, 0xe7, 0xe8, 0xd8,
	0xe9, 0x8e, 0x41, 0x98, 0xa4, 0x59, 0x95, 0x7d, 0x53, 0xe1, 0x7d, 0xbf, 0x27, 0x04, 0xd1, 0x4f,
	0xf4, 0xba, 0x38, 0x9c, 0xd4, 0xe1, 0xc5, 0x6d, 0x39, 0xe9, 0x52, 0x5c, 0x76, 0xcd, 0x7e, 0xec,
	0x88, 0x03, 0xfc, 0xf9, 0x0c, 0xa0, 0x71, 0x24, 0xba, 0x02, 0x0b, 0x81, 0x19, 0x58, 0x44, 0x1b,
	0xf9, 0x90, 0x67, 0x80, 0x07, 0xe4, 0x98, 0x6e, 0x2b, 0x83, 0xf8, 0xba, 0x67, 0xba, 0x6c, 0xe3,
	0x52, 0x12, 0x6e, 0xc9, 0x72, 0x04, 0x4c, 0x09, 0x6d, 0x58, 0x89, 0x12, 0x1e, 0x61, 0xcf, 0xc4,
	0x07, 0x16, 0xcd, 0x0a, 0x8d, 0xfb, 0xff, 0x4d, 0x37, 0x72, 0x6b, 0x77, 0xc4, 0xfe, 0x50, 0x70,
	0xf3, 0xd8, 0x5f, 0x34, 0xc6, 0x31, 0xd4, 0x6a, 0x0b, 0x1f, 0x10, 0x8b, 0x99, 0x34, 0x13, 0xa6,
	0xfe, 0x80, 0x58, 0xd4, 0x98, 0xaf, 0xc2, 0x32, 0x47, 0x0e, 0xcd, 0xe0, 0xe9, 0x7f, 0xed, 0x14,
	0x66, 0xd4, 0x29, 0x63, 0xdc, 0x80, 0x82, 0x15, 0x85, 0xd1, 0x13, 0x74, 0x30, 0x08, 0x02, 0x11,
	0x8e, 0x39, 0xa6, 0x7b, 0x81, 0x43, 0x44, 0xc8, 0x3c, 0xc2, 0xef, 0x4c, 0x1a, 0x89, 0x81, 0x67,
	0xb1, 0x53, 0xb6, 0x40, 0x4f, 0xe2, 0x10, 0xbc, 0xef, 0x59, 0xf4, 0xa8, 0x99, 0xba, 0xa0, 0xc8,
	0x33, 0x8a, 0x79, 0xba, 0xde, 0xf7, 0xac, 0xd2, 0x1e, 0x14, 0x27, 0x85, 0xe3, 0x2c, 0xbb, 0xad,
	0xf4, 0x1e, 0xa0, 0x71, 0x7f, 0xce, 0xb4, 0x5f, 0xff, 0x1f, 0x6e, 0xb0, 0x1b, 0x86, 0x1f, 0xf2,
	0xfb, 0xd8, 0xaf, 0xb0, 0xfa, 0x6a, 0x84, 0x65, 0x20, 0x71, 0x72, 0x6b, 0x89, 0xe2, 0x29, 0x3f,
	0x82, 0x9b, 0x53, 0xf8, 0xc5, 0xde, 0xdf, 0x02, 0x74, 0x38, 0x86, 0x65, 0xc2, 0xf2, 0x6a, 0x0a,
	0x46, 0xde, 0xe2, 0xd7, 0x91, 0xae, 0x3b, 0x03, 0x3b, 0xe8, 0xea, 0xd3, 0xaa, 0xb8, 0xac, 0xf0,
	0x6b, 0x67, 0x44, 0x2f, 0xf4, 0xae, 0xc2, 0x9c, 0xe9, 0x6b, 0x36, 0x79, 0x2a, 0x74, 0xcd, 0x9a,
	0x7e, 0x93, 0x3c, 0x9d, 0x74, 0xe9, 0xd0, 0x5b, 0xb0, 0x73, 0x06, 0xb5, 0x4c, 0x0e, 0xeb, 0xdb,
	0xc2, 0xca, 0xc5, 0x57, 0xf2, 0x16, 0xac, 0x74, 0xd2, 0xcc, 0x61, 0x95, 0xce, 0x1f, 0x58, 0x81,
	0x30, 0x47, 0xac, 0xe4, 0x37, 0x60, 0x9d, 0xc5, 0xb1, 0xdc, 0xe8, 0xec, 0x11, 0x1c, 0x0c, 0x3c,
	0xd2, 0x75, 0x7a, 0x3d, 0x8b, 0x4c, 0xf5, 0xfb, 0x0d, 0xd8, 0x98, 0xc0, 0x38, 0x45, 0x63, 0x1b,
	0xd6, 0x69, 0xa5, 0xd2, 0xfb, 0x46, 0xfc, 0xaa, 0x89, 0x68, 0x34, 0x03, 0xd2, 0x8f, 0x68, 0xa4,
	0xcb, 0x9a, 0x11, 0x35, 0x25, 0x1b, 0x33, 0xe5, 0x9b, 0x59, 0xd8, 0x98, 0x20, 0x52, 0xd8, 0xf2,
	0x26, 0xac, 0x59, 0x47, 0xaf, 0x6a, 0x3d, 0xcb, 0x39, 0xc0, 0x96, 0x76, 0x40, 0x34, 0x1d, 0x07,
	0xa4, 0xe7, 0x78, 0xc7, 0x23, 0x2d, 0xab, 0xd6, 0xd1, 0xab, 0x55, 0x86, 0xdf, 0x21, 0x15, 0x81,
	0xad, 0x19, 0x9c, 0x73, 0x7b, 0x02, 0x67, 0x36, 0xe4, 0xdc, 0x4e, 0xe1, 0x5c, 0x83, 0x7c, 0xdf,
	0x14, 0x17, 0x2c, 0xbf, 0xc0, 0xe7, 0xfb, 0x26, 0xbf, 0x54, 0x29, 0x0a, 0x3f, 0xe3, 0xa8, 0x19,
	0x81, 0xc2, 0xcf, 0x18, 0xea, 0x1a, 0x2c, 0xb9, 0x1e, 0x79, 0x6c, 0x5a, 0x56, 0xf4, 0x6a, 0x5e,
	0x14, 0xb0, 0x90, 0xfb, 0xb1, 0xe7, 0xf4, 0x35, 0xeb, 0xe8, 0x19, 0xab, 0x12, 0xb3, 0xea, 0x3c,
	0x5d, 0xd7, 0x8f, 0x9e, 0xc9, 0xef, 0xc3, 0x95, 0x2a, 0x61, 0xcd, 0xb1, 0xdf, 0x72, 0x89, 0x9d,
	0x3c, 0x4c, 0xb4, 0x32, 0xf0, 0xd0, 0xfa, 0xc5, 0xcc, 0x66, 0x8e, 0x5e, 0xc2, 0x3c, 0xb6, 0xfe,
	0xe4, 0xe0, 0x12, 0x96, 0xae, 0x14, 0x91, 0x22, 0xb4, 0x0a, 0x2c, 0x30, 0x99, 0x96, 0xe9, 0x07,
	0x4c, 0xe8, 0xe2, 0xf6, 0xed, 0x64, 0x39, 0x8c, 0x32, 0x96, 0x0d, 0x83, 0x18, 0x6d, 0xcf, 0x31,
	0x06, 0x7a, 0xa0, 0x32, 0x73, 0xea, 0xa6, 0x1f, 0xc8, 0x3f, 0xc8, 0x42, 0x71, 0x12, 0xd9, 0xe4,
	0x2d, 0x71, 0x45, 0x28, 0xb7, 0x71, 0x3f, 0x3c, 0x08, 0x4c, 0x64, 0x13, 0xf7, 0x09, 0xba, 0x0e,
	0x85, 0x48, 0xa3, 0x63, 0x1a, 0xa2, 0x93, 0x5b, 0x1a, 0x01, 0x6b, 0x06, 0xba, 0x0b, 0x17, 0x22,
	0x44, 0xe2, 0x68, 0xf2, 0x9c, 0x48, 0x23, 0x84, 0x78, 0x15, 0xde, 0x82, 0xf3, 0xe9, 0xad, 0x53,
	0x64, 0x70, 0xc1, 0x52, 0x74, 0x07, 0x2e, 0xb8, 0xc4, 0x33, 0x1d, 0x43, 0x8b, 0xf4, 0x44, 0xbc,
	0x6f, 0x3a, 0xcf, 0x11, 0x9d, 0x61, 0x67, 0xf4, 0x22, 0x08, 0x90, 0x36, 0x6c, 0x90, 0x78, 0xf7,
	0x54, 0xe0, 0x60, 0x45, 0xb4, 0x49, 0x7f, 0xca, 0xc0, 0xe5, 0x0e, 0x09, 0xa2, 0x41, 0xf2, 0xc3,
	0xc4, 0xbe, 0x05, 0xb3, 0xd4, 0x6d, 0x5f, 0x24, 0xe0, 0x7a, 0x32, 0x01, 0x09, 0x3e, 0x9a, 0x4d,
	0x95, 0x73, 0x4c, 0x4c, 0x7c, 0x9a, 0xb3, 0xb9, 0x54, 0x67, 0x4b, 0x90, 0x77, 0x5c, 0xe2, 0xe1,
	0xc0, 0xf1, 0xc2, 0x1b, 0x33, 0x5c, 0x47, 0xca, 0xd4, 0x6c, 0xac, 0x4c, 0x3d, 0x60, 0xe5, 0x2e,
	0x69, 0xd3, 0xf3, 0x25, 0x5a, 0xfe, 0x46, 0x06, 0x8a, 0xe3, 0x91, 0x11, 0xfb, 0xf3, 0x06, 0x2c,
	0x9b, 0xbe, 0x86, 0x2d, 0x4b, 0xf3, 0x07, 0xba, 0x4e, 0x7c, 0x5f, 0x94, 0xa3, 0x25, 0xd3, 0x2f,
	0x5b, 0x56, 0x87, 0xc3, 0xd0, 0xbb, 0x30, 0xcf, 0xcb, 0x13, 0xad, 0xcb, 0x34, 0x84, 0x37, 0xa7,
	0x84, 0x50, 0x65, 0xd4, 0x6a, 0xc8, 0x25, 0x3f, 0x80, 0xd5, 0x54, 0x8a, 0xc9, 0x2e, 0xd1, 0x8e,
	0xd8, 0xf3, 0x34, 0xd6, 0x98, 0xf1, 0xbb, 0x60, 0x9e, 0x78, 0x6c, 0xb8, 0x25, 0x7f, 0x0d, 0xce,
	0xb3, 0xda, 0xca, 0xf2, 0x34, 0x45, 0x0c, 0x45, 0xf8, 0x9a, 0x8b, 0x7d, 0x7e, 0xa3, 0xe4, 0xd5,
	0x39, 0xd3, 0x6f, 0x63, 0xdf, 0x8f, 0xc9, 0xcf, 0xc5, 0xe5, 0xd7, 0xe1, 0xd2, 0x50, 0x7e, 0xcd,
	0xa0, 0xe7, 0x6f, 0xea, 0x7d, 0x13, 0x2d, 0x1d, 0xd9, 0x58, 0xe9, 0x90, 0x9f, 0xc0, 0xe5, 0x31,
	0x69, 0x22, 0xf8, 0xaf, 0xc5, 0xf7, 0xe5, 0x0b, 0xc9, 0xa0, 0x26, 0xbc, 0x0c, 0xf7, 0xe4, 0x09,
	0xa1, 0xf9, 0x76, 0x16, 0x36, 0xa2, 0x51, 0xee, 0x0c, 0x7a, 0x3d, 0xe2, 0x47, 0x7b, 0xfd, 0x17,
	0x60, 0xb1, 0x4f, 0x3c, 0xfd, 0x10, 0xdb, 0xc1, 0xc8, 0x0d, 0x08, 0x41, 0x3c, 0x62, 0x03, 0x9f,
	0x78, 0x91, 0x1d, 0x4f, 0x97, 0xf1, 0x0b, 0x26, 0x17, 0x73, 0xfe, 0x23, 0x00, 0x83, 0x04, 0xd8,
	0xb4, 0xb4, 0x3e, 0x76, 0x8b, 0x33, 0xcc, 0x97, 0x77, 0x4e, 0x2a, 0x72, 0x63, 0x56, 0x6d, 0xed,
	0x32, 0xfe, 0x06, 0x76, 0x45, 0xdf, 0x6f, 0x84, 0x6b, 0xda, 0xf7, 0xc7, 0x91, 0x67, 0xea, 0xa3,
	0x3e, 0xcb, 0xc3, 0xd5, 0x49, 0x9a, 0x45, 0x12, 0xae, 0x43, 0xe1, 0xd0, 0xa4, 0x8f, 0x3e, 0xdb,
	0x30, 0x29, 0x22, 0x3c, 0x00, 0x87, 0x66, 0x50, 0x09, 0x61, 0x88, 0x40, 0x81, 0x77, 0xeb, 0x7d,
	0xec, 0xba, 0xa6, 0xdd, 0x13, 0xc7, 0xe0, 0xbd, 0xd3, 0x7a, 0xc9, 0x75, 0x6d, 0x75, 0xa9, 0x8c,
	0x06, 0x17, 0xc1, 0x3d, 0x5d, 0x0a, 0x22, 0x20, 0xf4, 0x14, 0xa2, 0x5d, 0xf7, 0x50, 0x19, 0xef,
	0xe6, 0xf7, 0xce, 0xa8, 0x2c, 0xd2, 0xca, 0xc6, 0x54, 0x22, 0x63, 0x0c, 0x81, 0xbe, 0x95, 0x81,
	0x12, 0x77, 0x30, 0xf0, 0xb0, 0xed, 0x9b, 0x8f, 0x8f, 0x69, 0x97, 0x3d, 0x34, 0x80, 0xe7, 0xf4,
	0xc1, 0xf3, 0x78, 0xdb, 0x15, 0xf2, 0x1e, 0x90, 0xe3, 0x98, 0x15, 0x97, 0x83, 0x74, 0x2c, 0xfa,
	0x6e, 0x06, 0x36, 0xa3, 0x41, 0x48, 0x35, 0x88, 0x3f, 0x2c, 0xde, 0x7f, 0xfe, 0x88, 0x4c, 0x32,
	0x6b, 0xc3, 0x38, 0x89, 0x06, 0x0d, 0xe0, 0xa2, 0x78, 0x84, 0x58, 0xa6, 0xfd, 0x64, 0x68, 0xce,
	0x1c, 0x33, 0x47, 0x39, 0xa3, 0x39, 0x3b, 0x4c, 0x52, 0xdd, 0xb4, 0x9f, 0xc4, 0x4c, 0xb8, 0x70,
	0x90, 0x84, 0x97, 0xde, 0x85, 0x0b, 0x63, 0x5b, 0xe7, 0x4c, 0x2f, 0x12, 0x05, 0x2e, 0x4f, 0xd8,
	0x0e, 0x67, 0x12, 0xf3, 0x15, 0x58, 0x3f, 0x29, 0xa9, 0x67, 0x92, 0xd5, 0x06, 0x79, 0x7a, 0x3e,
	0xce, 0x24, 0x71, 0x17, 0x2e, 0xa5, 0x87, 0xf4, 0x4c, 0x25, 0xe3, 0x6f, 0x19, 0xb8, 0x5d, 0x25,
	0x91, 0x41, 0x4f, 0xdb, 0xc2, 0xb6, 0xbf, 0x73, 0x5c, 0x7e, 0xfc, 0xd8, 0xb4, 0x4c, 0x1c, 0x10,
	0xf6, 0xb6, 0x1f, 0xbd, 0xbf, 0x02, 0x36, 0x35, 0x64, 0xb2, 0x0b, 0xaa, 0x58, 0xd1, 0x3e, 0x15,
	0x87, 0xf4, 0x61, 0x25, 0x9d, 0x51, 0x17, 0x87, 0xb0, 0x9a, 0x81, 0x5e, 0x87, 0xa2, 0x4d, 0x88,
	0xa1, 0x39, 0x9e, 0x41, 0x3c, 0x4d, 0xc7, 0x2e, 0x7d, 0x0d, 0x69, 0x07, 0x83, 0x63, 0xe2, 0xb1,
	0xfa, 0x9a, 0x57, 0x57, 0x28, 0x9e, 0x8d, 0x13, 0x2b, 0xd8, 0x6d, 0x92, 0xa7, 0x3b, 0x14, 0x87,
	0xde, 0x83, 0x8d, 0x04, 0x1f, 0x11, 0xe3, 0x6b, 0xc1, 0x3c, 0xc3, 0x98, 0xd7, 0xa2, 0xcc, 0xe1,
	0x80, 0x9b, 0x49, 0x90, 0xbf, 0x23, 0x06, 0x74, 0x53, 0x3c, 0x14, 0xf5, 0xb1, 0x05, 0x91, 0x4e,
	0x4f, 0x73, 0x29, 0xa9, 0xb8, 0xaf, 0x6e, 0x8c, 0xdd, 0x57, 0x31, 0x89, 0xbc, 0x6c, 0xab, 0x91,
	0x36, 0x89, 0xe9, 0x41, 0x55, 0xb8, 0x98, 0xe6, 0x73, 0x96, 0xcd, 0x55, 0x8a, 0x63, 0x67, 0x48,
	0xb8, 0xa0, 0x4a, 0x4e, 0x32, 0x12, 0xef, 0x43, 0x71, 0x62, 0x10, 0x72, 0x53, 0xa4, 0xad, 0x3a,
	0xa9, 0xa1, 0xf9, 0x61, 0x16, 0x56, 0xd2, 0xbc, 0x18, 0xef, 0x96, 0x33, 0x2c, 0xa3, 0xf1, 0x6e,
	0x39, 0xde, 0x13, 0x06, 0xc7, 0x2e, 0xdf, 0x5e, 0x85, 0x68, 0x4f, 0xc8, 0x86, 0xc9, 0xa9, 0x0d,
	0x70, 0x8e, 0x49, 0x3c, 0x4d, 0x03, 0x3c, 0xc3, 0x28, 0xe3, 0x0d, 0x70, 0x22, 0x4f, 0xb4, 0x21,
	0xf5, 0x45, 0x99, 0x3c, 0x21, 0x4f, 0xb4, 0x43, 0x1d, 0xcf, 0x13, 0x85, 0xfa, 0xa8, 0x08, 0xf3,
	0x47, 0xc4, 0xa3, 0x6b, 0xd6, 0x9b, 0x17, 0xd4, 0x70, 0x29, 0x7f, 0x91, 0x89, 0x46, 0x69, 0x24,
	0x83, 0xcd, 0x66, 0xf9, 0x4f, 0x0f, 0x4d, 0xf4, 0xb5, 0xfc, 0x58, 0x14, 0xf4, 0xd8, 0xbf, 0x94,
	0xbb, 0x70, 0x01, 0x8f, 0x7e, 0x7e, 0x68, 0x7d, 0xc7, 0x20, 0x96, 0x88, 0x94, 0x84, 0x93, 0x7f,
	0x45, 0x36, 0x61, 0x89, 0xf5, 0x23, 0x34, 0x9c, 0xda, 0xd1, 0x36, 0x0b, 0x53, 0x41, 0x85, 0xc1,
	0xe8, 0x57, 0xce, 0x1a, 0xe4, 0xb1, 0xeb, 0xf2, 0x78, 0xcf, 0x70, 0x4b, 0xb1, 0xf8, 0x0d, 0x30,
	0xe1, 0x49, 0x52, 0x48, 0x76, 0xe9, 0xf2, 0x5f, 0x32, 0x90, 0x0f, 0x37, 0x07, 0x5a, 0x85, 0x39,
	0xba, 0xa5, 0x86, 0x59, 0x9e, 0xd5, 0xb1, 0xcb, 0xbb, 0x6c, 0x0a, 0x1e, 0xd5, 0x8d, 0x19, 0x35,
	0xaf, 0x63, 0xf7, 0x21, 0x5d, 0x53, 0x23, 0x28, 0x92, 0x19, 0xc1, 0x4d, 0x9c, 0xd7, 0xb1, 0x2b,
	0xfe, 0xb9, 0x2c, 0x0c, 0x3d, 0x10, 0x06, 0xe6, 0x43, 0xf3, 0xa9, 0xae, 0x00, 0xf7, 0xa8, 0xae,
	0x59, 0xae, 0x2b, 0xc0, 0xbd, 0x9a, 0x31, 0x39, 0xf8, 0xb4, 0x63, 0xa7, 0x8a, 0xf8, 0xce, 0x67,
	0x22, 0xe7, 0x19, 0xc1, 0x92, 0x1e, 0xf9, 0x09, 0xb1, 0xfd, 0x79, 0x01, 0x12, 0xbf, 0x8b, 0x51,
	0x05, 0x66, 0x14, 0xfd, 0xd0, 0x41, 0x57, 0x92, 0xdb, 0x21, 0xf2, 0x9b, 0xa2, 0xb4, 0x9e, 0x8e,
	0xe4, 0xb5, 0x40, 0x3e, 0x87, 0xee, 0xc3, 0x02, 0x85, 0xb0, 0x3f, 0xcd, 0xff, 0x9d, 0xa4, 0x8f,
	0x60, 0xa9, 0x4a, 0x82, 0x72, 0xdf, 0xe7, 0x33, 0x2a, 0x74, 0x3d, 0x65, 0x48, 0x9c, 0xfc, 0xed,
	0x51, 0xba, 0x71, 0x32, 0xd1, 0x50, 0xf8, 0xf7, 0x33, 0x20, 0x4f, 0x9f, 0x9a, 0xa3, 0xb7, 0x52,
	0xc4, 0x9d, 0xee, 0xbf, 0x45, 0xe9, 0xed, 0xe7, 0x61, 0x1d, 0xda, 0xf7, 0x75, 0xb8, 0xc1, 0x9d,
	0x4f, 0x8e, 0x42, 0x39, 0x43, 0xdb, 0xc2, 0xc1, 0x63, 0xc7, 0xeb, 0xa3, 0xdb, 0xa7, 0x9d, 0x9c,
	0x97, 0x5e, 0x3a, 0x05, 0xe5, 0x50, 0xfd, 0x27, 0x19, 0x31, 0x9e, 0x9a, 0x34, 0x1f, 0x44, 0xff,
	0x9b, 0xfa, 0x16, 0x99, 0x32, 0x8e, 0x2c, 0xbd, 0x76, 0x46, 0xae, 0xe4, 0x66, 0x08, 0xe7, 0x72,
	0xe9, 0x9b, 0x21, 0x31, 0xfd, 0x4b, 0xdf, 0x0c, 0xc9, 0xd1, 0x1e, 0x17, 0xde, 0x39, 0x51, 0x78,
	0xe7, 0x34, 0xc2, 0x3b, 0xe9, 0xc2, 0x8f, 0x60, 0x95, 0x0f, 0xfa, 0xfa, 0x7e, 0x6c, 0xd0, 0x87,
	0xee, 0xa5, 0xc6, 0x62, 0xc2, 0x20, 0xb1, 0xf4, 0xf2, 0x29, 0xa9, 0x87, 0x7a, 0x8d, 0xc8, 0x23,
	0x98, 0x3f, 0x2b, 0xd1, 0x8b, 0x13, 0xdf, 0x8f, 0xb1, 0x57, 0x6c, 0xe9, 0xd6, 0x54, 0xba, 0xa8,
	0x77, 0xa9, 0xa3, 0xc3, 0x71, 0xef, 0x4e, 0x1a, 0x5a, 0x8e, 0x7b, 0x77, 0xe2, 0x3c, 0x52, 0x3e,
	0x87, 0x7c, 0x36, 0x36, 0x1e, 0x1b, 0xab, 0xa1, 0xbb, 0x29, 0x82, 0x26, 0xcd, 0xf3, 0x4a, 0xf7,
	0x4e, 0x47, 0x3c, 0x54, 0xda, 0x03, 0x29, 0x39, 0x27, 0x41, 0xb7, 0xa6, 0x0c, 0x3a, 0xc2, 0x19,
	0x53, 0xe9, 0xf6, 0x74, 0xc2, 0xa1, 0xa2, 0x63, 0xb8, 0x94, 0xfe, 0x34, 0x40, 0x2f, 0x9f, 0xe9,
	0xd9, 0x5c, 0xda, 0x3a, 0xdb, 0x8b, 0x43, 0x3e, 0x47, 0x5f, 0x57, 0xd7, 0xa6, 0xf6, 0x7e, 0xe8,
	0xcd, 0xb4, 0xe2, 0x76, 0x9a, 0x86, 0xb8, 0xf4, 0xd6, 0x73, 0x70, 0x86, 0xc6, 0xed, 0xac, 0xff,
	0xf1, 0xcb, 0xab, 0x99, 0x2f, 0xbe, 0xbc, 0x9a, 0xf9, 0xfb, 0x97, 0x57, 0x33, 0x1f, 0x26, 0xee,
	0xaf, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0x70, 0x14, 0x5c, 0x0f, 0x24, 0x25, 0x00, 0x00,
}

func (m *Constant) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Constant) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Constant) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *EchoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EchoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EchoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Data != nil {
		i -= len(*m.Data)
		copy(dAtA[i:], *m.Data)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Data)))
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}

func (m *EchoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *EchoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *EchoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Data != nil {
		i -= len(*m.Data)
		copy(dAtA[i:], *m.Data)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Data)))
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}

func (m *GetAMSSellerRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAMSSellerRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAMSSellerRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Region != nil {
		i -= len(*m.Region)
		copy(dAtA[i:], *m.Region)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Region)))
		i--
		dAtA[i] = 0x12
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAMSSellerResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAMSSellerResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAMSSellerResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.CreateTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CreateTime))
		i--
		dAtA[i] = 0x18
	}
	if m.Status != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Status))
		i--
		dAtA[i] = 0x10
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetCampaignsNumberForSellerMissionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCampaignsNumberForSellerMissionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetCampaignsNumberForSellerMissionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.EndTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.EndTime))
		i--
		dAtA[i] = 0x40
	}
	if m.StartTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.StartTime))
		i--
		dAtA[i] = 0x38
	}
	if m.RequiredNumber != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.RequiredNumber))
		i--
		dAtA[i] = 0x30
	}
	if m.LeastCommissionRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.LeastCommissionRate))
		i--
		dAtA[i] = 0x28
	}
	if m.LeastPromotionDays != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.LeastPromotionDays))
		i--
		dAtA[i] = 0x20
	}
	if m.CampaignGroup != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CampaignGroup))
		i--
		dAtA[i] = 0x18
	}
	if m.Region != nil {
		i -= len(*m.Region)
		copy(dAtA[i:], *m.Region)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Region)))
		i--
		dAtA[i] = 0x12
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetCampaignsNumberForSellerMissionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCampaignsNumberForSellerMissionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetCampaignsNumberForSellerMissionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Number != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Number))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRecommendationRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendationRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRecommendationRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ExtraData) > 0 {
		for k := range m.ExtraData {
			v := m.ExtraData[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.Language != nil {
		i -= len(*m.Language)
		copy(dAtA[i:], *m.Language)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Language)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Region != nil {
		i -= len(*m.Region)
		copy(dAtA[i:], *m.Region)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Region)))
		i--
		dAtA[i] = 0x12
	}
	if m.Shopid != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Shopid))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRecommendationResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendationResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRecommendationResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Data != nil {
		{
			size, err := m.Data.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCommissionbass(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.Msg != nil {
		i -= len(*m.Msg)
		copy(dAtA[i:], *m.Msg)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Msg)))
		i--
		dAtA[i] = 0x12
	}
	if m.Code != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Code))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *RecommendationInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecommendationInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RecommendationInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.IconUrl != nil {
		i -= len(*m.IconUrl)
		copy(dAtA[i:], *m.IconUrl)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.IconUrl)))
		i--
		dAtA[i] = 0x42
	}
	if m.RedirectionUrl != nil {
		i -= len(*m.RedirectionUrl)
		copy(dAtA[i:], *m.RedirectionUrl)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.RedirectionUrl)))
		i--
		dAtA[i] = 0x3a
	}
	if m.ButtonKey != nil {
		i -= len(*m.ButtonKey)
		copy(dAtA[i:], *m.ButtonKey)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.ButtonKey)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.LabelVariable) > 0 {
		for k := range m.LabelVariable {
			v := m.LabelVariable[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.LabelKey != nil {
		i -= len(*m.LabelKey)
		copy(dAtA[i:], *m.LabelKey)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.LabelKey)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.DescriptionVariable) > 0 {
		for k := range m.DescriptionVariable {
			v := m.DescriptionVariable[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1a
		}
	}
	if m.DescriptionKey != nil {
		i -= len(*m.DescriptionKey)
		copy(dAtA[i:], *m.DescriptionKey)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.DescriptionKey)))
		i--
		dAtA[i] = 0x12
	}
	if m.TitleKey != nil {
		i -= len(*m.TitleKey)
		copy(dAtA[i:], *m.TitleKey)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.TitleKey)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CheckSellerHasCreatedCampaignRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckSellerHasCreatedCampaignRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckSellerHasCreatedCampaignRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckSellerHasCreatedCampaignResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckSellerHasCreatedCampaignResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckSellerHasCreatedCampaignResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.HasCreatedCampaign != nil {
		i--
		if *m.HasCreatedCampaign {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAccountTcRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAccountTcRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAccountTcRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAccountTcResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAccountTcResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAccountTcResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Status != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Status))
		i--
		dAtA[i] = 0x10
	}
	if m.IsNew != nil {
		i--
		if *m.IsNew {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetAccountTcRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetAccountTcRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetAccountTcRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i -= len(*m.Source)
		copy(dAtA[i:], *m.Source)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Source)))
		i--
		dAtA[i] = 0x12
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetAccountTcResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetAccountTcResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetAccountTcResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Result != nil {
		i--
		if *m.Result {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckAMSFeatureToggleRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckAMSFeatureToggleRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckAMSFeatureToggleRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckAMSFeatureToggleResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckAMSFeatureToggleResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckAMSFeatureToggleResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Result != nil {
		i--
		if *m.Result {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRcmdCommissionRateRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRcmdCommissionRateRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRcmdCommissionRateRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x10
	}
	if m.ItemId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetRcmdCommissionRateResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRcmdCommissionRateResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetRcmdCommissionRateResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.FromLvx != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.FromLvx))
		i--
		dAtA[i] = 0x30
	}
	if m.PrefillRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.PrefillRate))
		i--
		dAtA[i] = 0x28
	}
	if m.MaxRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.MaxRate))
		i--
		dAtA[i] = 0x20
	}
	if m.MinRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.MinRate))
		i--
		dAtA[i] = 0x18
	}
	if m.Lv2GlobalBeCategoryId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Lv2GlobalBeCategoryId))
		i--
		dAtA[i] = 0x10
	}
	if m.Lv1GlobalBeCategoryId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Lv1GlobalBeCategoryId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetItemsOpenCampaignRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemsOpenCampaignRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetItemsOpenCampaignRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.ItemIds) > 0 {
		for iNdEx := len(m.ItemIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintCommissionbass(dAtA, i, uint64(m.ItemIds[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetItemsOpenCampaignResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetItemsOpenCampaignResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetItemsOpenCampaignResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ItemList) > 0 {
		for iNdEx := len(m.ItemList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.ItemList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *OpenCampaignAddedProduct) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenCampaignAddedProduct) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenCampaignAddedProduct) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PeriodEndTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.PeriodEndTime))
		i--
		dAtA[i] = 0x38
	}
	if m.PeriodStartTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.PeriodStartTime))
		i--
		dAtA[i] = 0x30
	}
	if m.CommissionRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionRate))
		i--
		dAtA[i] = 0x28
	}
	if m.CommissionStatus != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionStatus))
		i--
		dAtA[i] = 0x20
	}
	if m.CommissionId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionId))
		i--
		dAtA[i] = 0x18
	}
	if m.ItemName != nil {
		i -= len(*m.ItemName)
		copy(dAtA[i:], *m.ItemName)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.ItemName)))
		i--
		dAtA[i] = 0x12
	}
	if m.ItemId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetOpenCampaignsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetOpenCampaignsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetOpenCampaignsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i -= len(*m.Source)
		copy(dAtA[i:], *m.Source)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Source)))
		i--
		dAtA[i] = 0x2a
	}
	if m.Operator != nil {
		i -= len(*m.Operator)
		copy(dAtA[i:], *m.Operator)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.Operator)))
		i--
		dAtA[i] = 0x22
	}
	if m.CommissionRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionRate))
		i--
		dAtA[i] = 0x18
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *SetOpenCampaignItem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetOpenCampaignItem) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetOpenCampaignItem) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ItemName != nil {
		i -= len(*m.ItemName)
		copy(dAtA[i:], *m.ItemName)
		i = encodeVarintCommissionbass(dAtA, i, uint64(len(*m.ItemName)))
		i--
		dAtA[i] = 0x12
	}
	if m.ItemId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetOpenCampaignsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetOpenCampaignsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetOpenCampaignsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Results) > 0 {
		for iNdEx := len(m.Results) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Results[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.IsAllSuccess != nil {
		i--
		if *m.IsAllSuccess {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SetOpenCampaignResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetOpenCampaignResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SetOpenCampaignResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ErrCode != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ErrCode))
		i--
		dAtA[i] = 0x10
	}
	if m.ItemId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckItemResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckItemResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckItemResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ErrCode != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ErrCode))
		i--
		dAtA[i] = 0x18
	}
	if m.IsPass != nil {
		i--
		if *m.IsPass {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x10
	}
	if m.ItemId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ItemId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckItemIdListRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckItemIdListRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckItemIdListRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ItemIds) > 0 {
		for iNdEx := len(m.ItemIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintCommissionbass(dAtA, i, uint64(m.ItemIds[iNdEx]))
			i--
			dAtA[i] = 0x10
		}
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CheckItemIdListResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckItemIdListResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CheckItemIdListResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ErrCode != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ErrCode))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Items) > 0 {
		for iNdEx := len(m.Items) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Items[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *OpenCampaignSuggestionRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenCampaignSuggestionRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenCampaignSuggestionRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.DetailMap) > 0 {
		for k := range m.DetailMap {
			v := m.DetailMap[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x22
		}
	}
	if m.ShopId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ShopId))
		i--
		dAtA[i] = 0x18
	}
	if m.UserId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.UserId))
		i--
		dAtA[i] = 0x10
	}
	if m.MerchantId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.MerchantId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OpenCampaignSuggestionResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenCampaignSuggestionResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OpenCampaignSuggestionResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ButtonLinkMapping) > 0 {
		for k := range m.ButtonLinkMapping {
			v := m.ButtonLinkMapping[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.DescriptionTransifyKeyMapping) > 0 {
		for k := range m.DescriptionTransifyKeyMapping {
			v := m.DescriptionTransifyKeyMapping[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x2a
		}
	}
	if len(m.TitleTransifyKeyMapping) > 0 {
		for k := range m.TitleTransifyKeyMapping {
			v := m.TitleTransifyKeyMapping[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.DescriptionMapping) > 0 {
		for k := range m.DescriptionMapping {
			v := m.DescriptionMapping[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x1a
		}
	}
	if len(m.TitleMapping) > 0 {
		for k := range m.TitleMapping {
			v := m.TitleMapping[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintCommissionbass(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintCommissionbass(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0x12
		}
	}
	if m.HitCondition != nil {
		i--
		if *m.HitCondition {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetCommissionPlansByAffiliateInfoRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionPlansByAffiliateInfoRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetCommissionPlansByAffiliateInfoRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.NeedOrderCapExistingBuyer != nil {
		i--
		if *m.NeedOrderCapExistingBuyer {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x20
	}
	if m.NeedOrderCapNewBuyer != nil {
		i--
		if *m.NeedOrderCapNewBuyer {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.AffiliateId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.AffiliateId))
		i--
		dAtA[i] = 0x10
	}
	if m.Tenant != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Tenant))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetCommissionPlansByAffiliateInfoResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionPlansByAffiliateInfoResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetCommissionPlansByAffiliateInfoResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.OrderCapExistingBuyer != nil {
		{
			size, err := m.OrderCapExistingBuyer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCommissionbass(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	if m.OrderCapNewBuyer != nil {
		{
			size, err := m.OrderCapNewBuyer.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintCommissionbass(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	if len(m.CommissionPlans) > 0 {
		for iNdEx := len(m.CommissionPlans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CommissionPlans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CommissionPlanDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommissionPlanDetail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommissionPlanDetail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Version != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Version))
		i--
		dAtA[i] = 0x30
	}
	if len(m.CommissionRates) > 0 {
		for iNdEx := len(m.CommissionRates) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.CommissionRates[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintCommissionbass(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.PeriodEndTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.PeriodEndTime))
		i--
		dAtA[i] = 0x20
	}
	if m.PeriodStartTime != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.PeriodStartTime))
		i--
		dAtA[i] = 0x18
	}
	if m.CommissionType != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionType))
		i--
		dAtA[i] = 0x10
	}
	if m.CommissionId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CommissionRateDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommissionRateDetail) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CommissionRateDetail) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.CommissionRate != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CommissionRate))
		i--
		dAtA[i] = 0x28
	}
	if m.AppType != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.AppType))
		i--
		dAtA[i] = 0x20
	}
	if m.UserTypeV2 != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.UserTypeV2))
		i--
		dAtA[i] = 0x18
	}
	if m.AttributionModel != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.AttributionModel))
		i--
		dAtA[i] = 0x10
	}
	if m.ChannelSource != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.ChannelSource))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *OrderCap) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OrderCap) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OrderCap) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.CapOrderType != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CapOrderType))
		i--
		dAtA[i] = 0x38
	}
	if m.Version != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.Version))
		i--
		dAtA[i] = 0x30
	}
	if m.TagId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x28
	}
	if m.UserType != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.UserType))
		i--
		dAtA[i] = 0x20
	}
	if m.CapType != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CapType))
		i--
		dAtA[i] = 0x18
	}
	if m.CapValue != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CapValue))
		i--
		dAtA[i] = 0x10
	}
	if m.CapId != nil {
		i = encodeVarintCommissionbass(dAtA, i, uint64(*m.CapId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func encodeVarintCommissionbass(dAtA []byte, offset int, v uint64) int {
	offset -= sovCommissionbass(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Constant) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EchoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Data != nil {
		l = len(*m.Data)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *EchoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Data != nil {
		l = len(*m.Data)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAMSSellerRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.Region != nil {
		l = len(*m.Region)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAMSSellerResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.Status != nil {
		n += 1 + sovCommissionbass(uint64(*m.Status))
	}
	if m.CreateTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.CreateTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetCampaignsNumberForSellerMissionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.Region != nil {
		l = len(*m.Region)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.CampaignGroup != nil {
		n += 1 + sovCommissionbass(uint64(*m.CampaignGroup))
	}
	if m.LeastPromotionDays != nil {
		n += 1 + sovCommissionbass(uint64(*m.LeastPromotionDays))
	}
	if m.LeastCommissionRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.LeastCommissionRate))
	}
	if m.RequiredNumber != nil {
		n += 1 + sovCommissionbass(uint64(*m.RequiredNumber))
	}
	if m.StartTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.StartTime))
	}
	if m.EndTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.EndTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetCampaignsNumberForSellerMissionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Number != nil {
		n += 1 + sovCommissionbass(uint64(*m.Number))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRecommendationRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Shopid != nil {
		n += 1 + sovCommissionbass(uint64(*m.Shopid))
	}
	if m.Region != nil {
		l = len(*m.Region)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.Language != nil {
		l = len(*m.Language)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if len(m.ExtraData) > 0 {
		for k, v := range m.ExtraData {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRecommendationResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Code != nil {
		n += 1 + sovCommissionbass(uint64(*m.Code))
	}
	if m.Msg != nil {
		l = len(*m.Msg)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RecommendationInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TitleKey != nil {
		l = len(*m.TitleKey)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.DescriptionKey != nil {
		l = len(*m.DescriptionKey)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if len(m.DescriptionVariable) > 0 {
		for k, v := range m.DescriptionVariable {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if m.LabelKey != nil {
		l = len(*m.LabelKey)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if len(m.LabelVariable) > 0 {
		for k, v := range m.LabelVariable {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if m.ButtonKey != nil {
		l = len(*m.ButtonKey)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.RedirectionUrl != nil {
		l = len(*m.RedirectionUrl)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.IconUrl != nil {
		l = len(*m.IconUrl)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckSellerHasCreatedCampaignRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckSellerHasCreatedCampaignResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HasCreatedCampaign != nil {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAccountTcRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAccountTcResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IsNew != nil {
		n += 2
	}
	if m.Status != nil {
		n += 1 + sovCommissionbass(uint64(*m.Status))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetAccountTcRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetAccountTcResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != nil {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckAMSFeatureToggleRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckAMSFeatureToggleResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Result != nil {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRcmdCommissionRateRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ItemId))
	}
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetRcmdCommissionRateResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Lv1GlobalBeCategoryId != nil {
		n += 1 + sovCommissionbass(uint64(*m.Lv1GlobalBeCategoryId))
	}
	if m.Lv2GlobalBeCategoryId != nil {
		n += 1 + sovCommissionbass(uint64(*m.Lv2GlobalBeCategoryId))
	}
	if m.MinRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.MinRate))
	}
	if m.MaxRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.MaxRate))
	}
	if m.PrefillRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.PrefillRate))
	}
	if m.FromLvx != nil {
		n += 1 + sovCommissionbass(uint64(*m.FromLvx))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetItemsOpenCampaignRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ItemIds) > 0 {
		for _, e := range m.ItemIds {
			n += 1 + sovCommissionbass(uint64(e))
		}
	}
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetItemsOpenCampaignResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.ItemList) > 0 {
		for _, e := range m.ItemList {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OpenCampaignAddedProduct) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ItemId))
	}
	if m.ItemName != nil {
		l = len(*m.ItemName)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.CommissionId != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionId))
	}
	if m.CommissionStatus != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionStatus))
	}
	if m.CommissionRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionRate))
	}
	if m.PeriodStartTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.PeriodStartTime))
	}
	if m.PeriodEndTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.PeriodEndTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetOpenCampaignsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if m.CommissionRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionRate))
	}
	if m.Operator != nil {
		l = len(*m.Operator)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetOpenCampaignItem) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ItemId))
	}
	if m.ItemName != nil {
		l = len(*m.ItemName)
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetOpenCampaignsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IsAllSuccess != nil {
		n += 2
	}
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SetOpenCampaignResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ItemId))
	}
	if m.ErrCode != nil {
		n += 1 + sovCommissionbass(uint64(*m.ErrCode))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckItemResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ItemId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ItemId))
	}
	if m.IsPass != nil {
		n += 2
	}
	if m.ErrCode != nil {
		n += 1 + sovCommissionbass(uint64(*m.ErrCode))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckItemIdListRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if len(m.ItemIds) > 0 {
		for _, e := range m.ItemIds {
			n += 1 + sovCommissionbass(uint64(e))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CheckItemIdListResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Items) > 0 {
		for _, e := range m.Items {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.ErrCode != nil {
		n += 1 + sovCommissionbass(uint64(*m.ErrCode))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OpenCampaignSuggestionRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.MerchantId != nil {
		n += 1 + sovCommissionbass(uint64(*m.MerchantId))
	}
	if m.UserId != nil {
		n += 1 + sovCommissionbass(uint64(*m.UserId))
	}
	if m.ShopId != nil {
		n += 1 + sovCommissionbass(uint64(*m.ShopId))
	}
	if len(m.DetailMap) > 0 {
		for k, v := range m.DetailMap {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OpenCampaignSuggestionResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.HitCondition != nil {
		n += 2
	}
	if len(m.TitleMapping) > 0 {
		for k, v := range m.TitleMapping {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if len(m.DescriptionMapping) > 0 {
		for k, v := range m.DescriptionMapping {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if len(m.TitleTransifyKeyMapping) > 0 {
		for k, v := range m.TitleTransifyKeyMapping {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if len(m.DescriptionTransifyKeyMapping) > 0 {
		for k, v := range m.DescriptionTransifyKeyMapping {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if len(m.ButtonLinkMapping) > 0 {
		for k, v := range m.ButtonLinkMapping {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovCommissionbass(uint64(len(k))) + 1 + len(v) + sovCommissionbass(uint64(len(v)))
			n += mapEntrySize + 1 + sovCommissionbass(uint64(mapEntrySize))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetCommissionPlansByAffiliateInfoRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tenant != nil {
		n += 1 + sovCommissionbass(uint64(*m.Tenant))
	}
	if m.AffiliateId != nil {
		n += 1 + sovCommissionbass(uint64(*m.AffiliateId))
	}
	if m.NeedOrderCapNewBuyer != nil {
		n += 2
	}
	if m.NeedOrderCapExistingBuyer != nil {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetCommissionPlansByAffiliateInfoResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.CommissionPlans) > 0 {
		for _, e := range m.CommissionPlans {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.OrderCapNewBuyer != nil {
		l = m.OrderCapNewBuyer.Size()
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.OrderCapExistingBuyer != nil {
		l = m.OrderCapExistingBuyer.Size()
		n += 1 + l + sovCommissionbass(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CommissionPlanDetail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CommissionId != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionId))
	}
	if m.CommissionType != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionType))
	}
	if m.PeriodStartTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.PeriodStartTime))
	}
	if m.PeriodEndTime != nil {
		n += 1 + sovCommissionbass(uint64(*m.PeriodEndTime))
	}
	if len(m.CommissionRates) > 0 {
		for _, e := range m.CommissionRates {
			l = e.Size()
			n += 1 + l + sovCommissionbass(uint64(l))
		}
	}
	if m.Version != nil {
		n += 1 + sovCommissionbass(uint64(*m.Version))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CommissionRateDetail) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ChannelSource != nil {
		n += 1 + sovCommissionbass(uint64(*m.ChannelSource))
	}
	if m.AttributionModel != nil {
		n += 1 + sovCommissionbass(uint64(*m.AttributionModel))
	}
	if m.UserTypeV2 != nil {
		n += 1 + sovCommissionbass(uint64(*m.UserTypeV2))
	}
	if m.AppType != nil {
		n += 1 + sovCommissionbass(uint64(*m.AppType))
	}
	if m.CommissionRate != nil {
		n += 1 + sovCommissionbass(uint64(*m.CommissionRate))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OrderCap) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CapId != nil {
		n += 1 + sovCommissionbass(uint64(*m.CapId))
	}
	if m.CapValue != nil {
		n += 1 + sovCommissionbass(uint64(*m.CapValue))
	}
	if m.CapType != nil {
		n += 1 + sovCommissionbass(uint64(*m.CapType))
	}
	if m.UserType != nil {
		n += 1 + sovCommissionbass(uint64(*m.UserType))
	}
	if m.TagId != nil {
		n += 1 + sovCommissionbass(uint64(*m.TagId))
	}
	if m.Version != nil {
		n += 1 + sovCommissionbass(uint64(*m.Version))
	}
	if m.CapOrderType != nil {
		n += 1 + sovCommissionbass(uint64(*m.CapOrderType))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovCommissionbass(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozCommissionbass(x uint64) (n int) {
	return sovCommissionbass(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Constant) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Constant: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Constant: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EchoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EchoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EchoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Data = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *EchoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: EchoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: EchoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Data = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAMSSellerRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAMSSellerRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAMSSellerRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Region = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAMSSellerResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAMSSellerResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAMSSellerResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CreateTime = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCampaignsNumberForSellerMissionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetCampaignsNumberForSellerMissionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetCampaignsNumberForSellerMissionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Region = &s
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CampaignGroup", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CampaignGroup = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LeastPromotionDays", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LeastPromotionDays = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LeastCommissionRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LeastCommissionRate = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field RequiredNumber", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RequiredNumber = &v
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StartTime = &v
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.EndTime = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCampaignsNumberForSellerMissionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetCampaignsNumberForSellerMissionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetCampaignsNumberForSellerMissionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Number", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Number = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendationRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRecommendationRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRecommendationRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Shopid", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Shopid = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Region = &s
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Language", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Language = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ExtraData", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ExtraData == nil {
				m.ExtraData = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.ExtraData[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendationResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRecommendationResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRecommendationResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Code = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Msg = &s
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &RecommendationInfo{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecommendationInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RecommendationInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RecommendationInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TitleKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.TitleKey = &s
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DescriptionKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.DescriptionKey = &s
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DescriptionVariable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DescriptionVariable == nil {
				m.DescriptionVariable = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.DescriptionVariable[mapkey] = mapvalue
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.LabelKey = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LabelVariable", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.LabelVariable == nil {
				m.LabelVariable = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.LabelVariable[mapkey] = mapvalue
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ButtonKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ButtonKey = &s
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RedirectionUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.RedirectionUrl = &s
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.IconUrl = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckSellerHasCreatedCampaignRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckSellerHasCreatedCampaignRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckSellerHasCreatedCampaignRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckSellerHasCreatedCampaignResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckSellerHasCreatedCampaignResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckSellerHasCreatedCampaignResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasCreatedCampaign", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.HasCreatedCampaign = &b
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAccountTcRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAccountTcRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAccountTcRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAccountTcResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAccountTcResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAccountTcResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsNew", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.IsNew = &b
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetAccountTcRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetAccountTcRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetAccountTcRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetAccountTcResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetAccountTcResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetAccountTcResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Result = &b
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckAMSFeatureToggleRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckAMSFeatureToggleRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckAMSFeatureToggleRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckAMSFeatureToggleResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckAMSFeatureToggleResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckAMSFeatureToggleResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Result = &b
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRcmdCommissionRateRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRcmdCommissionRateRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRcmdCommissionRateRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ItemId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRcmdCommissionRateResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetRcmdCommissionRateResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetRcmdCommissionRateResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lv1GlobalBeCategoryId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Lv1GlobalBeCategoryId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lv2GlobalBeCategoryId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Lv2GlobalBeCategoryId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MinRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MinRate = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MaxRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MaxRate = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PrefillRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PrefillRate = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FromLvx", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FromLvx = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemsOpenCampaignRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetItemsOpenCampaignRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetItemsOpenCampaignRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemIds = append(m.ItemIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCommissionbass
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthCommissionbass
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ItemIds) == 0 {
					m.ItemIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemIds = append(m.ItemIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemIds", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetItemsOpenCampaignResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetItemsOpenCampaignResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetItemsOpenCampaignResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ItemList = append(m.ItemList, &OpenCampaignAddedProduct{})
			if err := m.ItemList[len(m.ItemList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenCampaignAddedProduct) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenCampaignAddedProduct: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenCampaignAddedProduct: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ItemId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ItemName = &s
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionId = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionStatus", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionStatus = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionRate = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeriodStartTime", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PeriodStartTime = &v
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeriodEndTime", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PeriodEndTime = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetOpenCampaignsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetOpenCampaignsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetOpenCampaignsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &SetOpenCampaignItem{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionRate", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionRate = &v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Operator = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetOpenCampaignItem) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetOpenCampaignItem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetOpenCampaignItem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ItemId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ItemName = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetOpenCampaignsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetOpenCampaignsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetOpenCampaignsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsAllSuccess", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.IsAllSuccess = &b
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &SetOpenCampaignResult{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetOpenCampaignResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SetOpenCampaignResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SetOpenCampaignResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ItemId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrCode", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ErrCode = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckItemResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckItemResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckItemResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ItemId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.IsPass = &b
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrCode", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ErrCode = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckItemIdListRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckItemIdListRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckItemIdListRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ItemIds = append(m.ItemIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCommissionbass
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthCommissionbass
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.ItemIds) == 0 {
					m.ItemIds = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ItemIds = append(m.ItemIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field ItemIds", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckItemIdListResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CheckItemIdListResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CheckItemIdListResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Items", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Items = append(m.Items, &CheckItemResult{})
			if err := m.Items[len(m.Items)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrCode", wireType)
			}
			var v int32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ErrCode = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenCampaignSuggestionRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenCampaignSuggestionRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenCampaignSuggestionRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field MerchantId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.MerchantId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UserId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShopId", wireType)
			}
			var v int64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShopId = &v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DetailMap", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DetailMap == nil {
				m.DetailMap = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.DetailMap[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenCampaignSuggestionResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OpenCampaignSuggestionResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OpenCampaignSuggestionResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HitCondition", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.HitCondition = &b
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TitleMapping", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TitleMapping == nil {
				m.TitleMapping = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.TitleMapping[mapkey] = mapvalue
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DescriptionMapping", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DescriptionMapping == nil {
				m.DescriptionMapping = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.DescriptionMapping[mapkey] = mapvalue
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TitleTransifyKeyMapping", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TitleTransifyKeyMapping == nil {
				m.TitleTransifyKeyMapping = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.TitleTransifyKeyMapping[mapkey] = mapvalue
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DescriptionTransifyKeyMapping", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DescriptionTransifyKeyMapping == nil {
				m.DescriptionTransifyKeyMapping = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.DescriptionTransifyKeyMapping[mapkey] = mapvalue
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ButtonLinkMapping", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ButtonLinkMapping == nil {
				m.ButtonLinkMapping = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCommissionbass
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCommissionbass
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipCommissionbass(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthCommissionbass
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.ButtonLinkMapping[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionPlansByAffiliateInfoRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetCommissionPlansByAffiliateInfoRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetCommissionPlansByAffiliateInfoRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tenant", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Tenant = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AffiliateId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AffiliateId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NeedOrderCapNewBuyer", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.NeedOrderCapNewBuyer = &b
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field NeedOrderCapExistingBuyer", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.NeedOrderCapExistingBuyer = &b
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionPlansByAffiliateInfoResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetCommissionPlansByAffiliateInfoResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetCommissionPlansByAffiliateInfoResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionPlans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommissionPlans = append(m.CommissionPlans, &CommissionPlanDetail{})
			if err := m.CommissionPlans[len(m.CommissionPlans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrderCapNewBuyer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OrderCapNewBuyer == nil {
				m.OrderCapNewBuyer = &OrderCap{}
			}
			if err := m.OrderCapNewBuyer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OrderCapExistingBuyer", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OrderCapExistingBuyer == nil {
				m.OrderCapExistingBuyer = &OrderCap{}
			}
			if err := m.OrderCapExistingBuyer.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommissionPlanDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommissionPlanDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommissionPlanDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionType = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeriodStartTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PeriodStartTime = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PeriodEndTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PeriodEndTime = &v
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionRates", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCommissionbass
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.CommissionRates = append(m.CommissionRates, &CommissionRateDetail{})
			if err := m.CommissionRates[len(m.CommissionRates)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Version = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommissionRateDetail) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CommissionRateDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CommissionRateDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ChannelSource", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ChannelSource = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AttributionModel", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AttributionModel = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserTypeV2", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UserTypeV2 = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AppType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AppType = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CommissionRate", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CommissionRate = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OrderCap) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OrderCap: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OrderCap: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CapId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CapId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CapValue", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CapValue = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CapType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CapType = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UserType = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Version = &v
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CapOrderType", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CapOrderType = &v
		default:
			iNdEx = preIndex
			skippy, err := skipCommissionbass(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthCommissionbass
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipCommissionbass(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCommissionbass
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCommissionbass
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthCommissionbass
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupCommissionbass
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthCommissionbass
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthCommissionbass        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCommissionbass          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupCommissionbass = fmt.Errorf("proto: unexpected end of group")
)
