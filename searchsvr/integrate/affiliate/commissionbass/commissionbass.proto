syntax = "proto2";
package commissionbass;

// Code generated by spcli. DO NOT EDIT.
//
// namespace affiliateplatform.commissionbass
//
// commands {
//   affiliateplatform.commissionbass.check_ams_feature_toggle
//   affiliateplatform.commissionbass.check_item_id_list
//   affiliateplatform.commissionbass.check_seller_has_created_campaign
//   affiliateplatform.commissionbass.echo
//   affiliateplatform.commissionbass.echo_error
//   affiliateplatform.commissionbass.get_account_tc
//   affiliateplatform.commissionbass.get_ams_recommendation_in_seller_platform
//   affiliateplatform.commissionbass.get_ams_seller
//   affiliateplatform.commissionbass.get_campaigns_number_for_seller_mission
//   affiliateplatform.commissionbass.get_commission_plans_by_affiliate_info
//   affiliateplatform.commissionbass.get_items_open_campaign
//   affiliateplatform.commissionbass.get_rcmd_commission_rate
//   affiliateplatform.commissionbass.open_campaign_suggestion
//   affiliateplatform.commissionbass.set_account_tc
//   affiliateplatform.commissionbass.set_open_campaigns
// }
option go_package = "commissionbass";

//import "spex/protobuf/service.proto";

message Constant {
	enum ErrorCode {
		ERROR_SUCCESS = **********;
		ERROR_RECORD_NOT_FOUND = **********;

		ERROR_INVALID_COMMISSION_RATE = **********;
		ERROR_BLACK_CATEGORY = **********;
		ERROR_BLACK_ITEM = **********;
		ERROR_BLACK_SHOP = **********;
		ERROR_INVALID_PARAM = **********;
		ERROR_SYSTEM_ERROR = **********;
		ERROR_NOT_TC_SHOP = **********;
		ERROR_BLACK_ATTRIBUTE = **********;
		ERROR_HAS_VALID_HIGHER_OPEN_CAMPAIGN =**********;
		ERROR_HAS_TERMINATING_OPEN_CAMPAIGN =**********;
		ERROR_COMMISSION_RATE_LOWER_LIMIT =**********;
		ERROR_COMMISSION_RATE_HIGHER_LIMIT =**********;
		ERROR_NOT_AMS_WHITE_SHOP = **********;
		ERROR_NO_RCMD_COMMISSION_RATE = **********;
		ERROR_SELLER_COMMISSION_OP_IS_FROZEN = **********;
	}

	enum AMSSellerStatus {
		AMS_SELLER_STATUS_NORMAL = 1;
		AMS_SELLER_STATUS_FROZEN = 2;
	}

	enum CampaignGroup {
		CAMPAIGN_GROUP_SHOP_CAMPAIGN = 1;
		CAMPAIGN_GROUP_TARGET_CAMPAIGN = 2;
		CAMPAIGN_GROUP_OPEN_CAMPAIGN = 3;
	}

  enum UserTypeV2 {
    New = 1;
    Existing = 2;
  }

  enum TrackingChannelSource{
    ChannelSocialMedia    = 1;
    ChannelShopeeVideo    = 2;
    ChannelLiveStreaming  = 3;
  }


  enum OrderAttributionModel{
    AttrModelIndirectCheckout  = 1;
    AttrModelDirectShop        = 2;
    AttrModelDirectItem        = 3;
  }

  enum CommissionType {
    COMMISSION_TYPE_UNKNOWN = 0;
    COMMISSION_TYPE_CATEGORY = 1;
    COMMISSION_TYPE_ITEM = 2;
    COMMISSION_TYPE_SHOP = 3;
    COMMISSION_TYPE_PRODUCT_LABEL_O_R_COLLECTION = 4;
    COMMISSION_TYPE_SHOP_TAG = 5;
    COMMISSION_TYPE_ITEM_TAG = 6;
  }

  // TODO(xiangqian.li) feature/span-49892
  enum AppType {
    Shopee = 1;
    Now = 2;
  }

  // TODO(xiangqian.li) feature/span-49892
  enum Tenant {
    TenantShopee = 1;
    TenantFood = 2;
  }

  enum CapOrderType{
    CapOrderTypeCheckout = 1;
    CapOrderTypeOrder = 2;
  }
}

service commissionbass {
    /*option (service.service) = {
        servicename: "affiliateplatform.commissionbass"
    };*/
	rpc Echo (EchoRequest) returns (EchoResponse) {}
	rpc EchoError (EchoRequest) returns (EchoResponse) {}
	// get_ams_seller get AMS account of seller
	rpc GetAmsSeller (GetAMSSellerRequest) returns (GetAMSSellerResponse) {}
	// get_campaigns_number_for_seller_mission get number of campaigns which match requirement of seller mission
	rpc GetCampaignsNumberForSellerMission (GetCampaignsNumberForSellerMissionRequest) returns (GetCampaignsNumberForSellerMissionResponse) {}
	rpc GetAmsRecommendationInSellerPlatform (GetRecommendationRequest) returns (GetRecommendationResponse) {}
	rpc CheckSellerHasCreatedCampaign (CheckSellerHasCreatedCampaignRequest) returns (CheckSellerHasCreatedCampaignResponse) {}

	//api doc:https://confluence.shopee.io/display/MKT/%5BTD%5DTo+Support+setup+AMS+commission+in+Seller+Center
	rpc GetAccountTc (GetAccountTcRequest) returns (GetAccountTcResponse) {}
	rpc SetAccountTc (SetAccountTcRequest) returns (SetAccountTcResponse) {}
	rpc CheckAmsFeatureToggle(CheckAMSFeatureToggleRequest) returns (CheckAMSFeatureToggleResponse) {}
	rpc CheckItemIdList(CheckItemIdListRequest) returns (CheckItemIdListResponse) {}
	rpc GetRcmdCommissionRate(GetRcmdCommissionRateRequest) returns (GetRcmdCommissionRateResponse){}
	rpc GetItemsOpenCampaign(GetItemsOpenCampaignRequest) returns (GetItemsOpenCampaignResponse) {}
	rpc SetOpenCampaigns(SetOpenCampaignsRequest) returns (SetOpenCampaignsResponse) {}
	rpc OpenCampaignSuggestion(OpenCampaignSuggestionRequest) returns (OpenCampaignSuggestionResponse) {}

    // 根据shopee user_id查询满足条件的所有佣金计划
    // 请求中需要指定tenant
    rpc GetCommissionPlansByAffiliateInfo(GetCommissionPlansByAffiliateInfoRequest) returns (GetCommissionPlansByAffiliateInfoResponse) {}
}

message EchoRequest {
	optional string data = 2;
}

message EchoResponse {
	optional string data = 2;
}

message GetAMSSellerRequest {
	optional int64 shop_id = 1;  // required
	optional string region = 2; // optional
}

message GetAMSSellerResponse {
	optional int64 shop_id = 1;
	optional int32 status = 2;  // Constant.AMSSellerStatus
	optional int64 create_time = 3; // the time when seller create AMS account
}

message GetCampaignsNumberForSellerMissionRequest {
	optional int64 shop_id = 1; // required
	optional string region = 2; // required
	optional int32 campaign_group = 3; // optional, Constant.CampaignGroup
	optional int32 least_promotion_days = 4; // optional, how long a commission promotes between start_time and end_time at least
	optional int32 least_commission_rate = 5; // optional, raw value * 1000, e.g. if ratio = 1%, then commission_rate = 1 * 1000 = 1000
	optional int64 required_number = 6; //  required, will return directly once reach the required number
	optional int64 start_time = 7; // required
	optional int64 end_time = 8; // required
}

message GetCampaignsNumberForSellerMissionResponse {
	optional int64 number = 1; // response.number <= request.required_number
}

message GetRecommendationRequest {
	optional int64 shopid = 1;      // the current shop
	optional string region = 2;    // the region of the shop
	optional string language = 3;   // the current language of shopee app
	map<string, string> extra_data = 5;  // if there is any extra data, we can put it into this map, for future use
}

message GetRecommendationResponse {
	optional int32 code = 1;        // 0: success, others: fail
	optional string msg = 2;        // the reason of failure
	optional RecommendationInfo data = 3;
}

message RecommendationInfo {
	optional string title_key = 1;
	optional string description_key = 2;
	map<string, string> description_variable = 3;
	optional string label_key = 4;
	map<string, string> label_variable = 5;
	optional string button_key = 6;
	optional string redirection_url = 7;
	optional string icon_url = 8;
}

message CheckSellerHasCreatedCampaignRequest {
	optional int64 shopId = 1;
}

message CheckSellerHasCreatedCampaignResponse {
	optional bool hasCreatedCampaign = 1;
}

message GetAccountTcRequest{
	optional int64 shop_id = 1;
}

message GetAccountTcResponse{
	optional bool is_new = 1;
	optional int32 status = 2;
}

message SetAccountTcRequest{
	optional int64 shop_id = 1;
	optional string source = 2;
}

message SetAccountTcResponse{
	optional bool result = 1;
}

message CheckAMSFeatureToggleRequest{
	optional int64 shop_id = 1;
}

message CheckAMSFeatureToggleResponse{
	optional bool result = 1;
}


message GetRcmdCommissionRateRequest{
	optional int64 item_id = 1;
	optional int64 shop_id = 2;
}

message GetRcmdCommissionRateResponse{
	optional int64 lv1_global_be_category_id = 1;
	optional int64 lv2_global_be_category_id = 2;
	optional int32 min_rate = 3;
	optional int32 max_rate = 4;
	optional int32 prefill_rate = 5;
	optional int32 from_lvx = 6;
}


message GetItemsOpenCampaignRequest{
	repeated int64 item_ids = 1;
	optional int64 shop_id = 2;
}

message GetItemsOpenCampaignResponse{
	repeated OpenCampaignAddedProduct item_list = 1;
}

message OpenCampaignAddedProduct{
	optional int64 item_id = 1;
	optional string item_name = 2;
	optional int64 commission_id = 3;
	optional int32 commission_status = 4;
	optional int32 commission_rate = 5;
	optional int64 period_start_time = 6;
	optional int64 period_end_time = 7;
}

message SetOpenCampaignsRequest{
	repeated SetOpenCampaignItem items = 1;
	optional int64 shop_id = 2;
	optional int32 commission_rate = 3;
	optional string operator = 4;
	optional string source = 5;
}

message SetOpenCampaignItem{
	optional int64 item_id = 1;
	optional string item_name = 2;
}

message SetOpenCampaignsResponse{
	optional bool is_all_success = 1;
	repeated SetOpenCampaignResult results = 2;
}

message SetOpenCampaignResult{
	optional int64 item_id = 1;
	optional int32 err_code = 2;
}

message CheckItemResult{
	optional int64 item_id = 1;
	optional bool is_pass = 2;
	optional int32 err_code = 3;
}

message CheckItemIdListRequest{
	optional int64 shop_id = 1;
	repeated int64 item_ids = 2;
}

message CheckItemIdListResponse{
	repeated CheckItemResult items = 1;
	optional int32 err_code = 2;
}

message OpenCampaignSuggestionRequest{
	optional int64 merchant_id = 1;
	optional int64 user_id = 2;
	optional int64 shop_id = 3;
	map<string, string> detail_map = 4;
}

message OpenCampaignSuggestionResponse{
	optional bool hit_condition = 1;
	map<string, string> title_mapping = 2;
	map<string, string> description_mapping = 3;
	map<string, string> title_transify_key_mapping = 4;
	map<string, string> description_transify_key_mapping = 5;
	map<string, string> button_link_mapping = 6;
}

message GetCommissionPlansByAffiliateInfoRequest {
  optional uint32 tenant = 1;  // required  Constant.Tenant
  optional uint64 affiliate_id = 2; // 取值0时，表示查询所有 AffiliateScopeAll 的佣金计划
  optional bool need_order_cap_new_buyer = 3;
  optional bool need_order_cap_existing_buyer = 4;
}

message GetCommissionPlansByAffiliateInfoResponse {
  repeated CommissionPlanDetail commission_plans = 1;
  optional OrderCap order_cap_new_buyer = 2; // returned when need_order_cap_new_buyer = TRUE
  optional OrderCap order_cap_existing_buyer = 3; // returned when need_order_cap_existing_buyer = TRUE
}

message CommissionPlanDetail {
  optional uint64 commission_id = 1;
  optional uint32 commission_type = 2; // Constant.CommissionType
  optional uint64 period_start_time = 3; // second
  optional uint64 period_end_time = 4; // second
  repeated CommissionRateDetail commission_rates = 5;

  optional uint32 version = 6;
}

message CommissionRateDetail {
  optional uint32 channel_source= 1; // Constant.TrackingChannelSource
  optional uint32 attribution_model = 2; // Constant.OrderAttributionModel
  optional uint32 user_type_v2 = 3; // Constant.UserTypeV2
  optional uint32 app_type = 4; // Constant.AppType
  optional uint32 commission_rate = 5;
}

// 2023-05-08之后cap在order上生效
message OrderCap {
  optional uint64 cap_id = 1;
  optional uint64 cap_value = 2;  // returned cap value = raw cap value * 100000
  optional uint32 cap_type = 3;  // Constant.CheckoutCapType
  optional uint32 user_type = 4; // Constant.CheckoutCapUserType
  optional uint64 tag_id = 5;
  optional uint32 version = 6;
  optional uint32 cap_order_type = 7; // Constant.CapOrderType
}
