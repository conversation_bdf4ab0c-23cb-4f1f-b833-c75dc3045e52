package affiliate_client

import (
	"context"
	"encoding/json"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/spex"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/affiliate/commissionbass"
	"github.com/gogo/protobuf/proto"
)

var affiliateClient commissionbass.Client

func InitAffiliateClient() error {
	var err error
	affiliateClient, err = spex.NewClient(commissionbass.NewClient)
	if err != nil {
		return err
	}
	return nil
}

func getCommissionPlansByUserId(ctx context.Context, traceInfo *traceinfo.TraceInfo, affiliateUserId uint64) (*commissionbass.GetCommissionPlansByAffiliateInfoResponse, error) {
	errorCode := "failed"
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("getCommissionPlansByUserId catch a panic", logkit.Any("err", e))
			errorCode = "failed"
		}
		reporter.ReportClientRequestError(1, "GetCommissionPlansByAffiliateInfo", errorCode)
	}()

	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.AffiliateClientTimeOut
	if timeout == 0 {
		timeout = 100
	}
	//if env.Environment() != "live" {
	//	timeout = 2000
	//}
	st := time.Now()
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	req := &commissionbass.GetCommissionPlansByAffiliateInfoRequest{}
	req.Tenant = proto.Uint32(2)
	req.AffiliateId = proto.Uint64(affiliateUserId)
	rsp, err := affiliateClient.GetCommissionPlansByAffiliateInfo(ctx, req)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetCommissionPlansByAffiliateInfo"), zap.String("cost", time.Since(st).String()))
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(req)
		rspStr, _ := json.Marshal(rsp)
		logkit.FromContext(ctx).Info("GetCommissionPlansByAffiliateInfo", logkit.String("req", string(reqStr)), logkit.String("rsp", string(rspStr)))
	}
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "GetCommissionPlansByAffiliateInfo")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		reqStr, _ := json.Marshal(req)
		logkit.FromContext(ctx).WithError(err).Error("failed to GetCommissionPlansByAffiliateInfo", logkit.String("req", string(reqStr)), logkit.String("cost", time.Since(st).String()))
		return &commissionbass.GetCommissionPlansByAffiliateInfoResponse{}, err
	}
	errorCode = "0"
	return rsp, err
}

// 查询用户生效的门店、菜品佣金计划，以及佣金基准佣金率
func GetCommissionPlansByAffiliateId(ctx context.Context, traceInfo *traceinfo.TraceInfo, affiliateUserId uint64) ([]uint64, []uint64, map[uint64]uint32) {
	rsp, err := getCommissionPlansByUserId(ctx, traceInfo, affiliateUserId)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("failed to GetCommissionPlansByAffiliateId", logkit.Uint64("affiliateUserId", affiliateUserId))
		return []uint64{}, []uint64{}, make(map[uint64]uint32)
	}
	plans := rsp.GetCommissionPlans()
	result := make(map[uint64]uint32, len(plans))
	storeCommPlans := make([]uint64, 0, len(plans))
	dishCommPlans := make([]uint64, 0, len(plans))
	for _, plan := range plans {
		if plan == nil {
			continue
		}
		if plan.GetCommissionType() == 2 || plan.GetCommissionType() == 6 {
			dishCommPlans = append(dishCommPlans, plan.GetCommissionId())
		} else if plan.GetCommissionType() == 3 || plan.GetCommissionType() == 5 {
			storeCommPlans = append(storeCommPlans, plan.GetCommissionId())
		}
		for _, rate := range plan.GetCommissionRates() {
			if rate == nil {
				continue
			}
			if rate.GetAppType() == 1 && (rate.GetAttributionModel() == 2 || rate.GetAttributionModel() == 3) &&
				rate.GetChannelSource() == 1 && rate.GetUserTypeV2() == 2 {
				if rate.GetCommissionRate() > 0 {
					result[plan.GetCommissionId()] = rate.GetCommissionRate()
					break
				}
			}
		}
	}
	return storeCommPlans, dishCommPlans, result
}

func GetItemBaseCommissionRate(planCommRate map[uint64]uint32, itemComm *o2oalgo.ItemToCommissionPlan) uint32 {
	if itemComm == nil || len(planCommRate) < 1 {
		return 0
	}
	var maxRate uint32
	for _, planId := range itemComm.GetStoreCommissionPlanIds() {
		rate, ok1 := planCommRate[planId]
		if ok1 {
			if rate > maxRate {
				maxRate = rate
			}
		}
	}
	for _, planId := range itemComm.GetDishCommissionPlanIds() {
		rate, ok1 := planCommRate[planId]
		if ok1 {
			if rate > maxRate {
				maxRate = rate
			}
		}
	}
	return maxRate
}
