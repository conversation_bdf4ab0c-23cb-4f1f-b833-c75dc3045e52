package mlplatform

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/golang/protobuf/proto"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	searchFS "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_ml_search_feature"
	feature_pb "git.garena.com/shopee/o2o-intelligence/ml-common/public-message/feature-server"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

var FeatureServerSpexClient searchFS.Client // 之后的feature server全部用这个client

func ReadFeatureFromFeatureServer(ctx context.Context, req *feature_pb.ReadReq) (rsp *feature_pb.ReadRsp, err error) {
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.FeatureClientTimeOut
	if timeout == 0 {
		timeout = 100
	}
	if env.Environment() != "live" {
		timeout = 5000
	}
	st := time.Now()
	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	rsp, err = FeatureServerSpexClient.Read(ctx, req)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "feature"), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "feature")
	if err != nil || rsp.GetErrCode() != 0 {
		metric_reporter.ReportClientRequestError(1, "feature", "failed")
		logkit.FromContext(ctx).WithError(err).Error("ReadFeatureFromCtrFeatureServer failed", logkit.String("cost", time.Since(st).String()), logkit.Any("rsp", rsp))
		return nil, err
	}
	metric_reporter.ReportClientRequestError(1, "feature", "0")
	if rsp == nil {
		logkit.FromContext(ctx).WithError(err).Error("ReadFeatureFromCtrFeatureServer rsp is nil", logkit.String("cost", time.Since(st).String()))
		return nil, errors.New("featureRsp is nil")
	}
	if rsp.GetErrCode() != 0 {
		logkit.FromContext(ctx).WithError(err).Error("ReadFeatureFromCtrFeatureServer rsp is nil", logkit.String("cost", time.Since(st).String()), logkit.Any("rsp", rsp))
		return nil, fmt.Errorf("featureRsp err-code:%v, err-desc:%v", rsp.GetErrCode(), rsp.GetErrDesc())
	}
	return rsp, nil
}

func ReadI2IFeatureFromFeatureServer(ctx context.Context, req *feature_pb.ReadReq) (rsp *feature_pb.ReadRsp, err error) {
	st := time.Now()
	errCode := "0"
	defer func() {
		metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "featureUserI2I")
		metric_reporter.ReportClientRequestError(1, "featureUserI2I", errCode)
	}()

	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.FeatureClientUserI2IRecallTimeOut
	if timeout == 0 {
		timeout = 50
	}
	if env.Environment() != "live" {
		timeout = 3000
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	rsp, err = FeatureServerSpexClient.Read(ctx, req)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "featureUserI2I"), zap.String("cost", time.Since(st).String()))

	if err != nil || rsp.GetErrCode() != 0 {
		errCode = "failed"
		logkit.FromContext(ctx).WithError(err).Error("ReadI2IFeatureFromFeatureServer failed", logkit.String("cost", time.Since(st).String()), logkit.Any("rsp", rsp))
		return nil, err
	}
	return rsp, err
}

func GetVNUserTagFromFeatureServer(ctx context.Context, userID uint64) (string, string, error) {
	featureReq := &feature_pb.ReadReq{}
	featureReq.IntKeys = []uint64{userID}
	// 1335是organic_user特征
	// 95是organic_user特征所在的特征组
	featureReq.Tags = append(featureReq.Tags, 1335)
	featureReq.FeatureGroup = 95
	featureRsp, err := ReadFeatureFromFeatureServer(ctx, featureReq)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("GetStoreFeatureFromFS error")
		return "", "", err
	}
	if featureRsp == nil || featureRsp.GetErrCode() != 0 {
		err = fmt.Errorf("GetStoreFeatureFromFS failed, err-code:%v, err-desc:%v", featureRsp.GetErrCode(), featureRsp.GetErrDesc())
		logkit.FromContext(ctx).WithError(err).Error("GetStoreFeatureFromFS failed")
		return "", "", err
	}
	var userTagInfo []string
	if len(featureRsp.Feas) > 0 {
		for _, feaList := range featureRsp.Feas {
			var userTag string
			if feaList != nil && len(feaList.Features) > 0 {
				for _, fea := range feaList.Features {
					if fea != nil && fea.Tag == 1335 {
						for _, value := range fea.GetBytesValue() {
							userTag = string(value)
						}
					}
				}
			}
			userTagInfo = append(userTagInfo, userTag)
		}
	}
	if len(userTagInfo) != 1 {
		err = fmt.Errorf("length is not same between key and feature")
		logkit.FromContext(ctx).WithError(err).Error("length is not the same")
		return "", "", err
	}
	userKey := "organic_user"
	userTag := userTagInfo[0]
	if userTag == apollo.SearchApolloCfg.FSUserTagName {
		userTag = "1"
	} else {
		userTag = "0"
	}
	return userKey, userTag, nil
}

func GetI2ITagFromFeatureServer(ctx context.Context, isDebug bool, publishId string, userID uint64) ([]uint64, error) {
	featureReq := &feature_pb.ReadReq{}
	featureReq.IntKeys = []uint64{userID}
	featureReq.ReqId = publishId
	featureReq.Debug = isDebug

	// 153是i2i特征组, 2266是i2i特征
	i2iTag := uint32(2266)
	i2iTagGroup := uint32(153)

	featureReq.Tags = append(featureReq.Tags, i2iTag)
	featureReq.FeatureGroup = i2iTagGroup
	featureRsp, err := ReadI2IFeatureFromFeatureServer(ctx, featureReq)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("GetI2ITagFromFeatureServer error")
		return nil, err
	}
	if featureRsp == nil || featureRsp.GetErrCode() != 0 {
		err = fmt.Errorf("GetI2ITagFromFeatureServer failed, err-code:%v, err-desc:%v", featureRsp.GetErrCode(), featureRsp.GetErrDesc())
		logkit.FromContext(ctx).WithError(err).Error("GetI2ITagFromFeatureServer failed")
		return nil, err
	}
	i2iStoreIds := make([]uint64, 0)
	if len(featureRsp.Feas) > 0 {
		for _, feaList := range featureRsp.Feas {
			if feaList != nil && len(feaList.Features) > 0 {
				for _, fea := range feaList.Features {
					if fea != nil && fea.Tag == int32(i2iTag) {
						for _, value := range fea.GetInt64Value() {
							i2iStoreIds = append(i2iStoreIds, uint64(value))
						}
					}
				}
			}
		}
	}
	if isDebug {
		logkit.FromContext(ctx).Info("GetI2ITagFromFeatureServer success", logkit.Uint64("userID", userID), logkit.String("publishId", publishId), logkit.Int("size", len(i2iStoreIds)), logkit.Uint64s("i2iStoreIds", i2iStoreIds))
	} else {
		logkit.FromContext(ctx).Debug("GetI2ITagFromFeatureServer success", logkit.Uint64("userID", userID), logkit.String("publishId", publishId), logkit.Int("size", len(i2iStoreIds)), logkit.Uint64s("i2iStoreIds", i2iStoreIds))
	}
	return i2iStoreIds, nil
}

const (
	//UserCategoryFeatureTagId uint32 = 1829
	//UserTagFeatureTagId      uint32 = 1830
	UserFeatureGroup uint32 = 1
	//ItemFeatureGroup         uint32 = 2
	DishFeatureGroup uint32 = 9
	//GeoHashFeatureGroup      uint32 = 197
)

type Uint64IdRecallItem struct {
	Value       uint64
	FsRecallTag int32
}

// 特征组测试平台：https://o2orp.i.sz.shopee.io/#/predictor/featureServerTest
const (
	SKUCate                     = 3695 // 存放sku的cate id
	SKULen                      = 3693 // 存放sku的每个cate的len
	SKUTotalList                = 3692 // 存放sku全部的菜品
	FlashSaleIdsList            = 3690 // eng_i_flash_sale_ids_list
	FlashSaleDishIdsList        = 3691 // eng_i_flash_sale_dish_ids_list
	GrStoreIdArray       uint32 = 3113
	GrStoreScoreArray    uint32 = 3112
)

const (
	DishCTR30D        = 3098 // dish_ctr_30d
	DishCVR30D        = 3099 // dish_cvr_30d
	SalesRank7DScore  = 3084 // sales_rank_7d_score
	RatingScore       = 3085 // rating_score
	UVRepurchaseScore = 3087 // uv_repurchase_score
)

func GetDishFeatureList(ctx context.Context, isDebug bool, publishId string, dishIds []uint64) (map[uint64]*foodalgo_search.DishFeature, error) {
	batchSize := 50
	if apollo.SearchApolloCfg.FSDishFeatureItemBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.FSDishFeatureItemBatchSize)
	}

	dishLen := len(dishIds)
	totalBatch := (dishLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return nil, nil
	}
	batchResultList := make([]map[uint64]*foodalgo_search.DishFeature, totalBatch)
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "GetDishFeatureList", func(params ...interface{}) {
			defer wg.Done()

			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > dishLen {
				end = dishLen
			}
			batchDishIds := dishIds[begin:end]
			batchFeatures, err := doGetDishFeatureList(ctx, publishId, isDebug, batchDishIds)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("doGetDishFeatureList failed")
				return
			}
			batchResultList[groupIndex] = batchFeatures
		}, i)
	}
	wg.Wait()

	rsp := make(map[uint64]*foodalgo_search.DishFeature, len(dishIds))
	for _, batchFeaturesMap := range batchResultList {
		for dishId, dishFeature := range batchFeaturesMap {
			rsp[dishId] = dishFeature
		}
	}

	if isDebug {
		logkit.FromContext(ctx).Info("GetDishFeatureList success", logkit.Int("len(dishIds)", len(dishIds)), logkit.Int("len(rsp)", len(rsp)), logkit.Any("rsp", rsp))
	}
	return rsp, nil
}

var dishFeaturesTags = []uint32{
	DishCVR30D,
	DishCTR30D,
	UVRepurchaseScore,
	RatingScore,
	SalesRank7DScore,
}

func doGetDishFeatureList(ctx context.Context, publishId string, isDebug bool, dishIds []uint64) (map[uint64]*foodalgo_search.DishFeature, error) {

	featureReq := &feature_pb.ReadReq{
		IntKeys:      dishIds,
		ReqId:        publishId,
		Debug:        isDebug,
		FeatureGroup: DishFeatureGroup,
		Tags:         dishFeaturesTags,
	}

	pt := time.Now()
	featureRsp, err := ReadFeatureFromFeatureServer(ctx, featureReq)
	metric_reporter.SearchRawReportSummary(time.Since(pt), metric_reporter2.SearchReportTypePhrase, "SearchStoresWithListingDish", "PipelineSearch", "4_DishFeatureFillingRPCOne", "")
	if err != nil || featureRsp == nil || featureRsp.GetErrCode() != 0 {
		logkit.FromContext(ctx).WithError(err).Error("doGetDishFeatureList error")
		return nil, err
	}

	if len(featureRsp.Feas) == 0 {
		logkit.FromContext(ctx).Error("doGetDishFeatureList error", logkit.Int("len(featureRsp.Feas)", 0))
		return nil, nil
	}
	if len(featureRsp.Feas) != len(dishIds) {
		logkit.FromContext(ctx).Error("doGetDishFeatureList error", logkit.Int("len(featureRsp.Feas)", len(featureRsp.Feas)), logkit.Int("len(dishIds)", len(dishIds)))
		return nil, nil
	}

	featuresMap := make(map[uint64]*foodalgo_search.DishFeature, 0)
	for idx, feaList := range featureRsp.Feas {
		df := &foodalgo_search.DishFeature{}
		dishId := dishIds[idx]
		if feaList != nil && len(feaList.Features) > 0 {
			for _, fea := range feaList.Features {
				if fea == nil || len(fea.GetFloatValue()) == 0 {
					continue
				}
				val := proto.Float32(fea.GetFloatValue()[0])
				switch fea.Tag {
				case DishCVR30D:
					df.DishCvr_30D = val
				case DishCTR30D:
					df.DishCtr_30D = val
				case UVRepurchaseScore:
					df.UvRepurchaseScore = val
				case RatingScore:
					df.RatingScore = val
				case SalesRank7DScore:
					df.SalesRank_7DScore = val
				}
			}
		}
		featuresMap[dishId] = df
	}

	return featuresMap, nil
}

// 特征组测试平台：https://o2orp.i.sz.shopee.io/#/predictor/featureServerTest
const (
	UserHistoricalPreferredCategories = 3104 // 用户历史偏好类目
)

func GetUserHistoricalPreferredCategories(ctx context.Context, publishId string, userID uint64) ([]uint32, error) {
	featureReq := &feature_pb.ReadReq{}
	featureReq.IntKeys = []uint64{userID}
	featureReq.ReqId = publishId
	featureReq.Tags = append(featureReq.Tags, UserHistoricalPreferredCategories)
	featureReq.FeatureGroup = UserFeatureGroup
	featureRsp, err := ReadFeatureFromFeatureServer(ctx, featureReq)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("GetUserCategoryAndTagFromFeatureServer error")
		return nil, err
	}
	if featureRsp == nil || featureRsp.GetErrCode() != 0 {
		err = fmt.Errorf("GetUserCategoryAndTagFromFeatureServer failed, err-code:%v, err-desc:%v", featureRsp.GetErrCode(), featureRsp.GetErrDesc())
		logkit.FromContext(ctx).WithError(err).Error("GetUserCategoryAndTagFromFeatureServer failed")
		return nil, err
	}

	var userHistoricalCategories []uint32
	if len(featureRsp.Feas) > 0 {
		for _, feaList := range featureRsp.Feas {
			if feaList != nil && len(feaList.Features) > 0 {
				for _, fea := range feaList.Features {
					if fea != nil && fea.Tag == int32(UserHistoricalPreferredCategories) {
						for _, value := range fea.GetInt64Value() {
							userHistoricalCategories = append(userHistoricalCategories, uint32(value))
						}
					}
				}
			}
		}
	}
	return userHistoricalCategories, nil
}

func GetDishFSRecallList(ctx context.Context, isDebug bool, publishId string, featureGroup uint32, tags []uint32, storeIds []uint64, cacheKeys []string) (map[string]*feature_pb.FeatureList, error) {
	batchSize := 50
	if apollo.SearchApolloCfg.FSDishFSRecallBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.FSDishFSRecallBatchSize)
	}

	storeLen := len(storeIds)
	totalBatch := (storeLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return nil, nil
	}
	batchResultList := make([]map[string]*feature_pb.FeatureList, totalBatch)
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "GetDishFSRecallList", func(params ...interface{}) {
			defer wg.Done()

			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > storeLen {
				end = storeLen
			}
			batchStoreIds := storeIds[begin:end]
			batchCacheKeys := cacheKeys[begin:end]
			batchFeatures, err := doGetDishFSRecall(ctx, publishId, isDebug, featureGroup, tags, batchStoreIds, batchCacheKeys)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("doGetDishFSRecallList failed")
				return
			}
			batchResultList[groupIndex] = batchFeatures
		}, i)
	}
	wg.Wait()

	rsp := make(map[string]*feature_pb.FeatureList, len(storeIds))
	for _, batchFSRecallsMap := range batchResultList {
		for cacheKey, dishFSRecalls := range batchFSRecallsMap {
			rsp[cacheKey] = dishFSRecalls
		}
	}

	if isDebug {
		logkit.FromContext(ctx).Info("GetDishFSRecallList success", logkit.Int("len(storeIds)", len(storeIds)), logkit.Int("len(rsp)", len(rsp)), logkit.Any("rsp", rsp))
	}
	return rsp, nil
}

func doGetDishFSRecall(ctx context.Context, publishId string, isDebug bool, featureGroup uint32, tags []uint32, storeIds []uint64, cacheKeys []string) (map[string]*feature_pb.FeatureList, error) {

	featureReq := &feature_pb.ReadReq{
		IntKeys:      storeIds,
		ReqId:        publishId,
		Debug:        isDebug,
		FeatureGroup: featureGroup,
		Tags:         tags,
	}

	pt := time.Now()
	featureRsp, err := ReadFeatureFromFeatureServer(ctx, featureReq)
	metric_reporter.SearchRawReportSummary(time.Since(pt), metric_reporter2.SearchReportTypePhrase, "SearchStoresWithListingDish", "PipelineSearch", "4_PhraseDishFSRecallGetCacheRPC", "")
	if err != nil || featureRsp == nil || featureRsp.GetErrCode() != 0 {
		logkit.FromContext(ctx).WithError(err).Error("doGetDishFSRecall error")
		return nil, err
	}

	if len(featureRsp.Feas) == 0 {
		logkit.FromContext(ctx).Error("doGetDishFSRecall error", logkit.Int("len(featureRsp.Feas)", 0))
		return nil, nil
	}
	if len(featureRsp.Feas) != len(storeIds) {
		logkit.FromContext(ctx).Error("doGetDishFSRecall error", logkit.Int("len(featureRsp.Feas)", len(featureRsp.Feas)), logkit.Int("len(storeIds)", len(storeIds)))
		return nil, nil
	}

	featuresMap := make(map[string]*feature_pb.FeatureList, 0)
	for idx, feaList := range featureRsp.Feas {
		cacheKey := cacheKeys[idx]
		featuresMap[cacheKey] = feaList
	}

	return featuresMap, nil
}
