package qp

import (
	"context"
	"encoding/json"
	common "git.garena.com/shopee/o2o-intelligence/common/common-lib/math"
	"strings"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/rediscache"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	spex "git.garena.com/shopee/o2o-intelligence/common/common-lib/spex_client_gray"
	util3 "git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

var QPServiceClient *QPService

type QPService struct {
	Client        qp.Client
	QPResultRedis *rediscache.QPResultRedis
}

var acc common.Accuracy = func() float64 { return 0.000001 }

func NewQPService(QPResultRedis *rediscache.QPResultRedis) *QPService {
	qp.SetGreySwitch(spex.DefaultSwitchFunc)
	var client qp.Client
	var err error
	client, err = qp.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.Fatal("failed to init qp spex client")
	}

	QPServiceClient = &QPService{
		Client:        client,
		QPResultRedis: QPResultRedis,
	}
	return QPServiceClient
}

func (q *QPService) GetQPResult(ctx context.Context, query string, needRewrite bool, traceInfo *traceinfo.TraceInfo, abtest string) (*traceinfo.QPResult, *qp.GetQueryProcessingKeywordResp, error) {
	if decision.IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeQP) {
		// 降级,请求QP cache
		logkit.FromContext(ctx).Error("Downgrade QP")
		reporter.ReportClientRequestError(1, "qp_downgrade", "true")
		resp, err := q.QPResultRedis.GetDowngradeCache(ctx, query, needRewrite, traceInfo)
		if err != nil {
			return nil, nil, err
		}
		qpResult := q.BuildRes(resp, traceInfo)
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		return qpResult, resp, nil
	}
	reporter.ReportClientRequestError(1, "qp_downgrade", "0")

	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.QPClientTimeOut)*time.Millisecond)
	defer cancel()
	var r *qp.GetQueryProcessingKeywordResp
	var err error
	hitCache := true
	// 先获取cache
	r, err = q.QPResultRedis.GetNormalResultCache(ctx, query, needRewrite, traceInfo)
	if err != nil {
		// cache取不到,再请求QP
		logger.MyDebug(ctx, traceInfo.IsDebug, "Normal qp cache get fail.", zap.String("err", err.Error()))
		qpAbtest := ""
		if apollo.SearchApolloCfg.QPABTestList != "" {
			abTest := strings.Split(apollo.SearchApolloCfg.QPABTestList, ",")
			for _, val := range abTest {
				abtestStr := traceInfo.ABTestGroup.GetABTestGroup(val)
				if abtestStr != "" {
					qpAbtest = qpAbtest + abtestStr + ","
				}
			}
			qpAbtest = strings.Trim(qpAbtest, ",")
		} else {
			qpAbtest = abtest
		}
		logger.MyDebug(ctx, traceInfo.IsDebug, "qp ab test", zap.String("abtest", qpAbtest))
		feature := 0
		if needRewrite == true {
			feature = 1
		}
		req := &qp.GetQueryProcessingKeywordReq{
			Query:       proto.String(query),
			NeedRewrite: proto.Bool(needRewrite),
			AbTest:      proto.String(qpAbtest),
			Feature:     proto.Int32(int32(feature)),
			Layers:      traceInfo.LayerList,
			AbtestTags:  traceInfo.TagList,
			CityId:      proto.Uint32(traceInfo.TraceRequest.CityId),
		}
		startTime := time.Now()
		r, err = q.Client.GetQueryProcessingKeyword(ctx, req)
		//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp"), zap.String("cost", time.Since(startTime).String()))
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "qp")
		if traceInfo.IsDebug {
			logger.MyDebug(ctx, traceInfo.IsDebug, "debug log: QP req and response", zap.Any("req", req), zap.Any("response", r))
		}
		if err != nil {
			traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
			logkit.FromContext(ctx).WithError(err).Error("get qp failed", logkit.String("cost", time.Since(startTime).String()))
			reporter.ReportClientRequestError(1, "qp", "failed")
			r, err = q.QPResultRedis.GetDowngradeCache(ctx, query, needRewrite, traceInfo)
			if err != nil {
				return nil, nil, err
			}
		} else {
			reporter.ReportClientRequestError(1, "qp", "0")
			hitCache = false
		}
	}
	qpResult := q.BuildRes(r, traceInfo)
	if !hitCache {
		err = q.QPResultRedis.SetDowngradeCache(ctx, query, needRewrite, r, traceInfo)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("set qp cache failed")
		}
		err = q.QPResultRedis.SetNormalResultCache(ctx, query, needRewrite, r, traceInfo)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("set qp normal cache failed")
		}
	}
	return qpResult, r, nil
}

func getProbFilterQueryCateIntents(traceInfo *traceinfo.TraceInfo, queryCateIntents []*qp.CategoryIntention) []*qp.CategoryIntention {
	// 过滤prob阈值低的
	var validCategoryTags = make([]*qp.CategoryIntention, 0, 3)

	validProbThreshold := traceInfo.PredictConfig.NewCateProbFilterScore
	if validProbThreshold == 0.0 {
		validProbThreshold = 0.7
	}
	for _, categoryTag := range queryCateIntents {
		if categoryTag.Score != nil && *categoryTag.Score >= validProbThreshold {
			validCategoryTags = append(validCategoryTags, categoryTag)
		}
	}
	return validCategoryTags
}

func (q *QPService) BuildRes(r *qp.GetQueryProcessingKeywordResp, traceInfo *traceinfo.TraceInfo) *traceinfo.QPResult {
	traceInfo.QPResponse = r
	qpResult := &traceinfo.QPResult{}
	segments := make([]string, 0, len(r.Segments))
	segmentsAscii := make([]string, 0, len(r.SegmentsAscii))
	for _, val := range r.Segments {
		segments = append(segments, *val.Token)
	}
	for _, val := range r.SegmentsAscii {
		segmentsAscii = append(segmentsAscii, *val.Token)
	}
	maxWordSegments := make([]string, 0, len(r.MaxWordSegments))
	for _, val := range r.MaxWordSegments {
		maxWordSegments = append(maxWordSegments, *val.Token)
	}
	storeIntents := make([]string, 0)
	for _, val := range r.StoreIntents {
		for _, valCat := range val.Cat1 {
			storeIntents = append(storeIntents, *valCat.Keyword)
		}
	}
	dishIntents := make([]string, 0)
	for _, val := range r.DishIntents {
		for _, valCat := range val.Cat1 {
			dishIntents = append(dishIntents, *valCat.Keyword)
		}
	}
	//// 开启 keyword label 控制是否补充挂菜// 补充挂菜remove
	//if apollo.SearchApolloCfg.OpenKeywordLabelControl {
	//	keywordLabel := r.GetKeywordLabel()
	//	if keywordLabel == qp.KeywordLabel_TOPTERM || keywordLabel == qp.KeywordLabel_MIDTERM {
	//		traceInfo.IsDishSupplement = true
	//	} else {
	//		traceInfo.IsDishSupplement = false
	//	}
	//} else {
	//	// 否则都需要补充挂菜
	//	traceInfo.IsDishSupplement = true
	//}

	qpResult.Segments = segments
	qpResult.SegmentsAscii = segmentsAscii
	qpResult.MaxWordSegments = maxWordSegments
	qpResult.StoreIntents = storeIntents
	qpResult.DishIntents = dishIntents
	qpResult.CategoryIntentions = r.CateIntents
	probFilterQueryCateIntents := getProbFilterQueryCateIntents(traceInfo, r.CateIntents)
	qpResult.ProbFilterCategoryIntentions = probFilterQueryCateIntents
	if r.GetCorrect() != nil && len(r.GetCorrect().GetCorrected()) > 0 {
		qpResult.CorrectWord = r.GetCorrect().GetCorrected()[0].GetCorrectQuery()
		correctionType := r.GetCorrect().GetCorrected()[0].GetFoodCorrectType()
		correctedLevel := r.GetCorrect().GetCorrected()[0].CorrectionLevel
		qpResult.CorrectLevel = correctedLevel
		qpResult.CorrectionType = correctionType
		if correctionType == qp.FoodCorrectionType_CorrectionModifyDueToCorrectedList || correctionType == qp.FoodCorrectionType_CorrectionModifyDueToThreshold {
			qpResult.IsQPCorrect = true
		} else {
			qpResult.IsQPCorrect = false
		}
		traceInfo.QPResult.CorrectWord = qpResult.CorrectWord
		traceInfo.QPResult.CorrectionType = qpResult.CorrectionType
		traceInfo.QPResult.IsQPCorrect = qpResult.IsQPCorrect
		traceInfo.QPResult.CorrectLevel = qpResult.CorrectLevel
	}
	traceInfo.QPResult.Segments = segments
	traceInfo.QPResult.SegmentsAscii = segmentsAscii
	traceInfo.QPResult.MaxWordSegments = maxWordSegments
	traceInfo.QPResult.StoreIntents = storeIntents
	traceInfo.QPResult.DishIntents = dishIntents
	traceInfo.QPResult.CategoryIntentions = r.CateIntents
	traceInfo.QPResult.ProbFilterCategoryIntentions = probFilterQueryCateIntents
	traceInfo.QPResult.NerResult = r.Ner
	traceInfo.QPResult.NerAndRecallResult = r.NerRecall
	traceInfo.QPResult.NerOrRecallResult = r.NerRecallOr
	traceInfo.QPResult.NerAsciiResult = r.NerAscii
	traceInfo.QPResult.NerAndRecallAsciiResult = r.NerRecallAscii
	traceInfo.QPResult.NerOrRecallAsciiResult = r.NerRecallOrAscii
	traceInfo.QPResult.QueryStoreIntention = r.GetStoreIntention().GetIntentionKeyword()
	traceInfo.QPResult.QueryDishIntention = r.GetDishIntention().GetIntentionKeyword()
	traceInfo.QPResult.QueryStoreIntentionProb = r.GetStoreIntention().GetIntentionProb()
	traceInfo.QPResult.QueryDishIntentionProb = r.GetDishIntention().GetIntentionProb()
	traceInfo.QPResult.QueryGeneralSubCateIds = r.GetQueryGeneralSubCate()
	traceInfo.QPResult.KeywordLabel = int32(r.GetKeywordLabel())
	queryTag := make([]string, 0)
	for _, tag := range r.GetQueryTag() {
		queryTag = append(queryTag, tag.GetTagName())
	}
	traceInfo.QPResult.QueryTag = queryTag
	queryRewriteNerList := make([][]*qp.Ner, 0)
	queryRewriteQueryList := make([]string, 0)
	for _, rewrite := range r.QueryRewriteList {
		queryRewriteNerList = append(queryRewriteNerList, rewrite.GetQueryRewriteNer())
		queryRewriteQueryList = append(queryRewriteQueryList, rewrite.GetRewriteQuery())
	}
	traceInfo.QPResult.RewriteNerResult = queryRewriteNerList
	traceInfo.QPResult.RewriteNerOriginResult = r.QueryRewriteList
	traceInfo.QPResult.RewriteQuery = queryRewriteQueryList
	traceInfo.QPResult.EnlargeRewriteQuerys = r.GetEnlargeRewriteQuerys()
	if r.GetEnlargeRewriteSegments() != nil {
		traceInfo.QPResult.EnlargeRewriteSegments = make([]traceinfo.RewriteSegment, 0, len(r.GetEnlargeRewriteSegments()))
		for _, segment := range r.GetEnlargeRewriteSegments() {
			tokens := make([]string, 0)
			for _, t := range segment.GetRewriteSegments() {
				tokens = append(tokens, t.GetToken())
			}
			if len(tokens) > 0 {
				traceInfo.QPResult.EnlargeRewriteSegments = append(traceInfo.QPResult.EnlargeRewriteSegments, traceinfo.RewriteSegment{
					RewriteQuery: segment.GetRewriteQuery(),
					Segments:     tokens,
				})
			}
		}
	}

	onlyUnknownNer := true
	for _, ner := range traceInfo.QPResult.NerResult {
		for _, nerType := range ner.GetNerType() {
			if nerType == qp.NERType_DISH {
				traceInfo.QPResult.IsNeedDishRecall = true
			}
			if nerType != qp.NERType_UNKNOWN {
				onlyUnknownNer = false
			}
		}
	}
	traceInfo.QPResult.OnlyUnknownNer = onlyUnknownNer
	traceInfo.QPResult.NerRecallResultOnlyDishNer = true
	for _, ner := range traceInfo.QPResult.NerAndRecallResult {
		for _, nerType := range ner.GetNerType() {
			if nerType != qp.NERType_DISH {
				traceInfo.QPResult.NerRecallResultOnlyDishNer = false
			}
		}
	}

	manualReviewL1Ids := make(map[uint32]struct{}, 0)
	manualReviewL2Ids := make(map[uint32]struct{}, 0)
	for _, cateIntention := range traceInfo.QPResult.CategoryIntentions {
		// 分数等于 1 的，则表示是人工审核的
		if acc.Equal(*cateIntention.Score, 1.0) {
			manualReviewL1Ids[cateIntention.GetLevel1ID()] = struct{}{}
			manualReviewL2Ids[cateIntention.GetLevel2ID()] = struct{}{}
		}
	}
	traceInfo.QPResult.QueryCateIntentsManualReviewL1Ids = manualReviewL1Ids
	traceInfo.QPResult.QueryCateIntentsManualReviewL2Ids = manualReviewL2Ids

	qpLog, _ := json.Marshal(r)
	traceInfo.QPResult.QPLog = util3.ByteToString(qpLog)
	traceInfo.QPResult.RewriteSegments = make([]traceinfo.RewriteSegment, 0)
	for _, rs := range r.RewriteSegments {
		rewriteSegments := rs.GetRewriteSegments()
		tokens := make([]string, 0)
		for _, t := range rewriteSegments {
			tokens = append(tokens, t.GetToken())
		}
		if len(tokens) > 0 {
			traceInfo.QPResult.RewriteSegments = append(traceInfo.QPResult.RewriteSegments, traceinfo.RewriteSegment{
				RewriteQuery: rs.GetRewriteQuery(),
				Segments:     tokens,
			})
		}
	}

	// query string
	if len(traceInfo.QPResult.NerAndRecallResult) > 0 {
		// 这里只提供初始化，等待各路召回配置再生成
		traceInfo.QPResult.TermWeightList = r.TermWeight
		traceInfo.QPResult.QueryStringListMap = make(map[int32][]traceinfo.QueryStringItem, 0)
	}
	// ID drop word
	dropWordStoreIntentions := make([]string, 0)
	dropWordDishIntentions := make([]string, 0)
	for _, dropWord := range r.DropWord {
		if dropWord.GetNerType() == qp.NERType_STORE {
			dropWordStoreIntentions = append(dropWordStoreIntentions, dropWord.GetToken())
		}
		if dropWord.GetNerType() == qp.NERType_DISH {
			dropWordDishIntentions = append(dropWordDishIntentions, dropWord.GetToken())
		}
	}
	traceInfo.QPResult.DropWordStoreIntents = dropWordStoreIntentions
	traceInfo.QPResult.DropWordDishIntents = dropWordDishIntentions
	return qpResult
}

func (q *QPService) GetOtherQuerySegments(ctx context.Context, query []string) [][]string {
	segmentsList := make([][]string, 0, len(query))
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.QPClientTimeOut)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	r, err := q.Client.GetQueryProcessingKeyword(ctx, &qp.GetQueryProcessingKeywordReq{
		OtherNeedTokenizerQuery: query,
		Feature:                 proto.Int32(2),
	})
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-segments"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "qp-segments")
	if err != nil {
		reporter.ReportClientRequestError(1, "qp-segments", "failed")
		logkit.FromContext(ctx).WithError(err).Error("get qp failed", zap.String("cost", time.Since(startTime).String()), zap.Any("query", query))
		segmentsList = append(segmentsList, query)
		return segmentsList
	}
	reporter.ReportClientRequestError(1, "qp-segments", "0")
	for _, segmentsToken := range r.SegmentsList {
		segments := make([]string, 0, len(segmentsToken.Segments))
		for _, val := range segmentsToken.Segments {
			segments = append(segments, *val.Token)
		}
		segmentsList = append(segmentsList, segments)
	}
	return segmentsList
}
