package foodalgo_shippingfee

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/client"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
	microclient "github.com/micro/go-micro/client"
)

type callOptsCtxKey struct{}

func WithCallOpts(ctx context.Context, opts ...microclient.CallOption) context.Context {
	return context.WithValue(ctx, callOptsCtxKey{}, opts)
}

type Method = string

type SwitchFunc func(ctx context.Context, serviceName string, method Method) bool

var greySwitch SwitchFunc = func(context.Context, string, Method) bool { return true }

func SetGreySwitch(s SwitchFunc) {
	greySwitch = s
}

type Client interface {
	GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, opts ...callopt.Option) (*GetBuyerShippingFeeResponse, error)

	GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, opts ...callopt.Option) (*GetDriverShippingFeeResponse, error)

	GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, opts ...callopt.Option) (*GetHubDriverShippingFeeConfigResponse, error)

	GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, opts ...callopt.Option) (*GetMinShippingFeeResponse, error)

	GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, opts ...callopt.Option) (*GetShippingFeeResponse, error)

	MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, opts ...callopt.Option) (*MGetStoreListShippingFeeResponse, error)
}

func NewSpexClient(opts ...client.Option) (Client, error) {
	namespace := "foodalgo.storelistshippingfeesvr"

	spexClient, err := client.NewClient(append([]client.Option{
		client.WithInterceptor(ClientVersionReport(version, spexServiceName)),
	}, opts...)...)
	if err != nil {
		return nil, err
	}

	cli := &foodalgoShippingfeeClient{
		c:              spexClient,
		microkitClient: NewClient(),
		namespace:      namespace,
	}

	return cli, nil
}

type foodalgoShippingfeeClient struct {
	c              client.Client
	microkitClient FoodalgoShippingfeeService
	namespace      string
}

func (client *foodalgoShippingfeeClient) GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, opts ...callopt.Option) (*GetBuyerShippingFeeResponse, error) {
	if greySwitch(ctx, client.namespace, "get_buyer_shipping_fee") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetBuyerShippingFee(ctx, in, callOpts...)
	}

	out := new(GetBuyerShippingFeeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_buyer_shipping_fee", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodalgoShippingfeeClient) GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, opts ...callopt.Option) (*GetDriverShippingFeeResponse, error) {
	if greySwitch(ctx, client.namespace, "get_driver_shipping_fee") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetDriverShippingFee(ctx, in, callOpts...)
	}

	out := new(GetDriverShippingFeeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_driver_shipping_fee", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodalgoShippingfeeClient) GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, opts ...callopt.Option) (*GetHubDriverShippingFeeConfigResponse, error) {
	if greySwitch(ctx, client.namespace, "get_hub_driver_shipping_fee_config") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetHubDriverShippingFeeConfig(ctx, in, callOpts...)
	}

	out := new(GetHubDriverShippingFeeConfigResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_hub_driver_shipping_fee_config", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodalgoShippingfeeClient) GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, opts ...callopt.Option) (*GetMinShippingFeeResponse, error) {
	if greySwitch(ctx, client.namespace, "get_min_shipping_fee") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetMinShippingFee(ctx, in, callOpts...)
	}

	out := new(GetMinShippingFeeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_min_shipping_fee", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodalgoShippingfeeClient) GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, opts ...callopt.Option) (*GetShippingFeeResponse, error) {
	if greySwitch(ctx, client.namespace, "get_shipping_fee") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetShippingFee(ctx, in, callOpts...)
	}

	out := new(GetShippingFeeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_shipping_fee", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodalgoShippingfeeClient) MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, opts ...callopt.Option) (*MGetStoreListShippingFeeResponse, error) {
	if greySwitch(ctx, client.namespace, "m_get_store_list_shipping_fee") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MGetStoreListShippingFee(ctx, in, callOpts...)
	}

	out := new(MGetStoreListShippingFeeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_list_shipping_fee", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}
