package foodalgo_shippingfee

import (
	"context"
	"fmt"
	"os"
	"strings"

	"git.garena.com/shopee/platform/golang_splib/client/callopt"
	"git.garena.com/shopee/platform/golang_splib/desc"
	"git.garena.com/shopee/platform/golang_splib/interceptor"
	"git.garena.com/shopee/platform/golang_splib/sps"
	"git.garena.com/shopee/platform/golang_splib/util"
	"git.garena.com/shopee/platform/service-governance/observability/metric"
)

const version = "v1.3.4"

const spexServiceName = "foodalgo.storelistshippingfeesvr"

func ClientVersionReport(version, serviceName string) interceptor.ClientInterceptor {
	return func(next interceptor.Invoker) interceptor.Invoker {
		return func(ctx context.Context, cmd string, request interface{}, response interface{}, opts ...callopt.Option) error {
			labels := map[string]string{"version": version, "type": "client", "caller": currentServiceName(), "callee": serviceName}
			_ = metric.RPCCustomReporter.ReportCounter("spsvg_stub_version", labels, 1)

			return next(ctx, cmd, request, response, opts...)
		}
	}
}

func ServerVersionReport(version string) interceptor.ServerInterceptor {
	return func(handler desc.Handler) desc.Handler {
		return func(ctx context.Context, request interface{}) (interface{}, error) {
			header := sps.FromIncomingContext(ctx)
			peerServiceName, _, _, _, _, _, _ := util.ParseInstanceID(header.InstanceID())
			labels := map[string]string{"version": version, "type": "server", "callee": currentServiceName(), "caller": peerServiceName}
			_ = metric.RPCCustomReporter.ReportCounter("spsvg_stub_version", labels, 1)

			return handler(ctx, request)
		}
	}
}

func currentServiceName() string {
	pro := strings.ToLower(os.Getenv("PROJECT_NAME"))
	mod := strings.ToLower(os.Getenv("MODULE_NAME"))

	return fmt.Sprintf("%s-%s", pro, mod)
}
