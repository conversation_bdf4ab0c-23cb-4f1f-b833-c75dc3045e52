// Code generated by protoc-gen-microkit. DO NOT EDIT.
// source: foodalgo_shippingfee/foodalgo_shippingfee.proto

/*
Package foodalgo_shippingfee is a generated protocol buffer package.

项目名 + “_” + 模块名

It is generated from these files:
	foodalgo_shippingfee/foodalgo_shippingfee.proto

It has these top-level messages:
	Location
	StoreOption
	DynamicContext
	BuyerInfo
	Amount
	OrderItem
	GetShippingFeeRequest
	BuyerAddition
	GetShippingFeeResponse
	GetBuyerShippingFeeRequest
	MGetStoreListShippingFeeRequest
	StoreDetail
	OrderDetail
	GetBuyerShippingFeeResponse
	MGetStoreListShippingFeeResponse
	StoreListShippingFee
	Route
	DriverInfo
	GetDriverShippingFeeRequest
	GetDriverShippingFeeResponse
	SingleDriverShippingFee
	GetHubDriverShippingFeeConfigRequest
	GetHubDriverShippingFeeConfigResponse
	GetMinShippingFeeRequest
	GetMinShippingFeeResponse
*/
package foodalgo_shippingfee

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"

import (
	micro "github.com/micro/go-micro"
	microkit1 "git.garena.com/shopee/feed/microkit"
	client "github.com/micro/go-micro/client"
	server "github.com/micro/go-micro/server"
	context "context"
	_ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// custom serviceName
const (
	serviceName = "foodalgo_shippingfee"
)

// Client API for FoodalgoShippingfee service

type FoodalgoShippingfeeService interface {
	GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, opts ...client.CallOption) (*GetShippingFeeResponse, error)
	GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, opts ...client.CallOption) (*GetBuyerShippingFeeResponse, error)
	GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, opts ...client.CallOption) (*GetDriverShippingFeeResponse, error)
	GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, opts ...client.CallOption) (*GetHubDriverShippingFeeConfigResponse, error)
	MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, opts ...client.CallOption) (*MGetStoreListShippingFeeResponse, error)
	GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, opts ...client.CallOption) (*GetMinShippingFeeResponse, error)
}

type foodalgoShippingfeeService struct {
	c    client.Client
	name string
}

func newFoodalgoShippingfeeService(name string, c client.Client) FoodalgoShippingfeeService {
	if c == nil {
		c = client.NewClient()
	}
	if len(name) == 0 {
		name = serviceName
	}
	return &foodalgoShippingfeeService{
		c:    c,
		name: name,
	}
}

func (c *foodalgoShippingfeeService) GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, opts ...client.CallOption) (*GetShippingFeeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.GetShippingFee", in)
	out := new(GetShippingFeeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodalgoShippingfeeService) GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, opts ...client.CallOption) (*GetBuyerShippingFeeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.GetBuyerShippingFee", in)
	out := new(GetBuyerShippingFeeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodalgoShippingfeeService) GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, opts ...client.CallOption) (*GetDriverShippingFeeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.GetDriverShippingFee", in)
	out := new(GetDriverShippingFeeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodalgoShippingfeeService) GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, opts ...client.CallOption) (*GetHubDriverShippingFeeConfigResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.GetHubDriverShippingFeeConfig", in)
	out := new(GetHubDriverShippingFeeConfigResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodalgoShippingfeeService) MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, opts ...client.CallOption) (*MGetStoreListShippingFeeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.MGetStoreListShippingFee", in)
	out := new(MGetStoreListShippingFeeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodalgoShippingfeeService) GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, opts ...client.CallOption) (*GetMinShippingFeeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodalgoShippingfee.GetMinShippingFee", in)
	out := new(GetMinShippingFeeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FoodalgoShippingfee service

type FoodalgoShippingfeeHandler interface {
	GetShippingFee(context.Context, *GetShippingFeeRequest, *GetShippingFeeResponse) error
	GetBuyerShippingFee(context.Context, *GetBuyerShippingFeeRequest, *GetBuyerShippingFeeResponse) error
	GetDriverShippingFee(context.Context, *GetDriverShippingFeeRequest, *GetDriverShippingFeeResponse) error
	GetHubDriverShippingFeeConfig(context.Context, *GetHubDriverShippingFeeConfigRequest, *GetHubDriverShippingFeeConfigResponse) error
	MGetStoreListShippingFee(context.Context, *MGetStoreListShippingFeeRequest, *MGetStoreListShippingFeeResponse) error
	GetMinShippingFee(context.Context, *GetMinShippingFeeRequest, *GetMinShippingFeeResponse) error
}

func registerFoodalgoShippingfeeHandler(s server.Server, hdlr FoodalgoShippingfeeHandler, opts ...server.HandlerOption) error {
	type foodalgoShippingfee interface {
		GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, out *GetShippingFeeResponse) error
		GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, out *GetBuyerShippingFeeResponse) error
		GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, out *GetDriverShippingFeeResponse) error
		GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, out *GetHubDriverShippingFeeConfigResponse) error
		MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, out *MGetStoreListShippingFeeResponse) error
		GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, out *GetMinShippingFeeResponse) error
	}
	type FoodalgoShippingfee struct {
		foodalgoShippingfee
	}
	h := &foodalgoShippingfeeHandler{hdlr}
	return s.Handle(s.NewHandler(&FoodalgoShippingfee{h}, opts...))
}

type foodalgoShippingfeeHandler struct {
	FoodalgoShippingfeeHandler
}

// Default service
var (
	service micro.Service
	cli     FoodalgoShippingfeeService
)

func GetServiceName() string {
	return serviceName
}

func NewService() micro.Service {
	service = micro.NewService(microkit1.DefaultOptions...)
	return service
}

func NewClient() FoodalgoShippingfeeService {
	cli = newFoodalgoShippingfeeService(serviceName, microkit1.DefaultClient)
	return cli
}

func RegisterFoodalgoShippingfeeHandler(hdlr FoodalgoShippingfeeHandler, opts ...server.HandlerOption) error {
	return registerFoodalgoShippingfeeHandler(service.Server(), hdlr, opts...)
}

func (h *foodalgoShippingfeeHandler) GetShippingFee(ctx context.Context, in *GetShippingFeeRequest, out *GetShippingFeeResponse) error {
	return h.FoodalgoShippingfeeHandler.GetShippingFee(ctx, in, out)
}

func (h *foodalgoShippingfeeHandler) GetBuyerShippingFee(ctx context.Context, in *GetBuyerShippingFeeRequest, out *GetBuyerShippingFeeResponse) error {
	return h.FoodalgoShippingfeeHandler.GetBuyerShippingFee(ctx, in, out)
}

func (h *foodalgoShippingfeeHandler) GetDriverShippingFee(ctx context.Context, in *GetDriverShippingFeeRequest, out *GetDriverShippingFeeResponse) error {
	return h.FoodalgoShippingfeeHandler.GetDriverShippingFee(ctx, in, out)
}

func (h *foodalgoShippingfeeHandler) GetHubDriverShippingFeeConfig(ctx context.Context, in *GetHubDriverShippingFeeConfigRequest, out *GetHubDriverShippingFeeConfigResponse) error {
	return h.FoodalgoShippingfeeHandler.GetHubDriverShippingFeeConfig(ctx, in, out)
}

func (h *foodalgoShippingfeeHandler) MGetStoreListShippingFee(ctx context.Context, in *MGetStoreListShippingFeeRequest, out *MGetStoreListShippingFeeResponse) error {
	return h.FoodalgoShippingfeeHandler.MGetStoreListShippingFee(ctx, in, out)
}

func (h *foodalgoShippingfeeHandler) GetMinShippingFee(ctx context.Context, in *GetMinShippingFeeRequest, out *GetMinShippingFeeResponse) error {
	return h.FoodalgoShippingfeeHandler.GetMinShippingFee(ctx, in, out)
}
