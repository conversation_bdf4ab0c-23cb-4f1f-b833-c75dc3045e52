syntax = "proto2";

// 项目名 + “_” + 模块名
package foodalgo_shippingfee;

// Code generated by spcli. DO NOT EDIT.
//
// namespace o2oalgo.food_logistic.shippingfeesvr
//
// commands {
//   o2oalgo.food_logistic.shippingfeesvr.get_buyer_shipping_fee
//   o2oalgo.food_logistic.shippingfeesvr.get_driver_shipping_fee
//   o2oalgo.food_logistic.shippingfeesvr.get_hub_driver_shipping_fee_config
//   o2oalgo.food_logistic.shippingfeesvr.get_min_shipping_fee
//   o2oalgo.food_logistic.shippingfeesvr.get_shipping_fee
//   o2oalgo.food_logistic.shippingfeesvr.m_get_store_list_shipping_fee
// }

import "spex/protobuf/service.proto";
//import "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor/microkit.proto";

service ShippingFee {// 模块名
//  option (microkit.ProjectName) = "foodalgo"; // 项目名
  option (service.service) = {
    servicename: "foodalgo.storelistshippingfeesvr"
  };

  rpc GetShippingFee(GetShippingFeeRequest) returns (GetShippingFeeResponse);
  rpc GetBuyerShippingFee(GetBuyerShippingFeeRequest) returns (GetBuyerShippingFeeResponse);
  rpc GetDriverShippingFee(GetDriverShippingFeeRequest) returns (GetDriverShippingFeeResponse);
  rpc GetHubDriverShippingFeeConfig(GetHubDriverShippingFeeConfigRequest) returns (GetHubDriverShippingFeeConfigResponse);
  rpc MGetStoreListShippingFee(MGetStoreListShippingFeeRequest) returns (MGetStoreListShippingFeeResponse);
  rpc GetMinShippingFee(GetMinShippingFeeRequest) returns (GetMinShippingFeeResponse);
}

enum ServiceType {
  ShopeeFood = 1;
  SPXInstantShopee = 2;
  SPXInstant = 3;
}

enum SceneType {
  Not = 0; // 无效
  BuyerCheckOut = 1; // Buyer    参数检查 -> order_id=0, request_id=0
  BuyerSubmit = 2; // Buyer    参数检查 -> order_id>0, request_id>0
  DriverQuery = 3; // Driver   参数检查 -> order_id>0, request_id=0
}

enum ShippingFeeType {
  BuyerFee = 0;  // 买家应收
  DriverEarn = 1;  // 骑手应收配送费
  BuyerRisingFee = 2;  // 买家加价费用
  DriverRisingEarn = 3;  // 骑手加价收入
}

message Location {
  optional string state = 1;
  optional string city = 2;
  optional string district = 3;
  optional string address = 4;
  optional float latitude = 5;
  optional float longitude = 6;
}

message StoreOption {
  enum PartnerType {
    NonPartner = 1;
    Partner = 2;
  }
  optional PartnerType partner_type = 1;
}

enum DeliveryMode {
  DeliveryMode_Not = 0;
  Platform = 1;
  Merchant = 2;
}

message DynamicContext {
  optional BuyerInfo buyer_info = 1;
  repeated OrderItem order_items = 2;
  optional Amount amount = 3;
}

message BuyerInfo {
  optional string phone_brand = 1;
  optional string phone_model = 2;
  optional string phone_system = 3;
  optional string phone_wifi = 4;
}

enum VatCalculate {
  VAT_CALCULATE_UNKNOWN = 0;
  VAT_CALCULATE_EXCLUSIVE = 1;
  VAT_CALCULATE_INCLUSIVE = 2;
}

message Amount {
  optional uint64 item_subtotal = 1;
  optional uint64 merchant_commission_rate = 2;
  optional uint64 merchant_food_voucher_subsidy = 3;
  optional uint64 tax_rate = 4;
  optional uint64 service_charge_fee_rate = 5;
  optional uint64 vat_rate = 6;
  optional VatCalculate vat_calculate = 7;
  optional uint64  merchant_service_fee = 8;
  optional uint64  platform_service_fee = 9;
  optional uint64  small_order_fee = 10;
}

message OrderItem {
  optional uint64 dish_id = 1;
  optional uint32 quantity = 2;
  optional uint64 unit_price = 3;        // 折扣价
  optional uint64 unit_list_price = 4;   // 划线价，即原价
}

message GetShippingFeeRequest {
  optional uint64 order_id = 1;
  repeated ShippingFeeType fee_type_list = 2;
  required Location origin = 3; // Start location[store]
  required Location destination = 4; // End location[buyer]
  optional StoreOption store_option = 5;
  optional ServiceType service_type = 6;
  optional SceneType   scene_type = 7;
  optional string      request_id = 8;
  optional uint64      buyer_id = 9;
  optional uint64      store_id = 10;
  optional DeliveryMode delivery_mode = 11;
  optional uint64       delivery_time = 12;
  optional DynamicContext dynamic_context = 13;
}

message BuyerAddition {
  optional int64 extra_fee = 1; // apply to ID, when show order info, shippingfee = buyer pay - extra_fee, extra_fee = extra_fee
  optional int64 promotion = 2; // apply to ID, must be a non-positive number, when show order info, shippingfee = buyer pay - promotion, promotion = promotion
}

message GetShippingFeeResponse {
  repeated uint64 fee_list = 1;  // 单位:本国货币
  optional double distance = 2;   // Distance 'origin'->'destination', unit:'m'
  optional BuyerAddition buyer_addition = 3;
  optional string request_id = 4;
}

message GetBuyerShippingFeeRequest{
  optional uint64 order_id = 1;
  required Location store_location = 2;
  required Location buyer_location = 3;
  optional StoreOption store_option = 4;
  optional ServiceType service_type = 5;
  optional SceneType   scene_type = 6;
  optional string      request_id = 7;
  optional uint64      buyer_id = 8;
  optional uint64      store_id = 9;
  optional DeliveryMode delivery_mode = 10;
  optional uint64       delivery_time = 11;
  optional DynamicContext dynamic_context = 12;
}

message MGetStoreListShippingFeeRequest{
  optional ServiceType service_type = 1;
  optional uint64      buyer_id = 2;
  required Location buyer_location = 3;
  repeated StoreDetail store_detail_list = 4;
}

message StoreDetail{
  optional uint64      store_id = 1;
  required Location store_location = 2;
  optional StoreOption store_option = 3;
  optional DeliveryMode delivery_mode = 4;
}

message OrderDetail{
  optional uint64 order_id = 1;
  required uint64 store_id = 2;  //用于统计drop off fee
  required Location store_location = 3;
  required Location buyer_location = 4;
  optional StoreOption store_option = 5;
  optional ServiceType service_type = 6;
}

message GetBuyerShippingFeeResponse{
  required uint64 buyer_base_fee = 1;
  required uint64 buyer_rising_fee = 2;
  required double distance = 3;
  optional BuyerAddition buyer_addition = 4;
  optional string request_id = 5;
}

message MGetStoreListShippingFeeResponse{
  map<uint64, StoreListShippingFee> shipping_fee_map = 1; // key: store_id
}

message StoreListShippingFee{
  required uint64 buyer_fee = 1;
  required double distance = 2;
}

enum RouteType {
  ROUTE_UNKNOWN = 0;
  ROUTE_PICKUP = 1;
  ROUTE_DELIVERY = 2;
  ROUTE_DRIVER = 3;
}

message Route {
  optional uint64 order_id = 1; //指订单的id
  optional RouteType route_type = 2;
  optional float latitude = 3;
  optional float longitude = 4;
}

message DriverInfo {
  optional uint64 driver_id = 1;
  optional uint32 driver_level = 2;
}

message GetDriverShippingFeeRequest{
  optional uint64 driver_delivery_order_id = 1;
  repeated OrderDetail order_detail_list = 2;
  repeated Route route_list = 3;
  optional ServiceType service_type = 4;
  optional DriverInfo driver_info = 5;
  optional uint64 slot_scheme_id = 6;
}

message GetDriverShippingFeeResponse {
  optional uint64 driver_total_base_earn = 1;
  optional uint64 driver_total_rising_earn = 2;
  optional double total_distance = 3;   // unit:'m'
  repeated SingleDriverShippingFee single_driver_shipping_fee_list = 4;
  repeated double routes_distance = 5;
}

message SingleDriverShippingFee {
  //total = base+rising
  required uint64 driver_base_earn = 1;
  required uint64 driver_rising_earn = 2;
  required double distance = 3;
}

message GetHubDriverShippingFeeConfigRequest {
  optional uint64   slot_scheme_id = 1;
}

message GetHubDriverShippingFeeConfigResponse {
  optional uint64 driver_min_shipping_fee = 1;
}

enum ModuleType {
  ModuleTypeBuyer = 1;
}

message GetMinShippingFeeRequest {
  required ModuleType  module_type    = 1;
  required ServiceType service_type   = 2;
  optional Location    store_location = 3;
  optional StoreOption store_option   = 4;
  optional uint64      store_id       = 5;
}

message GetMinShippingFeeResponse {
  optional uint64 min_shipping_fee = 1;
}
