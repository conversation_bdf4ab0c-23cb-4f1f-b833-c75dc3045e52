// Code generated by protoc-gen-go. DO NOT EDIT.
// source: foodalgo_shippingfee/foodalgo_shippingfee.proto

// 项目名 + “_” + 模块名

package foodalgo_shippingfee

import (
	fmt "fmt"
	_ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ServiceType int32

const (
	ServiceType_ShopeeFood       ServiceType = 1
	ServiceType_SPXInstantShopee ServiceType = 2
	ServiceType_SPXInstant       ServiceType = 3
)

var ServiceType_name = map[int32]string{
	1: "ShopeeFood",
	2: "SPXInstantShopee",
	3: "SPXInstant",
}

var ServiceType_value = map[string]int32{
	"ShopeeFood":       1,
	"SPXInstantShopee": 2,
	"SPXInstant":       3,
}

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return proto.EnumName(ServiceType_name, int32(x))
}

func (x *ServiceType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ServiceType_value, data, "ServiceType")
	if err != nil {
		return err
	}
	*x = ServiceType(value)
	return nil
}

func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{0}
}

type SceneType int32

const (
	SceneType_Not           SceneType = 0
	SceneType_BuyerCheckOut SceneType = 1
	SceneType_BuyerSubmit   SceneType = 2
	SceneType_DriverQuery   SceneType = 3
)

var SceneType_name = map[int32]string{
	0: "Not",
	1: "BuyerCheckOut",
	2: "BuyerSubmit",
	3: "DriverQuery",
}

var SceneType_value = map[string]int32{
	"Not":           0,
	"BuyerCheckOut": 1,
	"BuyerSubmit":   2,
	"DriverQuery":   3,
}

func (x SceneType) Enum() *SceneType {
	p := new(SceneType)
	*p = x
	return p
}

func (x SceneType) String() string {
	return proto.EnumName(SceneType_name, int32(x))
}

func (x *SceneType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SceneType_value, data, "SceneType")
	if err != nil {
		return err
	}
	*x = SceneType(value)
	return nil
}

func (SceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{1}
}

type ShippingFeeType int32

const (
	ShippingFeeType_BuyerFee         ShippingFeeType = 0
	ShippingFeeType_DriverEarn       ShippingFeeType = 1
	ShippingFeeType_BuyerRisingFee   ShippingFeeType = 2
	ShippingFeeType_DriverRisingEarn ShippingFeeType = 3
)

var ShippingFeeType_name = map[int32]string{
	0: "BuyerFee",
	1: "DriverEarn",
	2: "BuyerRisingFee",
	3: "DriverRisingEarn",
}

var ShippingFeeType_value = map[string]int32{
	"BuyerFee":         0,
	"DriverEarn":       1,
	"BuyerRisingFee":   2,
	"DriverRisingEarn": 3,
}

func (x ShippingFeeType) Enum() *ShippingFeeType {
	p := new(ShippingFeeType)
	*p = x
	return p
}

func (x ShippingFeeType) String() string {
	return proto.EnumName(ShippingFeeType_name, int32(x))
}

func (x *ShippingFeeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ShippingFeeType_value, data, "ShippingFeeType")
	if err != nil {
		return err
	}
	*x = ShippingFeeType(value)
	return nil
}

func (ShippingFeeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{2}
}

type DeliveryMode int32

const (
	DeliveryMode_DeliveryMode_Not DeliveryMode = 0
	DeliveryMode_Platform         DeliveryMode = 1
	DeliveryMode_Merchant         DeliveryMode = 2
)

var DeliveryMode_name = map[int32]string{
	0: "DeliveryMode_Not",
	1: "Platform",
	2: "Merchant",
}

var DeliveryMode_value = map[string]int32{
	"DeliveryMode_Not": 0,
	"Platform":         1,
	"Merchant":         2,
}

func (x DeliveryMode) Enum() *DeliveryMode {
	p := new(DeliveryMode)
	*p = x
	return p
}

func (x DeliveryMode) String() string {
	return proto.EnumName(DeliveryMode_name, int32(x))
}

func (x *DeliveryMode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DeliveryMode_value, data, "DeliveryMode")
	if err != nil {
		return err
	}
	*x = DeliveryMode(value)
	return nil
}

func (DeliveryMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{3}
}

type VatCalculate int32

const (
	VatCalculate_VAT_CALCULATE_UNKNOWN   VatCalculate = 0
	VatCalculate_VAT_CALCULATE_EXCLUSIVE VatCalculate = 1
	VatCalculate_VAT_CALCULATE_INCLUSIVE VatCalculate = 2
)

var VatCalculate_name = map[int32]string{
	0: "VAT_CALCULATE_UNKNOWN",
	1: "VAT_CALCULATE_EXCLUSIVE",
	2: "VAT_CALCULATE_INCLUSIVE",
}

var VatCalculate_value = map[string]int32{
	"VAT_CALCULATE_UNKNOWN":   0,
	"VAT_CALCULATE_EXCLUSIVE": 1,
	"VAT_CALCULATE_INCLUSIVE": 2,
}

func (x VatCalculate) Enum() *VatCalculate {
	p := new(VatCalculate)
	*p = x
	return p
}

func (x VatCalculate) String() string {
	return proto.EnumName(VatCalculate_name, int32(x))
}

func (x *VatCalculate) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(VatCalculate_value, data, "VatCalculate")
	if err != nil {
		return err
	}
	*x = VatCalculate(value)
	return nil
}

func (VatCalculate) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{4}
}

type RouteType int32

const (
	RouteType_ROUTE_UNKNOWN  RouteType = 0
	RouteType_ROUTE_PICKUP   RouteType = 1
	RouteType_ROUTE_DELIVERY RouteType = 2
	RouteType_ROUTE_DRIVER   RouteType = 3
)

var RouteType_name = map[int32]string{
	0: "ROUTE_UNKNOWN",
	1: "ROUTE_PICKUP",
	2: "ROUTE_DELIVERY",
	3: "ROUTE_DRIVER",
}

var RouteType_value = map[string]int32{
	"ROUTE_UNKNOWN":  0,
	"ROUTE_PICKUP":   1,
	"ROUTE_DELIVERY": 2,
	"ROUTE_DRIVER":   3,
}

func (x RouteType) Enum() *RouteType {
	p := new(RouteType)
	*p = x
	return p
}

func (x RouteType) String() string {
	return proto.EnumName(RouteType_name, int32(x))
}

func (x *RouteType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RouteType_value, data, "RouteType")
	if err != nil {
		return err
	}
	*x = RouteType(value)
	return nil
}

func (RouteType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{5}
}

type ModuleType int32

const (
	ModuleType_ModuleTypeBuyer ModuleType = 1
)

var ModuleType_name = map[int32]string{
	1: "ModuleTypeBuyer",
}

var ModuleType_value = map[string]int32{
	"ModuleTypeBuyer": 1,
}

func (x ModuleType) Enum() *ModuleType {
	p := new(ModuleType)
	*p = x
	return p
}

func (x ModuleType) String() string {
	return proto.EnumName(ModuleType_name, int32(x))
}

func (x *ModuleType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ModuleType_value, data, "ModuleType")
	if err != nil {
		return err
	}
	*x = ModuleType(value)
	return nil
}

func (ModuleType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{6}
}

type StoreOption_PartnerType int32

const (
	StoreOption_NonPartner StoreOption_PartnerType = 1
	StoreOption_Partner    StoreOption_PartnerType = 2
)

var StoreOption_PartnerType_name = map[int32]string{
	1: "NonPartner",
	2: "Partner",
}

var StoreOption_PartnerType_value = map[string]int32{
	"NonPartner": 1,
	"Partner":    2,
}

func (x StoreOption_PartnerType) Enum() *StoreOption_PartnerType {
	p := new(StoreOption_PartnerType)
	*p = x
	return p
}

func (x StoreOption_PartnerType) String() string {
	return proto.EnumName(StoreOption_PartnerType_name, int32(x))
}

func (x *StoreOption_PartnerType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StoreOption_PartnerType_value, data, "StoreOption_PartnerType")
	if err != nil {
		return err
	}
	*x = StoreOption_PartnerType(value)
	return nil
}

func (StoreOption_PartnerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{1, 0}
}

type Location struct {
	State                *string  `protobuf:"bytes,1,opt,name=state" json:"state,omitempty"`
	City                 *string  `protobuf:"bytes,2,opt,name=city" json:"city,omitempty"`
	District             *string  `protobuf:"bytes,3,opt,name=district" json:"district,omitempty"`
	Address              *string  `protobuf:"bytes,4,opt,name=address" json:"address,omitempty"`
	Latitude             *float32 `protobuf:"fixed32,5,opt,name=latitude" json:"latitude,omitempty"`
	Longitude            *float32 `protobuf:"fixed32,6,opt,name=longitude" json:"longitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Location) Reset()         { *m = Location{} }
func (m *Location) String() string { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()    {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{0}
}

func (m *Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Location.Unmarshal(m, b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Location.Marshal(b, m, deterministic)
}
func (m *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(m, src)
}
func (m *Location) XXX_Size() int {
	return xxx_messageInfo_Location.Size(m)
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetState() string {
	if m != nil && m.State != nil {
		return *m.State
	}
	return ""
}

func (m *Location) GetCity() string {
	if m != nil && m.City != nil {
		return *m.City
	}
	return ""
}

func (m *Location) GetDistrict() string {
	if m != nil && m.District != nil {
		return *m.District
	}
	return ""
}

func (m *Location) GetAddress() string {
	if m != nil && m.Address != nil {
		return *m.Address
	}
	return ""
}

func (m *Location) GetLatitude() float32 {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return 0
}

func (m *Location) GetLongitude() float32 {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return 0
}

type StoreOption struct {
	PartnerType          *StoreOption_PartnerType `protobuf:"varint,1,opt,name=partner_type,json=partnerType,enum=foodalgo_shippingfee.StoreOption_PartnerType" json:"partner_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *StoreOption) Reset()         { *m = StoreOption{} }
func (m *StoreOption) String() string { return proto.CompactTextString(m) }
func (*StoreOption) ProtoMessage()    {}
func (*StoreOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{1}
}

func (m *StoreOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreOption.Unmarshal(m, b)
}
func (m *StoreOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreOption.Marshal(b, m, deterministic)
}
func (m *StoreOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreOption.Merge(m, src)
}
func (m *StoreOption) XXX_Size() int {
	return xxx_messageInfo_StoreOption.Size(m)
}
func (m *StoreOption) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreOption.DiscardUnknown(m)
}

var xxx_messageInfo_StoreOption proto.InternalMessageInfo

func (m *StoreOption) GetPartnerType() StoreOption_PartnerType {
	if m != nil && m.PartnerType != nil {
		return *m.PartnerType
	}
	return StoreOption_NonPartner
}

type DynamicContext struct {
	BuyerInfo            *BuyerInfo   `protobuf:"bytes,1,opt,name=buyer_info,json=buyerInfo" json:"buyer_info,omitempty"`
	OrderItems           []*OrderItem `protobuf:"bytes,2,rep,name=order_items,json=orderItems" json:"order_items,omitempty"`
	Amount               *Amount      `protobuf:"bytes,3,opt,name=amount" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *DynamicContext) Reset()         { *m = DynamicContext{} }
func (m *DynamicContext) String() string { return proto.CompactTextString(m) }
func (*DynamicContext) ProtoMessage()    {}
func (*DynamicContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{2}
}

func (m *DynamicContext) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DynamicContext.Unmarshal(m, b)
}
func (m *DynamicContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DynamicContext.Marshal(b, m, deterministic)
}
func (m *DynamicContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DynamicContext.Merge(m, src)
}
func (m *DynamicContext) XXX_Size() int {
	return xxx_messageInfo_DynamicContext.Size(m)
}
func (m *DynamicContext) XXX_DiscardUnknown() {
	xxx_messageInfo_DynamicContext.DiscardUnknown(m)
}

var xxx_messageInfo_DynamicContext proto.InternalMessageInfo

func (m *DynamicContext) GetBuyerInfo() *BuyerInfo {
	if m != nil {
		return m.BuyerInfo
	}
	return nil
}

func (m *DynamicContext) GetOrderItems() []*OrderItem {
	if m != nil {
		return m.OrderItems
	}
	return nil
}

func (m *DynamicContext) GetAmount() *Amount {
	if m != nil {
		return m.Amount
	}
	return nil
}

type BuyerInfo struct {
	PhoneBrand           *string  `protobuf:"bytes,1,opt,name=phone_brand,json=phoneBrand" json:"phone_brand,omitempty"`
	PhoneModel           *string  `protobuf:"bytes,2,opt,name=phone_model,json=phoneModel" json:"phone_model,omitempty"`
	PhoneSystem          *string  `protobuf:"bytes,3,opt,name=phone_system,json=phoneSystem" json:"phone_system,omitempty"`
	PhoneWifi            *string  `protobuf:"bytes,4,opt,name=phone_wifi,json=phoneWifi" json:"phone_wifi,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyerInfo) Reset()         { *m = BuyerInfo{} }
func (m *BuyerInfo) String() string { return proto.CompactTextString(m) }
func (*BuyerInfo) ProtoMessage()    {}
func (*BuyerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{3}
}

func (m *BuyerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyerInfo.Unmarshal(m, b)
}
func (m *BuyerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyerInfo.Marshal(b, m, deterministic)
}
func (m *BuyerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyerInfo.Merge(m, src)
}
func (m *BuyerInfo) XXX_Size() int {
	return xxx_messageInfo_BuyerInfo.Size(m)
}
func (m *BuyerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BuyerInfo proto.InternalMessageInfo

func (m *BuyerInfo) GetPhoneBrand() string {
	if m != nil && m.PhoneBrand != nil {
		return *m.PhoneBrand
	}
	return ""
}

func (m *BuyerInfo) GetPhoneModel() string {
	if m != nil && m.PhoneModel != nil {
		return *m.PhoneModel
	}
	return ""
}

func (m *BuyerInfo) GetPhoneSystem() string {
	if m != nil && m.PhoneSystem != nil {
		return *m.PhoneSystem
	}
	return ""
}

func (m *BuyerInfo) GetPhoneWifi() string {
	if m != nil && m.PhoneWifi != nil {
		return *m.PhoneWifi
	}
	return ""
}

type Amount struct {
	ItemSubtotal               *uint64       `protobuf:"varint,1,opt,name=item_subtotal,json=itemSubtotal" json:"item_subtotal,omitempty"`
	MerchantCommissionRate     *uint64       `protobuf:"varint,2,opt,name=merchant_commission_rate,json=merchantCommissionRate" json:"merchant_commission_rate,omitempty"`
	MerchantFoodVoucherSubsidy *uint64       `protobuf:"varint,3,opt,name=merchant_food_voucher_subsidy,json=merchantFoodVoucherSubsidy" json:"merchant_food_voucher_subsidy,omitempty"`
	TaxRate                    *uint64       `protobuf:"varint,4,opt,name=tax_rate,json=taxRate" json:"tax_rate,omitempty"`
	ServiceChargeFeeRate       *uint64       `protobuf:"varint,5,opt,name=service_charge_fee_rate,json=serviceChargeFeeRate" json:"service_charge_fee_rate,omitempty"`
	VatRate                    *uint64       `protobuf:"varint,6,opt,name=vat_rate,json=vatRate" json:"vat_rate,omitempty"`
	VatCalculate               *VatCalculate `protobuf:"varint,7,opt,name=vat_calculate,json=vatCalculate,enum=foodalgo_shippingfee.VatCalculate" json:"vat_calculate,omitempty"`
	MerchantServiceFee         *uint64       `protobuf:"varint,8,opt,name=merchant_service_fee,json=merchantServiceFee" json:"merchant_service_fee,omitempty"`
	PlatformServiceFee         *uint64       `protobuf:"varint,9,opt,name=platform_service_fee,json=platformServiceFee" json:"platform_service_fee,omitempty"`
	SmallOrderFee              *uint64       `protobuf:"varint,10,opt,name=small_order_fee,json=smallOrderFee" json:"small_order_fee,omitempty"`
	XXX_NoUnkeyedLiteral       struct{}      `json:"-"`
	XXX_unrecognized           []byte        `json:"-"`
	XXX_sizecache              int32         `json:"-"`
}

func (m *Amount) Reset()         { *m = Amount{} }
func (m *Amount) String() string { return proto.CompactTextString(m) }
func (*Amount) ProtoMessage()    {}
func (*Amount) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{4}
}

func (m *Amount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Amount.Unmarshal(m, b)
}
func (m *Amount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Amount.Marshal(b, m, deterministic)
}
func (m *Amount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Amount.Merge(m, src)
}
func (m *Amount) XXX_Size() int {
	return xxx_messageInfo_Amount.Size(m)
}
func (m *Amount) XXX_DiscardUnknown() {
	xxx_messageInfo_Amount.DiscardUnknown(m)
}

var xxx_messageInfo_Amount proto.InternalMessageInfo

func (m *Amount) GetItemSubtotal() uint64 {
	if m != nil && m.ItemSubtotal != nil {
		return *m.ItemSubtotal
	}
	return 0
}

func (m *Amount) GetMerchantCommissionRate() uint64 {
	if m != nil && m.MerchantCommissionRate != nil {
		return *m.MerchantCommissionRate
	}
	return 0
}

func (m *Amount) GetMerchantFoodVoucherSubsidy() uint64 {
	if m != nil && m.MerchantFoodVoucherSubsidy != nil {
		return *m.MerchantFoodVoucherSubsidy
	}
	return 0
}

func (m *Amount) GetTaxRate() uint64 {
	if m != nil && m.TaxRate != nil {
		return *m.TaxRate
	}
	return 0
}

func (m *Amount) GetServiceChargeFeeRate() uint64 {
	if m != nil && m.ServiceChargeFeeRate != nil {
		return *m.ServiceChargeFeeRate
	}
	return 0
}

func (m *Amount) GetVatRate() uint64 {
	if m != nil && m.VatRate != nil {
		return *m.VatRate
	}
	return 0
}

func (m *Amount) GetVatCalculate() VatCalculate {
	if m != nil && m.VatCalculate != nil {
		return *m.VatCalculate
	}
	return VatCalculate_VAT_CALCULATE_UNKNOWN
}

func (m *Amount) GetMerchantServiceFee() uint64 {
	if m != nil && m.MerchantServiceFee != nil {
		return *m.MerchantServiceFee
	}
	return 0
}

func (m *Amount) GetPlatformServiceFee() uint64 {
	if m != nil && m.PlatformServiceFee != nil {
		return *m.PlatformServiceFee
	}
	return 0
}

func (m *Amount) GetSmallOrderFee() uint64 {
	if m != nil && m.SmallOrderFee != nil {
		return *m.SmallOrderFee
	}
	return 0
}

type OrderItem struct {
	DishId               *uint64  `protobuf:"varint,1,opt,name=dish_id,json=dishId" json:"dish_id,omitempty"`
	Quantity             *uint32  `protobuf:"varint,2,opt,name=quantity" json:"quantity,omitempty"`
	UnitPrice            *uint64  `protobuf:"varint,3,opt,name=unit_price,json=unitPrice" json:"unit_price,omitempty"`
	UnitListPrice        *uint64  `protobuf:"varint,4,opt,name=unit_list_price,json=unitListPrice" json:"unit_list_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderItem) Reset()         { *m = OrderItem{} }
func (m *OrderItem) String() string { return proto.CompactTextString(m) }
func (*OrderItem) ProtoMessage()    {}
func (*OrderItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{5}
}

func (m *OrderItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderItem.Unmarshal(m, b)
}
func (m *OrderItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderItem.Marshal(b, m, deterministic)
}
func (m *OrderItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderItem.Merge(m, src)
}
func (m *OrderItem) XXX_Size() int {
	return xxx_messageInfo_OrderItem.Size(m)
}
func (m *OrderItem) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderItem.DiscardUnknown(m)
}

var xxx_messageInfo_OrderItem proto.InternalMessageInfo

func (m *OrderItem) GetDishId() uint64 {
	if m != nil && m.DishId != nil {
		return *m.DishId
	}
	return 0
}

func (m *OrderItem) GetQuantity() uint32 {
	if m != nil && m.Quantity != nil {
		return *m.Quantity
	}
	return 0
}

func (m *OrderItem) GetUnitPrice() uint64 {
	if m != nil && m.UnitPrice != nil {
		return *m.UnitPrice
	}
	return 0
}

func (m *OrderItem) GetUnitListPrice() uint64 {
	if m != nil && m.UnitListPrice != nil {
		return *m.UnitListPrice
	}
	return 0
}

type GetShippingFeeRequest struct {
	OrderId              *uint64           `protobuf:"varint,1,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	FeeTypeList          []ShippingFeeType `protobuf:"varint,2,rep,name=fee_type_list,json=feeTypeList,enum=foodalgo_shippingfee.ShippingFeeType" json:"fee_type_list,omitempty"`
	Origin               *Location         `protobuf:"bytes,3,req,name=origin" json:"origin,omitempty"`
	Destination          *Location         `protobuf:"bytes,4,req,name=destination" json:"destination,omitempty"`
	StoreOption          *StoreOption      `protobuf:"bytes,5,opt,name=store_option,json=storeOption" json:"store_option,omitempty"`
	ServiceType          *ServiceType      `protobuf:"varint,6,opt,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	SceneType            *SceneType        `protobuf:"varint,7,opt,name=scene_type,json=sceneType,enum=foodalgo_shippingfee.SceneType" json:"scene_type,omitempty"`
	RequestId            *string           `protobuf:"bytes,8,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	BuyerId              *uint64           `protobuf:"varint,9,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	StoreId              *uint64           `protobuf:"varint,10,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	DeliveryMode         *DeliveryMode     `protobuf:"varint,11,opt,name=delivery_mode,json=deliveryMode,enum=foodalgo_shippingfee.DeliveryMode" json:"delivery_mode,omitempty"`
	DeliveryTime         *uint64           `protobuf:"varint,12,opt,name=delivery_time,json=deliveryTime" json:"delivery_time,omitempty"`
	DynamicContext       *DynamicContext   `protobuf:"bytes,13,opt,name=dynamic_context,json=dynamicContext" json:"dynamic_context,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetShippingFeeRequest) Reset()         { *m = GetShippingFeeRequest{} }
func (m *GetShippingFeeRequest) String() string { return proto.CompactTextString(m) }
func (*GetShippingFeeRequest) ProtoMessage()    {}
func (*GetShippingFeeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{6}
}

func (m *GetShippingFeeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShippingFeeRequest.Unmarshal(m, b)
}
func (m *GetShippingFeeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShippingFeeRequest.Marshal(b, m, deterministic)
}
func (m *GetShippingFeeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShippingFeeRequest.Merge(m, src)
}
func (m *GetShippingFeeRequest) XXX_Size() int {
	return xxx_messageInfo_GetShippingFeeRequest.Size(m)
}
func (m *GetShippingFeeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShippingFeeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShippingFeeRequest proto.InternalMessageInfo

func (m *GetShippingFeeRequest) GetOrderId() uint64 {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return 0
}

func (m *GetShippingFeeRequest) GetFeeTypeList() []ShippingFeeType {
	if m != nil {
		return m.FeeTypeList
	}
	return nil
}

func (m *GetShippingFeeRequest) GetOrigin() *Location {
	if m != nil {
		return m.Origin
	}
	return nil
}

func (m *GetShippingFeeRequest) GetDestination() *Location {
	if m != nil {
		return m.Destination
	}
	return nil
}

func (m *GetShippingFeeRequest) GetStoreOption() *StoreOption {
	if m != nil {
		return m.StoreOption
	}
	return nil
}

func (m *GetShippingFeeRequest) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

func (m *GetShippingFeeRequest) GetSceneType() SceneType {
	if m != nil && m.SceneType != nil {
		return *m.SceneType
	}
	return SceneType_Not
}

func (m *GetShippingFeeRequest) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

func (m *GetShippingFeeRequest) GetBuyerId() uint64 {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return 0
}

func (m *GetShippingFeeRequest) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *GetShippingFeeRequest) GetDeliveryMode() DeliveryMode {
	if m != nil && m.DeliveryMode != nil {
		return *m.DeliveryMode
	}
	return DeliveryMode_DeliveryMode_Not
}

func (m *GetShippingFeeRequest) GetDeliveryTime() uint64 {
	if m != nil && m.DeliveryTime != nil {
		return *m.DeliveryTime
	}
	return 0
}

func (m *GetShippingFeeRequest) GetDynamicContext() *DynamicContext {
	if m != nil {
		return m.DynamicContext
	}
	return nil
}

type BuyerAddition struct {
	ExtraFee             *int64   `protobuf:"varint,1,opt,name=extra_fee,json=extraFee" json:"extra_fee,omitempty"`
	Promotion            *int64   `protobuf:"varint,2,opt,name=promotion" json:"promotion,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyerAddition) Reset()         { *m = BuyerAddition{} }
func (m *BuyerAddition) String() string { return proto.CompactTextString(m) }
func (*BuyerAddition) ProtoMessage()    {}
func (*BuyerAddition) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{7}
}

func (m *BuyerAddition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyerAddition.Unmarshal(m, b)
}
func (m *BuyerAddition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyerAddition.Marshal(b, m, deterministic)
}
func (m *BuyerAddition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyerAddition.Merge(m, src)
}
func (m *BuyerAddition) XXX_Size() int {
	return xxx_messageInfo_BuyerAddition.Size(m)
}
func (m *BuyerAddition) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyerAddition.DiscardUnknown(m)
}

var xxx_messageInfo_BuyerAddition proto.InternalMessageInfo

func (m *BuyerAddition) GetExtraFee() int64 {
	if m != nil && m.ExtraFee != nil {
		return *m.ExtraFee
	}
	return 0
}

func (m *BuyerAddition) GetPromotion() int64 {
	if m != nil && m.Promotion != nil {
		return *m.Promotion
	}
	return 0
}

type GetShippingFeeResponse struct {
	FeeList              []uint64       `protobuf:"varint,1,rep,name=fee_list,json=feeList" json:"fee_list,omitempty"`
	Distance             *float64       `protobuf:"fixed64,2,opt,name=distance" json:"distance,omitempty"`
	BuyerAddition        *BuyerAddition `protobuf:"bytes,3,opt,name=buyer_addition,json=buyerAddition" json:"buyer_addition,omitempty"`
	RequestId            *string        `protobuf:"bytes,4,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetShippingFeeResponse) Reset()         { *m = GetShippingFeeResponse{} }
func (m *GetShippingFeeResponse) String() string { return proto.CompactTextString(m) }
func (*GetShippingFeeResponse) ProtoMessage()    {}
func (*GetShippingFeeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{8}
}

func (m *GetShippingFeeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShippingFeeResponse.Unmarshal(m, b)
}
func (m *GetShippingFeeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShippingFeeResponse.Marshal(b, m, deterministic)
}
func (m *GetShippingFeeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShippingFeeResponse.Merge(m, src)
}
func (m *GetShippingFeeResponse) XXX_Size() int {
	return xxx_messageInfo_GetShippingFeeResponse.Size(m)
}
func (m *GetShippingFeeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShippingFeeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShippingFeeResponse proto.InternalMessageInfo

func (m *GetShippingFeeResponse) GetFeeList() []uint64 {
	if m != nil {
		return m.FeeList
	}
	return nil
}

func (m *GetShippingFeeResponse) GetDistance() float64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *GetShippingFeeResponse) GetBuyerAddition() *BuyerAddition {
	if m != nil {
		return m.BuyerAddition
	}
	return nil
}

func (m *GetShippingFeeResponse) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

type GetBuyerShippingFeeRequest struct {
	OrderId              *uint64         `protobuf:"varint,1,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	StoreLocation        *Location       `protobuf:"bytes,2,req,name=store_location,json=storeLocation" json:"store_location,omitempty"`
	BuyerLocation        *Location       `protobuf:"bytes,3,req,name=buyer_location,json=buyerLocation" json:"buyer_location,omitempty"`
	StoreOption          *StoreOption    `protobuf:"bytes,4,opt,name=store_option,json=storeOption" json:"store_option,omitempty"`
	ServiceType          *ServiceType    `protobuf:"varint,5,opt,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	SceneType            *SceneType      `protobuf:"varint,6,opt,name=scene_type,json=sceneType,enum=foodalgo_shippingfee.SceneType" json:"scene_type,omitempty"`
	RequestId            *string         `protobuf:"bytes,7,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	BuyerId              *uint64         `protobuf:"varint,8,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	StoreId              *uint64         `protobuf:"varint,9,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	DeliveryMode         *DeliveryMode   `protobuf:"varint,10,opt,name=delivery_mode,json=deliveryMode,enum=foodalgo_shippingfee.DeliveryMode" json:"delivery_mode,omitempty"`
	DeliveryTime         *uint64         `protobuf:"varint,11,opt,name=delivery_time,json=deliveryTime" json:"delivery_time,omitempty"`
	DynamicContext       *DynamicContext `protobuf:"bytes,12,opt,name=dynamic_context,json=dynamicContext" json:"dynamic_context,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBuyerShippingFeeRequest) Reset()         { *m = GetBuyerShippingFeeRequest{} }
func (m *GetBuyerShippingFeeRequest) String() string { return proto.CompactTextString(m) }
func (*GetBuyerShippingFeeRequest) ProtoMessage()    {}
func (*GetBuyerShippingFeeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{9}
}

func (m *GetBuyerShippingFeeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyerShippingFeeRequest.Unmarshal(m, b)
}
func (m *GetBuyerShippingFeeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyerShippingFeeRequest.Marshal(b, m, deterministic)
}
func (m *GetBuyerShippingFeeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyerShippingFeeRequest.Merge(m, src)
}
func (m *GetBuyerShippingFeeRequest) XXX_Size() int {
	return xxx_messageInfo_GetBuyerShippingFeeRequest.Size(m)
}
func (m *GetBuyerShippingFeeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyerShippingFeeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyerShippingFeeRequest proto.InternalMessageInfo

func (m *GetBuyerShippingFeeRequest) GetOrderId() uint64 {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return 0
}

func (m *GetBuyerShippingFeeRequest) GetStoreLocation() *Location {
	if m != nil {
		return m.StoreLocation
	}
	return nil
}

func (m *GetBuyerShippingFeeRequest) GetBuyerLocation() *Location {
	if m != nil {
		return m.BuyerLocation
	}
	return nil
}

func (m *GetBuyerShippingFeeRequest) GetStoreOption() *StoreOption {
	if m != nil {
		return m.StoreOption
	}
	return nil
}

func (m *GetBuyerShippingFeeRequest) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

func (m *GetBuyerShippingFeeRequest) GetSceneType() SceneType {
	if m != nil && m.SceneType != nil {
		return *m.SceneType
	}
	return SceneType_Not
}

func (m *GetBuyerShippingFeeRequest) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

func (m *GetBuyerShippingFeeRequest) GetBuyerId() uint64 {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return 0
}

func (m *GetBuyerShippingFeeRequest) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *GetBuyerShippingFeeRequest) GetDeliveryMode() DeliveryMode {
	if m != nil && m.DeliveryMode != nil {
		return *m.DeliveryMode
	}
	return DeliveryMode_DeliveryMode_Not
}

func (m *GetBuyerShippingFeeRequest) GetDeliveryTime() uint64 {
	if m != nil && m.DeliveryTime != nil {
		return *m.DeliveryTime
	}
	return 0
}

func (m *GetBuyerShippingFeeRequest) GetDynamicContext() *DynamicContext {
	if m != nil {
		return m.DynamicContext
	}
	return nil
}

type MGetStoreListShippingFeeRequest struct {
	ServiceType          *ServiceType   `protobuf:"varint,1,opt,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	BuyerId              *uint64        `protobuf:"varint,2,opt,name=buyer_id,json=buyerId" json:"buyer_id,omitempty"`
	BuyerLocation        *Location      `protobuf:"bytes,3,req,name=buyer_location,json=buyerLocation" json:"buyer_location,omitempty"`
	StoreDetailList      []*StoreDetail `protobuf:"bytes,4,rep,name=store_detail_list,json=storeDetailList" json:"store_detail_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *MGetStoreListShippingFeeRequest) Reset()         { *m = MGetStoreListShippingFeeRequest{} }
func (m *MGetStoreListShippingFeeRequest) String() string { return proto.CompactTextString(m) }
func (*MGetStoreListShippingFeeRequest) ProtoMessage()    {}
func (*MGetStoreListShippingFeeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{10}
}

func (m *MGetStoreListShippingFeeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MGetStoreListShippingFeeRequest.Unmarshal(m, b)
}
func (m *MGetStoreListShippingFeeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MGetStoreListShippingFeeRequest.Marshal(b, m, deterministic)
}
func (m *MGetStoreListShippingFeeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetStoreListShippingFeeRequest.Merge(m, src)
}
func (m *MGetStoreListShippingFeeRequest) XXX_Size() int {
	return xxx_messageInfo_MGetStoreListShippingFeeRequest.Size(m)
}
func (m *MGetStoreListShippingFeeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetStoreListShippingFeeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MGetStoreListShippingFeeRequest proto.InternalMessageInfo

func (m *MGetStoreListShippingFeeRequest) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

func (m *MGetStoreListShippingFeeRequest) GetBuyerId() uint64 {
	if m != nil && m.BuyerId != nil {
		return *m.BuyerId
	}
	return 0
}

func (m *MGetStoreListShippingFeeRequest) GetBuyerLocation() *Location {
	if m != nil {
		return m.BuyerLocation
	}
	return nil
}

func (m *MGetStoreListShippingFeeRequest) GetStoreDetailList() []*StoreDetail {
	if m != nil {
		return m.StoreDetailList
	}
	return nil
}

type StoreDetail struct {
	StoreId              *uint64       `protobuf:"varint,1,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	StoreLocation        *Location     `protobuf:"bytes,2,req,name=store_location,json=storeLocation" json:"store_location,omitempty"`
	StoreOption          *StoreOption  `protobuf:"bytes,3,opt,name=store_option,json=storeOption" json:"store_option,omitempty"`
	DeliveryMode         *DeliveryMode `protobuf:"varint,4,opt,name=delivery_mode,json=deliveryMode,enum=foodalgo_shippingfee.DeliveryMode" json:"delivery_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StoreDetail) Reset()         { *m = StoreDetail{} }
func (m *StoreDetail) String() string { return proto.CompactTextString(m) }
func (*StoreDetail) ProtoMessage()    {}
func (*StoreDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{11}
}

func (m *StoreDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreDetail.Unmarshal(m, b)
}
func (m *StoreDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreDetail.Marshal(b, m, deterministic)
}
func (m *StoreDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreDetail.Merge(m, src)
}
func (m *StoreDetail) XXX_Size() int {
	return xxx_messageInfo_StoreDetail.Size(m)
}
func (m *StoreDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreDetail.DiscardUnknown(m)
}

var xxx_messageInfo_StoreDetail proto.InternalMessageInfo

func (m *StoreDetail) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *StoreDetail) GetStoreLocation() *Location {
	if m != nil {
		return m.StoreLocation
	}
	return nil
}

func (m *StoreDetail) GetStoreOption() *StoreOption {
	if m != nil {
		return m.StoreOption
	}
	return nil
}

func (m *StoreDetail) GetDeliveryMode() DeliveryMode {
	if m != nil && m.DeliveryMode != nil {
		return *m.DeliveryMode
	}
	return DeliveryMode_DeliveryMode_Not
}

type OrderDetail struct {
	OrderId              *uint64      `protobuf:"varint,1,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	StoreId              *uint64      `protobuf:"varint,2,req,name=store_id,json=storeId" json:"store_id,omitempty"`
	StoreLocation        *Location    `protobuf:"bytes,3,req,name=store_location,json=storeLocation" json:"store_location,omitempty"`
	BuyerLocation        *Location    `protobuf:"bytes,4,req,name=buyer_location,json=buyerLocation" json:"buyer_location,omitempty"`
	StoreOption          *StoreOption `protobuf:"bytes,5,opt,name=store_option,json=storeOption" json:"store_option,omitempty"`
	ServiceType          *ServiceType `protobuf:"varint,6,opt,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *OrderDetail) Reset()         { *m = OrderDetail{} }
func (m *OrderDetail) String() string { return proto.CompactTextString(m) }
func (*OrderDetail) ProtoMessage()    {}
func (*OrderDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{12}
}

func (m *OrderDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderDetail.Unmarshal(m, b)
}
func (m *OrderDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderDetail.Marshal(b, m, deterministic)
}
func (m *OrderDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderDetail.Merge(m, src)
}
func (m *OrderDetail) XXX_Size() int {
	return xxx_messageInfo_OrderDetail.Size(m)
}
func (m *OrderDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderDetail.DiscardUnknown(m)
}

var xxx_messageInfo_OrderDetail proto.InternalMessageInfo

func (m *OrderDetail) GetOrderId() uint64 {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return 0
}

func (m *OrderDetail) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *OrderDetail) GetStoreLocation() *Location {
	if m != nil {
		return m.StoreLocation
	}
	return nil
}

func (m *OrderDetail) GetBuyerLocation() *Location {
	if m != nil {
		return m.BuyerLocation
	}
	return nil
}

func (m *OrderDetail) GetStoreOption() *StoreOption {
	if m != nil {
		return m.StoreOption
	}
	return nil
}

func (m *OrderDetail) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

type GetBuyerShippingFeeResponse struct {
	BuyerBaseFee         *uint64        `protobuf:"varint,1,req,name=buyer_base_fee,json=buyerBaseFee" json:"buyer_base_fee,omitempty"`
	BuyerRisingFee       *uint64        `protobuf:"varint,2,req,name=buyer_rising_fee,json=buyerRisingFee" json:"buyer_rising_fee,omitempty"`
	Distance             *float64       `protobuf:"fixed64,3,req,name=distance" json:"distance,omitempty"`
	BuyerAddition        *BuyerAddition `protobuf:"bytes,4,opt,name=buyer_addition,json=buyerAddition" json:"buyer_addition,omitempty"`
	RequestId            *string        `protobuf:"bytes,5,opt,name=request_id,json=requestId" json:"request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetBuyerShippingFeeResponse) Reset()         { *m = GetBuyerShippingFeeResponse{} }
func (m *GetBuyerShippingFeeResponse) String() string { return proto.CompactTextString(m) }
func (*GetBuyerShippingFeeResponse) ProtoMessage()    {}
func (*GetBuyerShippingFeeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{13}
}

func (m *GetBuyerShippingFeeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBuyerShippingFeeResponse.Unmarshal(m, b)
}
func (m *GetBuyerShippingFeeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBuyerShippingFeeResponse.Marshal(b, m, deterministic)
}
func (m *GetBuyerShippingFeeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBuyerShippingFeeResponse.Merge(m, src)
}
func (m *GetBuyerShippingFeeResponse) XXX_Size() int {
	return xxx_messageInfo_GetBuyerShippingFeeResponse.Size(m)
}
func (m *GetBuyerShippingFeeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBuyerShippingFeeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBuyerShippingFeeResponse proto.InternalMessageInfo

func (m *GetBuyerShippingFeeResponse) GetBuyerBaseFee() uint64 {
	if m != nil && m.BuyerBaseFee != nil {
		return *m.BuyerBaseFee
	}
	return 0
}

func (m *GetBuyerShippingFeeResponse) GetBuyerRisingFee() uint64 {
	if m != nil && m.BuyerRisingFee != nil {
		return *m.BuyerRisingFee
	}
	return 0
}

func (m *GetBuyerShippingFeeResponse) GetDistance() float64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *GetBuyerShippingFeeResponse) GetBuyerAddition() *BuyerAddition {
	if m != nil {
		return m.BuyerAddition
	}
	return nil
}

func (m *GetBuyerShippingFeeResponse) GetRequestId() string {
	if m != nil && m.RequestId != nil {
		return *m.RequestId
	}
	return ""
}

type MGetStoreListShippingFeeResponse struct {
	ShippingFeeMap       map[uint64]*StoreListShippingFee `protobuf:"bytes,1,rep,name=shipping_fee_map,json=shippingFeeMap" json:"shipping_fee_map,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *MGetStoreListShippingFeeResponse) Reset()         { *m = MGetStoreListShippingFeeResponse{} }
func (m *MGetStoreListShippingFeeResponse) String() string { return proto.CompactTextString(m) }
func (*MGetStoreListShippingFeeResponse) ProtoMessage()    {}
func (*MGetStoreListShippingFeeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{14}
}

func (m *MGetStoreListShippingFeeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MGetStoreListShippingFeeResponse.Unmarshal(m, b)
}
func (m *MGetStoreListShippingFeeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MGetStoreListShippingFeeResponse.Marshal(b, m, deterministic)
}
func (m *MGetStoreListShippingFeeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetStoreListShippingFeeResponse.Merge(m, src)
}
func (m *MGetStoreListShippingFeeResponse) XXX_Size() int {
	return xxx_messageInfo_MGetStoreListShippingFeeResponse.Size(m)
}
func (m *MGetStoreListShippingFeeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetStoreListShippingFeeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MGetStoreListShippingFeeResponse proto.InternalMessageInfo

func (m *MGetStoreListShippingFeeResponse) GetShippingFeeMap() map[uint64]*StoreListShippingFee {
	if m != nil {
		return m.ShippingFeeMap
	}
	return nil
}

type StoreListShippingFee struct {
	BuyerFee             *uint64  `protobuf:"varint,1,req,name=buyer_fee,json=buyerFee" json:"buyer_fee,omitempty"`
	Distance             *float64 `protobuf:"fixed64,2,req,name=distance" json:"distance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreListShippingFee) Reset()         { *m = StoreListShippingFee{} }
func (m *StoreListShippingFee) String() string { return proto.CompactTextString(m) }
func (*StoreListShippingFee) ProtoMessage()    {}
func (*StoreListShippingFee) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{15}
}

func (m *StoreListShippingFee) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreListShippingFee.Unmarshal(m, b)
}
func (m *StoreListShippingFee) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreListShippingFee.Marshal(b, m, deterministic)
}
func (m *StoreListShippingFee) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreListShippingFee.Merge(m, src)
}
func (m *StoreListShippingFee) XXX_Size() int {
	return xxx_messageInfo_StoreListShippingFee.Size(m)
}
func (m *StoreListShippingFee) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreListShippingFee.DiscardUnknown(m)
}

var xxx_messageInfo_StoreListShippingFee proto.InternalMessageInfo

func (m *StoreListShippingFee) GetBuyerFee() uint64 {
	if m != nil && m.BuyerFee != nil {
		return *m.BuyerFee
	}
	return 0
}

func (m *StoreListShippingFee) GetDistance() float64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

type Route struct {
	OrderId              *uint64    `protobuf:"varint,1,opt,name=order_id,json=orderId" json:"order_id,omitempty"`
	RouteType            *RouteType `protobuf:"varint,2,opt,name=route_type,json=routeType,enum=foodalgo_shippingfee.RouteType" json:"route_type,omitempty"`
	Latitude             *float32   `protobuf:"fixed32,3,opt,name=latitude" json:"latitude,omitempty"`
	Longitude            *float32   `protobuf:"fixed32,4,opt,name=longitude" json:"longitude,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Route) Reset()         { *m = Route{} }
func (m *Route) String() string { return proto.CompactTextString(m) }
func (*Route) ProtoMessage()    {}
func (*Route) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{16}
}

func (m *Route) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Route.Unmarshal(m, b)
}
func (m *Route) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Route.Marshal(b, m, deterministic)
}
func (m *Route) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Route.Merge(m, src)
}
func (m *Route) XXX_Size() int {
	return xxx_messageInfo_Route.Size(m)
}
func (m *Route) XXX_DiscardUnknown() {
	xxx_messageInfo_Route.DiscardUnknown(m)
}

var xxx_messageInfo_Route proto.InternalMessageInfo

func (m *Route) GetOrderId() uint64 {
	if m != nil && m.OrderId != nil {
		return *m.OrderId
	}
	return 0
}

func (m *Route) GetRouteType() RouteType {
	if m != nil && m.RouteType != nil {
		return *m.RouteType
	}
	return RouteType_ROUTE_UNKNOWN
}

func (m *Route) GetLatitude() float32 {
	if m != nil && m.Latitude != nil {
		return *m.Latitude
	}
	return 0
}

func (m *Route) GetLongitude() float32 {
	if m != nil && m.Longitude != nil {
		return *m.Longitude
	}
	return 0
}

type DriverInfo struct {
	DriverId             *uint64  `protobuf:"varint,1,opt,name=driver_id,json=driverId" json:"driver_id,omitempty"`
	DriverLevel          *uint32  `protobuf:"varint,2,opt,name=driver_level,json=driverLevel" json:"driver_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DriverInfo) Reset()         { *m = DriverInfo{} }
func (m *DriverInfo) String() string { return proto.CompactTextString(m) }
func (*DriverInfo) ProtoMessage()    {}
func (*DriverInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{17}
}

func (m *DriverInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DriverInfo.Unmarshal(m, b)
}
func (m *DriverInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DriverInfo.Marshal(b, m, deterministic)
}
func (m *DriverInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DriverInfo.Merge(m, src)
}
func (m *DriverInfo) XXX_Size() int {
	return xxx_messageInfo_DriverInfo.Size(m)
}
func (m *DriverInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DriverInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DriverInfo proto.InternalMessageInfo

func (m *DriverInfo) GetDriverId() uint64 {
	if m != nil && m.DriverId != nil {
		return *m.DriverId
	}
	return 0
}

func (m *DriverInfo) GetDriverLevel() uint32 {
	if m != nil && m.DriverLevel != nil {
		return *m.DriverLevel
	}
	return 0
}

type GetDriverShippingFeeRequest struct {
	DriverDeliveryOrderId *uint64        `protobuf:"varint,1,opt,name=driver_delivery_order_id,json=driverDeliveryOrderId" json:"driver_delivery_order_id,omitempty"`
	OrderDetailList       []*OrderDetail `protobuf:"bytes,2,rep,name=order_detail_list,json=orderDetailList" json:"order_detail_list,omitempty"`
	RouteList             []*Route       `protobuf:"bytes,3,rep,name=route_list,json=routeList" json:"route_list,omitempty"`
	ServiceType           *ServiceType   `protobuf:"varint,4,opt,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	DriverInfo            *DriverInfo    `protobuf:"bytes,5,opt,name=driver_info,json=driverInfo" json:"driver_info,omitempty"`
	SlotSchemeId          *uint64        `protobuf:"varint,6,opt,name=slot_scheme_id,json=slotSchemeId" json:"slot_scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}       `json:"-"`
	XXX_unrecognized      []byte         `json:"-"`
	XXX_sizecache         int32          `json:"-"`
}

func (m *GetDriverShippingFeeRequest) Reset()         { *m = GetDriverShippingFeeRequest{} }
func (m *GetDriverShippingFeeRequest) String() string { return proto.CompactTextString(m) }
func (*GetDriverShippingFeeRequest) ProtoMessage()    {}
func (*GetDriverShippingFeeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{18}
}

func (m *GetDriverShippingFeeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDriverShippingFeeRequest.Unmarshal(m, b)
}
func (m *GetDriverShippingFeeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDriverShippingFeeRequest.Marshal(b, m, deterministic)
}
func (m *GetDriverShippingFeeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDriverShippingFeeRequest.Merge(m, src)
}
func (m *GetDriverShippingFeeRequest) XXX_Size() int {
	return xxx_messageInfo_GetDriverShippingFeeRequest.Size(m)
}
func (m *GetDriverShippingFeeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDriverShippingFeeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetDriverShippingFeeRequest proto.InternalMessageInfo

func (m *GetDriverShippingFeeRequest) GetDriverDeliveryOrderId() uint64 {
	if m != nil && m.DriverDeliveryOrderId != nil {
		return *m.DriverDeliveryOrderId
	}
	return 0
}

func (m *GetDriverShippingFeeRequest) GetOrderDetailList() []*OrderDetail {
	if m != nil {
		return m.OrderDetailList
	}
	return nil
}

func (m *GetDriverShippingFeeRequest) GetRouteList() []*Route {
	if m != nil {
		return m.RouteList
	}
	return nil
}

func (m *GetDriverShippingFeeRequest) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

func (m *GetDriverShippingFeeRequest) GetDriverInfo() *DriverInfo {
	if m != nil {
		return m.DriverInfo
	}
	return nil
}

func (m *GetDriverShippingFeeRequest) GetSlotSchemeId() uint64 {
	if m != nil && m.SlotSchemeId != nil {
		return *m.SlotSchemeId
	}
	return 0
}

type GetDriverShippingFeeResponse struct {
	DriverTotalBaseEarn         *uint64                    `protobuf:"varint,1,opt,name=driver_total_base_earn,json=driverTotalBaseEarn" json:"driver_total_base_earn,omitempty"`
	DriverTotalRisingEarn       *uint64                    `protobuf:"varint,2,opt,name=driver_total_rising_earn,json=driverTotalRisingEarn" json:"driver_total_rising_earn,omitempty"`
	TotalDistance               *float64                   `protobuf:"fixed64,3,opt,name=total_distance,json=totalDistance" json:"total_distance,omitempty"`
	SingleDriverShippingFeeList []*SingleDriverShippingFee `protobuf:"bytes,4,rep,name=single_driver_shipping_fee_list,json=singleDriverShippingFeeList" json:"single_driver_shipping_fee_list,omitempty"`
	RoutesDistance              []float64                  `protobuf:"fixed64,5,rep,name=routes_distance,json=routesDistance" json:"routes_distance,omitempty"`
	XXX_NoUnkeyedLiteral        struct{}                   `json:"-"`
	XXX_unrecognized            []byte                     `json:"-"`
	XXX_sizecache               int32                      `json:"-"`
}

func (m *GetDriverShippingFeeResponse) Reset()         { *m = GetDriverShippingFeeResponse{} }
func (m *GetDriverShippingFeeResponse) String() string { return proto.CompactTextString(m) }
func (*GetDriverShippingFeeResponse) ProtoMessage()    {}
func (*GetDriverShippingFeeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{19}
}

func (m *GetDriverShippingFeeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDriverShippingFeeResponse.Unmarshal(m, b)
}
func (m *GetDriverShippingFeeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDriverShippingFeeResponse.Marshal(b, m, deterministic)
}
func (m *GetDriverShippingFeeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDriverShippingFeeResponse.Merge(m, src)
}
func (m *GetDriverShippingFeeResponse) XXX_Size() int {
	return xxx_messageInfo_GetDriverShippingFeeResponse.Size(m)
}
func (m *GetDriverShippingFeeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDriverShippingFeeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetDriverShippingFeeResponse proto.InternalMessageInfo

func (m *GetDriverShippingFeeResponse) GetDriverTotalBaseEarn() uint64 {
	if m != nil && m.DriverTotalBaseEarn != nil {
		return *m.DriverTotalBaseEarn
	}
	return 0
}

func (m *GetDriverShippingFeeResponse) GetDriverTotalRisingEarn() uint64 {
	if m != nil && m.DriverTotalRisingEarn != nil {
		return *m.DriverTotalRisingEarn
	}
	return 0
}

func (m *GetDriverShippingFeeResponse) GetTotalDistance() float64 {
	if m != nil && m.TotalDistance != nil {
		return *m.TotalDistance
	}
	return 0
}

func (m *GetDriverShippingFeeResponse) GetSingleDriverShippingFeeList() []*SingleDriverShippingFee {
	if m != nil {
		return m.SingleDriverShippingFeeList
	}
	return nil
}

func (m *GetDriverShippingFeeResponse) GetRoutesDistance() []float64 {
	if m != nil {
		return m.RoutesDistance
	}
	return nil
}

type SingleDriverShippingFee struct {
	//total = base+rising
	DriverBaseEarn       *uint64  `protobuf:"varint,1,req,name=driver_base_earn,json=driverBaseEarn" json:"driver_base_earn,omitempty"`
	DriverRisingEarn     *uint64  `protobuf:"varint,2,req,name=driver_rising_earn,json=driverRisingEarn" json:"driver_rising_earn,omitempty"`
	Distance             *float64 `protobuf:"fixed64,3,req,name=distance" json:"distance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingleDriverShippingFee) Reset()         { *m = SingleDriverShippingFee{} }
func (m *SingleDriverShippingFee) String() string { return proto.CompactTextString(m) }
func (*SingleDriverShippingFee) ProtoMessage()    {}
func (*SingleDriverShippingFee) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{20}
}

func (m *SingleDriverShippingFee) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingleDriverShippingFee.Unmarshal(m, b)
}
func (m *SingleDriverShippingFee) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingleDriverShippingFee.Marshal(b, m, deterministic)
}
func (m *SingleDriverShippingFee) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingleDriverShippingFee.Merge(m, src)
}
func (m *SingleDriverShippingFee) XXX_Size() int {
	return xxx_messageInfo_SingleDriverShippingFee.Size(m)
}
func (m *SingleDriverShippingFee) XXX_DiscardUnknown() {
	xxx_messageInfo_SingleDriverShippingFee.DiscardUnknown(m)
}

var xxx_messageInfo_SingleDriverShippingFee proto.InternalMessageInfo

func (m *SingleDriverShippingFee) GetDriverBaseEarn() uint64 {
	if m != nil && m.DriverBaseEarn != nil {
		return *m.DriverBaseEarn
	}
	return 0
}

func (m *SingleDriverShippingFee) GetDriverRisingEarn() uint64 {
	if m != nil && m.DriverRisingEarn != nil {
		return *m.DriverRisingEarn
	}
	return 0
}

func (m *SingleDriverShippingFee) GetDistance() float64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

type GetHubDriverShippingFeeConfigRequest struct {
	SlotSchemeId         *uint64  `protobuf:"varint,1,opt,name=slot_scheme_id,json=slotSchemeId" json:"slot_scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHubDriverShippingFeeConfigRequest) Reset()         { *m = GetHubDriverShippingFeeConfigRequest{} }
func (m *GetHubDriverShippingFeeConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetHubDriverShippingFeeConfigRequest) ProtoMessage()    {}
func (*GetHubDriverShippingFeeConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{21}
}

func (m *GetHubDriverShippingFeeConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigRequest.Unmarshal(m, b)
}
func (m *GetHubDriverShippingFeeConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigRequest.Marshal(b, m, deterministic)
}
func (m *GetHubDriverShippingFeeConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHubDriverShippingFeeConfigRequest.Merge(m, src)
}
func (m *GetHubDriverShippingFeeConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigRequest.Size(m)
}
func (m *GetHubDriverShippingFeeConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHubDriverShippingFeeConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHubDriverShippingFeeConfigRequest proto.InternalMessageInfo

func (m *GetHubDriverShippingFeeConfigRequest) GetSlotSchemeId() uint64 {
	if m != nil && m.SlotSchemeId != nil {
		return *m.SlotSchemeId
	}
	return 0
}

type GetHubDriverShippingFeeConfigResponse struct {
	DriverMinShippingFee *uint64  `protobuf:"varint,1,opt,name=driver_min_shipping_fee,json=driverMinShippingFee" json:"driver_min_shipping_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHubDriverShippingFeeConfigResponse) Reset()         { *m = GetHubDriverShippingFeeConfigResponse{} }
func (m *GetHubDriverShippingFeeConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetHubDriverShippingFeeConfigResponse) ProtoMessage()    {}
func (*GetHubDriverShippingFeeConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{22}
}

func (m *GetHubDriverShippingFeeConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigResponse.Unmarshal(m, b)
}
func (m *GetHubDriverShippingFeeConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigResponse.Marshal(b, m, deterministic)
}
func (m *GetHubDriverShippingFeeConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHubDriverShippingFeeConfigResponse.Merge(m, src)
}
func (m *GetHubDriverShippingFeeConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetHubDriverShippingFeeConfigResponse.Size(m)
}
func (m *GetHubDriverShippingFeeConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHubDriverShippingFeeConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHubDriverShippingFeeConfigResponse proto.InternalMessageInfo

func (m *GetHubDriverShippingFeeConfigResponse) GetDriverMinShippingFee() uint64 {
	if m != nil && m.DriverMinShippingFee != nil {
		return *m.DriverMinShippingFee
	}
	return 0
}

type GetMinShippingFeeRequest struct {
	ModuleType           *ModuleType  `protobuf:"varint,1,req,name=module_type,json=moduleType,enum=foodalgo_shippingfee.ModuleType" json:"module_type,omitempty"`
	ServiceType          *ServiceType `protobuf:"varint,2,req,name=service_type,json=serviceType,enum=foodalgo_shippingfee.ServiceType" json:"service_type,omitempty"`
	StoreLocation        *Location    `protobuf:"bytes,3,opt,name=store_location,json=storeLocation" json:"store_location,omitempty"`
	StoreOption          *StoreOption `protobuf:"bytes,4,opt,name=store_option,json=storeOption" json:"store_option,omitempty"`
	StoreId              *uint64      `protobuf:"varint,5,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetMinShippingFeeRequest) Reset()         { *m = GetMinShippingFeeRequest{} }
func (m *GetMinShippingFeeRequest) String() string { return proto.CompactTextString(m) }
func (*GetMinShippingFeeRequest) ProtoMessage()    {}
func (*GetMinShippingFeeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{23}
}

func (m *GetMinShippingFeeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinShippingFeeRequest.Unmarshal(m, b)
}
func (m *GetMinShippingFeeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinShippingFeeRequest.Marshal(b, m, deterministic)
}
func (m *GetMinShippingFeeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinShippingFeeRequest.Merge(m, src)
}
func (m *GetMinShippingFeeRequest) XXX_Size() int {
	return xxx_messageInfo_GetMinShippingFeeRequest.Size(m)
}
func (m *GetMinShippingFeeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinShippingFeeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinShippingFeeRequest proto.InternalMessageInfo

func (m *GetMinShippingFeeRequest) GetModuleType() ModuleType {
	if m != nil && m.ModuleType != nil {
		return *m.ModuleType
	}
	return ModuleType_ModuleTypeBuyer
}

func (m *GetMinShippingFeeRequest) GetServiceType() ServiceType {
	if m != nil && m.ServiceType != nil {
		return *m.ServiceType
	}
	return ServiceType_ShopeeFood
}

func (m *GetMinShippingFeeRequest) GetStoreLocation() *Location {
	if m != nil {
		return m.StoreLocation
	}
	return nil
}

func (m *GetMinShippingFeeRequest) GetStoreOption() *StoreOption {
	if m != nil {
		return m.StoreOption
	}
	return nil
}

func (m *GetMinShippingFeeRequest) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

type GetMinShippingFeeResponse struct {
	MinShippingFee       *uint64  `protobuf:"varint,1,opt,name=min_shipping_fee,json=minShippingFee" json:"min_shipping_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMinShippingFeeResponse) Reset()         { *m = GetMinShippingFeeResponse{} }
func (m *GetMinShippingFeeResponse) String() string { return proto.CompactTextString(m) }
func (*GetMinShippingFeeResponse) ProtoMessage()    {}
func (*GetMinShippingFeeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_a468e4d15a5ad3de, []int{24}
}

func (m *GetMinShippingFeeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMinShippingFeeResponse.Unmarshal(m, b)
}
func (m *GetMinShippingFeeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMinShippingFeeResponse.Marshal(b, m, deterministic)
}
func (m *GetMinShippingFeeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMinShippingFeeResponse.Merge(m, src)
}
func (m *GetMinShippingFeeResponse) XXX_Size() int {
	return xxx_messageInfo_GetMinShippingFeeResponse.Size(m)
}
func (m *GetMinShippingFeeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMinShippingFeeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetMinShippingFeeResponse proto.InternalMessageInfo

func (m *GetMinShippingFeeResponse) GetMinShippingFee() uint64 {
	if m != nil && m.MinShippingFee != nil {
		return *m.MinShippingFee
	}
	return 0
}

func init() {
	proto.RegisterEnum("foodalgo_shippingfee.ServiceType", ServiceType_name, ServiceType_value)
	proto.RegisterEnum("foodalgo_shippingfee.SceneType", SceneType_name, SceneType_value)
	proto.RegisterEnum("foodalgo_shippingfee.ShippingFeeType", ShippingFeeType_name, ShippingFeeType_value)
	proto.RegisterEnum("foodalgo_shippingfee.DeliveryMode", DeliveryMode_name, DeliveryMode_value)
	proto.RegisterEnum("foodalgo_shippingfee.VatCalculate", VatCalculate_name, VatCalculate_value)
	proto.RegisterEnum("foodalgo_shippingfee.RouteType", RouteType_name, RouteType_value)
	proto.RegisterEnum("foodalgo_shippingfee.ModuleType", ModuleType_name, ModuleType_value)
	proto.RegisterEnum("foodalgo_shippingfee.StoreOption_PartnerType", StoreOption_PartnerType_name, StoreOption_PartnerType_value)
	proto.RegisterType((*Location)(nil), "foodalgo_shippingfee.Location")
	proto.RegisterType((*StoreOption)(nil), "foodalgo_shippingfee.StoreOption")
	proto.RegisterType((*DynamicContext)(nil), "foodalgo_shippingfee.DynamicContext")
	proto.RegisterType((*BuyerInfo)(nil), "foodalgo_shippingfee.BuyerInfo")
	proto.RegisterType((*Amount)(nil), "foodalgo_shippingfee.Amount")
	proto.RegisterType((*OrderItem)(nil), "foodalgo_shippingfee.OrderItem")
	proto.RegisterType((*GetShippingFeeRequest)(nil), "foodalgo_shippingfee.GetShippingFeeRequest")
	proto.RegisterType((*BuyerAddition)(nil), "foodalgo_shippingfee.BuyerAddition")
	proto.RegisterType((*GetShippingFeeResponse)(nil), "foodalgo_shippingfee.GetShippingFeeResponse")
	proto.RegisterType((*GetBuyerShippingFeeRequest)(nil), "foodalgo_shippingfee.GetBuyerShippingFeeRequest")
	proto.RegisterType((*MGetStoreListShippingFeeRequest)(nil), "foodalgo_shippingfee.MGetStoreListShippingFeeRequest")
	proto.RegisterType((*StoreDetail)(nil), "foodalgo_shippingfee.StoreDetail")
	proto.RegisterType((*OrderDetail)(nil), "foodalgo_shippingfee.OrderDetail")
	proto.RegisterType((*GetBuyerShippingFeeResponse)(nil), "foodalgo_shippingfee.GetBuyerShippingFeeResponse")
	proto.RegisterType((*MGetStoreListShippingFeeResponse)(nil), "foodalgo_shippingfee.MGetStoreListShippingFeeResponse")
	proto.RegisterMapType((map[uint64]*StoreListShippingFee)(nil), "foodalgo_shippingfee.MGetStoreListShippingFeeResponse.ShippingFeeMapEntry")
	proto.RegisterType((*StoreListShippingFee)(nil), "foodalgo_shippingfee.StoreListShippingFee")
	proto.RegisterType((*Route)(nil), "foodalgo_shippingfee.Route")
	proto.RegisterType((*DriverInfo)(nil), "foodalgo_shippingfee.DriverInfo")
	proto.RegisterType((*GetDriverShippingFeeRequest)(nil), "foodalgo_shippingfee.GetDriverShippingFeeRequest")
	proto.RegisterType((*GetDriverShippingFeeResponse)(nil), "foodalgo_shippingfee.GetDriverShippingFeeResponse")
	proto.RegisterType((*SingleDriverShippingFee)(nil), "foodalgo_shippingfee.SingleDriverShippingFee")
	proto.RegisterType((*GetHubDriverShippingFeeConfigRequest)(nil), "foodalgo_shippingfee.GetHubDriverShippingFeeConfigRequest")
	proto.RegisterType((*GetHubDriverShippingFeeConfigResponse)(nil), "foodalgo_shippingfee.GetHubDriverShippingFeeConfigResponse")
	proto.RegisterType((*GetMinShippingFeeRequest)(nil), "foodalgo_shippingfee.GetMinShippingFeeRequest")
	proto.RegisterType((*GetMinShippingFeeResponse)(nil), "foodalgo_shippingfee.GetMinShippingFeeResponse")
}

func init() {
	proto.RegisterFile("foodalgo_shippingfee/foodalgo_shippingfee.proto", fileDescriptor_a468e4d15a5ad3de)
}

var fileDescriptor_a468e4d15a5ad3de = []byte{
	// 2318 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0x4f, 0x6f, 0xdb, 0xc8,
	0x15, 0x5f, 0x52, 0xb2, 0x2d, 0x3d, 0x4a, 0x32, 0x33, 0xf1, 0x26, 0x8a, 0x9c, 0x34, 0x0e, 0x37,
	0x69, 0x0d, 0x77, 0xe3, 0x6c, 0xdd, 0x66, 0xbb, 0x48, 0x81, 0x45, 0x1c, 0xd9, 0x49, 0x95, 0xf5,
	0xbf, 0xa5, 0x12, 0xef, 0xee, 0xa5, 0x02, 0x4d, 0x8e, 0x6c, 0x22, 0x22, 0x47, 0x4b, 0x8e, 0xdc,
	0xa8, 0x40, 0xcf, 0x8b, 0xa2, 0x28, 0x5a, 0xf4, 0xb4, 0x87, 0x1e, 0x0a, 0xb4, 0x5f, 0xa1, 0x97,
	0x7e, 0x82, 0x02, 0x3d, 0xf5, 0xd0, 0x63, 0xbf, 0x41, 0x8f, 0x3d, 0x17, 0xc5, 0xbc, 0x19, 0x52,
	0x94, 0x4c, 0x29, 0x8e, 0x63, 0xa0, 0x37, 0xcd, 0xfb, 0x37, 0x6f, 0xde, 0x9f, 0xdf, 0x1b, 0x8e,
	0xe0, 0x41, 0x97, 0x31, 0xcf, 0xe9, 0x1d, 0xb3, 0x4e, 0x7c, 0xe2, 0xf7, 0xfb, 0x7e, 0x78, 0xdc,
	0xa5, 0x34, 0x97, 0xb8, 0xde, 0x8f, 0x18, 0x67, 0x64, 0x29, 0x8f, 0xd7, 0xf8, 0xfc, 0xd8, 0xe7,
	0xeb, 0xc7, 0x4e, 0x44, 0x43, 0x67, 0xdd, 0x65, 0xc1, 0x83, 0xf8, 0x84, 0xf5, 0x85, 0x1d, 0x4a,
	0xbd, 0x07, 0x81, 0xef, 0x46, 0xec, 0x95, 0xcf, 0x1f, 0xa0, 0x01, 0xf7, 0xfe, 0x31, 0x0d, 0xef,
	0xa7, 0x34, 0x8f, 0xc6, 0x6e, 0xe4, 0xf7, 0x39, 0x8b, 0x52, 0x39, 0xb9, 0x91, 0xf5, 0x67, 0x0d,
	0x4a, 0x3b, 0xcc, 0x75, 0xb8, 0xcf, 0x42, 0xb2, 0x04, 0x73, 0x31, 0x77, 0x38, 0xad, 0x6b, 0x2b,
	0xda, 0x6a, 0xd9, 0x96, 0x0b, 0x42, 0xa0, 0xe8, 0xfa, 0x7c, 0x58, 0xd7, 0x91, 0x88, 0xbf, 0x49,
	0x03, 0x4a, 0x9e, 0x1f, 0xf3, 0xc8, 0x77, 0x79, 0xbd, 0x80, 0xf4, 0x74, 0x4d, 0xea, 0xb0, 0xe0,
	0x78, 0x5e, 0x44, 0xe3, 0xb8, 0x5e, 0x44, 0x56, 0xb2, 0x14, 0x5a, 0x3d, 0x87, 0xfb, 0x7c, 0xe0,
	0xd1, 0xfa, 0xdc, 0x8a, 0xb6, 0xaa, 0xdb, 0xe9, 0x9a, 0xdc, 0x84, 0x72, 0x8f, 0x85, 0xc7, 0x92,
	0x39, 0x8f, 0xcc, 0x11, 0xc1, 0xfa, 0xb5, 0x06, 0x46, 0x9b, 0xb3, 0x88, 0xee, 0xf7, 0xd1, 0xd3,
	0x03, 0xa8, 0xf4, 0x9d, 0x88, 0x87, 0x34, 0xea, 0xf0, 0x61, 0x5f, 0x3a, 0x5c, 0xdb, 0xb8, 0xbf,
	0x9e, 0x1b, 0xd2, 0x8c, 0xe2, 0xfa, 0x81, 0xd4, 0x7a, 0x31, 0xec, 0x53, 0xdb, 0xe8, 0x8f, 0x16,
	0xd6, 0x1a, 0x18, 0x19, 0x1e, 0xa9, 0x01, 0xec, 0xb1, 0x50, 0x51, 0x4c, 0x8d, 0x18, 0xb0, 0x90,
	0x2c, 0x74, 0xeb, 0x6f, 0x1a, 0xd4, 0xb6, 0x86, 0xa1, 0x13, 0xf8, 0x6e, 0x93, 0x85, 0x9c, 0xbe,
	0xe6, 0xe4, 0x53, 0x80, 0xa3, 0xc1, 0x90, 0x46, 0x1d, 0x3f, 0xec, 0x32, 0x74, 0xc7, 0xd8, 0xb8,
	0x9d, 0xef, 0xce, 0x13, 0x21, 0xd7, 0x0a, 0xbb, 0xcc, 0x2e, 0x1f, 0x25, 0x3f, 0xc9, 0x63, 0x30,
	0x58, 0xe4, 0x09, 0x7d, 0x4e, 0x83, 0xb8, 0xae, 0xaf, 0x14, 0xa6, 0x1b, 0xd8, 0x17, 0x82, 0x2d,
	0x4e, 0x03, 0x1b, 0x58, 0xf2, 0x33, 0x26, 0x3f, 0x82, 0x79, 0x27, 0x60, 0x83, 0x50, 0x26, 0xc4,
	0xd8, 0xb8, 0x99, 0xaf, 0xbc, 0x89, 0x32, 0xb6, 0x92, 0xb5, 0x7e, 0xab, 0x41, 0x39, 0x75, 0x88,
	0xdc, 0x06, 0xa3, 0x7f, 0xc2, 0x42, 0xda, 0x39, 0x8a, 0x9c, 0xd0, 0x53, 0x65, 0x00, 0x48, 0x7a,
	0x22, 0x28, 0x23, 0x81, 0x80, 0x79, 0xb4, 0xa7, 0x4a, 0x42, 0x0a, 0xec, 0x0a, 0x0a, 0xb9, 0x03,
	0x15, 0x29, 0x10, 0x0f, 0x63, 0x4e, 0x03, 0x55, 0x1c, 0x52, 0xa9, 0x8d, 0x24, 0x72, 0x0b, 0xa4,
	0x42, 0xe7, 0xe7, 0x7e, 0xd7, 0x57, 0x25, 0x52, 0x46, 0xca, 0x17, 0x7e, 0xd7, 0xb7, 0xfe, 0x5d,
	0x80, 0x79, 0xe9, 0x24, 0xf9, 0x00, 0xaa, 0x22, 0x1c, 0x9d, 0x78, 0x70, 0xc4, 0x19, 0x77, 0x7a,
	0xe8, 0x50, 0xd1, 0xae, 0x08, 0x62, 0x5b, 0xd1, 0xc8, 0x27, 0x50, 0x0f, 0x68, 0xe4, 0x9e, 0x38,
	0x21, 0xef, 0xb8, 0x2c, 0x08, 0xfc, 0x38, 0xf6, 0x59, 0xd8, 0x89, 0x44, 0x1d, 0xeb, 0x28, 0x7f,
	0x2d, 0xe1, 0x37, 0x53, 0xb6, 0x2d, 0x0a, 0x7b, 0x13, 0x6e, 0xa5, 0x9a, 0x22, 0x56, 0x9d, 0x53,
	0x36, 0x70, 0x4f, 0x68, 0x24, 0xf6, 0x8b, 0x7d, 0x6f, 0x88, 0xce, 0x17, 0xed, 0x46, 0x22, 0xf4,
	0x94, 0x31, 0xef, 0x50, 0x8a, 0xb4, 0xa5, 0x04, 0xb9, 0x01, 0x25, 0xee, 0xbc, 0x96, 0x9b, 0x15,
	0x51, 0x7a, 0x81, 0x3b, 0xaf, 0xd1, 0xfa, 0x43, 0xb8, 0x1e, 0xd3, 0xe8, 0xd4, 0x77, 0x69, 0xc7,
	0x3d, 0x71, 0xa2, 0x63, 0xda, 0xe9, 0x52, 0x2a, 0x25, 0xe7, 0x50, 0x72, 0x49, 0xb1, 0x9b, 0xc8,
	0x7d, 0x4a, 0x29, 0xaa, 0xdd, 0x80, 0xd2, 0xa9, 0xc3, 0xa5, 0xdc, 0xbc, 0xb4, 0x78, 0xea, 0x70,
	0x64, 0x3d, 0x83, 0xaa, 0x60, 0xb9, 0x4e, 0xcf, 0x1d, 0xf4, 0x04, 0x7f, 0x01, 0xab, 0xde, 0xca,
	0x4f, 0xf4, 0xa1, 0xc3, 0x9b, 0x89, 0xa4, 0x5d, 0x39, 0xcd, 0xac, 0xc8, 0x47, 0xb0, 0x94, 0x1e,
	0x3c, 0xf1, 0xb1, 0x4b, 0x69, 0xbd, 0x84, 0xfb, 0x91, 0x84, 0xd7, 0x96, 0xac, 0xa7, 0x14, 0x35,
	0xfa, 0x3d, 0x87, 0x77, 0x59, 0x14, 0x8c, 0x69, 0x94, 0xa5, 0x46, 0xc2, 0xcb, 0x68, 0x7c, 0x17,
	0x16, 0xe3, 0xc0, 0xe9, 0xf5, 0x3a, 0xb2, 0xac, 0x85, 0x30, 0xa0, 0x70, 0x15, 0xc9, 0x58, 0xc3,
	0x4f, 0x29, 0xb5, 0xbe, 0xd1, 0xa0, 0x9c, 0x16, 0x34, 0xb9, 0x0e, 0x0b, 0x9e, 0x1f, 0x9f, 0x74,
	0x7c, 0x4f, 0xe5, 0x7a, 0x5e, 0x2c, 0x5b, 0x9e, 0x80, 0x8e, 0xaf, 0x07, 0x4e, 0xc8, 0x13, 0x20,
	0xaa, 0xda, 0xe9, 0x5a, 0x14, 0xd4, 0x20, 0xf4, 0x79, 0xa7, 0x1f, 0xf9, 0x2e, 0x55, 0x49, 0x2b,
	0x0b, 0xca, 0x81, 0x20, 0x08, 0x4f, 0x90, 0xdd, 0xf3, 0xe3, 0x44, 0x46, 0xa6, 0xaa, 0x2a, 0xc8,
	0x3b, 0x7e, 0x2c, 0xe5, 0xac, 0x7f, 0xce, 0xc1, 0xfb, 0xcf, 0x28, 0x6f, 0xab, 0x18, 0x8a, 0x84,
	0xd0, 0xaf, 0x07, 0x34, 0xe6, 0x22, 0x27, 0xaa, 0x39, 0x13, 0xb7, 0x16, 0x64, 0xe3, 0x79, 0xa4,
	0x05, 0x55, 0x91, 0x56, 0x01, 0x42, 0xb8, 0x01, 0x76, 0x6e, 0x6d, 0xe3, 0xde, 0x14, 0x24, 0x1a,
	0xd9, 0x96, 0x08, 0xd4, 0x95, 0x3f, 0x84, 0x13, 0xe4, 0x63, 0x98, 0x67, 0x91, 0x7f, 0xec, 0x87,
	0xf5, 0xc2, 0x8a, 0xbe, 0x6a, 0x6c, 0x7c, 0x27, 0xdf, 0x46, 0x82, 0xd6, 0xb6, 0x92, 0x16, 0xd0,
	0xe1, 0xd1, 0x98, 0xfb, 0x21, 0x92, 0xeb, 0xc5, 0x73, 0x29, 0x67, 0x55, 0xc8, 0x16, 0x54, 0x62,
	0x81, 0x91, 0x1d, 0x86, 0x20, 0x89, 0xf5, 0x69, 0x6c, 0xdc, 0x79, 0x23, 0x9a, 0xda, 0x46, 0x9c,
	0xc1, 0x64, 0x61, 0x45, 0x95, 0x06, 0x62, 0xf2, 0x3c, 0x56, 0xe7, 0x34, 0x2b, 0x52, 0x52, 0x46,
	0x21, 0x1e, 0x2d, 0x04, 0x90, 0xc6, 0x2e, 0x0d, 0x95, 0x0d, 0x59, 0xe1, 0x53, 0x70, 0xb0, 0x2d,
	0xe4, 0xd0, 0x42, 0x39, 0x4e, 0x7e, 0x8a, 0x62, 0x88, 0x64, 0xda, 0x44, 0xb6, 0x4a, 0x12, 0x5d,
	0x14, 0xa5, 0xe5, 0x89, 0x54, 0x2a, 0x9c, 0xf6, 0x54, 0xf1, 0x2e, 0x48, 0x10, 0x46, 0x96, 0x8c,
	0x82, 0xef, 0xa9, 0x52, 0x5d, 0xc0, 0x75, 0xcb, 0x13, 0x9d, 0xe7, 0xd1, 0x9e, 0x7f, 0x4a, 0xa3,
	0x21, 0x22, 0x5f, 0xdd, 0x98, 0xd5, 0x79, 0x5b, 0x4a, 0x54, 0x20, 0xa2, 0x5d, 0xf1, 0x32, 0x2b,
	0x81, 0x68, 0xa9, 0x21, 0xee, 0x07, 0xb4, 0x5e, 0x91, 0x88, 0x96, 0x10, 0x5f, 0xf8, 0x01, 0x25,
	0xbb, 0xb0, 0xe8, 0xc9, 0xe9, 0xd2, 0x71, 0xe5, 0x78, 0xa9, 0x57, 0x31, 0x23, 0x77, 0xa7, 0xec,
	0x37, 0x36, 0x8a, 0xec, 0x9a, 0x37, 0xb6, 0xb6, 0x9e, 0x43, 0x15, 0x11, 0x7e, 0xd3, 0xf3, 0x7c,
	0x4c, 0xd4, 0x32, 0x94, 0xe9, 0x6b, 0x1e, 0x39, 0xd8, 0x94, 0xa2, 0x9e, 0x0b, 0x76, 0x09, 0x09,
	0xa2, 0x6f, 0x6f, 0x42, 0xb9, 0x1f, 0xb1, 0x80, 0x61, 0x21, 0xe8, 0xc8, 0x1c, 0x11, 0xac, 0xbf,
	0x6a, 0x70, 0x6d, 0xb2, 0x47, 0xe2, 0x3e, 0x0b, 0x63, 0x04, 0x2e, 0xd1, 0x09, 0xd8, 0x04, 0xda,
	0x4a, 0x41, 0x84, 0xaf, 0x4b, 0x65, 0x65, 0xab, 0xdb, 0x82, 0x13, 0xba, 0x12, 0x92, 0x35, 0x3b,
	0x5d, 0x93, 0xe7, 0x50, 0x93, 0x09, 0x71, 0x94, 0x7b, 0x6a, 0x7c, 0x7d, 0x30, 0x63, 0x78, 0x26,
	0x27, 0xb1, 0xab, 0x47, 0x63, 0x07, 0x1b, 0xcf, 0x7d, 0x71, 0x22, 0xf7, 0xd6, 0x9f, 0xe6, 0xa0,
	0xf1, 0x8c, 0x72, 0x34, 0xf1, 0x76, 0x5d, 0xbe, 0x0d, 0x35, 0x59, 0x1a, 0x3d, 0xd5, 0x3f, 0x75,
	0xfd, 0x5c, 0x5d, 0x56, 0x45, 0xad, 0xf4, 0x7e, 0xb5, 0x9d, 0x9c, 0x35, 0x35, 0x73, 0xbe, 0x4e,
	0x97, 0xc7, 0x4c, 0xcd, 0x4c, 0xb6, 0x6b, 0xf1, 0x52, 0xda, 0x75, 0xee, 0x12, 0xda, 0x75, 0xfe,
	0x1d, 0xdb, 0x75, 0x61, 0x56, 0xbb, 0x96, 0xa6, 0xb7, 0x6b, 0xf9, 0x0d, 0xed, 0x0a, 0x97, 0xd5,
	0xae, 0xc6, 0xf9, 0xda, 0xb5, 0xf2, 0x0e, 0xed, 0xfa, 0xad, 0x0e, 0xb7, 0x77, 0x45, 0x8f, 0x61,
	0xe9, 0xf8, 0x71, 0xde, 0x40, 0x9a, 0xcc, 0x9d, 0x76, 0xa1, 0xdc, 0x65, 0x83, 0xab, 0x8f, 0x07,
	0xf7, 0x92, 0x2a, 0x75, 0x17, 0xae, 0xc8, 0x1c, 0x79, 0x94, 0x3b, 0x7e, 0x4f, 0x82, 0x43, 0x11,
	0xef, 0xb6, 0xb3, 0xca, 0x75, 0x0b, 0xa5, 0xed, 0xc5, 0x78, 0xb4, 0x10, 0x81, 0xb0, 0xfe, 0x9b,
	0x7c, 0x05, 0x48, 0xda, 0x58, 0x09, 0x68, 0xe3, 0x25, 0x70, 0x49, 0x1d, 0x3b, 0xd9, 0x6a, 0x85,
	0x0b, 0xb5, 0xda, 0x99, 0x7a, 0x2c, 0x5e, 0xac, 0x1e, 0xad, 0x7f, 0xe9, 0x60, 0xe0, 0x65, 0x69,
	0x14, 0x80, 0x69, 0x90, 0x95, 0x8d, 0x8d, 0x38, 0xfa, 0xcc, 0xd8, 0x14, 0x2e, 0x07, 0xcd, 0x8a,
	0x97, 0x81, 0x66, 0xff, 0xc7, 0xcb, 0x87, 0xf5, 0x1f, 0x0d, 0x96, 0x73, 0x27, 0x84, 0x9a, 0x71,
	0x77, 0x93, 0x23, 0x1f, 0x39, 0x31, 0x55, 0xe3, 0x53, 0x84, 0xb6, 0x82, 0xd4, 0x27, 0x4e, 0x8c,
	0x57, 0xdf, 0x55, 0x30, 0xa5, 0x54, 0xe4, 0xc7, 0x7e, 0x78, 0x8c, 0x72, 0x32, 0x05, 0x52, 0xdb,
	0x46, 0xb2, 0x90, 0xcc, 0x0e, 0x46, 0x91, 0x83, 0xd9, 0x83, 0xb1, 0x78, 0x49, 0x83, 0x71, 0x6e,
	0x72, 0x30, 0x7e, 0xa3, 0xc3, 0xca, 0x74, 0xc8, 0x51, 0x67, 0xe7, 0x60, 0x26, 0xfb, 0xe1, 0x97,
	0x4c, 0xe0, 0xf4, 0x71, 0xce, 0x1b, 0x1b, 0xcf, 0xf3, 0x3d, 0x7a, 0x93, 0xc5, 0xec, 0x6d, 0x78,
	0xd7, 0xe9, 0x6f, 0x87, 0x3c, 0x1a, 0xda, 0xb5, 0x78, 0x8c, 0xd8, 0x08, 0xe0, 0x6a, 0x8e, 0x18,
	0x31, 0xa1, 0xf0, 0x8a, 0x0e, 0x55, 0xcd, 0x8b, 0x9f, 0xe4, 0x31, 0xcc, 0x9d, 0x3a, 0xbd, 0x81,
	0xbc, 0x60, 0x18, 0x1b, 0x6b, 0x33, 0xea, 0x67, 0xd2, 0x1f, 0xa9, 0xf8, 0x48, 0xff, 0x44, 0xb3,
	0xf6, 0x61, 0x29, 0x4f, 0x44, 0x5c, 0x99, 0x64, 0x32, 0x46, 0x39, 0x97, 0xd8, 0x39, 0x99, 0x45,
	0x7d, 0x3c, 0x8b, 0xd6, 0x1f, 0x34, 0x98, 0xb3, 0xd9, 0x40, 0x7e, 0xd8, 0x4d, 0xeb, 0xd5, 0x4f,
	0x01, 0x22, 0x21, 0x23, 0x4b, 0x57, 0x9f, 0x35, 0x44, 0xd1, 0x96, 0x1c, 0xa2, 0x51, 0xf2, 0x73,
	0xec, 0x5d, 0xa5, 0x30, 0xeb, 0x5d, 0xa5, 0x38, 0xf9, 0xae, 0xb2, 0x03, 0xb0, 0x15, 0x09, 0x7c,
	0xc1, 0xcf, 0xff, 0x65, 0x28, 0x7b, 0xb8, 0x1a, 0xf9, 0x58, 0x92, 0x84, 0x96, 0x27, 0xbe, 0xec,
	0x15, 0xb3, 0x47, 0x4f, 0xd5, 0xb7, 0x7f, 0xd5, 0x36, 0x24, 0x6d, 0x47, 0x90, 0xac, 0xdf, 0x15,
	0xb0, 0x7d, 0xa4, 0xc5, 0x9c, 0xb1, 0xf5, 0x63, 0xa8, 0x2b, 0x13, 0x29, 0x1c, 0x4e, 0x84, 0xe4,
	0x7d, 0xc9, 0x4f, 0x20, 0x70, 0x5f, 0x05, 0x68, 0x17, 0xae, 0x48, 0xc1, 0xec, 0x1c, 0xd1, 0x67,
	0xcd, 0x91, 0x0c, 0x4a, 0xda, 0x8b, 0x6c, 0xb4, 0xc0, 0xfb, 0xe8, 0xa3, 0x24, 0xde, 0x68, 0xa7,
	0x80, 0x76, 0x96, 0x67, 0xc4, 0x5b, 0xc5, 0x1a, 0x75, 0x27, 0x81, 0xa6, 0x78, 0xa1, 0xd1, 0xbb,
	0x09, 0x46, 0x12, 0xe9, 0xb0, 0xcb, 0x14, 0xe6, 0xad, 0x4c, 0x99, 0x07, 0x69, 0x82, 0x6c, 0xf0,
	0x46, 0xc9, 0xba, 0x0b, 0xb5, 0xb8, 0xc7, 0x78, 0x27, 0x76, 0x4f, 0x68, 0x80, 0x30, 0x2f, 0x9f,
	0x0b, 0x2a, 0x82, 0xda, 0x46, 0x62, 0xcb, 0xb3, 0xfe, 0xa1, 0xc3, 0xcd, 0xfc, 0x94, 0xa8, 0xb6,
	0xfe, 0x21, 0x5c, 0x53, 0x9e, 0xe0, 0x73, 0x8a, 0x44, 0x36, 0xea, 0x44, 0xa1, 0xca, 0xc8, 0x55,
	0xc9, 0x7d, 0x21, 0x98, 0x02, 0xe0, 0xb6, 0x9d, 0x28, 0xcc, 0x24, 0x52, 0x2a, 0x29, 0xa0, 0x43,
	0x35, 0x3d, 0x9b, 0x48, 0x54, 0x93, 0x78, 0x87, 0x8a, 0xf7, 0xa0, 0x26, 0x35, 0x32, 0xb0, 0x27,
	0xbe, 0x07, 0xaa, 0x48, 0xdd, 0x4a, 0xb0, 0x2f, 0x86, 0xdb, 0x42, 0xa5, 0x47, 0x3b, 0x6a, 0x9b,
	0x31, 0xe4, 0xc9, 0xdc, 0x22, 0xa6, 0xbd, 0xf8, 0xa1, 0xf2, 0xd9, 0x43, 0x2f, 0xc7, 0xf9, 0x0c,
	0xcc, 0xec, 0xf7, 0x60, 0x11, 0xd3, 0x1c, 0x8f, 0x9c, 0x9b, 0x5b, 0x29, 0xac, 0x6a, 0x76, 0x4d,
	0x92, 0x13, 0xef, 0xac, 0xdf, 0x68, 0x70, 0x7d, 0xca, 0x0e, 0x02, 0xfb, 0x95, 0xcb, 0xd9, 0x40,
	0x22, 0xf6, 0x4b, 0x7a, 0x1a, 0xc3, 0x0f, 0x81, 0x28, 0xc9, 0xf1, 0xe8, 0x09, 0x59, 0x65, 0x23,
	0x13, 0xb8, 0x19, 0x93, 0xc2, 0xda, 0x81, 0xbb, 0xcf, 0x28, 0xff, 0xe9, 0xe0, 0xe8, 0x8c, 0x3b,
	0x4d, 0x16, 0x76, 0xfd, 0xe3, 0xa4, 0xfd, 0xce, 0x56, 0x8c, 0x96, 0x53, 0x31, 0x3f, 0x83, 0x7b,
	0x6f, 0xb0, 0xa6, 0x2a, 0xe7, 0x21, 0x5c, 0x57, 0x07, 0x08, 0xfc, 0x70, 0x2c, 0x43, 0xca, 0xee,
	0x92, 0x64, 0xef, 0xfa, 0x61, 0xc6, 0x88, 0xf5, 0x77, 0x1d, 0xea, 0xcf, 0x28, 0x1f, 0xa7, 0x26,
	0x2e, 0x6e, 0x82, 0x11, 0x30, 0x6f, 0xd0, 0x4b, 0xef, 0xb5, 0xfa, 0x6a, 0x6d, 0x5a, 0x5f, 0xec,
	0xa2, 0x20, 0xf6, 0x16, 0x04, 0xe9, 0xef, 0x33, 0x0d, 0xaa, 0xa3, 0x8d, 0xb7, 0x6d, 0xd0, 0xbc,
	0x3b, 0x92, 0xf6, 0xee, 0xf7, 0xc7, 0x8b, 0x7d, 0xaa, 0x65, 0xef, 0x72, 0x73, 0x63, 0xf7, 0x5c,
	0x6b, 0x1b, 0x6e, 0xe4, 0x04, 0x53, 0x65, 0x68, 0x15, 0xcc, 0x29, 0xa9, 0xa9, 0x05, 0x63, 0x1a,
	0x6b, 0x4d, 0x30, 0x32, 0xa1, 0x20, 0x35, 0x80, 0x36, 0xfe, 0xb7, 0xf0, 0x94, 0x31, 0xcf, 0xd4,
	0xc8, 0x12, 0x98, 0xed, 0x83, 0x2f, 0x5b, 0xa1, 0x28, 0x38, 0x2e, 0x39, 0xa6, 0x8e, 0x52, 0x29,
	0xd5, 0x2c, 0xac, 0xb5, 0xa0, 0x9c, 0x7e, 0xe3, 0x91, 0x05, 0x28, 0xec, 0x31, 0x6e, 0xbe, 0x47,
	0xae, 0xa8, 0xe7, 0x87, 0xe6, 0x09, 0x75, 0x5f, 0xed, 0x0f, 0xb8, 0xa9, 0x91, 0x45, 0x30, 0xe4,
	0x15, 0x6b, 0x70, 0x14, 0xf8, 0xdc, 0xd4, 0x05, 0x41, 0x56, 0xdb, 0xe7, 0x03, 0x1a, 0x0d, 0xcd,
	0xc2, 0xda, 0x57, 0xb0, 0x38, 0xf1, 0x56, 0x46, 0x2a, 0x50, 0x7a, 0xa2, 0x26, 0xae, 0xf9, 0x9e,
	0xd8, 0x5b, 0x6a, 0x88, 0xee, 0x30, 0x35, 0x42, 0xa0, 0xf6, 0x64, 0xec, 0x6e, 0x65, 0xea, 0xc2,
	0xeb, 0xad, 0x89, 0x3e, 0x32, 0x0b, 0x6b, 0x8f, 0xa1, 0x92, 0xbd, 0x61, 0xa3, 0x54, 0x66, 0xdd,
	0x91, 0x5e, 0x57, 0xa0, 0x74, 0xa0, 0x1e, 0x35, 0x4d, 0x4d, 0xac, 0x76, 0xd5, 0xa3, 0xa8, 0xa9,
	0xaf, 0xb9, 0x50, 0xc9, 0x3e, 0xae, 0x92, 0x1b, 0xf0, 0xfe, 0xe1, 0xe6, 0x8b, 0x4e, 0x73, 0x73,
	0xa7, 0xf9, 0x72, 0x67, 0xf3, 0xc5, 0x76, 0xe7, 0xe5, 0xde, 0x67, 0x7b, 0xfb, 0x5f, 0xec, 0x99,
	0xef, 0x91, 0x65, 0xb8, 0x3e, 0xce, 0xda, 0xfe, 0xb2, 0xb9, 0xf3, 0xb2, 0xdd, 0x3a, 0xdc, 0x36,
	0xb5, 0xb3, 0xcc, 0xd6, 0x5e, 0xc2, 0xd4, 0xd7, 0x0e, 0xa1, 0x9c, 0xce, 0x7a, 0x11, 0x43, 0x7b,
	0xff, 0xe5, 0x98, 0x65, 0x13, 0x2a, 0x92, 0x74, 0xd0, 0x6a, 0x7e, 0xf6, 0xf2, 0x40, 0x86, 0x40,
	0x52, 0xb6, 0xb6, 0x77, 0x5a, 0x87, 0xdb, 0xf6, 0x57, 0xa6, 0x3e, 0x92, 0xda, 0xb2, 0x05, 0xc9,
	0x2c, 0xac, 0xdd, 0x01, 0x18, 0x35, 0x0e, 0xb9, 0x0a, 0x8b, 0xa3, 0x15, 0x06, 0xd0, 0xd4, 0x36,
	0xfe, 0x38, 0x0f, 0x46, 0x16, 0xd3, 0x5e, 0x41, 0x6d, 0xfc, 0xcd, 0x87, 0x7c, 0x3f, 0xbf, 0x80,
	0x73, 0x5f, 0x4f, 0x1b, 0x1f, 0x9e, 0x4f, 0x58, 0xd5, 0xec, 0x2f, 0xe0, 0x6a, 0xce, 0x0d, 0x9c,
	0x7c, 0x34, 0xd5, 0xc8, 0x94, 0xe7, 0x9c, 0xc6, 0x0f, 0xde, 0x42, 0x43, 0xed, 0xfd, 0x4b, 0x58,
	0xca, 0x9b, 0x95, 0x64, 0xba, 0xa9, 0x69, 0x57, 0x9d, 0xc6, 0xc6, 0xdb, 0xa8, 0xa8, 0xed, 0xbf,
	0xd5, 0xe0, 0xd6, 0x4c, 0xe8, 0x25, 0x8f, 0xa6, 0x5a, 0x7d, 0x23, 0xfa, 0x37, 0x7e, 0x72, 0x21,
	0x5d, 0xe5, 0xda, 0xaf, 0x34, 0xa8, 0x4f, 0xbb, 0xcf, 0x93, 0x87, 0x6f, 0x7b, 0xff, 0x97, 0x0e,
	0x7d, 0x7c, 0xb1, 0xcf, 0x06, 0xc2, 0xe1, 0xca, 0x19, 0xc8, 0x23, 0xeb, 0x53, 0x4f, 0x97, 0x3b,
	0x68, 0x1a, 0x0f, 0xce, 0x2d, 0x2f, 0x77, 0x6d, 0x54, 0x7e, 0xff, 0x97, 0x7a, 0x29, 0xd1, 0xf9,
	0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x69, 0xdd, 0xb7, 0x89, 0xd1, 0x1d, 0x00, 0x00,
}
