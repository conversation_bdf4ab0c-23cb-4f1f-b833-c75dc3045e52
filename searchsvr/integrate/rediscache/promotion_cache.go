package rediscache

import (
	"context"
	"fmt"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/game_platform/go-authsdk/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/geohash"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

const (
	promotionCachePrefix      = "foodalgo:search:promotion:cache:%v:%v:%v:%s:%s"
	promotionCachePrefixForVN = "foodalgo:search:promotion:cache:%v:%v:%v:%d:%s:%s"
	promotionCacheTTL         = 5 * time.Minute
	promotionGeoHashLen       = 7
	StorePromotionCachekey    = "new:foodalgo:search:promotion:cache:%v:%v:%v:%s:%s"
)

type PromotionCacheRedis struct {
	client *redis.Client
}

func NewPromotionCacheRedis(client *redis.Client) *PromotionCacheRedis {
	return &PromotionCacheRedis{
		client: client,
	}
}

func (s *PromotionCacheRedis) Get(ctx context.Context, loc *foodalgo_search.Geo,
	keyword string, buyerId uint64) (map[uint64]*model.PromotionData, error) {
	geo := geohash.RedisEncodeWithPrecision(float64(loc.GetLatitude()), float64(loc.GetLongitude()), promotionGeoHashLen)
	key := fmt.Sprintf(promotionCachePrefix, buyerId, geo, keyword, env.GetEnv(), env.GetCID())
	return s.getCache(ctx, loc, keyword, key)
}

func (s *PromotionCacheRedis) GetStorePromo(ctx context.Context, loc *foodalgo_search.Geo,
	keyword string, buyerId uint64) (map[uint64]*model.StorePromotionData, error) {
	geo := geohash.RedisEncodeWithPrecision(float64(loc.GetLatitude()), float64(loc.GetLongitude()), promotionGeoHashLen)
	key := fmt.Sprintf(StorePromotionCachekey, buyerId, geo, keyword, env.GetEnv(), env.GetCID())
	return s.getPromoCache(ctx, loc, keyword, key)
}

func (s *PromotionCacheRedis) getCache(ctx context.Context, loc *foodalgo_search.Geo, keyword string, key string) (map[uint64]*model.PromotionData, error) {
	retMap := make(map[uint64]*model.PromotionData)
	hit := 0
	defer metric_reporter2.ReportHitRateRequest(1, hit, "Promotion.getCache")
	startTime := time.Now()
	val, err := s.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "getPromoCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "getPromoCache")
	if err != nil && err != redis.Nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("PromotionCacheRedis.Get redis get",
			zap.String("loc", loc.String()), zap.String("keyword", keyword), zap.String("key", key), zap.String("val", val))
		reporter.ReportClientRequestError(1, "getPromoCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "getPromoCache", "0")
	if err == redis.Nil {
		return nil, nil
	}

	err = json.Unmarshal([]byte(val), &retMap)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("PromotionCacheRedis.Get redis get",
			zap.String("loc", loc.String()), zap.String("keyword", keyword), zap.String("key", key), zap.String("val", val))
		reporter.ReportClientRequestError(1, "getPromoCache.Unmarshal", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, zap.Error(err))
	}
	reporter.ReportClientRequestError(1, "getPromoCache.Unmarshal", "0")
	hit = 1
	return retMap, nil
}

func (s *PromotionCacheRedis) getPromoCache(ctx context.Context, loc *foodalgo_search.Geo, keyword string, key string) (map[uint64]*model.StorePromotionData, error) {
	retMap := make(map[uint64]*model.StorePromotionData)
	hit := 0
	defer metric_reporter2.ReportHitRateRequest(1, hit, "Promotion.getPromoCache")
	startTime := time.Now()
	val, err := s.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "getStorePromoCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "getStorePromoCache")
	if err != nil && err != redis.Nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("PromotionCacheRedis.Get redis get",
			zap.String("loc", loc.String()), zap.String("keyword", keyword), zap.String("key", key), zap.String("val", val))
		reporter.ReportClientRequestError(1, "getStorePromoCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "getStorePromoCache", "0")
	if err == redis.Nil {
		return nil, nil
	}

	err = json.Unmarshal([]byte(val), &retMap)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("PromotionCacheRedis.Get redis get",
			zap.String("loc", loc.String()), zap.String("keyword", keyword), zap.String("key", key), zap.String("val", val))
		reporter.ReportClientRequestError(1, "getStorePromoCache.Unmarshal", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, zap.Error(err))
	}
	reporter.ReportClientRequestError(1, "getStorePromoCache.Unmarshal", "0")
	hit = 1
	return retMap, nil
}

func (s *PromotionCacheRedis) Set(ctx context.Context, loc *foodalgo_search.Geo, keyword string,
	buyerId uint64, promotion map[uint64]*model.PromotionData) error {
	geo := geohash.RedisEncodeWithPrecision(float64(loc.GetLatitude()), float64(loc.GetLongitude()), promotionGeoHashLen)
	key := fmt.Sprintf(promotionCachePrefix, buyerId, geo, keyword, env.GetEnv(), env.GetCID())

	return s.setCache(ctx, loc, promotion, key)
}

func (s *PromotionCacheRedis) SetStorePromo(ctx context.Context, loc *foodalgo_search.Geo, keyword string,
	buyerId uint64, promotion map[uint64]*model.StorePromotionData) error {
	geo := geohash.RedisEncodeWithPrecision(float64(loc.GetLatitude()), float64(loc.GetLongitude()), promotionGeoHashLen)
	key := fmt.Sprintf(StorePromotionCachekey, buyerId, geo, keyword, env.GetEnv(), env.GetCID())

	return s.setPromoCache(ctx, loc, promotion, key)
}

func (s *PromotionCacheRedis) setCache(ctx context.Context, loc *foodalgo_search.Geo, promotion map[uint64]*model.PromotionData, key string) error {
	val, err := json.Marshal(promotion)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SearchESCacheRedis.Set json marshal",
			zap.String("loc", loc.String()), zap.String("key", key), zap.Any("searchStoreResultIDs", promotion))
		reporter.ReportClientRequestError(1, "setPromoCache.Marshal", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "setPromoCache.Marshal", "0")
	startTime := time.Now()
	err = s.client.Set(ctx, key, val, promotionCacheTTL).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "setPromoCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "setPromoCache")
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SearchESCacheRedis.Set redis set",
			zap.String("loc", loc.String()), zap.String("key", key), zap.String("val", string(val)))
		reporter.ReportClientRequestError(1, "setPromoCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "setPromoCache", "0")
	return nil
}

func (s *PromotionCacheRedis) setPromoCache(ctx context.Context, loc *foodalgo_search.Geo, promotion map[uint64]*model.StorePromotionData, key string) error {
	val, err := json.Marshal(promotion)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SearchESCacheRedis.Set json marshal",
			zap.String("loc", loc.String()), zap.String("key", key), zap.Any("searchStoreResultIDs", promotion))
		reporter.ReportClientRequestError(1, "setStorePromoCache.Marshal", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "setStorePromoCache.Marshal", "0")
	startTime := time.Now()
	err = s.client.Set(ctx, key, val, promotionCacheTTL).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "setStorePromoCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "setStorePromoCache")
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SearchESCacheRedis.Set redis set",
			zap.String("loc", loc.String()), zap.String("key", key), zap.String("val", string(val)))
		reporter.ReportClientRequestError(1, "setStorePromoCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "setStorePromoCache", "0")
	return nil
}
