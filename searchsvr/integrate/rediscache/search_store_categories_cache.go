package rediscache

import (
	"context"
	"fmt"
	"strconv"
	"time"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	SearchStoreCategoriesResultCacheKey = "foodalgo:store_categories:%s:%s:%s:%s:%s:%s" // publishId, storeIds, categories,keyword, env, cid
)

type SearchStoreCategoriesResultCache struct {
	client *redis.Client
}

func NewSearchStoreCategoriesResultCache(client *redis.Client) *SearchStoreCategoriesResultCache {
	return &SearchStoreCategoriesResultCache{
		client: client,
	}
}

func getSearchStoreCategoriesResultCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	publishIdKey := traceInfo.TraceRequest.PublishId
	store := "s"
	for _, storeId := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
		store = store + "_" + strconv.FormatUint(uint64(storeId), 10)
	}
	cate := "c"
	for _, category := range traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds() {
		cate = cate + "_" + strconv.FormatUint(category, 10)
	}
	key := fmt.Sprintf(SearchStoreCategoriesResultCacheKey, publishIdKey, store, cate, traceInfo.QueryKeyword, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getSearchStoreCategoriesResultCacheKey key", zap.String("key", key))
	return key
}

func (r *SearchStoreCategoriesResultCache) GetSearchStoreCategoriesResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]uint32, error) {
	hit := 0
	defer func() {
		metric_reporter2.ReportHitRateRequest(1, hit, "GetSearchStoreCategoriesResult")
	}()
	key := getSearchStoreCategoriesResultCacheKey(ctx, traceInfo)
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetSearchStoreCategoriesResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetSearchStoreCategoriesResult")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchStoreCategoriesResult get redis failed",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchStoreCategoriesResult", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetSearchStoreCategoriesResult", "0")
	if err == redis.Nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchStoreCategoriesResult nil from cache", logkit.String("key", key))
		return nil, nil
	}
	data := foodalgo_search.SearchDishesResp_Store{}
	err = proto.Unmarshal([]byte(val), &data)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchStoreCategoriesResult redis get",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchStoreCategoriesResult.Unmarshal", "failed")
		return nil, fmt.Errorf("parse to gogo proto error")
	}
	reporter.ReportClientRequestError(1, "GetSearchStoreCategoriesResult.Unmarshal", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchStoreCategoriesResult success from cache", logkit.String("key", key), logkit.Int("size", len(data.GetL3SkuCategoryIds())))
	hit = 1
	return data.GetL3SkuCategoryIds(), nil
}

func (r *SearchStoreCategoriesResultCache) SetSearchStoreCategoriesResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, data []uint32) error {
	if len(data) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchStoreCategoriesResult data is empty", logkit.Any("req", traceInfo.TraceRequest))
		return nil
	}
	key := getSearchStoreCategoriesResultCacheKey(ctx, traceInfo)
	rsp := foodalgo_search.SearchDishesResp_Store{L3SkuCategoryIds: data}
	val, err := proto.Marshal(&rsp)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SetSearchStoreCategoriesResult proto marshal failed",
			zap.String("key", key),
			zap.Any("cal", data))
		reporter.ReportClientRequestError(1, "SetSearchStoreCategoriesResult.Marshal", "failed")
		return fmt.Errorf("parse from proto error")
	}
	reporter.ReportClientRequestError(1, "SetSearchStoreCategoriesResult.Marshal", "0")
	expireTime := apollo.SearchApolloCfg.UserCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 300 // 300s,5min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "SetSearchStoreCategoriesResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetSearchStoreCategoriesResult")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SetSearchStoreCategoriesResult",
			zap.String("key", key),
			zap.String("val", string(val)),
			zap.Any("expire", expire))
		reporter.ReportClientRequestError(1, "SetSearchStoreCategoriesResult", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetSearchStoreCategoriesResult", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchStoreCategoriesResult set to cache", zap.String("key", key), zap.Int("size", len(data)))
	return nil
}
