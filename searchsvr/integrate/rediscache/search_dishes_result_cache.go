package rediscache

import (
	"context"
	"fmt"
	"strconv"
	"time"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	SearchDishesResultCacheKey = "foodalgo:store_dishes:%s:%s:%s:%s:%d:%d:%s:%s" // publishId, storeIds, categories, keyword, from, env, cid
)

type SearchDishesResultCache struct {
	client *redis.Client
}

func NewSearchDishesResultCache(client *redis.Client) *SearchDishesResultCache {
	return &SearchDishesResultCache{
		client: client,
	}
}

func getSearchDishesResultCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	publishIdKey := traceInfo.TraceRequest.PublishId
	store := "s"
	for _, storeId := range traceInfo.TraceRequest.GetFilterType().GetStoreIds() {
		store = store + "_" + strconv.FormatUint(uint64(storeId), 10)
	}
	cate := "c"
	for _, category := range traceInfo.TraceRequest.GetFilterType().GetL3SkuCategoryIds() {
		cate = cate + "_" + strconv.FormatUint(category, 10)
	}
	key := fmt.Sprintf(SearchDishesResultCacheKey, publishIdKey, store, cate, traceInfo.QueryKeyword, traceInfo.TraceRequest.SearchTime, traceInfo.From, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getSearchDishesResultCacheKey key", zap.String("key", key))
	return key
}

func (r *SearchDishesResultCache) GetSearchDishesResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*foodalgo_search.SearchDishesResp_Store, error) {
	hit := 0
	defer func() {
		metric_reporter2.ReportHitRateRequest(1, hit, "GetSearchDishesResult")
	}()
	key := getSearchDishesResultCacheKey(ctx, traceInfo)
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetSearchDishesResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetSearchDishesResult")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchDishesResult get redis failed",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchDishesResult", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetSearchDishesResult", "0")
	if err == redis.Nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchDishesResult nil from cache", logkit.String("key", key))
		return nil, nil
	}
	data := foodalgo_search.SearchDishesResp{}
	err = proto.Unmarshal([]byte(val), &data)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchDishesResult redis get",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchDishesResult.Unmarshal", "failed")
		return nil, fmt.Errorf("parse to gogo proto error")
	}
	reporter.ReportClientRequestError(1, "GetSearchDishesResult.Unmarshal", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchDishesResult success from cache", logkit.String("key", key), logkit.Int("size", len(data.GetStores())))
	hit = 1
	return data.GetStores(), nil
}

func (r *SearchDishesResultCache) SetSearchDishesResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, data []*foodalgo_search.SearchDishesResp_Store) error {
	if len(data) == 0 || (len(data) == 1 && len(data[0].GetDishes()) == 0) {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchDishesResult data is empty", logkit.Any("req", traceInfo.TraceRequest))
		return nil
	}
	key := getSearchDishesResultCacheKey(ctx, traceInfo)
	rsp := foodalgo_search.SearchDishesResp{Stores: data}
	val, err := proto.Marshal(&rsp)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SetSearchDishesResult proto marshal failed",
			zap.String("key", key),
			zap.Any("data", data))
		reporter.ReportClientRequestError(1, "SetSearchDishesResult.Marshal", "failed")
		return fmt.Errorf("parse from proto error")
	}
	reporter.ReportClientRequestError(1, "SetSearchDishesResult.Marshal", "0")
	expireTime := apollo.SearchApolloCfg.UserCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 300 // 300s,5min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "SetSearchDishesResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetSearchDishesResult")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SetSearchDishesResult",
			zap.String("key", key),
			zap.String("val", string(val)),
			zap.Any("expire", expire))
		reporter.ReportClientRequestError(1, "SetSearchDishesResult", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetSearchDishesResult", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchDishesResult set to cache", zap.String("key", key), zap.Int("size", len(data)))
	return nil
}
