package rediscache

import (
	"context"
	"fmt"
	"strings"
	"time"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"github.com/gogo/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	// 降级QP 缓存
	QPCacheKey        = "QP:%s:%s:%s" // query, env, cid
	QPRewriteCacheKey = "QP_Rewrite:%s:%s:%s"

	// 正常QP结果缓存
	QPNormalCachePrefix        = "normal:QP:%s:%s:%s"
	QPNormalRewriteCachePrefix = "normal:QP_Rewrite:%s:%s:%s"

	QPCacheTTL       = 12 * time.Hour
	QPNormalCacheTTL = 1 * time.Hour
)

type QPResultRedis struct {
	client *redis.Client
}

func NewQPResultRedis(client *redis.Client) *QPResultRedis {
	return &QPResultRedis{
		client: client,
	}
}

func (r *QPResultRedis) GetDowngradeCache(ctx context.Context, query string, rewrite bool, traceInfo *traceinfo.TraceInfo) (*qp.GetQueryProcessingKeywordResp, error) {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return nil, fmt.Errorf("downgrade redis")
	}
	hit := 0
	key := ""
	if rewrite {
		key = fmt.Sprintf(QPRewriteCacheKey, query, env.GetEnv(), env.GetCID())
	} else {
		key = fmt.Sprintf(QPCacheKey, query, env.GetEnv(), env.GetCID())
	}
	defer metric_reporter2.ReportHitRateRequest(1, hit, "QPResultRedis.GetDowngradeCache")
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-GetDowngradeCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "qp-GetDowngradeCache")
	if err != nil && err != redis.Nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"QPResultRedis.Get redis get",
			zap.String("query", query),
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "qp-GetDowngradeCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "qp-GetDowngradeCache", "0")
	if err == redis.Nil {
		return nil, fmt.Errorf("cache is nil")
	}
	var qpResp qp.GetQueryProcessingKeywordResp
	err = proto.Unmarshal([]byte(val), &qpResp)
	if err != nil {
		reporter.ReportClientRequestError(1, "qp-GetDowngradeCache.Unmarshal", "failed")
		return nil, fmt.Errorf("proto parse fail")
	}
	reporter.ReportClientRequestError(1, "qp-GetDowngradeCache.Unmarshal", "0")
	hit = 1
	logger.MyDebug(ctx, traceInfo.IsDebug, "get qp cache", zap.String("key", key), zap.Any("val", qpResp))
	return &qpResp, nil
}

func (r *QPResultRedis) SetDowngradeCache(ctx context.Context, query string, rewrite bool, qpResp *qp.GetQueryProcessingKeywordResp, traceInfo *traceinfo.TraceInfo) error {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return fmt.Errorf("downgrade redis")
	}

	if qpResp == nil {
		return nil
	}
	key := ""
	if rewrite {
		key = fmt.Sprintf(QPRewriteCacheKey, query, env.GetEnv(), env.GetCID())
	} else {
		key = fmt.Sprintf(QPCacheKey, query, env.GetEnv(), env.GetCID())
	}

	val, err := proto.Marshal(qpResp)
	if err != nil {
		reporter.ReportClientRequestError(1, "qp-SetDowngradeCache.Marshal", "failed")
		return fmt.Errorf("proto marshal error")
	}
	reporter.ReportClientRequestError(1, "qp-SetDowngradeCache.Marshal", "0")
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, QPCacheTTL).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-SetDowngradeCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "qp-SetDowngradeCache")
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"QPResultRedis.Set redis set",
			zap.String("keyword", query),
			zap.String("key", key),
			zap.String("val", string(val)))
		reporter.ReportClientRequestError(1, "qp-SetDowngradeCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "qp-SetDowngradeCache", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "set qp cache", zap.String("key", key), zap.Any("val", qpResp))
	return nil
}

func (r *QPResultRedis) GetNormalResultCache(ctx context.Context, query string, rewrite bool, traceInfo *traceinfo.TraceInfo) (*qp.GetQueryProcessingKeywordResp, error) {
	if !apollo.SearchApolloCfg.QPNormalCacheSwitch {
		return nil, fmt.Errorf("close normal cache")
	}

	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return nil, fmt.Errorf("downgrade redis")
	}

	hit := 0
	key := ""
	if rewrite {
		key = fmt.Sprintf(QPNormalRewriteCachePrefix, query, env.GetEnv(), env.GetCID())
	} else {
		key = fmt.Sprintf(QPNormalCachePrefix, query, env.GetEnv(), env.GetCID())
	}

	if apollo.SearchApolloCfg.QPABTestList != "" {
		abTest := strings.Split(apollo.SearchApolloCfg.QPABTestList, ",")
		for _, val := range abTest {
			key = fmt.Sprintf("%s:%s", key, traceInfo.ABTestGroup.GetABTestVal(val))
		}
	}

	defer metric_reporter2.ReportHitRateRequest(1, hit, "QPResultRedis.GetNormalResultCache")
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-GetNormalCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "qp-GetNormalCache")
	if err != nil && err != redis.Nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"QPResultRedis.Get redis get",
			zap.String("query", query),
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "qp-GetNormalCache", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "qp-GetNormalCache", "0")
	if err == redis.Nil {
		return nil, fmt.Errorf("cache is nil")
	}
	var qpResp qp.GetQueryProcessingKeywordResp
	err = proto.Unmarshal([]byte(val), &qpResp)
	if err != nil {
		reporter.ReportClientRequestError(1, "qp-GetNormalCache.Unmarshal", "failed")
		return nil, fmt.Errorf("proto parse fail")
	}
	reporter.ReportClientRequestError(1, "qp-GetNormalCache.Unmarshal", "0")
	hit = 1
	logger.MyDebug(ctx, traceInfo.IsDebug, "get qp cache", zap.String("key", key), zap.Any("val", qpResp))
	return &qpResp, nil
}

func (r *QPResultRedis) SetNormalResultCache(ctx context.Context, query string, rewrite bool, qpResp *qp.GetQueryProcessingKeywordResp, traceInfo *traceinfo.TraceInfo) error {
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		return fmt.Errorf("downgrade redis")
	}

	if qpResp == nil {
		return nil
	}
	key := ""
	if rewrite {
		key = fmt.Sprintf(QPNormalRewriteCachePrefix, query, env.GetEnv(), env.GetCID())
	} else {
		key = fmt.Sprintf(QPNormalCachePrefix, query, env.GetEnv(), env.GetCID())
	}

	if apollo.SearchApolloCfg.QPABTestList != "" {
		abTest := strings.Split(apollo.SearchApolloCfg.QPABTestList, ",")
		for _, val := range abTest {
			key = fmt.Sprintf("%s:%s", key, traceInfo.ABTestGroup.GetABTestVal(val))
		}
	}

	val, err := proto.Marshal(qpResp)
	if err != nil {
		reporter.ReportClientRequestError(1, "qp-SetNormalCache.Marshal", "failed")
		return fmt.Errorf("proto marshal error")
	}
	reporter.ReportClientRequestError(1, "qp-SetNormalCache.Marshal", "0")
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, QPNormalCacheTTL).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-SetNormalCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "qp-SetNormalCache")
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"QPResultRedis.Set redis set",
			zap.String("keyword", query),
			zap.String("key", key),
			zap.String("val", string(val)))
		reporter.ReportClientRequestError(1, "qp-SetNormalCache", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "qp-SetNormalCache", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "set qp cache", zap.String("key", key), zap.Any("val", qpResp))
	return nil
}
