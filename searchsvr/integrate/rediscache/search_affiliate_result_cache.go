package rediscache

import (
	"context"
	"fmt"
	"time"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/gogo/protobuf/proto"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	SearchDishesAffiliateResultCacheKey = "foodalgo:search_dishes_affiliate:%s:%d:%s:%s:%s" // publishId, storeIds, keyword, env, cid
)

type SearchAffiliateResultCache struct {
	client *redis.Client
}

func NewSearchAffiliateResultCache(client *redis.Client) *SearchAffiliateResultCache {
	return &SearchAffiliateResultCache{
		client: client,
	}
}

func getSearchDishesAffiliateResultCacheKey(ctx context.Context, traceInfo *traceinfo.TraceInfo) string {
	key := fmt.Sprintf(SearchDishesAffiliateResultCacheKey, traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.StoreId, traceInfo.QueryKeyword, env.GetEnv(), env.GetCID())
	logger.MyDebug(ctx, traceInfo.IsDebug, "getSearchDishesAffiliateResultCacheKey key", zap.String("key", key))
	return key
}

func (r *SearchAffiliateResultCache) GetSearchStoresAffiliateResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*foodalgo_search.SearchStoresAffiliateResp_StoreInfo, error) {
	hit := 0
	defer func() {
		metric_reporter2.ReportHitRateRequest(1, hit, "GetSearchStoresAffiliateResult")
	}()
	key := getSearchDishesAffiliateResultCacheKey(ctx, traceInfo)
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetSearchStoresAffiliateResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetSearchStoresAffiliateResult")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchStoresAffiliateResult get redis failed",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchStoresAffiliateResult", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetSearchStoresAffiliateResult", "0")
	if err == redis.Nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchStoresAffiliateResult nil from cache", logkit.String("key", key))
		return nil, nil
	}
	data := foodalgo_search.SearchStoresAffiliateResp{}
	err = proto.Unmarshal([]byte(val), &data)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchStoresAffiliateResult redis get",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchStoresAffiliateResult.Unmarshal", "failed")
		return nil, fmt.Errorf("parse to gogo proto error")
	}
	reporter.ReportClientRequestError(1, "GetSearchStoresAffiliateResult.Unmarshal", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchStoresAffiliateResult success from cache", logkit.String("key", key), logkit.Int("size", len(data.GetStores())))
	hit = 1
	return data.GetStores(), nil
}

func (r *SearchAffiliateResultCache) SetSearchStoresAffiliateResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, stores []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo) error {
	if len(stores) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchStoresAffiliateResult data is empty, skip cache")
		return nil
	}
	key := getSearchDishesAffiliateResultCacheKey(ctx, traceInfo)
	rsp := foodalgo_search.SearchStoresAffiliateResp{Stores: stores}
	val, err := proto.Marshal(&rsp)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SetSearchStoresAffiliateResult proto marshal failed",
			zap.String("key", key),
			zap.Any("stores", stores))
		reporter.ReportClientRequestError(1, "SetSearchStoresAffiliateResult.Marshal", "failed")
		return fmt.Errorf("parse from proto error")
	}
	reporter.ReportClientRequestError(1, "SetSearchStoresAffiliateResult.Marshal", "0")
	expireTime := apollo.SearchApolloCfg.AffiliateCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 900 // 900s,15min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "SetSearchStoresAffiliateResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetSearchStoresAffiliateResult")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SetSearchStoresAffiliateResult",
			zap.String("key", key),
			zap.String("val", string(val)),
			zap.Any("expire", expire))
		reporter.ReportClientRequestError(1, "SetSearchStoresAffiliateResult", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetSearchStoresAffiliateResult", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchStoresAffiliateResult set to cache", zap.String("key", key), zap.Int("size", len(stores)))
	return nil
}

func (r *SearchAffiliateResultCache) GetSearchDishesAffiliateResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) ([]*foodalgo_search.SearchDishesAffiliateResp_DishInfo, error) {
	hit := 0
	defer func() {
		metric_reporter2.ReportHitRateRequest(1, hit, "GetSearchDishesAffiliateResult")
	}()
	key := getSearchDishesAffiliateResultCacheKey(ctx, traceInfo)
	startTime := time.Now()
	val, err := r.client.Get(ctx, key).Result()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetSearchDishesAffiliateResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "GetSearchDishesAffiliateResult")
	if err != nil && err != redis.Nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchDishesAffiliateResult get redis failed",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchDishesAffiliateResult", "failed")
		return nil, errors.Wrap(errno.ErrRedisUnknown, logkit.Err(err))
	}
	reporter.ReportClientRequestError(1, "GetSearchDishesAffiliateResult", "0")
	if err == redis.Nil {
		logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchDishesAffiliateResult nil from cache", logkit.String("key", key))
		return nil, nil
	}
	data := foodalgo_search.SearchDishesAffiliateResp{}
	err = proto.Unmarshal([]byte(val), &data)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"GetSearchDishesAffiliateResult redis get",
			zap.String("key", key),
			zap.String("val", val))
		reporter.ReportClientRequestError(1, "GetSearchDishesAffiliateResult.Unmarshal", "failed")
		return nil, fmt.Errorf("parse to gogo proto error")
	}
	reporter.ReportClientRequestError(1, "GetSearchDishesAffiliateResult.Unmarshal", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "GetSearchDishesAffiliateResult success from cache", logkit.String("key", key), logkit.Int("size", len(data.GetDishes())))
	hit = 1
	return data.GetDishes(), nil
}

func (r *SearchAffiliateResultCache) SetSearchDishesAffiliateResult(ctx context.Context, traceInfo *traceinfo.TraceInfo, dishes []*foodalgo_search.SearchDishesAffiliateResp_DishInfo) error {
	if len(dishes) == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchDishesAffiliateResult data is empty, skip cache")
		return nil
	}
	key := getSearchDishesAffiliateResultCacheKey(ctx, traceInfo)
	rsp := foodalgo_search.SearchDishesAffiliateResp{Dishes: dishes}
	val, err := proto.Marshal(&rsp)
	if err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("SetSearchDishesAffiliateResult proto marshal failed",
			zap.String("key", key),
			zap.Any("dishes", dishes))
		reporter.ReportClientRequestError(1, "SetSearchDishesAffiliateResult.Marshal", "failed")
		return fmt.Errorf("parse from proto error")
	}
	reporter.ReportClientRequestError(1, "SetSearchDishesAffiliateResult.Marshal", "0")
	expireTime := apollo.SearchApolloCfg.AffiliateCacheExpireSeconds
	if expireTime == 0 {
		expireTime = 900 // 900s,15min
	}
	var expire = time.Duration(expireTime) * time.Second
	startTime := time.Now()
	err = r.client.Set(ctx, key, val, expire).Err()
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "SetSearchDishesAffiliateResult"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeMiddleware, "", "", "SetSearchDishesAffiliateResult")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).With(logkit.Err(err)).Error(
			"SetSearchDishesResult",
			zap.String("key", key),
			zap.String("val", string(val)),
			zap.Any("expire", expire))
		reporter.ReportClientRequestError(1, "SetSearchDishesAffiliateResult", "failed")
		return errors.Wrap(errno.ErrRedisUnknown)
	}
	reporter.ReportClientRequestError(1, "SetSearchDishesAffiliateResult", "0")
	logger.MyDebug(ctx, traceInfo.IsDebug, "SetSearchDishesAffiliateResult set to cache", zap.String("key", key), zap.Int("size", len(dishes)))
	return nil
}
