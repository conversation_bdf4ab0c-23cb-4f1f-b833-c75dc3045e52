package localcache

import (
	"context"
	"errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"strconv"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

func (s *StoreCacheSys) MGetDishFlashSaleCache(ctx context.Context, flashSaleIds []uint64, flashSaleMap map[uint64]*o2oalgo.FlashSaleDishDiscount) []uint64 {
	missFlashSaleIds := make([]uint64, 0, len(flashSaleIds))
	for _, flashSaleId := range flashSaleIds {
		if flashSaleId == 0 {
			continue
		}

		key := strconv.FormatUint(flashSaleId, 10)
		pb := new(o2oalgo.FlashSaleDishDiscount)
		err := s.flashSaleCache.GetFn(util.StringToByte(key), func(bVal []byte) error {
			// 如果key没有找到，会直接报错，不会往下走
			if len(bVal) > 0 {
				// key找到，且有值
				return pb.Unmarshal(bVal)
			}
			return errors.New("dish flash sale entry is expire")
		})

		if err == nil {
			// key找到，且有值
			flashSaleMap[flashSaleId] = pb
		} else {
			// key没有找到，或者key找到，但是值是空的
			missFlashSaleIds = append(missFlashSaleIds, flashSaleId)
		}
	}
	return missFlashSaleIds
}

func (s *StoreCacheSys) MetGetDishFlashSaleCacheBatch(ctx context.Context, flashSaleIds []uint64, flashSaleMap map[uint64]*o2oalgo.FlashSaleDishDiscount) []uint64 {
	batchSize := 100
	if apollo.SearchApolloCfg.FSDishFeatureCacheBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.FSDishFeatureItemBatchSize)
	}

	flashSaleLen := len(flashSaleIds)
	totalBatch := (flashSaleLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return []uint64{}
	}
	batchResultList := make([]map[uint64]*o2oalgo.FlashSaleDishDiscount, totalBatch)
	batchMissList := make([][]uint64, totalBatch)
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "MetGetDishFlashSaleCacheBatch", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > flashSaleLen {
				end = flashSaleLen
			}
			batchFlashSaleIds := flashSaleIds[begin:end]
			batchList := make(map[uint64]*o2oalgo.FlashSaleDishDiscount, len(batchFlashSaleIds))
			missIds := s.MGetDishFlashSaleCache(ctx, batchFlashSaleIds, batchList)
			batchMissList[groupIndex] = missIds
			batchResultList[groupIndex] = batchList
		}, i)
	}
	wg.Wait()

	for _, batchFlashSaleMap := range batchResultList {
		for dishId, dishFeature := range batchFlashSaleMap {
			flashSaleMap[dishId] = dishFeature
		}
	}
	missIds := make([]uint64, 0, len(flashSaleIds))
	for _, ids := range batchMissList {
		missIds = append(missIds, ids...)
	}
	return missIds
}

func (s *StoreCacheSys) MSetDishFlashSaleCache(ctx context.Context, flashSaleMap map[uint64]*o2oalgo.FlashSaleDishDiscount, cacheTime int) {
	if cacheTime <= 0 {
		// 不使用缓存
		return
	}
	for key, flashSale := range flashSaleMap {
		if flashSale == nil {
			continue
		}

		id := strconv.FormatUint(key, 10)
		bVal, err1 := flashSale.Marshal()
		if err1 != nil {
			logkit.FromContext(ctx).Error("failed to flashSale Marshal", logkit.Any("err", err1), logkit.Any("flashSale", flashSale))
			continue
		}
		err := s.flashSaleCache.Set(util.StringToByte(id), bVal, GetNextCacheTimeSec(cacheTime))
		if err != nil {
			logkit.FromContext(ctx).Error("failed to set flashSale cache", logkit.Any("err", err))
		}
	}
}
