package localcache

import (
	"context"
	"errors"
	"strconv"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
)

func (s *StoreCacheSys) MGetDishMetaCache(ctx context.Context, dishIds []uint64, dishMetaMap map[uint64]*o2oalgo.Dish, useCache bool) []uint64 {
	if !useCache {
		return dishIds
	}
	missDishIds := make([]uint64, 0, len(dishIds))
	for _, dishId := range dishIds {
		if dishId == 0 {
			continue
		}

		key := strconv.FormatUint(dishId, 10)
		pb := new(o2oalgo.Dish)
		err := s.dishMetaCache.GetFn(util.StringToByte(key), func(bVal []byte) error {
			// 如果key没有找到，会直接报错，不会往下走
			if len(bVal) > 0 {
				// key找到，且有值
				return pb.XXX_Unmarshal(bVal)
			}

			return errors.New("entry is expire")
		})

		if err == nil {
			// key找到，且有值
			dishMetaMap[dishId] = pb
		} else {
			// key没有找到，或者key找到，但是值是空的
			missDishIds = append(missDishIds, dishId)
		}
	}
	return missDishIds
}

func (s *StoreCacheSys) MSetDishMetaCache(ctx context.Context, dishMetaMap map[uint64]*o2oalgo.Dish, cacheTime int) {
	if cacheTime <= 0 {
		// 不使用缓存
		return
	}
	for key, dish := range dishMetaMap {
		id := strconv.FormatUint(key, 10)
		bval, _ := dish.Marshal()
		err := s.dishMetaCache.Set(util.StringToByte(id), bval, GetNextCacheTimeSec(cacheTime))
		if err != nil {
			logkit.FromContext(ctx).Error("failed to set dish meta cache", logkit.Any("err", err))
		}
	}
}
