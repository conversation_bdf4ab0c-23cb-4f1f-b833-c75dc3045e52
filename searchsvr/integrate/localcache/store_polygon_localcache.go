package localcache

import (
	"context"
	"encoding/binary"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"math"
	"time"

	metric "git.garena.com/shopee/digital-purchase/common/literals"
	"git.garena.com/shopee/digital-purchase/common/monitor/prometheus"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"
)

const (
	featureGeoPolygon = 28  // 多边形的特征（结合了特殊模式）
	MaxGetPolygonNum  = 100 // 多边形缓存截断，防止一次性大批量RPC调用
)

func (s *StoreCacheSys) MGetStorePolygon(ctx context.Context, keyList map[string]*polygon.Polygon) error {
	fetchKeyList := make([]string, 0)

	defer func() {
		if e := recover(); e != nil {
			logkit.Error("local cache MGet panic", logkit.Any("err", e), logkit.Any("val", keyList))
		}
	}()
	for key := range keyList {
		polygon := &polygon.Polygon{}
		err := s.polygonCache.GetFn([]byte(key), func(bytes []byte) error {
			if len(bytes) > 0 {
				polygon = UmMarshalPolygon(bytes)
			} else {
				polygon = nil
			}
			return nil
		})
		if err != nil {
			polygon = nil
			fetchKeyList = append(fetchKeyList, key)
		}
		keyList[key] = polygon
	}
	defer func() {
		prometheus.ReportCounter(metric.CacheQueryTotal, float64(len(keyList)), prometheus.Label{Key: "name", Val: "StorePolygonData"})
		prometheus.ReportCounter(metric.CacheHit, float64(len(keyList)-len(fetchKeyList)), prometheus.Label{Key: "name", Val: "StorePolygonData"})
	}()

	if len(fetchKeyList) > 0 {
		result, err := s.GetStorePolygonFetchFunction(ctx, fetchKeyList)
		if err != nil {
			logkit.FromContext(ctx).Error("failed to get value with", zap.Strings("key", fetchKeyList), logkit.Err(err))
			return err
		}
		for _, key := range fetchKeyList {
			if value, ok := result[key]; ok {
				if value == nil {
					err = s.polygonCache.Set([]byte(key), []byte{}, GetNextCacheTimeSec(storePolygonCacheExpireSec))
				} else {
					err = s.polygonCache.Set([]byte(key), MarshalPolygon(value), GetNextCacheTimeSec(storePolygonCacheExpireSec))
				}
				if err != nil {
					logkit.FromContext(ctx).Error("failed to set cache", logkit.Any("err", err))
				}
				keyList[key] = value
			}
		}
	}

	return nil
}

// GetStorePolygonFetchFunction
// @Description: 当本地缓存拿不到时，指导local cache怎么去load
// @param keyList 缓存key list
// @param option 保留字段
// @return map[string]*geo.Polygon 多边形
// @return error 可能的错误
func (s *StoreCacheSys) GetStorePolygonFetchFunction(ctx context.Context, keyList []string) (map[string]*polygon.Polygon, error) {

	// 从配置拿到本地缓存批次数据，比如500
	maxBatchSize := apollo.SearchApolloCfg.PolygonDistanceLocalCacheBatchSize
	if maxBatchSize == 0 {
		maxBatchSize = MaxGetPolygonNum
	}
	if len(keyList) > int(maxBatchSize) {
		keyList = keyList[:maxBatchSize]
	}
	startTime := time.Now()
	polygonRsp, err := integrate.DataManageServiceClient.GetDataNoCache(ctx, featureGeoPolygon, keyList)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "GetPolygonDataNoCache"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "GetPolygonDataNoCache")
	if err != nil {
		metric_reporter.ReportClientRequestError(1, "GetPolygonDataNoCache", "failed")
		logkit.FromContext(ctx).Error("localcache.LoadStorePolygon get polygon error", zap.Error(err))
		return nil, err
	}
	metric_reporter.ReportClientRequestError(1, "GetPolygonDataNoCache", "0")
	polygonMap, err := polygon.BuildPolygon(ctx, polygonRsp)
	if err != nil {
		logkit.FromContext(ctx).Error("localcache.LoadStorePolygon build polygon error", zap.Error(err))
		return nil, err
	}
	return polygonMap, nil
}

func MarshalPolygon(polygon *polygon.Polygon) []byte {
	if polygon == nil || len(polygon.Latitudes) == 0 {
		return []byte{}
	}
	bytes := make([]byte, 0, len(polygon.Latitudes)*16)
	for i := 0; i < len(polygon.Latitudes); i++ {
		latBytes := Float64ToByte(polygon.Latitudes[i])
		lngBytes := Float64ToByte(polygon.Longitudes[i])
		bytes = append(bytes, latBytes...)
		bytes = append(bytes, lngBytes...)
	}
	return bytes
}
func UmMarshalPolygon(bytes []byte) *polygon.Polygon {
	if len(bytes)%16 != 0 {
		return nil
	}
	i := 0

	latitudes := make([]float64, 0, 200)
	longitudes := make([]float64, 0, 200)

	for i+16 <= len(bytes) {
		lat := ByteToFloat64(bytes[i : i+8])
		lng := ByteToFloat64(bytes[i+8 : i+16])
		latitudes = append(latitudes, lat)
		longitudes = append(longitudes, lng)

		i += 16
	}
	return &polygon.Polygon{Latitudes: latitudes, Longitudes: longitudes}
}

func Float64ToByte(float float64) []byte {
	bits := math.Float64bits(float)
	bytes := make([]byte, 8)
	binary.LittleEndian.PutUint64(bytes, bits)
	return bytes
}

func ByteToFloat64(bytes []byte) float64 {
	bits := binary.LittleEndian.Uint64(bytes)
	return math.Float64frombits(bits)
}
