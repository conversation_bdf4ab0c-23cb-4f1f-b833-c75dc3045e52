package localcache

import (
	"context"
	"errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
)

func (s *StoreCacheSys) MGetDishesFromFSCache(ctx context.Context, cacheKeys []string, dishFSRecallMap map[string]model.DishInfos) []string {
	missKeys := make([]string, 0, len(cacheKeys))
	for _, cacheKey := range cacheKeys {
		if len(cacheKey) == 0 {
			continue
		}

		pb := new(foodalgo_search.DishInfosCache)
		err := s.dishFromFSRecallCache.GetFn(util.StringToByte(cacheKey), func(bVal []byte) error {
			// 如果key没有找到，会直接报错，不会往下走
			if len(bVal) > 0 {
				// key找到，且有值
				return pb.XXX_Unmarshal(bVal)
			}
			return errors.New("store->dishes fs recall entry is expire")
		})

		if err == nil {
			// key找到，且有值
			dishInfos := make(model.DishInfos, 0, len(pb.DishListCache))
			for _, dish := range pb.DishListCache {
				dishInfos = append(dishInfos, &model.DishInfo{
					DishId:               dish.GetDishId(),
					StoreId:              dish.GetStoreId(),
					ESScore:              dish.GetEsScore(),
					Score:                dish.GetScore(),
					DishRecallIds:        dish.GetRecallIds(),
					DishRecallTypes:      dish.GetRecallTypes(),
					DishRecallPriorities: util2.ConvertInt32oIntSlice(dish.GetRecallPriorities()),
					RecallPriority:       int(dish.GetRecallPriority()),
					IsFromFS:             dish.GetIsFromFs(),
					IsFlashSale:          dish.GetIsFlashSale(),
					FlashSaleIds:         dish.GetFlashSaleIds(),
				})
			}
			dishFSRecallMap[cacheKey] = dishInfos
		} else {
			// key没有找到，或者key找到，但是值是空的
			missKeys = append(missKeys, cacheKey)
		}
	}
	return missKeys
}

func (s *StoreCacheSys) MSetDishesFromFSCache(ctx context.Context, dishFSRecallMap map[string]model.DishInfos, cacheTime int) {
	if cacheTime <= 0 {
		// 不使用缓存
		return
	}
	for key, dishes := range dishFSRecallMap {

		// dishes 转成 pb
		pbDishes := &foodalgo_search.DishInfosCache{
			Key:           proto.String(key),
			DishListCache: make([]*foodalgo_search.DishInfoCache, 0, len(dishes)),
		}
		for _, dish := range dishes {
			pbDishes.DishListCache = append(pbDishes.DishListCache, &foodalgo_search.DishInfoCache{
				DishId:           proto.Uint64(dish.DishId),
				StoreId:          proto.Uint64(dish.StoreId),
				EsScore:          proto.Float64(dish.ESScore),
				Score:            proto.Float64(dish.Score),
				RecallIds:        dish.DishRecallIds,
				RecallTypes:      dish.DishRecallTypes,
				RecallPriorities: util2.ConvertIntToInt32Slice(dish.DishRecallPriorities),
				RecallPriority:   proto.Int(dish.RecallPriority),
				IsFlashSale:      proto.Bool(dish.IsFlashSale),
				IsFromFs:         proto.Bool(dish.IsFromFS),
				FlashSaleIds:     dish.FlashSaleIds,
			})
		}

		bVal, err := proto.Marshal(pbDishes) // Convert your dish data to bytes
		if err != nil {
			logkit.FromContext(ctx).Error("failed to marshal dish data", logkit.Any("err", err))
			return
		}

		// Set to cache
		err = s.dishFromFSRecallCache.Set(util.StringToByte(key), bVal, GetNextCacheTimeSec(cacheTime))
		if err != nil {
			logkit.FromContext(ctx).Error("failed to set store->dishes fs recall cache", logkit.Any("err", err))
		}
	}
}
