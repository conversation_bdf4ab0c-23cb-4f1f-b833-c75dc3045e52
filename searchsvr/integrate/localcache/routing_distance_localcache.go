package localcache

import (
	"context"
	"encoding/binary"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"strconv"
	"strings"

	metric "git.garena.com/shopee/digital-purchase/common/literals"
	"git.garena.com/shopee/digital-purchase/common/monitor/prometheus"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/geo_service"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/geo_routing"
)

func (s *StoreCacheSys) MGetRoutingDistance(ctx context.Context, traceInfo *traceinfo.TraceInfo, keyList map[string]uint64) error {
	fetchKeyList := make([]string, 0)

	defer func() {
		if e := recover(); e != nil {
			logkit.Error("local cache MGet panic", logkit.Any("err", e), logkit.Any("val", keyList))
		}
	}()
	for key := range keyList {
		var dis uint64
		_ = s.polygonCache.GetFn([]byte(key), func(bytes []byte) error {
			if len(bytes) > 4 {
				dis = binary.LittleEndian.Uint64(bytes)
			}
			return nil
		})
		if dis == 0 {
			fetchKeyList = append(fetchKeyList, key)
		}
		keyList[key] = dis
	}
	defer func() {
		prometheus.ReportCounter(metric.CacheQueryTotal, float64(len(keyList)), prometheus.Label{Key: "name", Val: "RoutingDistanceData"})
		prometheus.ReportCounter(metric.CacheHit, float64(len(keyList)-len(fetchKeyList)), prometheus.Label{Key: "name", Val: "RoutingDistanceData"})
	}()

	if len(fetchKeyList) > 0 {
		result, err := s.GetRoutingDistanceFetchFunction(ctx, traceInfo, fetchKeyList)
		if err != nil {
			logkit.FromContext(ctx).Error("failed to get value with", zap.Strings("key", fetchKeyList), logkit.Err(err))
			return err
		}
		for _, key := range fetchKeyList {
			if value, ok := result[key]; ok {
				if value == 0 {
					err = s.polygonCache.Set([]byte(key), []byte{}, GetNextCacheTimeSec(routingDistanceNilDataExpireSec))
				} else {
					bytes := make([]byte, 8, 8)
					binary.LittleEndian.PutUint64(bytes, value)
					err = s.polygonCache.Set([]byte(key), bytes, GetNextCacheTimeSec(routingDistanceDataExpireSec))
				}
				if err != nil {
					logkit.FromContext(ctx).Error("failed to set cache", logkit.Any("err", err))
				}
				keyList[key] = value
			}
		}
	}

	return nil
}

// GetRoutingDistanceFetchFunction
// @Description: 实际上的加载缓存函数
// @param ctx 上下文
// @param 没有加载到的缓存key list. key: oriLng_oriLat_destLng_destLat(ori N * 1 dest)
// @return map[string]*geo.Polygon 多边形
// @return error 可能的错误
func (s *StoreCacheSys) GetRoutingDistanceFetchFunction(ctx context.Context, traceInfo *traceinfo.TraceInfo, keyList []string) (map[string]uint64, error) {
	distanceMap := make(map[string]uint64, len(keyList))
	if len(keyList) == 0 {
		return map[string]uint64{}, nil
	}
	ls := strings.Split(keyList[0], "_")
	if len(ls) != 4 {
		return map[string]uint64{}, nil
	}
	oriLng, _ := strconv.ParseFloat(ls[0], 64)
	oriLat, _ := strconv.ParseFloat(ls[1], 64)
	destLng, _ := strconv.ParseFloat(ls[2], 64)
	destLat, _ := strconv.ParseFloat(ls[3], 64)

	origins := []*geo_routing.Location{
		{
			Lon: proto.Float64(oriLng),
			Lat: proto.Float64(oriLat),
		},
	}
	destination := &geo_routing.Location{
		Lon: proto.Float64(destLng),
		Lat: proto.Float64(destLat),
	}
	for i, key := range keyList {
		if i == 0 {
			continue
		}
		ls := strings.Split(key, "_")
		if len(ls) != 4 {
			continue
		}
		lng, _ := strconv.ParseFloat(ls[0], 64)
		lat, _ := strconv.ParseFloat(ls[1], 64)
		origins = append(origins, &geo_routing.Location{Lon: proto.Float64(lng), Lat: proto.Float64(lat)})
	}
	routingRsp, _ := geo_service.GeoServiceClient.GetRoutingDistances(ctx, traceInfo, origins, destination)
	if len(keyList) != len(routingRsp) {
		return map[string]uint64{}, nil
	}
	for i, key := range keyList {
		distanceMap[key] = routingRsp[i]
	}
	return distanceMap, nil
}
