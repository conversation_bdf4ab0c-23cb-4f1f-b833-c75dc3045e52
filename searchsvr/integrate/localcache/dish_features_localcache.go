package localcache

import (
	"context"
	"errors"
	"strconv"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

func (s *StoreCacheSys) MGetDishFeatureCache(ctx context.Context, dishIds []uint64, dishFeatureMap map[uint64]*foodalgo_search.DishFeature) []uint64 {
	missDishIds := make([]uint64, 0, len(dishIds))
	for _, dishId := range dishIds {
		if dishId == 0 {
			continue
		}

		key := strconv.FormatUint(dishId, 10)
		pb := new(foodalgo_search.DishFeature)
		err := s.dishFeatureCache.GetFn(util.StringToByte(key), func(bVal []byte) error {
			// 如果key没有找到，会直接报错，不会往下走
			if len(bVal) > 0 {
				// key找到，且有值
				return pb.Unmarshal(bVal)
			}
			return errors.New("dish features entry is expire")
		})

		if err == nil {
			// key找到，且有值
			dishFeatureMap[dishId] = pb
		} else {
			// key没有找到，或者key找到，但是值是空的
			missDishIds = append(missDishIds, dishId)
		}
	}
	return missDishIds
}

func (s *StoreCacheSys) MetGetDishFeatureCacheBatch(ctx context.Context, dishIds []uint64, dishFeatureMap map[uint64]*foodalgo_search.DishFeature) []uint64 {
	batchSize := 100
	if apollo.SearchApolloCfg.FSDishFeatureCacheBatchSize > 0 {
		batchSize = int(apollo.SearchApolloCfg.FSDishFeatureItemBatchSize)
	}

	dishLen := len(dishIds)
	totalBatch := (dishLen + batchSize - 1) / batchSize
	if totalBatch == 0 {
		return []uint64{}
	}
	batchResultList := make([]map[uint64]*foodalgo_search.DishFeature, totalBatch)
	batchMissList := make([][]uint64, totalBatch)
	var wg sync.WaitGroup
	for i := 0; i < totalBatch; i++ {
		wg.Add(1)
		goroutine.WithGo(ctx, "GetDishFeatureCacheList", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			groupIndex := param[0].(int)
			begin := groupIndex * batchSize
			end := begin + batchSize
			if end > dishLen {
				end = dishLen
			}
			batchDishIds := dishIds[begin:end]
			batchList := make(map[uint64]*foodalgo_search.DishFeature, len(batchDishIds))
			missIds := s.MGetDishFeatureCache(ctx, batchDishIds, batchList)
			batchMissList[groupIndex] = missIds
			batchResultList[groupIndex] = batchList
		}, i)
	}
	wg.Wait()

	for _, batchFeaturesMap := range batchResultList {
		for dishId, dishFeature := range batchFeaturesMap {
			dishFeatureMap[dishId] = dishFeature
		}
	}
	missIds := make([]uint64, 0, len(dishIds))
	for _, ids := range batchMissList {
		missIds = append(missIds, ids...)
	}
	return missIds
}

func (s *StoreCacheSys) MSetDishFeatureCache(ctx context.Context, dishFeatureMap map[uint64]*foodalgo_search.DishFeature, cacheTime int) {
	if cacheTime <= 0 {
		// 不使用缓存
		return
	}
	for key, dish := range dishFeatureMap {
		if dish == nil {
			continue
		}

		id := strconv.FormatUint(key, 10)
		bVal, err1 := dish.Marshal()
		if err1 != nil {
			logkit.FromContext(ctx).Error("failed to dish Marshal", logkit.Any("err", err1), logkit.Any("dish", dish))
			continue
		}
		err := s.dishFeatureCache.Set(util.StringToByte(id), bVal, GetNextCacheTimeSec(cacheTime))
		if err != nil {
			logkit.FromContext(ctx).Error("failed to set dish features cache", logkit.Any("err", err))
		}
	}
}
