package localcache

import (
	"testing"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/golang/protobuf/proto"
)

// 单元测试函数
func TestDish_Marshal(t *testing.T) {
	dish := &foodalgo_search.DishFeature{
		DishCvr_30D:       proto.Float32(0.15801162),
		DishCtr_30D:       proto.Float32(0.014668713),
		RatingScore:       proto.Float32(0.8),
		SalesRank_7DScore: proto.Float32(0),
		UvRepurchaseScore: proto.Float32(0),
		IsFlashSale:       proto.Uint32(0),
	}
	bVal, err := dish.Marshal()
	println(bVal, err)

	dish2 := &foodalgo_search.DishFeature{
		DishCvr_30D:       proto.Float32(0.15801162),
		DishCtr_30D:       proto.Float32(0.014668713),
		RatingScore:       proto.Float32(0.8),
		SalesRank_7DScore: proto.Float32(0),
		//UvRepurchaseScore: proto.Float32(0),
		IsFlashSale: proto.Uint32(0),
	}
	bVal2, err2 := dish2.Marshal()
	println(bVal2, err2)

	dish3 := &foodalgo_search.DishFeature{
		DishCvr_30D:       proto.Float32(0.15801162),
		DishCtr_30D:       proto.Float32(0.014668713),
		RatingScore:       proto.Float32(0.8),
		SalesRank_7DScore: proto.Float32(0),
		UvRepurchaseScore: proto.Float32(0),
		//IsFlashSale: proto.Uint32(0),
	}
	bVal3, err3 := dish3.Marshal()
	println(bVal3, err3)
}
