package httpclient

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const IOS = "ios"
const Android = "android"

type PromotionRequestData struct {
	StoreIDs         []uint64                  `json:"store_ids"`
	PromotionTypes   []int32                   `json:"promotion_types"`
	CategoryInfo     *RequestDataCategoryInfo  `json:"category_info"`
	LocationInfo     *RequestDataLocationInfo  `json:"location_info"`
	PromotionInfo    *RequestDataPromotionInfo `json:"promotion_info"`
	ValidateCategory bool                      `json:"validate_category"`
}

type RequestDataCategoryInfo struct {
	FoodyServiceIDs []uint32 `json:"foody_service_ids"`
	CategoryIDs     []uint32 `json:"category_ids"`
}

type RequestDataLocationInfo struct {
	CityID      uint32   `json:"city_id"`
	DistrictIDs []uint32 `json:"district_ids"`
}
type RequestDataPromotionInfo struct {
	SortType          int32  `json:"sort_type"`
	AppType           uint32 `json:"app_type"`
	ClientType        uint32 `json:"client_type"`
	IsSupportFreeItem bool   `json:"is_support_free_item"`
}

type PromotionResponseData struct {
	Result string `json:"result"`
	Reply  []struct {
		StoreId                 uint64 `json:"store_id"`
		HasShippingFeePromotion bool   `json:"has_shipping_fee_promotion"`
		HasShopPromotion        bool   `json:"has_shop_promotion"`
	} `json:"reply"`
}

// promotion 参数梳理：https://confluence.shopee.io/x/F-yjnQ
func GetPromotionData(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (map[uint64]*model.PromotionData, error) {
	apiUrl := vnHttpClientConfig.getPromotionHost + vnHttpClientConfig.getPromotionUrl
	hasStorePromotion := false // promotion type传参里是否包含4-StorePromotion 类型
	promotionFilter := make([]int32, 0, len(traceInfo.TraceRequest.GetFilterType().GetPromotionFilter()))
	for _, f := range traceInfo.TraceRequest.GetFilterType().GetPromotionFilter() {
		if f == foodalgo_search.SearchRequest_StorePromotion {
			hasStorePromotion = true
		}
		promotionFilter = append(promotionFilter, int32(f))
	}
	var clientType uint32
	if traceInfo.TraceRequest.ExtClientInfo.GetOs() == IOS {
		clientType = 3
	}
	if traceInfo.TraceRequest.ExtClientInfo.GetOs() == Android {
		clientType = 4
	}
	var foodyServiceIDs []uint32
	var isSupportFreeItem bool
	if hasStorePromotion {
		foodyServiceIDs = []uint32{1, 4, 5, 6, 7, 12, 13, 17, 21, 22, 23}
		isSupportFreeItem = true
	}
	requestData := &PromotionRequestData{
		StoreIDs:       storeIds,
		PromotionTypes: promotionFilter,
		CategoryInfo: &RequestDataCategoryInfo{
			FoodyServiceIDs: foodyServiceIDs,
			CategoryIDs:     traceInfo.TraceRequest.GetFilterType().GetL1Categories(),
		},
		LocationInfo: &RequestDataLocationInfo{
			CityID:      traceInfo.TraceRequest.CityId,
			DistrictIDs: []uint32{}, // 全是空
		},
		PromotionInfo: &RequestDataPromotionInfo{
			SortType:          int32(traceInfo.TraceRequest.GetSortType()),
			ClientType:        clientType, // 3-ios, 4-android
			AppType:           traceInfo.TraceRequest.AppType,
			IsSupportFreeItem: isSupportFreeItem,
		},
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "VN GetPromotionData", logkit.String("apiUrl", apiUrl), logkit.Any("requestData", requestData))

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error encoding request data", logkit.Any("requestData", requestData))
		return nil, err
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error creating HTTP request", logkit.Any("requestData", requestData))
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-FOODY-REQUEST-ID", strconv.FormatInt(time.Now().UnixNano(), 10))
	req.Header.Set("X-FOODY-API-VERSION", "1000")
	req.Header.Set("X-FOODY-APP-ID", "8007")
	req.Header.Set("X-FOODY-COUNTRY", "vn")

	res, err := httpClient.Do(req)
	defer func(Res *http.Response) {
		if res != nil {
			err := Res.Body.Close()
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error closing HTTP request", logkit.Any("requestData", requestData))
			}
		}
	}(res)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error sending HTTP request", logkit.Any("requestData", requestData))
		return nil, err
	}

	if res == nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).Error("VNPromotion Error sending HTTP request with empty res", logkit.Any("requestData", requestData))
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error sending HTTP request without 200", logkit.String("Status", res.Status), logkit.Any("requestData", requestData))
		return nil, err
	}

	responseData := PromotionResponseData{}
	err = json.NewDecoder(res.Body).Decode(&responseData)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion Error decoding response data", logkit.Any("requestData", requestData))
		return nil, err
	}
	if responseData.Result != "success" {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VNPromotion failed", logkit.Any("requestData", requestData), logkit.Any("responseData", responseData))
		return nil, err
	}

	retMap := make(map[uint64]*model.PromotionData)
	for _, val := range responseData.Reply {
		retMap[val.StoreId] = &model.PromotionData{
			HasShopPromotion:        val.HasShopPromotion,
			HasShippingFeePromotion: val.HasShippingFeePromotion,
		}
	}

	logger.MyDebug(ctx, traceInfo.IsDebug, "VN GetPromotionData", logkit.Any("retMap", retMap))
	return retMap, err
}
