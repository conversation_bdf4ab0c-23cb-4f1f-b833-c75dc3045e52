package httpclient

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

type StoreStatus int

const (
	StoreOpeningStatusOpen  StoreStatus = 1
	StoreOpeningStatusClose StoreStatus = 2
	StoreOpeningStatusBusy  StoreStatus = 3
)

type IsOpeningRequestData struct {
	RestaurantIds []uint64 `json:"restaurant_ids"`
}

type Rsp struct {
	RestaurantStatuses []RspItem `json:"restaurant_statuses"`
}
type RspItem struct {
	RestaurantId uint64 `json:"restaurant_id"`
	Status       int    `json:"status"` //OPEN = 1, CLOSE = 2, BUSY = 3
}
type IsOpeningResponseData struct {
	Result string `json:"result"`
	Reply  Rsp    `json:"reply"`
}

func (m *IsOpeningResponseData) GetReply() *Rsp {
	if m != nil {
		return &m.Reply
	}
	return nil
}

func (m *Rsp) GetRestaurantStatuses() []RspItem {
	if m != nil {
		return m.RestaurantStatuses
	}
	return nil
}

func GetIsOpeningData(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIds []uint64) (map[uint64]int, error) {
	apiUrl := vnHttpClientConfig.getIsOpeningHost + vnHttpClientConfig.getIsOpeningUrl

	requestData := &IsOpeningRequestData{
		RestaurantIds: storeIds,
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "VN GetIsOpeningData", logkit.String("apiUrl", apiUrl), logkit.Any("req", requestData))

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData Error encoding request data", logkit.Any("requestData", requestData))
		return nil, err
	}

	req, err := http.NewRequest("POST", apiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData Error creating HTTP request", logkit.Any("requestData", requestData))
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-FOODY-REQUEST-ID", strconv.FormatInt(time.Now().UnixNano(), 10))
	req.Header.Set("X-FOODY-API-VERSION", "1000")
	req.Header.Set("X-FOODY-APP-ID", "8007")
	req.Header.Set("X-FOODY-COUNTRY", "vn")

	res, err := httpClient.Do(req)
	defer func(Res *http.Response) {
		if res != nil {
			err := Res.Body.Close()
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData  Error closing HTTP request", logkit.Any("requestData", requestData))
			}
		}
	}(res)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData Error sending HTTP request", logkit.Any("requestData", requestData))
		return nil, err
	}

	responseData := IsOpeningResponseData{}
	err = json.NewDecoder(res.Body).Decode(&responseData)
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData Error decoding response data", logkit.Any("requestData", requestData), logkit.Any("responseData", responseData))
		return nil, err
	}
	if responseData.Result != "success" {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("VN GetIsOpeningData failed", logkit.Any("requestData", requestData), logkit.Any("responseData", responseData))
		return nil, err
	}

	retMap := make(map[uint64]int)
	for _, val := range responseData.GetReply().GetRestaurantStatuses() {
		retMap[val.RestaurantId] = val.Status
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "VN GetIsOpeningData", logkit.Any("retMap", retMap))
	return retMap, nil
}
