package httpclient

import (
	"net/http"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
)

var (
	httpClient         *http.Client
	vnHttpClientConfig *VNHttpClientConfig
)

func Init(vnHttpConfig *config.VNHttpConfig) {
	var timeoutMillisecond int
	if apollo.SearchApolloCfg.VNHttpClientTimeoutMillisecond == 0 {
		timeoutMillisecond = 1000
	} else {
		timeoutMillisecond = apollo.SearchApolloCfg.VNHttpClientTimeoutMillisecond
	}

	var maxIdleConns int
	if apollo.SearchApolloCfg.VNHttpClientMaxIdleConns == 0 {
		maxIdleConns = 40
	} else {
		maxIdleConns = apollo.SearchApolloCfg.VNHttpClientMaxIdleConns
	}

	var maxIdleConnsPerHost int
	if apollo.SearchApolloCfg.VNHttpClientMaxIdleConnsPerHost == 0 {
		maxIdleConnsPerHost = 20
	} else {
		maxIdleConnsPerHost = apollo.SearchApolloCfg.VNHttpClientMaxIdleConnsPerHost
	}

	var idleConnTimeout int
	if apollo.SearchApolloCfg.VNHttpClientIdleConnTimeoutSeconds == 0 {
		idleConnTimeout = 90
	} else {
		idleConnTimeout = apollo.SearchApolloCfg.VNHttpClientIdleConnTimeoutSeconds
	}

	httpClient = &http.Client{
		Timeout: time.Duration(timeoutMillisecond) * time.Millisecond,
		Transport: &http.Transport{
			MaxIdleConns:        maxIdleConns,
			MaxIdleConnsPerHost: maxIdleConnsPerHost,
			IdleConnTimeout:     time.Duration(idleConnTimeout) * time.Second,
		},
	}

	vnHttpClientConfig = &VNHttpClientConfig{
		getPromotionHost: vnHttpConfig.Promotion.Host,
		getPromotionUrl:  vnHttpConfig.Promotion.Url,
		getIsOpeningHost: vnHttpConfig.IsOpening.Host,
		getIsOpeningUrl:  vnHttpConfig.IsOpening.Url,
	}
}

type VNHttpClientConfig struct {
	getPromotionHost string
	getPromotionUrl  string
	getIsOpeningHost string
	getIsOpeningUrl  string
}
