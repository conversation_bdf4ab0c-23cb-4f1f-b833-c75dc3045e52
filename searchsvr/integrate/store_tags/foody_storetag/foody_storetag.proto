syntax = "proto2";
import "spex/protobuf/service.proto";

package foody_storetag;

// Code generated by spcli. DO NOT EDIT.
//
// namespace shopeefood.merchant.storetag
//
// commands {
//   shopeefood.merchant.storetag.get_tag
//   shopeefood.merchant.storetag.get_tags_by_store_id
//   shopeefood.merchant.storetag.create_tag_type
//   shopeefood.merchant.storetag.get_tag_types
//   shopeefood.merchant.storetag.update_tag_type
//   shopeefood.merchant.storetag.m_get_tag_type
//   shopeefood.merchant.storetag.delete_tag_type
//   shopeefood.merchant.storetag.create_tag
//   shopeefood.merchant.storetag.update_tag
//   shopeefood.merchant.storetag.delete_tag
//   shopeefood.merchant.storetag.search_tag
//   shopeefood.merchant.storetag.get_stores_by_tag_id
//   shopeefood.merchant.storetag.get_store_i_ds_by_tag_id
//   shopeefood.merchant.storetag.m_get_tag
//   shopeefood.merchant.storetag.m_get_all_tag
//   shopeefood.merchant.storetag.m_get_tags_by_store_i_ds
//   shopeefood.merchant.storetag.get_tag_type_by_name
//   shopeefood.merchant.storetag.m_add_tag_binding
//   shopeefood.merchant.storetag.m_delete_tag_binding
//   shopeefood.merchant.storetag.create_async_task
//   shopeefood.merchant.storetag.get_async_tasks
// }

import "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor/microkit.proto";
import "git.garena.com/shopee/foody/service/pb/foody_base/foody_base.proto";

service StoreTag {
  option (microkit.ProjectName) = "foody";
  option (service.service) = {
    servicename: "shopeefood.merchant.storetag" // 添加spex的namespace
  };

  rpc GetTag(GetTagRequest)returns(GetTagResponse){}

  rpc GetTagsByStoreID(GetTagsByStoreIDRequest)returns(GetTagsByStoreIDResponse){}

  rpc CreateTagType(CreateTagTypeRequest) returns (CreateTagTypeResponse) {}
  rpc GetTagTypes(GetTagTypesRequest) returns (GetTagTypesResponse) {}
  rpc UpdateTagType(UpdateTagTypeRequest) returns (UpdateTagTypeResponse) {}
  rpc MGetTagType(MGetTagTypeRequest) returns (MGetTagTypeResponse) {}
  rpc DeleteTagType(DeleteTagTypeRequest) returns(DeleteTagTypeResponse) {}

  rpc CreateTag(CreateTagRequest) returns (CreateTagResponse) {}
  rpc UpdateTag(UpdateTagRequest) returns(UpdateTagResponse) {}
  rpc DeleteTag(DeleteTagRequest) returns (DeleteTagResponse) {}
  rpc SearchTag(SearchTagRequest) returns (SearchTagResponse) {}
  rpc GetStoresByTagID(GetStoresByTagIDRequest) returns (GetStoresByTagIDResponse) {}
  rpc GetStoreIDsByTagID(GetStoreIDsByTagIDRequest) returns (GetStoreIDsByTagIDResponse) {}
  rpc MGetTag(MGetTagRequest) returns (MGetTagResponse) {}
  rpc MGetAllTag(MGetAllTagRequest) returns (MGetAllTagResponse) {}
  rpc MGetTagsByStoreIDs(MGetTagsByStoreIDsRequest)returns(MGetTagsByStoreIDsResponse){}
  rpc GetTagTypeByName(GetTagTypeByNameRequest)returns(GetTagTypeByNameResponse){}

  rpc MAddTagBinding(MAddTagBindingRequest)returns(MAddTagBindingResponse){}
  rpc MDeleteTagBinding(MDeleteTagBindingRequest)returns(MDeleteTagBindingResponse){}


  // async task functions
  rpc CreateAsyncTask(CreateAsyncTaskRequest) returns (CreateAsyncTaskResponse) {}
  rpc GetAsyncTasks(GetAsyncTasksRequest) returns (GetAsyncTasksResponse) {}
}

message GetTagRequest {
  optional uint64 id = 1;
}

message GetTagResponse {
  optional foody_base.Tag tag = 1;
}


message StoreTagBinding {
  optional uint64 id = 1;
  optional uint64 store_id = 2;
  optional uint64 tag_id = 3;
  optional uint32 deleted = 4;
  optional uint64 create_time = 5;
  optional uint64 update_time = 6;
}

message StoreTagTypeBinding {
  optional uint64 id = 1;
  optional uint64 store_id = 2;
  optional uint64 tag_type_id = 3;
  optional uint32 is_deleted = 4;
  optional uint64 create_time = 5;
  optional uint64 update_time = 6;
}

enum TagScene {
  Admin = 1;
  Buyer = 2;
}

message GetTagsByStoreIDRequest {
  optional uint64 store_id = 1;
  optional TagScene tag_scene = 2;
}

message GetTagsByStoreIDResponse {
  repeated foody_base.Tag tags = 1;
}

message GetTagTypesRequest {
  optional uint32 page_num = 1;
  optional uint32 page_size = 2;
  optional bool has_tag = 3;
  optional foody_base.TagType.Status status = 4;
}

message GetTagTypesResponse {
  repeated foody_base.TagType tag_types = 1;
  optional uint64 total = 2;
}

message CreateTagTypeRequest {
  optional string type_name = 1;
  optional Source source = 2;
}

message CreateTagTypeResponse {
  optional uint64 id = 1;
}

message UpdateTagTypeRequest {
  optional uint64 id = 1;
  optional string type_name = 2;
  optional foody_base.TagType.Status status = 3;
  optional Source source = 4;
}

message UpdateTagTypeResponse {
  optional uint64 id = 1;
}

message CreateTagRequest {
  optional foody_base.Tag tag = 1;
  optional Source source = 2;
}

message CreateTagResponse {
  optional uint64 id = 1;
}

message UpdateTagRequest {
  optional foody_base.Tag tag = 1;
  optional Source source = 2;
}

message UpdateTagResponse {
  optional uint64 id = 1;
}

message DeleteTagRequest {
  optional uint64 id = 1;
  optional Source source = 2;
}

message DeleteTagResponse {
}

message SearchTagRequest {
  optional string keyword = 1;
  optional uint64 tag_type_id = 2;
  optional uint32 page_num = 3;
  optional uint32 page_size = 4;
}

message SearchTagResponse {
  repeated foody_base.Tag tags = 1;
  optional uint64 total = 2;
}

message MGetTagTypeRequest {
  repeated uint64 ids = 1;
}

message MGetTagTypeResponse {
  repeated foody_base.TagType tag_types = 1;
}

message DeleteTagTypeRequest {
  optional uint64 id = 1;
  optional Source source = 2;
}

message DeleteTagTypeResponse {

}

message GetStoresByTagIDRequest {
  optional uint64 tag_id = 1;
  optional uint32 page_num = 2;
  optional uint32 page_size = 3;
}

message GetStoresByTagIDResponse {
  repeated foody_base.Store stores = 1;
  optional uint64 total = 2;
}

message MGetTagRequest {
  repeated uint64 ids = 1;
}

message MGetTagResponse {
  repeated foody_base.Tag tags = 1;
}

message MGetAllTagRequest {
  repeated uint64 ids = 1;
}

message MGetAllTagResponse {
  repeated foody_base.Tag tags = 1;
}

message MGetTagsByStoreIDsRequest {
  repeated uint64 store_ids = 1;
  optional TagScene tag_scene = 2;
}

message StoreTags {
  optional uint64 store_id = 1;
  repeated foody_base.Tag tags = 2;
}

message MGetTagsByStoreIDsResponse {
  repeated StoreTags store_tags_list = 1;
}

message GetTagTypeByNameRequest {
  optional string name = 1;
}

message GetTagTypeByNameResponse {
  optional foody_base.TagType tag_type = 1;
}

enum Source {
  SOURCE_DEFAULT = 0;
  SOURCE_ADMIN = 1;
  SOURCE_CAMPAIGN = 2;
}

message FailedResult {
  enum ErrorCode {
    ERROR_OTHER = 0;
    ERROR_MAX_LIMIT = 1;
  }
  optional uint64 store_id = 1;
  optional ErrorCode error_code = 2;
}

message MAddTagBindingRequest {
  optional uint64 tag_id = 1;
  repeated uint64 store_ids = 2;
  optional Source source = 3;
}

message MAddTagBindingResponse {
  repeated FailedResult results = 1;
}

message MDeleteTagBindingRequest {
  optional uint64 tag_id = 1;
  repeated uint64 store_ids = 2;
  optional Source source = 3;
}

message MDeleteTagBindingResponse {
  repeated FailedResult results = 1;
}


message CreateAsyncTaskRequest {
  optional string file_name = 1;
  optional string file_link = 2;
  optional foody_base.AsyncTask.Type task_type = 3;
  optional string operator = 4;
  optional uint64 reference_id = 5;
}

message CreateAsyncTaskResponse {
  optional uint64 task_id = 1;
}

message GetAsyncTasksRequest {
  optional uint64 reference_id = 1;
  repeated foody_base.AsyncTask.Type task_type = 2;
  optional uint32 page_num = 3;
  optional uint32 page_size = 4;
}

message GetAsyncTasksResponse {
  repeated foody_base.AsyncTask tasks = 1;
  optional uint64 total = 2;
}

message GetStoreIDsByTagIDRequest {
  optional uint64 tag_id = 1;
  optional uint64 start_id = 2;
  optional uint64 limit = 3;
}

message GetStoreIDsByTagIDResponse {
  repeated uint64 store_ids = 1;
  optional uint64 last_id = 2;
}