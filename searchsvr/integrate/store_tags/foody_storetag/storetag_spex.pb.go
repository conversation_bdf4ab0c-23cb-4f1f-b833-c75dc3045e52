package foody_storetag

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/desc"
	sp_errors "git.garena.com/shopee/platform/golang_splib/errors"
	sp_common "git.garena.com/shopee/sp_protocol/golang/common.pb"
)

type SpexFoodyStoretagHandler interface {
	GetTag(context.Context, *GetTagRequest, *GetTagResponse) error

	GetTagsByStoreID(context.Context, *GetTagsByStoreIDRequest, *GetTagsByStoreIDResponse) error

	CreateTagType(context.Context, *CreateTagTypeRequest, *CreateTagTypeResponse) error

	GetTagTypes(context.Context, *GetTagTypesRequest, *GetTagTypesResponse) error

	UpdateTagType(context.Context, *UpdateTagTypeRequest, *UpdateTagTypeResponse) error

	MGetTagType(context.Context, *MGetTagTypeRequest, *MGetTagTypeResponse) error

	DeleteTagType(context.Context, *DeleteTagTypeRequest, *DeleteTagTypeResponse) error

	CreateTag(context.Context, *CreateTagRequest, *CreateTagResponse) error

	UpdateTag(context.Context, *UpdateTagRequest, *UpdateTagResponse) error

	DeleteTag(context.Context, *DeleteTagRequest, *DeleteTagResponse) error

	SearchTag(context.Context, *SearchTagRequest, *SearchTagResponse) error

	GetStoresByTagID(context.Context, *GetStoresByTagIDRequest, *GetStoresByTagIDResponse) error

	GetStoreIDsByTagID(context.Context, *GetStoreIDsByTagIDRequest, *GetStoreIDsByTagIDResponse) error

	MGetTag(context.Context, *MGetTagRequest, *MGetTagResponse) error

	MGetAllTag(context.Context, *MGetAllTagRequest, *MGetAllTagResponse) error

	MGetTagsByStoreIDs(context.Context, *MGetTagsByStoreIDsRequest, *MGetTagsByStoreIDsResponse) error

	GetTagTypeByName(context.Context, *GetTagTypeByNameRequest, *GetTagTypeByNameResponse) error

	MAddTagBinding(context.Context, *MAddTagBindingRequest, *MAddTagBindingResponse) error

	MDeleteTagBinding(context.Context, *MDeleteTagBindingRequest, *MDeleteTagBindingResponse) error

	// async task functions
	CreateAsyncTask(context.Context, *CreateAsyncTaskRequest, *CreateAsyncTaskResponse) error

	GetAsyncTasks(context.Context, *GetAsyncTasksRequest, *GetAsyncTasksResponse) error
}

type spexFoodyStoretagServiceImpl struct {
	methods []desc.Method
	handler SpexFoodyStoretagHandler
}

func (s *spexFoodyStoretagServiceImpl) Name() string {
	return "FoodyStoretag"
}

func (s *spexFoodyStoretagServiceImpl) Methods() []desc.Method {
	return s.methods
}

func (s *spexFoodyStoretagServiceImpl) Backend() desc.Backend {
	return desc.SPEX
}

type method struct {
	handler      desc.Handler
	command      string
	requestType  interface{}
	responseType interface{}
}

func newMethod(command string, handler desc.Handler, reqType interface{}, respType interface{}) *method {
	return &method{
		command:      command,
		handler:      handler,
		requestType:  reqType,
		responseType: respType,
	}
}

func (m *method) Command() string {
	return m.command
}

func (m *method) RequestType() interface{} {
	return m.requestType
}

func (m *method) ResponseType() interface{} {
	return m.responseType
}

func (m *method) Handler() desc.Handler {
	return m.handler
}

func newService(handler SpexFoodyStoretagHandler) desc.Service {
	namespace := "shopeefood.merchant.storetag"

	hs := spexFoodyStoretagServiceImpl{
		handler: handler,
	}
	hs.methods = append(hs.methods,
		newMethod(namespace+"."+"get_tag", hs.GetTag, &GetTagRequest{}, &GetTagResponse{}),
		newMethod(namespace+"."+"get_tags_by_store_id", hs.GetTagsByStoreID, &GetTagsByStoreIDRequest{}, &GetTagsByStoreIDResponse{}),
		newMethod(namespace+"."+"create_tag_type", hs.CreateTagType, &CreateTagTypeRequest{}, &CreateTagTypeResponse{}),
		newMethod(namespace+"."+"get_tag_types", hs.GetTagTypes, &GetTagTypesRequest{}, &GetTagTypesResponse{}),
		newMethod(namespace+"."+"update_tag_type", hs.UpdateTagType, &UpdateTagTypeRequest{}, &UpdateTagTypeResponse{}),
		newMethod(namespace+"."+"m_get_tag_type", hs.MGetTagType, &MGetTagTypeRequest{}, &MGetTagTypeResponse{}),
		newMethod(namespace+"."+"delete_tag_type", hs.DeleteTagType, &DeleteTagTypeRequest{}, &DeleteTagTypeResponse{}),
		newMethod(namespace+"."+"create_tag", hs.CreateTag, &CreateTagRequest{}, &CreateTagResponse{}),
		newMethod(namespace+"."+"update_tag", hs.UpdateTag, &UpdateTagRequest{}, &UpdateTagResponse{}),
		newMethod(namespace+"."+"delete_tag", hs.DeleteTag, &DeleteTagRequest{}, &DeleteTagResponse{}),
		newMethod(namespace+"."+"search_tag", hs.SearchTag, &SearchTagRequest{}, &SearchTagResponse{}),
		newMethod(namespace+"."+"get_stores_by_tag_id", hs.GetStoresByTagID, &GetStoresByTagIDRequest{}, &GetStoresByTagIDResponse{}),
		newMethod(namespace+"."+"get_store_i_ds_by_tag_id", hs.GetStoreIDsByTagID, &GetStoreIDsByTagIDRequest{}, &GetStoreIDsByTagIDResponse{}),
		newMethod(namespace+"."+"m_get_tag", hs.MGetTag, &MGetTagRequest{}, &MGetTagResponse{}),
		newMethod(namespace+"."+"m_get_all_tag", hs.MGetAllTag, &MGetAllTagRequest{}, &MGetAllTagResponse{}),
		newMethod(namespace+"."+"m_get_tags_by_store_i_ds", hs.MGetTagsByStoreIDs, &MGetTagsByStoreIDsRequest{}, &MGetTagsByStoreIDsResponse{}),
		newMethod(namespace+"."+"get_tag_type_by_name", hs.GetTagTypeByName, &GetTagTypeByNameRequest{}, &GetTagTypeByNameResponse{}),
		newMethod(namespace+"."+"m_add_tag_binding", hs.MAddTagBinding, &MAddTagBindingRequest{}, &MAddTagBindingResponse{}),
		newMethod(namespace+"."+"m_delete_tag_binding", hs.MDeleteTagBinding, &MDeleteTagBindingRequest{}, &MDeleteTagBindingResponse{}),
		newMethod(namespace+"."+"create_async_task", hs.CreateAsyncTask, &CreateAsyncTaskRequest{}, &CreateAsyncTaskResponse{}),
		newMethod(namespace+"."+"get_async_tasks", hs.GetAsyncTasks, &GetAsyncTasksRequest{}, &GetAsyncTasksResponse{}),
	)

	return &hs
}

func (s *spexFoodyStoretagServiceImpl) GetTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTagResponse{}

	appErr := s.handler.GetTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetTagsByStoreID(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTagsByStoreIDRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTagsByStoreIDResponse{}

	appErr := s.handler.GetTagsByStoreID(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) CreateTagType(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateTagTypeRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateTagTypeResponse{}

	appErr := s.handler.CreateTagType(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetTagTypes(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTagTypesRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTagTypesResponse{}

	appErr := s.handler.GetTagTypes(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) UpdateTagType(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateTagTypeRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateTagTypeResponse{}

	appErr := s.handler.UpdateTagType(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MGetTagType(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetTagTypeRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetTagTypeResponse{}

	appErr := s.handler.MGetTagType(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) DeleteTagType(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*DeleteTagTypeRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := DeleteTagTypeResponse{}

	appErr := s.handler.DeleteTagType(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) CreateTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateTagResponse{}

	appErr := s.handler.CreateTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) UpdateTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateTagResponse{}

	appErr := s.handler.UpdateTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) DeleteTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*DeleteTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := DeleteTagResponse{}

	appErr := s.handler.DeleteTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) SearchTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchTagResponse{}

	appErr := s.handler.SearchTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetStoresByTagID(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetStoresByTagIDRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetStoresByTagIDResponse{}

	appErr := s.handler.GetStoresByTagID(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetStoreIDsByTagID(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetStoreIDsByTagIDRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetStoreIDsByTagIDResponse{}

	appErr := s.handler.GetStoreIDsByTagID(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MGetTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetTagResponse{}

	appErr := s.handler.MGetTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MGetAllTag(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetAllTagRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetAllTagResponse{}

	appErr := s.handler.MGetAllTag(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MGetTagsByStoreIDs(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetTagsByStoreIDsRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetTagsByStoreIDsResponse{}

	appErr := s.handler.MGetTagsByStoreIDs(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetTagTypeByName(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTagTypeByNameRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTagTypeByNameResponse{}

	appErr := s.handler.GetTagTypeByName(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MAddTagBinding(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MAddTagBindingRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MAddTagBindingResponse{}

	appErr := s.handler.MAddTagBinding(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) MDeleteTagBinding(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MDeleteTagBindingRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MDeleteTagBindingResponse{}

	appErr := s.handler.MDeleteTagBinding(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) CreateAsyncTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateAsyncTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateAsyncTaskResponse{}

	appErr := s.handler.CreateAsyncTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyStoretagServiceImpl) GetAsyncTasks(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetAsyncTasksRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetAsyncTasksResponse{}

	appErr := s.handler.GetAsyncTasks(ctx, req, &resp)

	return &resp, appErr
}
