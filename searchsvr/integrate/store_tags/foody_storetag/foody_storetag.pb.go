// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: foody_storetag.proto

package foody_storetag

import (
	fmt "fmt"
	_ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
	foody_base "git.garena.com/shopee/foody/service/pb/foody_base"
	_ "git.garena.com/shopee/sp_protocol/desc/pb/spex/protobuf/service"
	proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TagScene int32

const (
	TagScene_Admin TagScene = 1
	TagScene_Buyer TagScene = 2
)

var TagScene_name = map[int32]string{
	1: "Admin",
	2: "Buyer",
}

var TagScene_value = map[string]int32{
	"Admin": 1,
	"Buyer": 2,
}

func (x TagScene) Enum() *TagScene {
	p := new(TagScene)
	*p = x
	return p
}

func (x TagScene) String() string {
	return proto.EnumName(TagScene_name, int32(x))
}

func (x *TagScene) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TagScene_value, data, "TagScene")
	if err != nil {
		return err
	}
	*x = TagScene(value)
	return nil
}

func (TagScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{0}
}

type Source int32

const (
	Source_SOURCE_DEFAULT  Source = 0
	Source_SOURCE_ADMIN    Source = 1
	Source_SOURCE_CAMPAIGN Source = 2
)

var Source_name = map[int32]string{
	0: "SOURCE_DEFAULT",
	1: "SOURCE_ADMIN",
	2: "SOURCE_CAMPAIGN",
}

var Source_value = map[string]int32{
	"SOURCE_DEFAULT":  0,
	"SOURCE_ADMIN":    1,
	"SOURCE_CAMPAIGN": 2,
}

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}

func (x *Source) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Source_value, data, "Source")
	if err != nil {
		return err
	}
	*x = Source(value)
	return nil
}

func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{1}
}

type FailedResult_ErrorCode int32

const (
	FailedResult_ERROR_OTHER     FailedResult_ErrorCode = 0
	FailedResult_ERROR_MAX_LIMIT FailedResult_ErrorCode = 1
)

var FailedResult_ErrorCode_name = map[int32]string{
	0: "ERROR_OTHER",
	1: "ERROR_MAX_LIMIT",
}

var FailedResult_ErrorCode_value = map[string]int32{
	"ERROR_OTHER":     0,
	"ERROR_MAX_LIMIT": 1,
}

func (x FailedResult_ErrorCode) Enum() *FailedResult_ErrorCode {
	p := new(FailedResult_ErrorCode)
	*p = x
	return p
}

func (x FailedResult_ErrorCode) String() string {
	return proto.EnumName(FailedResult_ErrorCode_name, int32(x))
}

func (x *FailedResult_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(FailedResult_ErrorCode_value, data, "FailedResult_ErrorCode")
	if err != nil {
		return err
	}
	*x = FailedResult_ErrorCode(value)
	return nil
}

func (FailedResult_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{35, 0}
}

type GetTagRequest struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagRequest) Reset()         { *m = GetTagRequest{} }
func (m *GetTagRequest) String() string { return proto.CompactTextString(m) }
func (*GetTagRequest) ProtoMessage()    {}
func (*GetTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{0}
}
func (m *GetTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagRequest.Merge(m, src)
}
func (m *GetTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagRequest proto.InternalMessageInfo

func (m *GetTagRequest) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

type GetTagResponse struct {
	Tag                  *foody_base.Tag `protobuf:"bytes,1,opt,name=tag" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetTagResponse) Reset()         { *m = GetTagResponse{} }
func (m *GetTagResponse) String() string { return proto.CompactTextString(m) }
func (*GetTagResponse) ProtoMessage()    {}
func (*GetTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{1}
}
func (m *GetTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagResponse.Merge(m, src)
}
func (m *GetTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagResponse proto.InternalMessageInfo

func (m *GetTagResponse) GetTag() *foody_base.Tag {
	if m != nil {
		return m.Tag
	}
	return nil
}

type StoreTagBinding struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	StoreId              *uint64  `protobuf:"varint,2,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	TagId                *uint64  `protobuf:"varint,3,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	Deleted              *uint32  `protobuf:"varint,4,opt,name=deleted" json:"deleted,omitempty"`
	CreateTime           *uint64  `protobuf:"varint,5,opt,name=create_time,json=createTime" json:"create_time,omitempty"`
	UpdateTime           *uint64  `protobuf:"varint,6,opt,name=update_time,json=updateTime" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreTagBinding) Reset()         { *m = StoreTagBinding{} }
func (m *StoreTagBinding) String() string { return proto.CompactTextString(m) }
func (*StoreTagBinding) ProtoMessage()    {}
func (*StoreTagBinding) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{2}
}
func (m *StoreTagBinding) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreTagBinding) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreTagBinding.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreTagBinding) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreTagBinding.Merge(m, src)
}
func (m *StoreTagBinding) XXX_Size() int {
	return m.Size()
}
func (m *StoreTagBinding) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreTagBinding.DiscardUnknown(m)
}

var xxx_messageInfo_StoreTagBinding proto.InternalMessageInfo

func (m *StoreTagBinding) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *StoreTagBinding) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *StoreTagBinding) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *StoreTagBinding) GetDeleted() uint32 {
	if m != nil && m.Deleted != nil {
		return *m.Deleted
	}
	return 0
}

func (m *StoreTagBinding) GetCreateTime() uint64 {
	if m != nil && m.CreateTime != nil {
		return *m.CreateTime
	}
	return 0
}

func (m *StoreTagBinding) GetUpdateTime() uint64 {
	if m != nil && m.UpdateTime != nil {
		return *m.UpdateTime
	}
	return 0
}

type StoreTagTypeBinding struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	StoreId              *uint64  `protobuf:"varint,2,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	TagTypeId            *uint64  `protobuf:"varint,3,opt,name=tag_type_id,json=tagTypeId" json:"tag_type_id,omitempty"`
	IsDeleted            *uint32  `protobuf:"varint,4,opt,name=is_deleted,json=isDeleted" json:"is_deleted,omitempty"`
	CreateTime           *uint64  `protobuf:"varint,5,opt,name=create_time,json=createTime" json:"create_time,omitempty"`
	UpdateTime           *uint64  `protobuf:"varint,6,opt,name=update_time,json=updateTime" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreTagTypeBinding) Reset()         { *m = StoreTagTypeBinding{} }
func (m *StoreTagTypeBinding) String() string { return proto.CompactTextString(m) }
func (*StoreTagTypeBinding) ProtoMessage()    {}
func (*StoreTagTypeBinding) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{3}
}
func (m *StoreTagTypeBinding) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreTagTypeBinding) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreTagTypeBinding.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreTagTypeBinding) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreTagTypeBinding.Merge(m, src)
}
func (m *StoreTagTypeBinding) XXX_Size() int {
	return m.Size()
}
func (m *StoreTagTypeBinding) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreTagTypeBinding.DiscardUnknown(m)
}

var xxx_messageInfo_StoreTagTypeBinding proto.InternalMessageInfo

func (m *StoreTagTypeBinding) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *StoreTagTypeBinding) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *StoreTagTypeBinding) GetTagTypeId() uint64 {
	if m != nil && m.TagTypeId != nil {
		return *m.TagTypeId
	}
	return 0
}

func (m *StoreTagTypeBinding) GetIsDeleted() uint32 {
	if m != nil && m.IsDeleted != nil {
		return *m.IsDeleted
	}
	return 0
}

func (m *StoreTagTypeBinding) GetCreateTime() uint64 {
	if m != nil && m.CreateTime != nil {
		return *m.CreateTime
	}
	return 0
}

func (m *StoreTagTypeBinding) GetUpdateTime() uint64 {
	if m != nil && m.UpdateTime != nil {
		return *m.UpdateTime
	}
	return 0
}

type GetTagsByStoreIDRequest struct {
	StoreId              *uint64   `protobuf:"varint,1,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	TagScene             *TagScene `protobuf:"varint,2,opt,name=tag_scene,json=tagScene,enum=foody_storetag.TagScene" json:"tag_scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetTagsByStoreIDRequest) Reset()         { *m = GetTagsByStoreIDRequest{} }
func (m *GetTagsByStoreIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetTagsByStoreIDRequest) ProtoMessage()    {}
func (*GetTagsByStoreIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{4}
}
func (m *GetTagsByStoreIDRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagsByStoreIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagsByStoreIDRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagsByStoreIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagsByStoreIDRequest.Merge(m, src)
}
func (m *GetTagsByStoreIDRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTagsByStoreIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagsByStoreIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagsByStoreIDRequest proto.InternalMessageInfo

func (m *GetTagsByStoreIDRequest) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *GetTagsByStoreIDRequest) GetTagScene() TagScene {
	if m != nil && m.TagScene != nil {
		return *m.TagScene
	}
	return TagScene_Admin
}

type GetTagsByStoreIDResponse struct {
	Tags                 []*foody_base.Tag `protobuf:"bytes,1,rep,name=tags" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetTagsByStoreIDResponse) Reset()         { *m = GetTagsByStoreIDResponse{} }
func (m *GetTagsByStoreIDResponse) String() string { return proto.CompactTextString(m) }
func (*GetTagsByStoreIDResponse) ProtoMessage()    {}
func (*GetTagsByStoreIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{5}
}
func (m *GetTagsByStoreIDResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagsByStoreIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagsByStoreIDResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagsByStoreIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagsByStoreIDResponse.Merge(m, src)
}
func (m *GetTagsByStoreIDResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetTagsByStoreIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagsByStoreIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagsByStoreIDResponse proto.InternalMessageInfo

func (m *GetTagsByStoreIDResponse) GetTags() []*foody_base.Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type GetTagTypesRequest struct {
	PageNum              *uint32                    `protobuf:"varint,1,opt,name=page_num,json=pageNum" json:"page_num,omitempty"`
	PageSize             *uint32                    `protobuf:"varint,2,opt,name=page_size,json=pageSize" json:"page_size,omitempty"`
	HasTag               *bool                      `protobuf:"varint,3,opt,name=has_tag,json=hasTag" json:"has_tag,omitempty"`
	Status               *foody_base.TagType_Status `protobuf:"varint,4,opt,name=status,enum=foody_base.TagType_Status" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetTagTypesRequest) Reset()         { *m = GetTagTypesRequest{} }
func (m *GetTagTypesRequest) String() string { return proto.CompactTextString(m) }
func (*GetTagTypesRequest) ProtoMessage()    {}
func (*GetTagTypesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{6}
}
func (m *GetTagTypesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagTypesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagTypesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagTypesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagTypesRequest.Merge(m, src)
}
func (m *GetTagTypesRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTagTypesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagTypesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagTypesRequest proto.InternalMessageInfo

func (m *GetTagTypesRequest) GetPageNum() uint32 {
	if m != nil && m.PageNum != nil {
		return *m.PageNum
	}
	return 0
}

func (m *GetTagTypesRequest) GetPageSize() uint32 {
	if m != nil && m.PageSize != nil {
		return *m.PageSize
	}
	return 0
}

func (m *GetTagTypesRequest) GetHasTag() bool {
	if m != nil && m.HasTag != nil {
		return *m.HasTag
	}
	return false
}

func (m *GetTagTypesRequest) GetStatus() foody_base.TagType_Status {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return foody_base.TagType_INACTIVE
}

type GetTagTypesResponse struct {
	TagTypes             []*foody_base.TagType `protobuf:"bytes,1,rep,name=tag_types,json=tagTypes" json:"tag_types,omitempty"`
	Total                *uint64               `protobuf:"varint,2,opt,name=total" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetTagTypesResponse) Reset()         { *m = GetTagTypesResponse{} }
func (m *GetTagTypesResponse) String() string { return proto.CompactTextString(m) }
func (*GetTagTypesResponse) ProtoMessage()    {}
func (*GetTagTypesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{7}
}
func (m *GetTagTypesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagTypesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagTypesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagTypesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagTypesResponse.Merge(m, src)
}
func (m *GetTagTypesResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetTagTypesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagTypesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagTypesResponse proto.InternalMessageInfo

func (m *GetTagTypesResponse) GetTagTypes() []*foody_base.TagType {
	if m != nil {
		return m.TagTypes
	}
	return nil
}

func (m *GetTagTypesResponse) GetTotal() uint64 {
	if m != nil && m.Total != nil {
		return *m.Total
	}
	return 0
}

type CreateTagTypeRequest struct {
	TypeName             *string  `protobuf:"bytes,1,opt,name=type_name,json=typeName" json:"type_name,omitempty"`
	Source               *Source  `protobuf:"varint,2,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTagTypeRequest) Reset()         { *m = CreateTagTypeRequest{} }
func (m *CreateTagTypeRequest) String() string { return proto.CompactTextString(m) }
func (*CreateTagTypeRequest) ProtoMessage()    {}
func (*CreateTagTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{8}
}
func (m *CreateTagTypeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateTagTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateTagTypeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateTagTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagTypeRequest.Merge(m, src)
}
func (m *CreateTagTypeRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateTagTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagTypeRequest proto.InternalMessageInfo

func (m *CreateTagTypeRequest) GetTypeName() string {
	if m != nil && m.TypeName != nil {
		return *m.TypeName
	}
	return ""
}

func (m *CreateTagTypeRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type CreateTagTypeResponse struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTagTypeResponse) Reset()         { *m = CreateTagTypeResponse{} }
func (m *CreateTagTypeResponse) String() string { return proto.CompactTextString(m) }
func (*CreateTagTypeResponse) ProtoMessage()    {}
func (*CreateTagTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{9}
}
func (m *CreateTagTypeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateTagTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateTagTypeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateTagTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagTypeResponse.Merge(m, src)
}
func (m *CreateTagTypeResponse) XXX_Size() int {
	return m.Size()
}
func (m *CreateTagTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagTypeResponse proto.InternalMessageInfo

func (m *CreateTagTypeResponse) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

type UpdateTagTypeRequest struct {
	Id                   *uint64                    `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	TypeName             *string                    `protobuf:"bytes,2,opt,name=type_name,json=typeName" json:"type_name,omitempty"`
	Status               *foody_base.TagType_Status `protobuf:"varint,3,opt,name=status,enum=foody_base.TagType_Status" json:"status,omitempty"`
	Source               *Source                    `protobuf:"varint,4,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *UpdateTagTypeRequest) Reset()         { *m = UpdateTagTypeRequest{} }
func (m *UpdateTagTypeRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateTagTypeRequest) ProtoMessage()    {}
func (*UpdateTagTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{10}
}
func (m *UpdateTagTypeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateTagTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateTagTypeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateTagTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTagTypeRequest.Merge(m, src)
}
func (m *UpdateTagTypeRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateTagTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTagTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTagTypeRequest proto.InternalMessageInfo

func (m *UpdateTagTypeRequest) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *UpdateTagTypeRequest) GetTypeName() string {
	if m != nil && m.TypeName != nil {
		return *m.TypeName
	}
	return ""
}

func (m *UpdateTagTypeRequest) GetStatus() foody_base.TagType_Status {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return foody_base.TagType_INACTIVE
}

func (m *UpdateTagTypeRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type UpdateTagTypeResponse struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTagTypeResponse) Reset()         { *m = UpdateTagTypeResponse{} }
func (m *UpdateTagTypeResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateTagTypeResponse) ProtoMessage()    {}
func (*UpdateTagTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{11}
}
func (m *UpdateTagTypeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateTagTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateTagTypeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateTagTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTagTypeResponse.Merge(m, src)
}
func (m *UpdateTagTypeResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateTagTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTagTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTagTypeResponse proto.InternalMessageInfo

func (m *UpdateTagTypeResponse) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

type CreateTagRequest struct {
	Tag                  *foody_base.Tag `protobuf:"bytes,1,opt,name=tag" json:"tag,omitempty"`
	Source               *Source         `protobuf:"varint,2,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CreateTagRequest) Reset()         { *m = CreateTagRequest{} }
func (m *CreateTagRequest) String() string { return proto.CompactTextString(m) }
func (*CreateTagRequest) ProtoMessage()    {}
func (*CreateTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{12}
}
func (m *CreateTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagRequest.Merge(m, src)
}
func (m *CreateTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagRequest proto.InternalMessageInfo

func (m *CreateTagRequest) GetTag() *foody_base.Tag {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *CreateTagRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type CreateTagResponse struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTagResponse) Reset()         { *m = CreateTagResponse{} }
func (m *CreateTagResponse) String() string { return proto.CompactTextString(m) }
func (*CreateTagResponse) ProtoMessage()    {}
func (*CreateTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{13}
}
func (m *CreateTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagResponse.Merge(m, src)
}
func (m *CreateTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *CreateTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagResponse proto.InternalMessageInfo

func (m *CreateTagResponse) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

type UpdateTagRequest struct {
	Tag                  *foody_base.Tag `protobuf:"bytes,1,opt,name=tag" json:"tag,omitempty"`
	Source               *Source         `protobuf:"varint,2,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateTagRequest) Reset()         { *m = UpdateTagRequest{} }
func (m *UpdateTagRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateTagRequest) ProtoMessage()    {}
func (*UpdateTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{14}
}
func (m *UpdateTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTagRequest.Merge(m, src)
}
func (m *UpdateTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *UpdateTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTagRequest proto.InternalMessageInfo

func (m *UpdateTagRequest) GetTag() *foody_base.Tag {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *UpdateTagRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type UpdateTagResponse struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTagResponse) Reset()         { *m = UpdateTagResponse{} }
func (m *UpdateTagResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateTagResponse) ProtoMessage()    {}
func (*UpdateTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{15}
}
func (m *UpdateTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UpdateTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UpdateTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UpdateTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTagResponse.Merge(m, src)
}
func (m *UpdateTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *UpdateTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTagResponse proto.InternalMessageInfo

func (m *UpdateTagResponse) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

type DeleteTagRequest struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Source               *Source  `protobuf:"varint,2,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTagRequest) Reset()         { *m = DeleteTagRequest{} }
func (m *DeleteTagRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteTagRequest) ProtoMessage()    {}
func (*DeleteTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{16}
}
func (m *DeleteTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTagRequest.Merge(m, src)
}
func (m *DeleteTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTagRequest proto.InternalMessageInfo

func (m *DeleteTagRequest) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *DeleteTagRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type DeleteTagResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTagResponse) Reset()         { *m = DeleteTagResponse{} }
func (m *DeleteTagResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteTagResponse) ProtoMessage()    {}
func (*DeleteTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{17}
}
func (m *DeleteTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTagResponse.Merge(m, src)
}
func (m *DeleteTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTagResponse proto.InternalMessageInfo

type SearchTagRequest struct {
	Keyword              *string  `protobuf:"bytes,1,opt,name=keyword" json:"keyword,omitempty"`
	TagTypeId            *uint64  `protobuf:"varint,2,opt,name=tag_type_id,json=tagTypeId" json:"tag_type_id,omitempty"`
	PageNum              *uint32  `protobuf:"varint,3,opt,name=page_num,json=pageNum" json:"page_num,omitempty"`
	PageSize             *uint32  `protobuf:"varint,4,opt,name=page_size,json=pageSize" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchTagRequest) Reset()         { *m = SearchTagRequest{} }
func (m *SearchTagRequest) String() string { return proto.CompactTextString(m) }
func (*SearchTagRequest) ProtoMessage()    {}
func (*SearchTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{18}
}
func (m *SearchTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SearchTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SearchTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SearchTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchTagRequest.Merge(m, src)
}
func (m *SearchTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *SearchTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SearchTagRequest proto.InternalMessageInfo

func (m *SearchTagRequest) GetKeyword() string {
	if m != nil && m.Keyword != nil {
		return *m.Keyword
	}
	return ""
}

func (m *SearchTagRequest) GetTagTypeId() uint64 {
	if m != nil && m.TagTypeId != nil {
		return *m.TagTypeId
	}
	return 0
}

func (m *SearchTagRequest) GetPageNum() uint32 {
	if m != nil && m.PageNum != nil {
		return *m.PageNum
	}
	return 0
}

func (m *SearchTagRequest) GetPageSize() uint32 {
	if m != nil && m.PageSize != nil {
		return *m.PageSize
	}
	return 0
}

type SearchTagResponse struct {
	Tags                 []*foody_base.Tag `protobuf:"bytes,1,rep,name=tags" json:"tags,omitempty"`
	Total                *uint64           `protobuf:"varint,2,opt,name=total" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SearchTagResponse) Reset()         { *m = SearchTagResponse{} }
func (m *SearchTagResponse) String() string { return proto.CompactTextString(m) }
func (*SearchTagResponse) ProtoMessage()    {}
func (*SearchTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{19}
}
func (m *SearchTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SearchTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SearchTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SearchTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchTagResponse.Merge(m, src)
}
func (m *SearchTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *SearchTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SearchTagResponse proto.InternalMessageInfo

func (m *SearchTagResponse) GetTags() []*foody_base.Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *SearchTagResponse) GetTotal() uint64 {
	if m != nil && m.Total != nil {
		return *m.Total
	}
	return 0
}

type MGetTagTypeRequest struct {
	Ids                  []uint64 `protobuf:"varint,1,rep,name=ids" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MGetTagTypeRequest) Reset()         { *m = MGetTagTypeRequest{} }
func (m *MGetTagTypeRequest) String() string { return proto.CompactTextString(m) }
func (*MGetTagTypeRequest) ProtoMessage()    {}
func (*MGetTagTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{20}
}
func (m *MGetTagTypeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagTypeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagTypeRequest.Merge(m, src)
}
func (m *MGetTagTypeRequest) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagTypeRequest proto.InternalMessageInfo

func (m *MGetTagTypeRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type MGetTagTypeResponse struct {
	TagTypes             []*foody_base.TagType `protobuf:"bytes,1,rep,name=tag_types,json=tagTypes" json:"tag_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MGetTagTypeResponse) Reset()         { *m = MGetTagTypeResponse{} }
func (m *MGetTagTypeResponse) String() string { return proto.CompactTextString(m) }
func (*MGetTagTypeResponse) ProtoMessage()    {}
func (*MGetTagTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{21}
}
func (m *MGetTagTypeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagTypeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagTypeResponse.Merge(m, src)
}
func (m *MGetTagTypeResponse) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagTypeResponse proto.InternalMessageInfo

func (m *MGetTagTypeResponse) GetTagTypes() []*foody_base.TagType {
	if m != nil {
		return m.TagTypes
	}
	return nil
}

type DeleteTagTypeRequest struct {
	Id                   *uint64  `protobuf:"varint,1,opt,name=id" json:"id,omitempty"`
	Source               *Source  `protobuf:"varint,2,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTagTypeRequest) Reset()         { *m = DeleteTagTypeRequest{} }
func (m *DeleteTagTypeRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteTagTypeRequest) ProtoMessage()    {}
func (*DeleteTagTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{22}
}
func (m *DeleteTagTypeRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteTagTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteTagTypeRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteTagTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTagTypeRequest.Merge(m, src)
}
func (m *DeleteTagTypeRequest) XXX_Size() int {
	return m.Size()
}
func (m *DeleteTagTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTagTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTagTypeRequest proto.InternalMessageInfo

func (m *DeleteTagTypeRequest) GetId() uint64 {
	if m != nil && m.Id != nil {
		return *m.Id
	}
	return 0
}

func (m *DeleteTagTypeRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type DeleteTagTypeResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTagTypeResponse) Reset()         { *m = DeleteTagTypeResponse{} }
func (m *DeleteTagTypeResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteTagTypeResponse) ProtoMessage()    {}
func (*DeleteTagTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{23}
}
func (m *DeleteTagTypeResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *DeleteTagTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_DeleteTagTypeResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *DeleteTagTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTagTypeResponse.Merge(m, src)
}
func (m *DeleteTagTypeResponse) XXX_Size() int {
	return m.Size()
}
func (m *DeleteTagTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTagTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTagTypeResponse proto.InternalMessageInfo

type GetStoresByTagIDRequest struct {
	TagId                *uint64  `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	PageNum              *uint32  `protobuf:"varint,2,opt,name=page_num,json=pageNum" json:"page_num,omitempty"`
	PageSize             *uint32  `protobuf:"varint,3,opt,name=page_size,json=pageSize" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoresByTagIDRequest) Reset()         { *m = GetStoresByTagIDRequest{} }
func (m *GetStoresByTagIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoresByTagIDRequest) ProtoMessage()    {}
func (*GetStoresByTagIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{24}
}
func (m *GetStoresByTagIDRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoresByTagIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoresByTagIDRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoresByTagIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoresByTagIDRequest.Merge(m, src)
}
func (m *GetStoresByTagIDRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetStoresByTagIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoresByTagIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoresByTagIDRequest proto.InternalMessageInfo

func (m *GetStoresByTagIDRequest) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetStoresByTagIDRequest) GetPageNum() uint32 {
	if m != nil && m.PageNum != nil {
		return *m.PageNum
	}
	return 0
}

func (m *GetStoresByTagIDRequest) GetPageSize() uint32 {
	if m != nil && m.PageSize != nil {
		return *m.PageSize
	}
	return 0
}

type GetStoresByTagIDResponse struct {
	Stores               []*foody_base.Store `protobuf:"bytes,1,rep,name=stores" json:"stores,omitempty"`
	Total                *uint64             `protobuf:"varint,2,opt,name=total" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetStoresByTagIDResponse) Reset()         { *m = GetStoresByTagIDResponse{} }
func (m *GetStoresByTagIDResponse) String() string { return proto.CompactTextString(m) }
func (*GetStoresByTagIDResponse) ProtoMessage()    {}
func (*GetStoresByTagIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{25}
}
func (m *GetStoresByTagIDResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoresByTagIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoresByTagIDResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoresByTagIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoresByTagIDResponse.Merge(m, src)
}
func (m *GetStoresByTagIDResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetStoresByTagIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoresByTagIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoresByTagIDResponse proto.InternalMessageInfo

func (m *GetStoresByTagIDResponse) GetStores() []*foody_base.Store {
	if m != nil {
		return m.Stores
	}
	return nil
}

func (m *GetStoresByTagIDResponse) GetTotal() uint64 {
	if m != nil && m.Total != nil {
		return *m.Total
	}
	return 0
}

type MGetTagRequest struct {
	Ids                  []uint64 `protobuf:"varint,1,rep,name=ids" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MGetTagRequest) Reset()         { *m = MGetTagRequest{} }
func (m *MGetTagRequest) String() string { return proto.CompactTextString(m) }
func (*MGetTagRequest) ProtoMessage()    {}
func (*MGetTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{26}
}
func (m *MGetTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagRequest.Merge(m, src)
}
func (m *MGetTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagRequest proto.InternalMessageInfo

func (m *MGetTagRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type MGetTagResponse struct {
	Tags                 []*foody_base.Tag `protobuf:"bytes,1,rep,name=tags" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MGetTagResponse) Reset()         { *m = MGetTagResponse{} }
func (m *MGetTagResponse) String() string { return proto.CompactTextString(m) }
func (*MGetTagResponse) ProtoMessage()    {}
func (*MGetTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{27}
}
func (m *MGetTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagResponse.Merge(m, src)
}
func (m *MGetTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagResponse proto.InternalMessageInfo

func (m *MGetTagResponse) GetTags() []*foody_base.Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type MGetAllTagRequest struct {
	Ids                  []uint64 `protobuf:"varint,1,rep,name=ids" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MGetAllTagRequest) Reset()         { *m = MGetAllTagRequest{} }
func (m *MGetAllTagRequest) String() string { return proto.CompactTextString(m) }
func (*MGetAllTagRequest) ProtoMessage()    {}
func (*MGetAllTagRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{28}
}
func (m *MGetAllTagRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetAllTagRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetAllTagRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetAllTagRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetAllTagRequest.Merge(m, src)
}
func (m *MGetAllTagRequest) XXX_Size() int {
	return m.Size()
}
func (m *MGetAllTagRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetAllTagRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MGetAllTagRequest proto.InternalMessageInfo

func (m *MGetAllTagRequest) GetIds() []uint64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

type MGetAllTagResponse struct {
	Tags                 []*foody_base.Tag `protobuf:"bytes,1,rep,name=tags" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MGetAllTagResponse) Reset()         { *m = MGetAllTagResponse{} }
func (m *MGetAllTagResponse) String() string { return proto.CompactTextString(m) }
func (*MGetAllTagResponse) ProtoMessage()    {}
func (*MGetAllTagResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{29}
}
func (m *MGetAllTagResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetAllTagResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetAllTagResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetAllTagResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetAllTagResponse.Merge(m, src)
}
func (m *MGetAllTagResponse) XXX_Size() int {
	return m.Size()
}
func (m *MGetAllTagResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetAllTagResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MGetAllTagResponse proto.InternalMessageInfo

func (m *MGetAllTagResponse) GetTags() []*foody_base.Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type MGetTagsByStoreIDsRequest struct {
	StoreIds             []uint64  `protobuf:"varint,1,rep,name=store_ids,json=storeIds" json:"store_ids,omitempty"`
	TagScene             *TagScene `protobuf:"varint,2,opt,name=tag_scene,json=tagScene,enum=foody_storetag.TagScene" json:"tag_scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *MGetTagsByStoreIDsRequest) Reset()         { *m = MGetTagsByStoreIDsRequest{} }
func (m *MGetTagsByStoreIDsRequest) String() string { return proto.CompactTextString(m) }
func (*MGetTagsByStoreIDsRequest) ProtoMessage()    {}
func (*MGetTagsByStoreIDsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{30}
}
func (m *MGetTagsByStoreIDsRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagsByStoreIDsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagsByStoreIDsRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagsByStoreIDsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagsByStoreIDsRequest.Merge(m, src)
}
func (m *MGetTagsByStoreIDsRequest) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagsByStoreIDsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagsByStoreIDsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagsByStoreIDsRequest proto.InternalMessageInfo

func (m *MGetTagsByStoreIDsRequest) GetStoreIds() []uint64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *MGetTagsByStoreIDsRequest) GetTagScene() TagScene {
	if m != nil && m.TagScene != nil {
		return *m.TagScene
	}
	return TagScene_Admin
}

type StoreTags struct {
	StoreId              *uint64           `protobuf:"varint,1,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	Tags                 []*foody_base.Tag `protobuf:"bytes,2,rep,name=tags" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *StoreTags) Reset()         { *m = StoreTags{} }
func (m *StoreTags) String() string { return proto.CompactTextString(m) }
func (*StoreTags) ProtoMessage()    {}
func (*StoreTags) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{31}
}
func (m *StoreTags) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreTags) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreTags.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreTags) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreTags.Merge(m, src)
}
func (m *StoreTags) XXX_Size() int {
	return m.Size()
}
func (m *StoreTags) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreTags.DiscardUnknown(m)
}

var xxx_messageInfo_StoreTags proto.InternalMessageInfo

func (m *StoreTags) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *StoreTags) GetTags() []*foody_base.Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type MGetTagsByStoreIDsResponse struct {
	StoreTagsList        []*StoreTags `protobuf:"bytes,1,rep,name=store_tags_list,json=storeTagsList" json:"store_tags_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MGetTagsByStoreIDsResponse) Reset()         { *m = MGetTagsByStoreIDsResponse{} }
func (m *MGetTagsByStoreIDsResponse) String() string { return proto.CompactTextString(m) }
func (*MGetTagsByStoreIDsResponse) ProtoMessage()    {}
func (*MGetTagsByStoreIDsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{32}
}
func (m *MGetTagsByStoreIDsResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MGetTagsByStoreIDsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MGetTagsByStoreIDsResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MGetTagsByStoreIDsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MGetTagsByStoreIDsResponse.Merge(m, src)
}
func (m *MGetTagsByStoreIDsResponse) XXX_Size() int {
	return m.Size()
}
func (m *MGetTagsByStoreIDsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MGetTagsByStoreIDsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MGetTagsByStoreIDsResponse proto.InternalMessageInfo

func (m *MGetTagsByStoreIDsResponse) GetStoreTagsList() []*StoreTags {
	if m != nil {
		return m.StoreTagsList
	}
	return nil
}

type GetTagTypeByNameRequest struct {
	Name                 *string  `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTagTypeByNameRequest) Reset()         { *m = GetTagTypeByNameRequest{} }
func (m *GetTagTypeByNameRequest) String() string { return proto.CompactTextString(m) }
func (*GetTagTypeByNameRequest) ProtoMessage()    {}
func (*GetTagTypeByNameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{33}
}
func (m *GetTagTypeByNameRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagTypeByNameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagTypeByNameRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagTypeByNameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagTypeByNameRequest.Merge(m, src)
}
func (m *GetTagTypeByNameRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetTagTypeByNameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagTypeByNameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagTypeByNameRequest proto.InternalMessageInfo

func (m *GetTagTypeByNameRequest) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

type GetTagTypeByNameResponse struct {
	TagType              *foody_base.TagType `protobuf:"bytes,1,opt,name=tag_type,json=tagType" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetTagTypeByNameResponse) Reset()         { *m = GetTagTypeByNameResponse{} }
func (m *GetTagTypeByNameResponse) String() string { return proto.CompactTextString(m) }
func (*GetTagTypeByNameResponse) ProtoMessage()    {}
func (*GetTagTypeByNameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{34}
}
func (m *GetTagTypeByNameResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetTagTypeByNameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetTagTypeByNameResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetTagTypeByNameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTagTypeByNameResponse.Merge(m, src)
}
func (m *GetTagTypeByNameResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetTagTypeByNameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTagTypeByNameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTagTypeByNameResponse proto.InternalMessageInfo

func (m *GetTagTypeByNameResponse) GetTagType() *foody_base.TagType {
	if m != nil {
		return m.TagType
	}
	return nil
}

type FailedResult struct {
	StoreId              *uint64                 `protobuf:"varint,1,opt,name=store_id,json=storeId" json:"store_id,omitempty"`
	ErrorCode            *FailedResult_ErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,enum=foody_storetag.FailedResult_ErrorCode" json:"error_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *FailedResult) Reset()         { *m = FailedResult{} }
func (m *FailedResult) String() string { return proto.CompactTextString(m) }
func (*FailedResult) ProtoMessage()    {}
func (*FailedResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{35}
}
func (m *FailedResult) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FailedResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FailedResult.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FailedResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FailedResult.Merge(m, src)
}
func (m *FailedResult) XXX_Size() int {
	return m.Size()
}
func (m *FailedResult) XXX_DiscardUnknown() {
	xxx_messageInfo_FailedResult.DiscardUnknown(m)
}

var xxx_messageInfo_FailedResult proto.InternalMessageInfo

func (m *FailedResult) GetStoreId() uint64 {
	if m != nil && m.StoreId != nil {
		return *m.StoreId
	}
	return 0
}

func (m *FailedResult) GetErrorCode() FailedResult_ErrorCode {
	if m != nil && m.ErrorCode != nil {
		return *m.ErrorCode
	}
	return FailedResult_ERROR_OTHER
}

type MAddTagBindingRequest struct {
	TagId                *uint64  `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	StoreIds             []uint64 `protobuf:"varint,2,rep,name=store_ids,json=storeIds" json:"store_ids,omitempty"`
	Source               *Source  `protobuf:"varint,3,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MAddTagBindingRequest) Reset()         { *m = MAddTagBindingRequest{} }
func (m *MAddTagBindingRequest) String() string { return proto.CompactTextString(m) }
func (*MAddTagBindingRequest) ProtoMessage()    {}
func (*MAddTagBindingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{36}
}
func (m *MAddTagBindingRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MAddTagBindingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MAddTagBindingRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MAddTagBindingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MAddTagBindingRequest.Merge(m, src)
}
func (m *MAddTagBindingRequest) XXX_Size() int {
	return m.Size()
}
func (m *MAddTagBindingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MAddTagBindingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MAddTagBindingRequest proto.InternalMessageInfo

func (m *MAddTagBindingRequest) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *MAddTagBindingRequest) GetStoreIds() []uint64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *MAddTagBindingRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type MAddTagBindingResponse struct {
	Results              []*FailedResult `protobuf:"bytes,1,rep,name=results" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MAddTagBindingResponse) Reset()         { *m = MAddTagBindingResponse{} }
func (m *MAddTagBindingResponse) String() string { return proto.CompactTextString(m) }
func (*MAddTagBindingResponse) ProtoMessage()    {}
func (*MAddTagBindingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{37}
}
func (m *MAddTagBindingResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MAddTagBindingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MAddTagBindingResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MAddTagBindingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MAddTagBindingResponse.Merge(m, src)
}
func (m *MAddTagBindingResponse) XXX_Size() int {
	return m.Size()
}
func (m *MAddTagBindingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MAddTagBindingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MAddTagBindingResponse proto.InternalMessageInfo

func (m *MAddTagBindingResponse) GetResults() []*FailedResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type MDeleteTagBindingRequest struct {
	TagId                *uint64  `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	StoreIds             []uint64 `protobuf:"varint,2,rep,name=store_ids,json=storeIds" json:"store_ids,omitempty"`
	Source               *Source  `protobuf:"varint,3,opt,name=source,enum=foody_storetag.Source" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MDeleteTagBindingRequest) Reset()         { *m = MDeleteTagBindingRequest{} }
func (m *MDeleteTagBindingRequest) String() string { return proto.CompactTextString(m) }
func (*MDeleteTagBindingRequest) ProtoMessage()    {}
func (*MDeleteTagBindingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{38}
}
func (m *MDeleteTagBindingRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MDeleteTagBindingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MDeleteTagBindingRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MDeleteTagBindingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MDeleteTagBindingRequest.Merge(m, src)
}
func (m *MDeleteTagBindingRequest) XXX_Size() int {
	return m.Size()
}
func (m *MDeleteTagBindingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MDeleteTagBindingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MDeleteTagBindingRequest proto.InternalMessageInfo

func (m *MDeleteTagBindingRequest) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *MDeleteTagBindingRequest) GetStoreIds() []uint64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *MDeleteTagBindingRequest) GetSource() Source {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return Source_SOURCE_DEFAULT
}

type MDeleteTagBindingResponse struct {
	Results              []*FailedResult `protobuf:"bytes,1,rep,name=results" json:"results,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *MDeleteTagBindingResponse) Reset()         { *m = MDeleteTagBindingResponse{} }
func (m *MDeleteTagBindingResponse) String() string { return proto.CompactTextString(m) }
func (*MDeleteTagBindingResponse) ProtoMessage()    {}
func (*MDeleteTagBindingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{39}
}
func (m *MDeleteTagBindingResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *MDeleteTagBindingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_MDeleteTagBindingResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *MDeleteTagBindingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MDeleteTagBindingResponse.Merge(m, src)
}
func (m *MDeleteTagBindingResponse) XXX_Size() int {
	return m.Size()
}
func (m *MDeleteTagBindingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MDeleteTagBindingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MDeleteTagBindingResponse proto.InternalMessageInfo

func (m *MDeleteTagBindingResponse) GetResults() []*FailedResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type CreateAsyncTaskRequest struct {
	FileName             *string                    `protobuf:"bytes,1,opt,name=file_name,json=fileName" json:"file_name,omitempty"`
	FileLink             *string                    `protobuf:"bytes,2,opt,name=file_link,json=fileLink" json:"file_link,omitempty"`
	TaskType             *foody_base.AsyncTask_Type `protobuf:"varint,3,opt,name=task_type,json=taskType,enum=foody_base.AsyncTask_Type" json:"task_type,omitempty"`
	Operator             *string                    `protobuf:"bytes,4,opt,name=operator" json:"operator,omitempty"`
	ReferenceId          *uint64                    `protobuf:"varint,5,opt,name=reference_id,json=referenceId" json:"reference_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *CreateAsyncTaskRequest) Reset()         { *m = CreateAsyncTaskRequest{} }
func (m *CreateAsyncTaskRequest) String() string { return proto.CompactTextString(m) }
func (*CreateAsyncTaskRequest) ProtoMessage()    {}
func (*CreateAsyncTaskRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{40}
}
func (m *CreateAsyncTaskRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateAsyncTaskRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateAsyncTaskRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateAsyncTaskRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAsyncTaskRequest.Merge(m, src)
}
func (m *CreateAsyncTaskRequest) XXX_Size() int {
	return m.Size()
}
func (m *CreateAsyncTaskRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAsyncTaskRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAsyncTaskRequest proto.InternalMessageInfo

func (m *CreateAsyncTaskRequest) GetFileName() string {
	if m != nil && m.FileName != nil {
		return *m.FileName
	}
	return ""
}

func (m *CreateAsyncTaskRequest) GetFileLink() string {
	if m != nil && m.FileLink != nil {
		return *m.FileLink
	}
	return ""
}

func (m *CreateAsyncTaskRequest) GetTaskType() foody_base.AsyncTask_Type {
	if m != nil && m.TaskType != nil {
		return *m.TaskType
	}
	return foody_base.AsyncTask_STORE_TAG_BINDING_ATTACH
}

func (m *CreateAsyncTaskRequest) GetOperator() string {
	if m != nil && m.Operator != nil {
		return *m.Operator
	}
	return ""
}

func (m *CreateAsyncTaskRequest) GetReferenceId() uint64 {
	if m != nil && m.ReferenceId != nil {
		return *m.ReferenceId
	}
	return 0
}

type CreateAsyncTaskResponse struct {
	TaskId               *uint64  `protobuf:"varint,1,opt,name=task_id,json=taskId" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAsyncTaskResponse) Reset()         { *m = CreateAsyncTaskResponse{} }
func (m *CreateAsyncTaskResponse) String() string { return proto.CompactTextString(m) }
func (*CreateAsyncTaskResponse) ProtoMessage()    {}
func (*CreateAsyncTaskResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{41}
}
func (m *CreateAsyncTaskResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CreateAsyncTaskResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CreateAsyncTaskResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CreateAsyncTaskResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAsyncTaskResponse.Merge(m, src)
}
func (m *CreateAsyncTaskResponse) XXX_Size() int {
	return m.Size()
}
func (m *CreateAsyncTaskResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAsyncTaskResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAsyncTaskResponse proto.InternalMessageInfo

func (m *CreateAsyncTaskResponse) GetTaskId() uint64 {
	if m != nil && m.TaskId != nil {
		return *m.TaskId
	}
	return 0
}

type GetAsyncTasksRequest struct {
	ReferenceId          *uint64                     `protobuf:"varint,1,opt,name=reference_id,json=referenceId" json:"reference_id,omitempty"`
	TaskType             []foody_base.AsyncTask_Type `protobuf:"varint,2,rep,name=task_type,json=taskType,enum=foody_base.AsyncTask_Type" json:"task_type,omitempty"`
	PageNum              *uint32                     `protobuf:"varint,3,opt,name=page_num,json=pageNum" json:"page_num,omitempty"`
	PageSize             *uint32                     `protobuf:"varint,4,opt,name=page_size,json=pageSize" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetAsyncTasksRequest) Reset()         { *m = GetAsyncTasksRequest{} }
func (m *GetAsyncTasksRequest) String() string { return proto.CompactTextString(m) }
func (*GetAsyncTasksRequest) ProtoMessage()    {}
func (*GetAsyncTasksRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{42}
}
func (m *GetAsyncTasksRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAsyncTasksRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAsyncTasksRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAsyncTasksRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAsyncTasksRequest.Merge(m, src)
}
func (m *GetAsyncTasksRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetAsyncTasksRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAsyncTasksRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetAsyncTasksRequest proto.InternalMessageInfo

func (m *GetAsyncTasksRequest) GetReferenceId() uint64 {
	if m != nil && m.ReferenceId != nil {
		return *m.ReferenceId
	}
	return 0
}

func (m *GetAsyncTasksRequest) GetTaskType() []foody_base.AsyncTask_Type {
	if m != nil {
		return m.TaskType
	}
	return nil
}

func (m *GetAsyncTasksRequest) GetPageNum() uint32 {
	if m != nil && m.PageNum != nil {
		return *m.PageNum
	}
	return 0
}

func (m *GetAsyncTasksRequest) GetPageSize() uint32 {
	if m != nil && m.PageSize != nil {
		return *m.PageSize
	}
	return 0
}

type GetAsyncTasksResponse struct {
	Tasks                []*foody_base.AsyncTask `protobuf:"bytes,1,rep,name=tasks" json:"tasks,omitempty"`
	Total                *uint64                 `protobuf:"varint,2,opt,name=total" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAsyncTasksResponse) Reset()         { *m = GetAsyncTasksResponse{} }
func (m *GetAsyncTasksResponse) String() string { return proto.CompactTextString(m) }
func (*GetAsyncTasksResponse) ProtoMessage()    {}
func (*GetAsyncTasksResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{43}
}
func (m *GetAsyncTasksResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetAsyncTasksResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetAsyncTasksResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetAsyncTasksResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAsyncTasksResponse.Merge(m, src)
}
func (m *GetAsyncTasksResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetAsyncTasksResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAsyncTasksResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetAsyncTasksResponse proto.InternalMessageInfo

func (m *GetAsyncTasksResponse) GetTasks() []*foody_base.AsyncTask {
	if m != nil {
		return m.Tasks
	}
	return nil
}

func (m *GetAsyncTasksResponse) GetTotal() uint64 {
	if m != nil && m.Total != nil {
		return *m.Total
	}
	return 0
}

type GetStoreIDsByTagIDRequest struct {
	TagId                *uint64  `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id,omitempty"`
	StartId              *uint64  `protobuf:"varint,2,opt,name=start_id,json=startId" json:"start_id,omitempty"`
	Limit                *uint64  `protobuf:"varint,3,opt,name=limit" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoreIDsByTagIDRequest) Reset()         { *m = GetStoreIDsByTagIDRequest{} }
func (m *GetStoreIDsByTagIDRequest) String() string { return proto.CompactTextString(m) }
func (*GetStoreIDsByTagIDRequest) ProtoMessage()    {}
func (*GetStoreIDsByTagIDRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{44}
}
func (m *GetStoreIDsByTagIDRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreIDsByTagIDRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreIDsByTagIDRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoreIDsByTagIDRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreIDsByTagIDRequest.Merge(m, src)
}
func (m *GetStoreIDsByTagIDRequest) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreIDsByTagIDRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreIDsByTagIDRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreIDsByTagIDRequest proto.InternalMessageInfo

func (m *GetStoreIDsByTagIDRequest) GetTagId() uint64 {
	if m != nil && m.TagId != nil {
		return *m.TagId
	}
	return 0
}

func (m *GetStoreIDsByTagIDRequest) GetStartId() uint64 {
	if m != nil && m.StartId != nil {
		return *m.StartId
	}
	return 0
}

func (m *GetStoreIDsByTagIDRequest) GetLimit() uint64 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

type GetStoreIDsByTagIDResponse struct {
	StoreIds             []uint64 `protobuf:"varint,1,rep,name=store_ids,json=storeIds" json:"store_ids,omitempty"`
	LastId               *uint64  `protobuf:"varint,2,opt,name=last_id,json=lastId" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStoreIDsByTagIDResponse) Reset()         { *m = GetStoreIDsByTagIDResponse{} }
func (m *GetStoreIDsByTagIDResponse) String() string { return proto.CompactTextString(m) }
func (*GetStoreIDsByTagIDResponse) ProtoMessage()    {}
func (*GetStoreIDsByTagIDResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_74e5e878914f39ff, []int{45}
}
func (m *GetStoreIDsByTagIDResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *GetStoreIDsByTagIDResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_GetStoreIDsByTagIDResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *GetStoreIDsByTagIDResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStoreIDsByTagIDResponse.Merge(m, src)
}
func (m *GetStoreIDsByTagIDResponse) XXX_Size() int {
	return m.Size()
}
func (m *GetStoreIDsByTagIDResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStoreIDsByTagIDResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetStoreIDsByTagIDResponse proto.InternalMessageInfo

func (m *GetStoreIDsByTagIDResponse) GetStoreIds() []uint64 {
	if m != nil {
		return m.StoreIds
	}
	return nil
}

func (m *GetStoreIDsByTagIDResponse) GetLastId() uint64 {
	if m != nil && m.LastId != nil {
		return *m.LastId
	}
	return 0
}

func init() {
	proto.RegisterEnum("foody_storetag.TagScene", TagScene_name, TagScene_value)
	proto.RegisterEnum("foody_storetag.Source", Source_name, Source_value)
	proto.RegisterEnum("foody_storetag.FailedResult_ErrorCode", FailedResult_ErrorCode_name, FailedResult_ErrorCode_value)
	proto.RegisterType((*GetTagRequest)(nil), "foody_storetag.GetTagRequest")
	proto.RegisterType((*GetTagResponse)(nil), "foody_storetag.GetTagResponse")
	proto.RegisterType((*StoreTagBinding)(nil), "foody_storetag.StoreTagBinding")
	proto.RegisterType((*StoreTagTypeBinding)(nil), "foody_storetag.StoreTagTypeBinding")
	proto.RegisterType((*GetTagsByStoreIDRequest)(nil), "foody_storetag.GetTagsByStoreIDRequest")
	proto.RegisterType((*GetTagsByStoreIDResponse)(nil), "foody_storetag.GetTagsByStoreIDResponse")
	proto.RegisterType((*GetTagTypesRequest)(nil), "foody_storetag.GetTagTypesRequest")
	proto.RegisterType((*GetTagTypesResponse)(nil), "foody_storetag.GetTagTypesResponse")
	proto.RegisterType((*CreateTagTypeRequest)(nil), "foody_storetag.CreateTagTypeRequest")
	proto.RegisterType((*CreateTagTypeResponse)(nil), "foody_storetag.CreateTagTypeResponse")
	proto.RegisterType((*UpdateTagTypeRequest)(nil), "foody_storetag.UpdateTagTypeRequest")
	proto.RegisterType((*UpdateTagTypeResponse)(nil), "foody_storetag.UpdateTagTypeResponse")
	proto.RegisterType((*CreateTagRequest)(nil), "foody_storetag.CreateTagRequest")
	proto.RegisterType((*CreateTagResponse)(nil), "foody_storetag.CreateTagResponse")
	proto.RegisterType((*UpdateTagRequest)(nil), "foody_storetag.UpdateTagRequest")
	proto.RegisterType((*UpdateTagResponse)(nil), "foody_storetag.UpdateTagResponse")
	proto.RegisterType((*DeleteTagRequest)(nil), "foody_storetag.DeleteTagRequest")
	proto.RegisterType((*DeleteTagResponse)(nil), "foody_storetag.DeleteTagResponse")
	proto.RegisterType((*SearchTagRequest)(nil), "foody_storetag.SearchTagRequest")
	proto.RegisterType((*SearchTagResponse)(nil), "foody_storetag.SearchTagResponse")
	proto.RegisterType((*MGetTagTypeRequest)(nil), "foody_storetag.MGetTagTypeRequest")
	proto.RegisterType((*MGetTagTypeResponse)(nil), "foody_storetag.MGetTagTypeResponse")
	proto.RegisterType((*DeleteTagTypeRequest)(nil), "foody_storetag.DeleteTagTypeRequest")
	proto.RegisterType((*DeleteTagTypeResponse)(nil), "foody_storetag.DeleteTagTypeResponse")
	proto.RegisterType((*GetStoresByTagIDRequest)(nil), "foody_storetag.GetStoresByTagIDRequest")
	proto.RegisterType((*GetStoresByTagIDResponse)(nil), "foody_storetag.GetStoresByTagIDResponse")
	proto.RegisterType((*MGetTagRequest)(nil), "foody_storetag.MGetTagRequest")
	proto.RegisterType((*MGetTagResponse)(nil), "foody_storetag.MGetTagResponse")
	proto.RegisterType((*MGetAllTagRequest)(nil), "foody_storetag.MGetAllTagRequest")
	proto.RegisterType((*MGetAllTagResponse)(nil), "foody_storetag.MGetAllTagResponse")
	proto.RegisterType((*MGetTagsByStoreIDsRequest)(nil), "foody_storetag.MGetTagsByStoreIDsRequest")
	proto.RegisterType((*StoreTags)(nil), "foody_storetag.StoreTags")
	proto.RegisterType((*MGetTagsByStoreIDsResponse)(nil), "foody_storetag.MGetTagsByStoreIDsResponse")
	proto.RegisterType((*GetTagTypeByNameRequest)(nil), "foody_storetag.GetTagTypeByNameRequest")
	proto.RegisterType((*GetTagTypeByNameResponse)(nil), "foody_storetag.GetTagTypeByNameResponse")
	proto.RegisterType((*FailedResult)(nil), "foody_storetag.FailedResult")
	proto.RegisterType((*MAddTagBindingRequest)(nil), "foody_storetag.MAddTagBindingRequest")
	proto.RegisterType((*MAddTagBindingResponse)(nil), "foody_storetag.MAddTagBindingResponse")
	proto.RegisterType((*MDeleteTagBindingRequest)(nil), "foody_storetag.MDeleteTagBindingRequest")
	proto.RegisterType((*MDeleteTagBindingResponse)(nil), "foody_storetag.MDeleteTagBindingResponse")
	proto.RegisterType((*CreateAsyncTaskRequest)(nil), "foody_storetag.CreateAsyncTaskRequest")
	proto.RegisterType((*CreateAsyncTaskResponse)(nil), "foody_storetag.CreateAsyncTaskResponse")
	proto.RegisterType((*GetAsyncTasksRequest)(nil), "foody_storetag.GetAsyncTasksRequest")
	proto.RegisterType((*GetAsyncTasksResponse)(nil), "foody_storetag.GetAsyncTasksResponse")
	proto.RegisterType((*GetStoreIDsByTagIDRequest)(nil), "foody_storetag.GetStoreIDsByTagIDRequest")
	proto.RegisterType((*GetStoreIDsByTagIDResponse)(nil), "foody_storetag.GetStoreIDsByTagIDResponse")
}

func init() { proto.RegisterFile("foody_storetag.proto", fileDescriptor_74e5e878914f39ff) }

var fileDescriptor_74e5e878914f39ff = []byte{
	// 1801 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x59, 0xcb, 0x73, 0xdb, 0xc6,
	0x19, 0x27, 0x48, 0x89, 0x8f, 0x4f, 0x16, 0x49, 0xad, 0x24, 0x8b, 0x82, 0x6d, 0x5a, 0x82, 0x6b,
	0xf9, 0xd1, 0x9a, 0x6a, 0xd5, 0xa9, 0x3b, 0x3d, 0x75, 0xa8, 0x87, 0x5d, 0xb6, 0xa2, 0xec, 0x82,
	0x54, 0xc7, 0xd3, 0x4e, 0xcb, 0x81, 0x89, 0x15, 0x84, 0xf2, 0x01, 0x06, 0x0b, 0x26, 0xa1, 0x33,
	0x93, 0x53, 0x4e, 0x39, 0xe6, 0x94, 0x9c, 0x73, 0xc8, 0x4c, 0x2e, 0x39, 0xe5, 0x0f, 0xc8, 0x2d,
	0x93, 0x53, 0xfe, 0x84, 0x8c, 0x73, 0xcf, 0x2d, 0x97, 0x9c, 0x32, 0xbb, 0xc0, 0x82, 0x00, 0x16,
	0xa0, 0x28, 0x25, 0x99, 0xdc, 0xb0, 0xbb, 0xdf, 0x7e, 0xdf, 0xef, 0x7b, 0xec, 0xf7, 0x20, 0x61,
	0xed, 0xcc, 0xb2, 0xf4, 0x49, 0x87, 0x38, 0x96, 0x8d, 0x1d, 0xcd, 0xa8, 0x8d, 0x6c, 0xcb, 0xb1,
	0x50, 0x31, 0xbc, 0x2b, 0xdf, 0x20, 0x23, 0xfc, 0xf6, 0x2e, 0x3b, 0x7b, 0x39, 0x3e, 0xdb, 0x25,
	0xd8, 0x7e, 0xd3, 0xec, 0x62, 0x97, 0x58, 0xfe, 0xa7, 0x61, 0x3a, 0x35, 0x43, 0xb3, 0xf1, 0x50,
	0xab, 0x75, 0xad, 0xc1, 0x2e, 0x39, 0xb7, 0x46, 0x18, 0xef, 0x9e, 0x61, 0xac, 0xef, 0x0e, 0xcc,
	0xae, 0x6d, 0xf5, 0x4c, 0xc7, 0xbd, 0xdb, 0x7d, 0x64, 0xe0, 0xe1, 0x23, 0x7f, 0x4f, 0xc7, 0xa4,
	0x6b, 0x9b, 0x23, 0xc7, 0xb2, 0x7d, 0x3a, 0x8f, 0xe5, 0x7e, 0x02, 0x4b, 0x8a, 0x8a, 0x4b, 0xdf,
	0x1d, 0xbd, 0x74, 0x37, 0x3a, 0x2f, 0x35, 0x82, 0x03, 0x9f, 0x2e, 0x0f, 0xe5, 0x36, 0x2c, 0x3f,
	0xc5, 0x4e, 0x5b, 0x33, 0x54, 0xfc, 0xc6, 0x18, 0x13, 0x07, 0x15, 0x21, 0x6d, 0xea, 0x15, 0x69,
	0x4b, 0xba, 0xbf, 0xa0, 0xa6, 0x4d, 0x5d, 0xf9, 0x23, 0x14, 0x39, 0x01, 0x19, 0x59, 0x43, 0x82,
	0xd1, 0x36, 0x64, 0x1c, 0xcd, 0x60, 0x24, 0x4b, 0x7b, 0xa5, 0x5a, 0x80, 0x25, 0xa5, 0xa2, 0x67,
	0xca, 0x67, 0x12, 0x94, 0x5a, 0xd4, 0x2c, 0x6d, 0xcd, 0xd8, 0x37, 0x87, 0xba, 0x39, 0x34, 0xa2,
	0x8c, 0xd1, 0x26, 0xe4, 0x99, 0xe5, 0x3a, 0xa6, 0x5e, 0x49, 0xb3, 0xdd, 0x1c, 0x5b, 0x37, 0x74,
	0xb4, 0x0e, 0x59, 0x47, 0x33, 0xe8, 0x41, 0x86, 0x1d, 0x2c, 0x3a, 0x9a, 0xd1, 0xd0, 0x51, 0x05,
	0x72, 0x3a, 0xee, 0x63, 0x07, 0xeb, 0x95, 0x85, 0x2d, 0xe9, 0xfe, 0xb2, 0xca, 0x97, 0xe8, 0x36,
	0x2c, 0x75, 0x6d, 0xac, 0x39, 0xb8, 0xe3, 0x98, 0x03, 0x5c, 0x59, 0x64, 0xb7, 0xc0, 0xdd, 0x6a,
	0x9b, 0x03, 0x4c, 0x09, 0xc6, 0x23, 0xdd, 0x27, 0xc8, 0xba, 0x04, 0xee, 0x16, 0x25, 0x50, 0xbe,
	0x90, 0x60, 0x95, 0x23, 0x6e, 0x4f, 0x46, 0xf8, 0x0a, 0xa8, 0xab, 0xb0, 0x44, 0x51, 0x3b, 0x93,
	0x11, 0x9e, 0x42, 0x2f, 0x38, 0x2e, 0xbf, 0x86, 0x8e, 0x6e, 0x01, 0x98, 0xa4, 0x13, 0xd6, 0xa0,
	0x60, 0x92, 0xc3, 0x9f, 0x4d, 0x87, 0x1e, 0x6c, 0xb8, 0xae, 0x22, 0xfb, 0x13, 0xa6, 0x4b, 0xe3,
	0x90, 0x7b, 0x35, 0x08, 0x5b, 0x0a, 0xc3, 0xfe, 0x13, 0x50, 0x8c, 0x1d, 0xd2, 0xc5, 0x43, 0xcc,
	0x54, 0x2a, 0xee, 0x55, 0x6a, 0x91, 0x78, 0x6f, 0x6b, 0x46, 0x8b, 0x9e, 0xab, 0x79, 0xc7, 0xfb,
	0x52, 0xfe, 0x0a, 0x15, 0x51, 0x98, 0x17, 0x21, 0x77, 0x60, 0xc1, 0xd1, 0x0c, 0x52, 0x91, 0xb6,
	0x32, 0x71, 0x21, 0xc2, 0x0e, 0x95, 0x8f, 0x24, 0x40, 0x2e, 0x07, 0x6a, 0x1f, 0x12, 0x40, 0x3a,
	0xd2, 0x0c, 0xdc, 0x19, 0x8e, 0x07, 0x0c, 0xe9, 0xb2, 0x9a, 0xa3, 0xeb, 0x93, 0xf1, 0x00, 0xdd,
	0x80, 0x02, 0x3b, 0x22, 0xe6, 0x2b, 0x17, 0xe9, 0xb2, 0xca, 0x68, 0x5b, 0xe6, 0x2b, 0x8c, 0x36,
	0x20, 0x77, 0xae, 0x91, 0x0e, 0x8d, 0x4c, 0x6a, 0xf9, 0xbc, 0x9a, 0x3d, 0xd7, 0x48, 0x5b, 0x33,
	0xd0, 0x1e, 0x64, 0x89, 0xa3, 0x39, 0x63, 0xc2, 0x4c, 0x5e, 0xdc, 0x93, 0x23, 0x70, 0xa8, 0xf4,
	0x5a, 0x8b, 0x51, 0xa8, 0x1e, 0xa5, 0xf2, 0x5f, 0x58, 0x0d, 0x41, 0xf3, 0xf4, 0xfa, 0xbd, 0x6b,
	0x2a, 0xea, 0x61, 0xae, 0xdc, 0x6a, 0x0c, 0x37, 0x66, 0x25, 0x76, 0x13, 0xad, 0xc1, 0xa2, 0x63,
	0x39, 0x5a, 0xdf, 0x8b, 0x15, 0x77, 0xa1, 0x74, 0x61, 0xed, 0xc0, 0xf5, 0xab, 0x77, 0xc1, 0xd3,
	0xfd, 0x06, 0x14, 0x58, 0xf4, 0x0c, 0xb5, 0x01, 0x66, 0xca, 0x17, 0xd4, 0x3c, 0xdd, 0x38, 0xd1,
	0x06, 0x18, 0xd5, 0x20, 0x4b, 0xac, 0xb1, 0xdd, 0xe5, 0x4e, 0xba, 0x1e, 0x75, 0x52, 0x8b, 0x9d,
	0xaa, 0x1e, 0x95, 0x72, 0x0f, 0xd6, 0x23, 0x42, 0x3c, 0x2d, 0xa2, 0x2f, 0xfc, 0x13, 0x09, 0xd6,
	0x4e, 0xdd, 0x28, 0x0a, 0xc3, 0x89, 0xc6, 0x7e, 0x08, 0x5e, 0x3a, 0x02, 0x6f, 0x6a, 0xe6, 0xcc,
	0xbc, 0x66, 0x0e, 0xa8, 0xb4, 0x30, 0xaf, 0x4a, 0x11, 0xa0, 0x09, 0x2a, 0x61, 0x28, 0xfb, 0xba,
	0x73, 0x6d, 0x2e, 0x4e, 0x5b, 0x97, 0x36, 0xf1, 0x1d, 0x58, 0x09, 0x88, 0x49, 0xc6, 0xe2, 0x83,
	0xfe, 0x65, 0xb1, 0x04, 0xc4, 0x24, 0x60, 0x51, 0xa1, 0xec, 0xa6, 0x9b, 0xe4, 0x84, 0x7f, 0x69,
	0xc1, 0xab, 0xb0, 0x12, 0xe0, 0xe9, 0x0a, 0x56, 0xde, 0x93, 0xa0, 0xdc, 0xc2, 0x9a, 0xdd, 0x3d,
	0x0f, 0x48, 0xaa, 0x40, 0xae, 0x87, 0x27, 0x6f, 0x59, 0xb6, 0xee, 0x05, 0x37, 0x5f, 0x46, 0x53,
	0x67, 0x3a, 0x9a, 0x3a, 0x83, 0x49, 0x21, 0x33, 0x23, 0x29, 0x2c, 0x84, 0x93, 0x82, 0x72, 0x02,
	0x2b, 0x01, 0x14, 0x97, 0xc8, 0x4e, 0x09, 0x0f, 0x77, 0x07, 0x50, 0x73, 0x9a, 0x18, 0xb8, 0x5e,
	0x65, 0xc8, 0x98, 0xba, 0xcb, 0x6f, 0x41, 0xa5, 0x9f, 0xca, 0x53, 0x58, 0x0d, 0xd1, 0x5d, 0x35,
	0x7f, 0x28, 0xff, 0x82, 0x35, 0xdf, 0xb8, 0xb3, 0x9e, 0xe6, 0x65, 0x9d, 0xb6, 0x01, 0xeb, 0x11,
	0xbe, 0x9e, 0xe3, 0xce, 0x59, 0x0d, 0x61, 0x09, 0x9d, 0xec, 0x4f, 0xda, 0x9a, 0x31, 0xad, 0x21,
	0xd3, 0xaa, 0x2c, 0x05, 0xab, 0x72, 0xd0, 0x37, 0xe9, 0x19, 0xbe, 0xc9, 0x44, 0x7c, 0xf3, 0x1f,
	0x56, 0x40, 0x22, 0x92, 0x3c, 0x43, 0x3d, 0xa0, 0xc9, 0x84, 0x1e, 0x78, 0x56, 0x5a, 0x09, 0x5a,
	0x89, 0x5d, 0x51, 0x3d, 0x82, 0x04, 0x47, 0x29, 0x50, 0x6c, 0x86, 0xfb, 0x1a, 0xd1, 0x49, 0x8f,
	0xa1, 0xd4, 0x8c, 0xb4, 0x36, 0x73, 0x15, 0xae, 0xbb, 0xb0, 0x42, 0xef, 0xd5, 0xfb, 0xfd, 0x99,
	0xec, 0xff, 0xe2, 0xc6, 0x0a, 0x27, 0xbb, 0x8c, 0x04, 0x0b, 0x36, 0x9b, 0xd1, 0xe2, 0x4a, 0x02,
	0x45, 0x82, 0x97, 0x72, 0x2e, 0x2f, 0xef, 0xd5, 0x72, 0x72, 0xd5, 0x62, 0xfe, 0x0f, 0x28, 0xf0,
	0xe6, 0x87, 0xcc, 0xea, 0x15, 0x38, 0xfa, 0xf4, 0x2c, 0xf4, 0x1d, 0x90, 0xe3, 0xd0, 0x7b, 0x06,
	0xa8, 0x43, 0xc9, 0xe5, 0x4e, 0x69, 0x3b, 0x7d, 0x93, 0x38, 0x9e, 0x2d, 0x36, 0x85, 0x90, 0xe5,
	0x88, 0xd4, 0x65, 0xc2, 0x3f, 0x8f, 0x4d, 0xe2, 0x28, 0x8f, 0x78, 0x9f, 0xc3, 0x1a, 0xb5, 0x09,
	0x2d, 0x3f, 0xdc, 0x38, 0x08, 0x16, 0x02, 0xc5, 0x93, 0x7d, 0x2b, 0x7f, 0xe7, 0x9d, 0x4a, 0x90,
	0xdc, 0x43, 0x53, 0x83, 0x3c, 0x7f, 0x91, 0x5e, 0x36, 0x8e, 0x7d, 0x90, 0x39, 0xef, 0x41, 0x2a,
	0x1f, 0x4b, 0x70, 0xed, 0x89, 0x66, 0xf6, 0xb1, 0xae, 0x62, 0x32, 0xee, 0xcf, 0x6c, 0xac, 0x8e,
	0x00, 0xb0, 0x6d, 0x5b, 0x76, 0xa7, 0x6b, 0xe9, 0xdc, 0x19, 0x3b, 0x51, 0x25, 0x83, 0xcc, 0x6a,
	0x47, 0x94, 0xfc, 0xc0, 0xd2, 0xb1, 0x5a, 0xc0, 0xfc, 0x53, 0xf9, 0x03, 0x14, 0xfc, 0x7d, 0x54,
	0x82, 0xa5, 0x23, 0x55, 0x7d, 0xa6, 0x76, 0x9e, 0xb5, 0xff, 0x76, 0xa4, 0x96, 0x53, 0x68, 0x15,
	0x4a, 0xee, 0x46, 0xb3, 0xfe, 0xa2, 0x73, 0xdc, 0x68, 0x36, 0xda, 0x65, 0x49, 0x79, 0x07, 0xd6,
	0x9b, 0x75, 0x5d, 0x9f, 0x36, 0xdf, 0x17, 0x3c, 0xe1, 0x50, 0x48, 0xa5, 0x23, 0x21, 0x35, 0x4d,
	0x2d, 0x99, 0xb9, 0x52, 0xcb, 0x73, 0xb8, 0x1e, 0x15, 0xee, 0x19, 0xfb, 0x31, 0xe4, 0x6c, 0xa6,
	0x28, 0x0f, 0xff, 0x9b, 0xb3, 0xac, 0xa1, 0x72, 0x62, 0xe5, 0x5d, 0xa8, 0x34, 0xfd, 0x6c, 0xf5,
	0x2b, 0x68, 0xd4, 0x82, 0xcd, 0x18, 0xf9, 0x3f, 0x51, 0xa9, 0xaf, 0x24, 0xb8, 0xee, 0x36, 0x0f,
	0x75, 0x32, 0x19, 0x76, 0xdb, 0x1a, 0xe9, 0x05, 0x5e, 0xf8, 0x99, 0xd9, 0x0f, 0xb7, 0x81, 0x74,
	0x83, 0xf5, 0x59, 0xfc, 0xb0, 0x6f, 0x0e, 0x7b, 0xbc, 0x09, 0xa3, 0x1b, 0xc7, 0xe6, 0xb0, 0x87,
	0xfe, 0x4c, 0x9f, 0x3f, 0xe9, 0xb9, 0xf1, 0x1c, 0xd3, 0x87, 0xf9, 0xa2, 0x6a, 0xbc, 0xce, 0x90,
	0x1e, 0xfd, 0x42, 0x32, 0xe4, 0xad, 0x11, 0xb6, 0x35, 0xc7, 0xb2, 0x59, 0x11, 0x2d, 0xa8, 0xfe,
	0x1a, 0x6d, 0xc3, 0x35, 0x1b, 0x9f, 0x61, 0x1b, 0x0f, 0xbb, 0x2c, 0xcc, 0xdd, 0xc9, 0x64, 0xc9,
	0xdf, 0x6b, 0xe8, 0xca, 0x1e, 0x6c, 0x08, 0xba, 0x78, 0xf6, 0xd9, 0x80, 0x1c, 0x83, 0xe4, 0x7b,
	0x28, 0x4b, 0x97, 0x0d, 0x5d, 0xf9, 0x54, 0x82, 0x35, 0x9a, 0x1f, 0xf9, 0x0d, 0x32, 0x6d, 0x8e,
	0xc2, 0xf2, 0x24, 0x41, 0x5e, 0x58, 0x4f, 0xea, 0xde, 0x79, 0xf5, 0xbc, 0x6a, 0x23, 0xf1, 0x6f,
	0x58, 0x8f, 0x60, 0xf5, 0xd4, 0xfb, 0x2d, 0x2c, 0x52, 0xe6, 0xdc, 0xf9, 0xeb, 0xb1, 0x28, 0x54,
	0x97, 0x26, 0x71, 0x1a, 0xd8, 0xe4, 0x85, 0xb0, 0x71, 0x38, 0x7f, 0xd1, 0x25, 0x8e, 0x66, 0x3b,
	0xa1, 0x31, 0x54, 0xb3, 0x9d, 0x86, 0x4e, 0x85, 0xf4, 0xcd, 0x81, 0xe9, 0xf0, 0xd9, 0x99, 0x2d,
	0x14, 0x15, 0xe4, 0x38, 0x21, 0x9e, 0x16, 0x33, 0x6b, 0xca, 0x06, 0xe4, 0xfa, 0x1a, 0x09, 0x88,
	0xca, 0xd2, 0x65, 0x43, 0x7f, 0xb8, 0x05, 0x79, 0x5e, 0x4b, 0x50, 0x01, 0x16, 0xeb, 0xfa, 0xc0,
	0x1c, 0x96, 0x25, 0xfa, 0xb9, 0x3f, 0x9e, 0x60, 0xbb, 0x9c, 0x7e, 0x78, 0x00, 0x59, 0xf7, 0x2d,
	0x21, 0x04, 0xc5, 0xd6, 0xb3, 0x53, 0xf5, 0xe0, 0xa8, 0x73, 0x78, 0xf4, 0xa4, 0x7e, 0x7a, 0xdc,
	0x2e, 0xa7, 0x50, 0x19, 0xae, 0x79, 0x7b, 0xf5, 0xc3, 0x66, 0xe3, 0xa4, 0x2c, 0xd1, 0x6c, 0xe6,
	0xed, 0x1c, 0xd4, 0x9b, 0xcf, 0xeb, 0x8d, 0xa7, 0x27, 0xe5, 0xf4, 0xde, 0x0f, 0x25, 0xc8, 0xf3,
	0x5a, 0x80, 0x1a, 0x90, 0x75, 0x93, 0x39, 0xba, 0x15, 0x7d, 0x67, 0xa1, 0x7a, 0x2f, 0x57, 0x93,
	0x8e, 0xbd, 0x46, 0x27, 0x85, 0x0c, 0x28, 0x47, 0xcb, 0x14, 0xba, 0x17, 0x7f, 0x4b, 0x18, 0xa8,
	0xe5, 0xfb, 0x17, 0x13, 0xfa, 0x82, 0xfe, 0x07, 0xcb, 0xa1, 0x49, 0x0c, 0xfd, 0x26, 0x7a, 0x39,
	0x6e, 0x1a, 0x94, 0xef, 0x5e, 0x40, 0xe5, 0xf3, 0x7f, 0x01, 0x4b, 0x81, 0x69, 0x15, 0x29, 0xf1,
	0xd0, 0x82, 0x53, 0xb6, 0x7c, 0x67, 0x26, 0x4d, 0x10, 0x79, 0x68, 0xe0, 0x12, 0x91, 0xc7, 0x0d,
	0x8e, 0x22, 0xf2, 0xd8, 0xa9, 0xcd, 0x45, 0x1e, 0xe8, 0x93, 0x45, 0xe4, 0x62, 0xb3, 0x2d, 0x22,
	0x8f, 0x69, 0xb4, 0x5d, 0xe4, 0xa1, 0x06, 0x57, 0x44, 0x1e, 0xd7, 0x57, 0x8b, 0xc8, 0xe3, 0xbb,
	0xe4, 0x14, 0x52, 0xa1, 0xe0, 0xbb, 0x03, 0x6d, 0x25, 0x7a, 0x8a, 0xf3, 0xdd, 0x9e, 0x41, 0x11,
	0xe4, 0xe9, 0x1b, 0x4a, 0xe4, 0x19, 0x1d, 0x22, 0x45, 0x9e, 0xc2, 0xfc, 0xe7, 0xf2, 0xf4, 0x55,
	0x10, 0x79, 0x46, 0x87, 0x41, 0x91, 0xa7, 0x38, 0xda, 0x31, 0x9e, 0xfe, 0x54, 0x25, 0xf2, 0x8c,
	0x8e, 0x7d, 0x22, 0x4f, 0x61, 0x24, 0xf3, 0x1f, 0x63, 0x68, 0x1a, 0x88, 0x7d, 0x8c, 0x71, 0x93,
	0x49, 0xec, 0x63, 0x8c, 0x1d, 0x2c, 0x94, 0x14, 0x1a, 0xb0, 0x5f, 0x9d, 0x22, 0x89, 0x10, 0x3d,
	0x48, 0xe2, 0x20, 0x64, 0x64, 0xf9, 0xe1, 0x3c, 0xa4, 0xbe, 0xb8, 0x63, 0xc8, 0x79, 0x01, 0x8a,
	0xaa, 0x09, 0x91, 0xcb, 0x19, 0xdf, 0x4e, 0x3c, 0xf7, 0xb9, 0x9d, 0x02, 0x4c, 0x67, 0x0a, 0xb4,
	0x1d, 0x77, 0x21, 0x34, 0x96, 0xc8, 0xca, 0x2c, 0x92, 0xa0, 0x4d, 0xc4, 0x8e, 0x5d, 0xb4, 0x49,
	0xe2, 0x4c, 0x22, 0xda, 0x24, 0x79, 0x00, 0x08, 0x26, 0xde, 0x69, 0x43, 0x9e, 0x94, 0x78, 0x85,
	0x0e, 0x3f, 0x29, 0xf1, 0x8a, 0xbd, 0xbd, 0x92, 0x42, 0x1a, 0x14, 0xc3, 0xad, 0x28, 0x12, 0xde,
	0x77, 0x6c, 0x9f, 0x2c, 0xef, 0x5c, 0x44, 0xe6, 0x8b, 0xf8, 0x3f, 0xac, 0x08, 0xbd, 0x21, 0x12,
	0x30, 0x26, 0xb5, 0xaf, 0xf2, 0x83, 0x39, 0x28, 0x7d, 0x59, 0x3a, 0x94, 0x22, 0x5d, 0x16, 0xda,
	0x89, 0xcf, 0x2b, 0xd1, 0x96, 0x52, 0xbe, 0x77, 0x21, 0x5d, 0x30, 0x73, 0x86, 0x5a, 0x1d, 0x31,
	0x73, 0xc6, 0x75, 0x6d, 0x62, 0xe6, 0x8c, 0xed, 0x97, 0x94, 0x94, 0xfc, 0xbb, 0x0f, 0x3e, 0xaf,
	0x2c, 0x32, 0xda, 0xf7, 0xbf, 0xfb, 0xbe, 0x56, 0x95, 0x6f, 0xba, 0x7f, 0x5b, 0xd0, 0x9d, 0xda,
	0x00, 0xdb, 0xdd, 0x73, 0x6d, 0xe8, 0xd4, 0x38, 0x97, 0xfd, 0xf2, 0x97, 0xaf, 0xab, 0xd2, 0xd7,
	0xaf, 0xab, 0xd2, 0x37, 0xaf, 0xab, 0xd2, 0x87, 0xdf, 0x56, 0x53, 0x3f, 0x06, 0x00, 0x00, 0xff,
	0xff, 0x98, 0xbd, 0x26, 0xa9, 0x8c, 0x19, 0x00, 0x00,
}

func (m *GetTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Tag != nil {
		{
			size, err := m.Tag.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StoreTagBinding) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreTagBinding) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreTagBinding) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.UpdateTime != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.UpdateTime))
		i--
		dAtA[i] = 0x30
	}
	if m.CreateTime != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.CreateTime))
		i--
		dAtA[i] = 0x28
	}
	if m.Deleted != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Deleted))
		i--
		dAtA[i] = 0x20
	}
	if m.TagId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *StoreTagTypeBinding) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreTagTypeBinding) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreTagTypeBinding) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.UpdateTime != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.UpdateTime))
		i--
		dAtA[i] = 0x30
	}
	if m.CreateTime != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.CreateTime))
		i--
		dAtA[i] = 0x28
	}
	if m.IsDeleted != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.IsDeleted))
		i--
		dAtA[i] = 0x20
	}
	if m.TagTypeId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagTypeId))
		i--
		dAtA[i] = 0x18
	}
	if m.StoreId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StoreId))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetTagsByStoreIDRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagsByStoreIDRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagsByStoreIDRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.TagScene != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagScene))
		i--
		dAtA[i] = 0x10
	}
	if m.StoreId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetTagsByStoreIDResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagsByStoreIDResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagsByStoreIDResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetTagTypesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTypesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagTypesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Status != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Status))
		i--
		dAtA[i] = 0x20
	}
	if m.HasTag != nil {
		i--
		if *m.HasTag {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x18
	}
	if m.PageSize != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageSize))
		i--
		dAtA[i] = 0x10
	}
	if m.PageNum != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageNum))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetTagTypesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTypesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagTypesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Total != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Total))
		i--
		dAtA[i] = 0x10
	}
	if len(m.TagTypes) > 0 {
		for iNdEx := len(m.TagTypes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TagTypes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CreateTagTypeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagTypeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateTagTypeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x10
	}
	if m.TypeName != nil {
		i -= len(*m.TypeName)
		copy(dAtA[i:], *m.TypeName)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.TypeName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateTagTypeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagTypeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateTagTypeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *UpdateTagTypeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateTagTypeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateTagTypeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x20
	}
	if m.Status != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Status))
		i--
		dAtA[i] = 0x18
	}
	if m.TypeName != nil {
		i -= len(*m.TypeName)
		copy(dAtA[i:], *m.TypeName)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.TypeName)))
		i--
		dAtA[i] = 0x12
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *UpdateTagTypeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateTagTypeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateTagTypeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *CreateTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x10
	}
	if m.Tag != nil {
		{
			size, err := m.Tag.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *UpdateTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x10
	}
	if m.Tag != nil {
		{
			size, err := m.Tag.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UpdateTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UpdateTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeleteTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeleteTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *SearchTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SearchTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageSize))
		i--
		dAtA[i] = 0x20
	}
	if m.PageNum != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageNum))
		i--
		dAtA[i] = 0x18
	}
	if m.TagTypeId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagTypeId))
		i--
		dAtA[i] = 0x10
	}
	if m.Keyword != nil {
		i -= len(*m.Keyword)
		copy(dAtA[i:], *m.Keyword)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.Keyword)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SearchTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SearchTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SearchTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Total != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Total))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagTypeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagTypeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagTypeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Ids) > 0 {
		for iNdEx := len(m.Ids) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.Ids[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagTypeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagTypeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagTypeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.TagTypes) > 0 {
		for iNdEx := len(m.TagTypes) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.TagTypes[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *DeleteTagTypeRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTagTypeRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteTagTypeRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x10
	}
	if m.Id != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *DeleteTagTypeResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteTagTypeResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *DeleteTagTypeResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	return len(dAtA) - i, nil
}

func (m *GetStoresByTagIDRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoresByTagIDRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoresByTagIDRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageSize))
		i--
		dAtA[i] = 0x18
	}
	if m.PageNum != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageNum))
		i--
		dAtA[i] = 0x10
	}
	if m.TagId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetStoresByTagIDResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoresByTagIDResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoresByTagIDResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Total != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Total))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Stores) > 0 {
		for iNdEx := len(m.Stores) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Stores[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Ids) > 0 {
		for iNdEx := len(m.Ids) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.Ids[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetAllTagRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetAllTagRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetAllTagRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Ids) > 0 {
		for iNdEx := len(m.Ids) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.Ids[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetAllTagResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetAllTagResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetAllTagResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagsByStoreIDsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagsByStoreIDsRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagsByStoreIDsRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.TagScene != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagScene))
		i--
		dAtA[i] = 0x10
	}
	if len(m.StoreIds) > 0 {
		for iNdEx := len(m.StoreIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.StoreIds[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func (m *StoreTags) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreTags) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreTags) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x12
		}
	}
	if m.StoreId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MGetTagsByStoreIDsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MGetTagsByStoreIDsResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MGetTagsByStoreIDsResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.StoreTagsList) > 0 {
		for iNdEx := len(m.StoreTagsList) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.StoreTagsList[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetTagTypeByNameRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTypeByNameRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagTypeByNameRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Name != nil {
		i -= len(*m.Name)
		copy(dAtA[i:], *m.Name)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.Name)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *GetTagTypeByNameResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTypeByNameResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetTagTypeByNameResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.TagType != nil {
		{
			size, err := m.TagType.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FailedResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FailedResult) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FailedResult) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ErrorCode != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.ErrorCode))
		i--
		dAtA[i] = 0x10
	}
	if m.StoreId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StoreId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MAddTagBindingRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MAddTagBindingRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MAddTagBindingRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x18
	}
	if len(m.StoreIds) > 0 {
		for iNdEx := len(m.StoreIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.StoreIds[iNdEx]))
			i--
			dAtA[i] = 0x10
		}
	}
	if m.TagId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MAddTagBindingResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MAddTagBindingResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MAddTagBindingResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Results) > 0 {
		for iNdEx := len(m.Results) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Results[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *MDeleteTagBindingRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MDeleteTagBindingRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MDeleteTagBindingRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Source != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Source))
		i--
		dAtA[i] = 0x18
	}
	if len(m.StoreIds) > 0 {
		for iNdEx := len(m.StoreIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.StoreIds[iNdEx]))
			i--
			dAtA[i] = 0x10
		}
	}
	if m.TagId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *MDeleteTagBindingResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MDeleteTagBindingResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *MDeleteTagBindingResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Results) > 0 {
		for iNdEx := len(m.Results) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Results[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *CreateAsyncTaskRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateAsyncTaskRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateAsyncTaskRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.ReferenceId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.ReferenceId))
		i--
		dAtA[i] = 0x28
	}
	if m.Operator != nil {
		i -= len(*m.Operator)
		copy(dAtA[i:], *m.Operator)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.Operator)))
		i--
		dAtA[i] = 0x22
	}
	if m.TaskType != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TaskType))
		i--
		dAtA[i] = 0x18
	}
	if m.FileLink != nil {
		i -= len(*m.FileLink)
		copy(dAtA[i:], *m.FileLink)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.FileLink)))
		i--
		dAtA[i] = 0x12
	}
	if m.FileName != nil {
		i -= len(*m.FileName)
		copy(dAtA[i:], *m.FileName)
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(len(*m.FileName)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CreateAsyncTaskResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateAsyncTaskResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CreateAsyncTaskResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.TaskId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TaskId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAsyncTasksRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAsyncTasksRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAsyncTasksRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.PageSize != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageSize))
		i--
		dAtA[i] = 0x20
	}
	if m.PageNum != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.PageNum))
		i--
		dAtA[i] = 0x18
	}
	if len(m.TaskType) > 0 {
		for iNdEx := len(m.TaskType) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.TaskType[iNdEx]))
			i--
			dAtA[i] = 0x10
		}
	}
	if m.ReferenceId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.ReferenceId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetAsyncTasksResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAsyncTasksResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetAsyncTasksResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Total != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Total))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Tasks) > 0 {
		for iNdEx := len(m.Tasks) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tasks[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFoodyStoretag(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *GetStoreIDsByTagIDRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreIDsByTagIDRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoreIDsByTagIDRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Limit != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.Limit))
		i--
		dAtA[i] = 0x18
	}
	if m.StartId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.StartId))
		i--
		dAtA[i] = 0x10
	}
	if m.TagId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.TagId))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *GetStoreIDsByTagIDResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetStoreIDsByTagIDResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *GetStoreIDsByTagIDResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.LastId != nil {
		i = encodeVarintFoodyStoretag(dAtA, i, uint64(*m.LastId))
		i--
		dAtA[i] = 0x10
	}
	if len(m.StoreIds) > 0 {
		for iNdEx := len(m.StoreIds) - 1; iNdEx >= 0; iNdEx-- {
			i = encodeVarintFoodyStoretag(dAtA, i, uint64(m.StoreIds[iNdEx]))
			i--
			dAtA[i] = 0x8
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintFoodyStoretag(dAtA []byte, offset int, v uint64) int {
	offset -= sovFoodyStoretag(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *GetTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreTagBinding) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.StoreId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StoreId))
	}
	if m.TagId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagId))
	}
	if m.Deleted != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Deleted))
	}
	if m.CreateTime != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.CreateTime))
	}
	if m.UpdateTime != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.UpdateTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreTagTypeBinding) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.StoreId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StoreId))
	}
	if m.TagTypeId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagTypeId))
	}
	if m.IsDeleted != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.IsDeleted))
	}
	if m.CreateTime != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.CreateTime))
	}
	if m.UpdateTime != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.UpdateTime))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagsByStoreIDRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StoreId))
	}
	if m.TagScene != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagScene))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagsByStoreIDResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagTypesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.PageNum != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageNum))
	}
	if m.PageSize != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageSize))
	}
	if m.HasTag != nil {
		n += 2
	}
	if m.Status != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Status))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagTypesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TagTypes) > 0 {
		for _, e := range m.TagTypes {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.Total != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Total))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateTagTypeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TypeName != nil {
		l = len(*m.TypeName)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateTagTypeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateTagTypeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.TypeName != nil {
		l = len(*m.TypeName)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.Status != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Status))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateTagTypeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Tag != nil {
		l = m.Tag.Size()
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *UpdateTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SearchTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Keyword != nil {
		l = len(*m.Keyword)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.TagTypeId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagTypeId))
	}
	if m.PageNum != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageNum))
	}
	if m.PageSize != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *SearchTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.Total != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Total))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagTypeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Ids) > 0 {
		for _, e := range m.Ids {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagTypeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TagTypes) > 0 {
		for _, e := range m.TagTypes {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteTagTypeRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Id))
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DeleteTagTypeResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoresByTagIDRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagId))
	}
	if m.PageNum != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageNum))
	}
	if m.PageSize != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoresByTagIDResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Stores) > 0 {
		for _, e := range m.Stores {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.Total != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Total))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Ids) > 0 {
		for _, e := range m.Ids {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetAllTagRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Ids) > 0 {
		for _, e := range m.Ids {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetAllTagResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagsByStoreIDsRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreIds) > 0 {
		for _, e := range m.StoreIds {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.TagScene != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagScene))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreTags) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StoreId))
	}
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MGetTagsByStoreIDsResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreTagsList) > 0 {
		for _, e := range m.StoreTagsList {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagTypeByNameRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Name != nil {
		l = len(*m.Name)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetTagTypeByNameResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagType != nil {
		l = m.TagType.Size()
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FailedResult) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StoreId))
	}
	if m.ErrorCode != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.ErrorCode))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MAddTagBindingRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagId))
	}
	if len(m.StoreIds) > 0 {
		for _, e := range m.StoreIds {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MAddTagBindingResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MDeleteTagBindingRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagId))
	}
	if len(m.StoreIds) > 0 {
		for _, e := range m.StoreIds {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.Source != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Source))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MDeleteTagBindingResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Results) > 0 {
		for _, e := range m.Results {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateAsyncTaskRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FileName != nil {
		l = len(*m.FileName)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.FileLink != nil {
		l = len(*m.FileLink)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.TaskType != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TaskType))
	}
	if m.Operator != nil {
		l = len(*m.Operator)
		n += 1 + l + sovFoodyStoretag(uint64(l))
	}
	if m.ReferenceId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.ReferenceId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *CreateAsyncTaskResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TaskId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TaskId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAsyncTasksRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ReferenceId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.ReferenceId))
	}
	if len(m.TaskType) > 0 {
		for _, e := range m.TaskType {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.PageNum != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageNum))
	}
	if m.PageSize != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.PageSize))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetAsyncTasksResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tasks) > 0 {
		for _, e := range m.Tasks {
			l = e.Size()
			n += 1 + l + sovFoodyStoretag(uint64(l))
		}
	}
	if m.Total != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Total))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoreIDsByTagIDRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.TagId))
	}
	if m.StartId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.StartId))
	}
	if m.Limit != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.Limit))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *GetStoreIDsByTagIDResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.StoreIds) > 0 {
		for _, e := range m.StoreIds {
			n += 1 + sovFoodyStoretag(uint64(e))
		}
	}
	if m.LastId != nil {
		n += 1 + sovFoodyStoretag(uint64(*m.LastId))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovFoodyStoretag(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozFoodyStoretag(x uint64) (n int) {
	return sovFoodyStoretag(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GetTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &foody_base.Tag{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreTagBinding) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreTagBinding: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreTagBinding: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StoreId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Deleted", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Deleted = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CreateTime = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UpdateTime = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreTagTypeBinding) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreTagTypeBinding: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreTagTypeBinding: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StoreId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagTypeId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagTypeId = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsDeleted", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDeleted = &v
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CreateTime = &v
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.UpdateTime = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagsByStoreIDRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagsByStoreIDRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagsByStoreIDRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StoreId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagScene", wireType)
			}
			var v TagScene
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= TagScene(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagScene = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagsByStoreIDResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagsByStoreIDResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagsByStoreIDResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, &foody_base.Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTypesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagTypesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagTypesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageNum", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageNum = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageSize = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field HasTag", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.HasTag = &b
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v foody_base.TagType_Status
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= foody_base.TagType_Status(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTypesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagTypesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagTypesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagTypes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TagTypes = append(m.TagTypes, &foody_base.TagType{})
			if err := m.TagTypes[len(m.TagTypes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Total = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagTypeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateTagTypeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateTagTypeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.TypeName = &s
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagTypeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateTagTypeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateTagTypeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateTagTypeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateTagTypeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateTagTypeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TypeName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.TypeName = &s
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var v foody_base.TagType_Status
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= foody_base.TagType_Status(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Status = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateTagTypeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateTagTypeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateTagTypeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &foody_base.Tag{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Tag == nil {
				m.Tag = &foody_base.Tag{}
			}
			if err := m.Tag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UpdateTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UpdateTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SearchTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SearchTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Keyword", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Keyword = &s
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagTypeId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagTypeId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageNum", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageNum = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageSize = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SearchTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SearchTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SearchTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, &foody_base.Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Total = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagTypeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagTypeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagTypeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Ids = append(m.Ids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Ids) == 0 {
					m.Ids = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Ids = append(m.Ids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Ids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagTypeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagTypeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagTypeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagTypes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TagTypes = append(m.TagTypes, &foody_base.TagType{})
			if err := m.TagTypes[len(m.TagTypes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTagTypeRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteTagTypeRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteTagTypeRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Id = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteTagTypeResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DeleteTagTypeResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DeleteTagTypeResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoresByTagIDRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoresByTagIDRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoresByTagIDRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageNum", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageNum = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageSize = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoresByTagIDResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoresByTagIDResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoresByTagIDResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoresOriginal", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Stores = append(m.Stores, &foody_base.Store{})
			if err := m.Stores[len(m.Stores)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Total = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Ids = append(m.Ids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Ids) == 0 {
					m.Ids = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Ids = append(m.Ids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Ids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, &foody_base.Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetAllTagRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetAllTagRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetAllTagRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Ids = append(m.Ids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Ids) == 0 {
					m.Ids = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Ids = append(m.Ids, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Ids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetAllTagResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetAllTagResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetAllTagResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, &foody_base.Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagsByStoreIDsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagsByStoreIDsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagsByStoreIDsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagScene", wireType)
			}
			var v TagScene
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= TagScene(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagScene = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreTags) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreTags: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreTags: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StoreId = &v
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, &foody_base.Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MGetTagsByStoreIDsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MGetTagsByStoreIDsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MGetTagsByStoreIDsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreTagsList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.StoreTagsList = append(m.StoreTagsList, &StoreTags{})
			if err := m.StoreTagsList[len(m.StoreTagsList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTypeByNameRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagTypeByNameRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagTypeByNameRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Name = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTypeByNameResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetTagTypeByNameResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetTagTypeByNameResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TagType == nil {
				m.TagType = &foody_base.TagType{}
			}
			if err := m.TagType.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FailedResult) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FailedResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FailedResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StoreId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorCode", wireType)
			}
			var v FailedResult_ErrorCode
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= FailedResult_ErrorCode(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ErrorCode = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MAddTagBindingRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MAddTagBindingRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MAddTagBindingRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MAddTagBindingResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MAddTagBindingResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MAddTagBindingResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &FailedResult{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MDeleteTagBindingRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MDeleteTagBindingRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MDeleteTagBindingRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var v Source
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= Source(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Source = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MDeleteTagBindingResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MDeleteTagBindingResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MDeleteTagBindingResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Results", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Results = append(m.Results, &FailedResult{})
			if err := m.Results[len(m.Results)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateAsyncTaskRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateAsyncTaskRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateAsyncTaskRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.FileName = &s
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.FileLink = &s
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskType", wireType)
			}
			var v foody_base.AsyncTask_Type
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= foody_base.AsyncTask_Type(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TaskType = &v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Operator", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Operator = &s
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReferenceId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReferenceId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateAsyncTaskResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CreateAsyncTaskResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CreateAsyncTaskResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TaskId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAsyncTasksRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAsyncTasksRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAsyncTasksRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReferenceId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReferenceId = &v
		case 2:
			if wireType == 0 {
				var v foody_base.AsyncTask_Type
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= foody_base.AsyncTask_Type(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TaskType = append(m.TaskType, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				if elementCount != 0 && len(m.TaskType) == 0 {
					m.TaskType = make([]foody_base.AsyncTask_Type, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v foody_base.AsyncTask_Type
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= foody_base.AsyncTask_Type(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TaskType = append(m.TaskType, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field TaskType", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageNum", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageNum = &v
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field PageSize", wireType)
			}
			var v uint32
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint32(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.PageSize = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAsyncTasksResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetAsyncTasksResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetAsyncTasksResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tasks", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tasks = append(m.Tasks, &foody_base.AsyncTask{})
			if err := m.Tasks[len(m.Tasks)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Total = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreIDsByTagIDRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreIDsByTagIDRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreIDsByTagIDRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TagId = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.StartId = &v
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Limit = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetStoreIDsByTagIDResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GetStoreIDsByTagIDResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GetStoreIDsByTagIDResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.StoreIds = append(m.StoreIds, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFoodyStoretag
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthFoodyStoretag
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.StoreIds) == 0 {
					m.StoreIds = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFoodyStoretag
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.StoreIds = append(m.StoreIds, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreIds", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastId", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.LastId = &v
		default:
			iNdEx = preIndex
			skippy, err := skipFoodyStoretag(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFoodyStoretag
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipFoodyStoretag(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFoodyStoretag
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFoodyStoretag
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthFoodyStoretag
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupFoodyStoretag
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthFoodyStoretag
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthFoodyStoretag        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFoodyStoretag          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupFoodyStoretag = fmt.Errorf("proto: unexpected end of group")
)
