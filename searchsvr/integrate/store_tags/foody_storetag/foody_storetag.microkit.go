// Code generated by protoc-gen-microkit. DO NOT EDIT.
// source: foody_storetag.proto

/*
Package foody_storetag is a generated protocol buffer package.

It is generated from these files:
	foody_storetag.proto

It has these top-level messages:
	GetTagRequest
	GetTagResponse
	StoreTagBinding
	StoreTagTypeBinding
	GetTagsByStoreIDRequest
	GetTagsByStoreIDResponse
	GetTagTypesRequest
	GetTagTypesResponse
	CreateTagTypeRequest
	CreateTagTypeResponse
	UpdateTagTypeRequest
	UpdateTagTypeResponse
	CreateTagRequest
	CreateTagResponse
	UpdateTagRequest
	UpdateTagResponse
	DeleteTagRequest
	DeleteTagResponse
	SearchTagRequest
	SearchTagResponse
	MGetTagTypeRequest
	MGetTagTypeResponse
	DeleteTagTypeRequest
	DeleteTagTypeResponse
	GetStoresByTagIDRequest
	GetStoresByTagIDResponse
	MGetTagRequest
	MGetTagResponse
	MGetAllTagRequest
	MGetAllTagResponse
	MGetTagsByStoreIDsRequest
	StoreTags
	MGetTagsByStoreIDsResponse
	GetTagTypeByNameRequest
	GetTagTypeByNameResponse
	FailedResult
	MAddTagBindingRequest
	MAddTagBindingResponse
	MDeleteTagBindingRequest
	MDeleteTagBindingResponse
	CreateAsyncTaskRequest
	CreateAsyncTaskResponse
	GetAsyncTasksRequest
	GetAsyncTasksResponse
	GetStoreIDsByTagIDRequest
	GetStoreIDsByTagIDResponse
*/
package foody_storetag

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "git.garena.com/shopee/sp_protocol/desc/pb/spex/protobuf/service"
import _ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
import _ "git.garena.com/shopee/foody/service/pb/foody_base"

import (
	micro "github.com/micro/go-micro"
	microkit1 "git.garena.com/shopee/feed/microkit"
	client "github.com/micro/go-micro/client"
	server "github.com/micro/go-micro/server"
	context "context"
	_ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// custom serviceName
const (
	serviceName = "foody_storetag"
)

// Client API for FoodyStoretag service

type FoodyStoretagService interface {
	GetTag(ctx context.Context, in *GetTagRequest, opts ...client.CallOption) (*GetTagResponse, error)
	GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, opts ...client.CallOption) (*GetTagsByStoreIDResponse, error)
	CreateTagType(ctx context.Context, in *CreateTagTypeRequest, opts ...client.CallOption) (*CreateTagTypeResponse, error)
	GetTagTypes(ctx context.Context, in *GetTagTypesRequest, opts ...client.CallOption) (*GetTagTypesResponse, error)
	UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, opts ...client.CallOption) (*UpdateTagTypeResponse, error)
	MGetTagType(ctx context.Context, in *MGetTagTypeRequest, opts ...client.CallOption) (*MGetTagTypeResponse, error)
	DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, opts ...client.CallOption) (*DeleteTagTypeResponse, error)
	CreateTag(ctx context.Context, in *CreateTagRequest, opts ...client.CallOption) (*CreateTagResponse, error)
	UpdateTag(ctx context.Context, in *UpdateTagRequest, opts ...client.CallOption) (*UpdateTagResponse, error)
	DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...client.CallOption) (*DeleteTagResponse, error)
	SearchTag(ctx context.Context, in *SearchTagRequest, opts ...client.CallOption) (*SearchTagResponse, error)
	GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, opts ...client.CallOption) (*GetStoresByTagIDResponse, error)
	GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, opts ...client.CallOption) (*GetStoreIDsByTagIDResponse, error)
	MGetTag(ctx context.Context, in *MGetTagRequest, opts ...client.CallOption) (*MGetTagResponse, error)
	MGetAllTag(ctx context.Context, in *MGetAllTagRequest, opts ...client.CallOption) (*MGetAllTagResponse, error)
	MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, opts ...client.CallOption) (*MGetTagsByStoreIDsResponse, error)
	GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, opts ...client.CallOption) (*GetTagTypeByNameResponse, error)
	MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, opts ...client.CallOption) (*MAddTagBindingResponse, error)
	MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, opts ...client.CallOption) (*MDeleteTagBindingResponse, error)
	// async task functions
	CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, opts ...client.CallOption) (*CreateAsyncTaskResponse, error)
	GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, opts ...client.CallOption) (*GetAsyncTasksResponse, error)
}

type foodyStoretagService struct {
	c    client.Client
	name string
}

func newFoodyStoretagService(name string, c client.Client) FoodyStoretagService {
	if c == nil {
		c = client.NewClient()
	}
	if len(name) == 0 {
		name = serviceName
	}
	return &foodyStoretagService{
		c:    c,
		name: name,
	}
}

func (c *foodyStoretagService) GetTag(ctx context.Context, in *GetTagRequest, opts ...client.CallOption) (*GetTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetTag", in)
	out := new(GetTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, opts ...client.CallOption) (*GetTagsByStoreIDResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetTagsByStoreID", in)
	out := new(GetTagsByStoreIDResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) CreateTagType(ctx context.Context, in *CreateTagTypeRequest, opts ...client.CallOption) (*CreateTagTypeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.CreateTagType", in)
	out := new(CreateTagTypeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetTagTypes(ctx context.Context, in *GetTagTypesRequest, opts ...client.CallOption) (*GetTagTypesResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetTagTypes", in)
	out := new(GetTagTypesResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, opts ...client.CallOption) (*UpdateTagTypeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.UpdateTagType", in)
	out := new(UpdateTagTypeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MGetTagType(ctx context.Context, in *MGetTagTypeRequest, opts ...client.CallOption) (*MGetTagTypeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MGetTagType", in)
	out := new(MGetTagTypeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, opts ...client.CallOption) (*DeleteTagTypeResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.DeleteTagType", in)
	out := new(DeleteTagTypeResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) CreateTag(ctx context.Context, in *CreateTagRequest, opts ...client.CallOption) (*CreateTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.CreateTag", in)
	out := new(CreateTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) UpdateTag(ctx context.Context, in *UpdateTagRequest, opts ...client.CallOption) (*UpdateTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.UpdateTag", in)
	out := new(UpdateTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...client.CallOption) (*DeleteTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.DeleteTag", in)
	out := new(DeleteTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) SearchTag(ctx context.Context, in *SearchTagRequest, opts ...client.CallOption) (*SearchTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.SearchTag", in)
	out := new(SearchTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, opts ...client.CallOption) (*GetStoresByTagIDResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetStoresByTagID", in)
	out := new(GetStoresByTagIDResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, opts ...client.CallOption) (*GetStoreIDsByTagIDResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetStoreIDsByTagID", in)
	out := new(GetStoreIDsByTagIDResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MGetTag(ctx context.Context, in *MGetTagRequest, opts ...client.CallOption) (*MGetTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MGetTag", in)
	out := new(MGetTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MGetAllTag(ctx context.Context, in *MGetAllTagRequest, opts ...client.CallOption) (*MGetAllTagResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MGetAllTag", in)
	out := new(MGetAllTagResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, opts ...client.CallOption) (*MGetTagsByStoreIDsResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MGetTagsByStoreIDs", in)
	out := new(MGetTagsByStoreIDsResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, opts ...client.CallOption) (*GetTagTypeByNameResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetTagTypeByName", in)
	out := new(GetTagTypeByNameResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, opts ...client.CallOption) (*MAddTagBindingResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MAddTagBinding", in)
	out := new(MAddTagBindingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, opts ...client.CallOption) (*MDeleteTagBindingResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.MDeleteTagBinding", in)
	out := new(MDeleteTagBindingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, opts ...client.CallOption) (*CreateAsyncTaskResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.CreateAsyncTask", in)
	out := new(CreateAsyncTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyStoretagService) GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, opts ...client.CallOption) (*GetAsyncTasksResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyStoretag.GetAsyncTasks", in)
	out := new(GetAsyncTasksResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FoodyStoretag service

type FoodyStoretagHandler interface {
	GetTag(context.Context, *GetTagRequest, *GetTagResponse) error
	GetTagsByStoreID(context.Context, *GetTagsByStoreIDRequest, *GetTagsByStoreIDResponse) error
	CreateTagType(context.Context, *CreateTagTypeRequest, *CreateTagTypeResponse) error
	GetTagTypes(context.Context, *GetTagTypesRequest, *GetTagTypesResponse) error
	UpdateTagType(context.Context, *UpdateTagTypeRequest, *UpdateTagTypeResponse) error
	MGetTagType(context.Context, *MGetTagTypeRequest, *MGetTagTypeResponse) error
	DeleteTagType(context.Context, *DeleteTagTypeRequest, *DeleteTagTypeResponse) error
	CreateTag(context.Context, *CreateTagRequest, *CreateTagResponse) error
	UpdateTag(context.Context, *UpdateTagRequest, *UpdateTagResponse) error
	DeleteTag(context.Context, *DeleteTagRequest, *DeleteTagResponse) error
	SearchTag(context.Context, *SearchTagRequest, *SearchTagResponse) error
	GetStoresByTagID(context.Context, *GetStoresByTagIDRequest, *GetStoresByTagIDResponse) error
	GetStoreIDsByTagID(context.Context, *GetStoreIDsByTagIDRequest, *GetStoreIDsByTagIDResponse) error
	MGetTag(context.Context, *MGetTagRequest, *MGetTagResponse) error
	MGetAllTag(context.Context, *MGetAllTagRequest, *MGetAllTagResponse) error
	MGetTagsByStoreIDs(context.Context, *MGetTagsByStoreIDsRequest, *MGetTagsByStoreIDsResponse) error
	GetTagTypeByName(context.Context, *GetTagTypeByNameRequest, *GetTagTypeByNameResponse) error
	MAddTagBinding(context.Context, *MAddTagBindingRequest, *MAddTagBindingResponse) error
	MDeleteTagBinding(context.Context, *MDeleteTagBindingRequest, *MDeleteTagBindingResponse) error
	// async task functions
	CreateAsyncTask(context.Context, *CreateAsyncTaskRequest, *CreateAsyncTaskResponse) error
	GetAsyncTasks(context.Context, *GetAsyncTasksRequest, *GetAsyncTasksResponse) error
}

func registerFoodyStoretagHandler(s server.Server, hdlr FoodyStoretagHandler, opts ...server.HandlerOption) error {
	type foodyStoretag interface {
		GetTag(ctx context.Context, in *GetTagRequest, out *GetTagResponse) error
		GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, out *GetTagsByStoreIDResponse) error
		CreateTagType(ctx context.Context, in *CreateTagTypeRequest, out *CreateTagTypeResponse) error
		GetTagTypes(ctx context.Context, in *GetTagTypesRequest, out *GetTagTypesResponse) error
		UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, out *UpdateTagTypeResponse) error
		MGetTagType(ctx context.Context, in *MGetTagTypeRequest, out *MGetTagTypeResponse) error
		DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, out *DeleteTagTypeResponse) error
		CreateTag(ctx context.Context, in *CreateTagRequest, out *CreateTagResponse) error
		UpdateTag(ctx context.Context, in *UpdateTagRequest, out *UpdateTagResponse) error
		DeleteTag(ctx context.Context, in *DeleteTagRequest, out *DeleteTagResponse) error
		SearchTag(ctx context.Context, in *SearchTagRequest, out *SearchTagResponse) error
		GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, out *GetStoresByTagIDResponse) error
		GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, out *GetStoreIDsByTagIDResponse) error
		MGetTag(ctx context.Context, in *MGetTagRequest, out *MGetTagResponse) error
		MGetAllTag(ctx context.Context, in *MGetAllTagRequest, out *MGetAllTagResponse) error
		MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, out *MGetTagsByStoreIDsResponse) error
		GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, out *GetTagTypeByNameResponse) error
		MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, out *MAddTagBindingResponse) error
		MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, out *MDeleteTagBindingResponse) error
		CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, out *CreateAsyncTaskResponse) error
		GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, out *GetAsyncTasksResponse) error
	}
	type FoodyStoretag struct {
		foodyStoretag
	}
	h := &foodyStoretagHandler{hdlr}
	return s.Handle(s.NewHandler(&FoodyStoretag{h}, opts...))
}

type foodyStoretagHandler struct {
	FoodyStoretagHandler
}

// Default service
var (
	service micro.Service
	cli     FoodyStoretagService
)

func GetServiceName() string {
	return serviceName
}

func NewService() micro.Service {
	service = micro.NewService(microkit1.DefaultOptions...)
	return service
}

func NewClient() FoodyStoretagService {
	cli = newFoodyStoretagService(serviceName, microkit1.DefaultClient)
	return cli
}

func RegisterFoodyStoretagHandler(hdlr FoodyStoretagHandler, opts ...server.HandlerOption) error {
	return registerFoodyStoretagHandler(service.Server(), hdlr, opts...)
}

func (h *foodyStoretagHandler) GetTag(ctx context.Context, in *GetTagRequest, out *GetTagResponse) error {
	return h.FoodyStoretagHandler.GetTag(ctx, in, out)
}

func (h *foodyStoretagHandler) GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, out *GetTagsByStoreIDResponse) error {
	return h.FoodyStoretagHandler.GetTagsByStoreID(ctx, in, out)
}

func (h *foodyStoretagHandler) CreateTagType(ctx context.Context, in *CreateTagTypeRequest, out *CreateTagTypeResponse) error {
	return h.FoodyStoretagHandler.CreateTagType(ctx, in, out)
}

func (h *foodyStoretagHandler) GetTagTypes(ctx context.Context, in *GetTagTypesRequest, out *GetTagTypesResponse) error {
	return h.FoodyStoretagHandler.GetTagTypes(ctx, in, out)
}

func (h *foodyStoretagHandler) UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, out *UpdateTagTypeResponse) error {
	return h.FoodyStoretagHandler.UpdateTagType(ctx, in, out)
}

func (h *foodyStoretagHandler) MGetTagType(ctx context.Context, in *MGetTagTypeRequest, out *MGetTagTypeResponse) error {
	return h.FoodyStoretagHandler.MGetTagType(ctx, in, out)
}

func (h *foodyStoretagHandler) DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, out *DeleteTagTypeResponse) error {
	return h.FoodyStoretagHandler.DeleteTagType(ctx, in, out)
}

func (h *foodyStoretagHandler) CreateTag(ctx context.Context, in *CreateTagRequest, out *CreateTagResponse) error {
	return h.FoodyStoretagHandler.CreateTag(ctx, in, out)
}

func (h *foodyStoretagHandler) UpdateTag(ctx context.Context, in *UpdateTagRequest, out *UpdateTagResponse) error {
	return h.FoodyStoretagHandler.UpdateTag(ctx, in, out)
}

func (h *foodyStoretagHandler) DeleteTag(ctx context.Context, in *DeleteTagRequest, out *DeleteTagResponse) error {
	return h.FoodyStoretagHandler.DeleteTag(ctx, in, out)
}

func (h *foodyStoretagHandler) SearchTag(ctx context.Context, in *SearchTagRequest, out *SearchTagResponse) error {
	return h.FoodyStoretagHandler.SearchTag(ctx, in, out)
}

func (h *foodyStoretagHandler) GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, out *GetStoresByTagIDResponse) error {
	return h.FoodyStoretagHandler.GetStoresByTagID(ctx, in, out)
}

func (h *foodyStoretagHandler) GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, out *GetStoreIDsByTagIDResponse) error {
	return h.FoodyStoretagHandler.GetStoreIDsByTagID(ctx, in, out)
}

func (h *foodyStoretagHandler) MGetTag(ctx context.Context, in *MGetTagRequest, out *MGetTagResponse) error {
	return h.FoodyStoretagHandler.MGetTag(ctx, in, out)
}

func (h *foodyStoretagHandler) MGetAllTag(ctx context.Context, in *MGetAllTagRequest, out *MGetAllTagResponse) error {
	return h.FoodyStoretagHandler.MGetAllTag(ctx, in, out)
}

func (h *foodyStoretagHandler) MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, out *MGetTagsByStoreIDsResponse) error {
	return h.FoodyStoretagHandler.MGetTagsByStoreIDs(ctx, in, out)
}

func (h *foodyStoretagHandler) GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, out *GetTagTypeByNameResponse) error {
	return h.FoodyStoretagHandler.GetTagTypeByName(ctx, in, out)
}

func (h *foodyStoretagHandler) MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, out *MAddTagBindingResponse) error {
	return h.FoodyStoretagHandler.MAddTagBinding(ctx, in, out)
}

func (h *foodyStoretagHandler) MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, out *MDeleteTagBindingResponse) error {
	return h.FoodyStoretagHandler.MDeleteTagBinding(ctx, in, out)
}

func (h *foodyStoretagHandler) CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, out *CreateAsyncTaskResponse) error {
	return h.FoodyStoretagHandler.CreateAsyncTask(ctx, in, out)
}

func (h *foodyStoretagHandler) GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, out *GetAsyncTasksResponse) error {
	return h.FoodyStoretagHandler.GetAsyncTasks(ctx, in, out)
}
