package foody_storetag

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/client"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
	microclient "github.com/micro/go-micro/client"
)

type callOptsCtxKey struct{}

func WithCallOpts(ctx context.Context, opts ...microclient.CallOption) context.Context {
	return context.WithValue(ctx, callOptsCtxKey{}, opts)
}

type Method = string

type SwitchFunc func(ctx context.Context, serviceName string, method Method) bool

var greySwitch SwitchFunc = func(context.Context, string, Method) bool { return true }

func SetGreySwitch(s SwitchFunc) {
	greySwitch = s
}

type Client interface {
	GetTag(ctx context.Context, in *GetTagRequest, opts ...callopt.Option) (*GetTagResponse, error)

	GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, opts ...callopt.Option) (*GetTagsByStoreIDResponse, error)

	CreateTagType(ctx context.Context, in *CreateTagTypeRequest, opts ...callopt.Option) (*CreateTagTypeResponse, error)

	GetTagTypes(ctx context.Context, in *GetTagTypesRequest, opts ...callopt.Option) (*GetTagTypesResponse, error)

	UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, opts ...callopt.Option) (*UpdateTagTypeResponse, error)

	MGetTagType(ctx context.Context, in *MGetTagTypeRequest, opts ...callopt.Option) (*MGetTagTypeResponse, error)

	DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, opts ...callopt.Option) (*DeleteTagTypeResponse, error)

	CreateTag(ctx context.Context, in *CreateTagRequest, opts ...callopt.Option) (*CreateTagResponse, error)

	UpdateTag(ctx context.Context, in *UpdateTagRequest, opts ...callopt.Option) (*UpdateTagResponse, error)

	DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...callopt.Option) (*DeleteTagResponse, error)

	SearchTag(ctx context.Context, in *SearchTagRequest, opts ...callopt.Option) (*SearchTagResponse, error)

	GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, opts ...callopt.Option) (*GetStoresByTagIDResponse, error)

	GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, opts ...callopt.Option) (*GetStoreIDsByTagIDResponse, error)

	MGetTag(ctx context.Context, in *MGetTagRequest, opts ...callopt.Option) (*MGetTagResponse, error)

	MGetAllTag(ctx context.Context, in *MGetAllTagRequest, opts ...callopt.Option) (*MGetAllTagResponse, error)

	MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, opts ...callopt.Option) (*MGetTagsByStoreIDsResponse, error)

	GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, opts ...callopt.Option) (*GetTagTypeByNameResponse, error)

	MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, opts ...callopt.Option) (*MAddTagBindingResponse, error)

	MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, opts ...callopt.Option) (*MDeleteTagBindingResponse, error)

	// async task functions
	CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, opts ...callopt.Option) (*CreateAsyncTaskResponse, error)

	GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, opts ...callopt.Option) (*GetAsyncTasksResponse, error)
}

func NewSpexClient(opts ...client.Option) (Client, error) {
	namespace := "shopeefood.merchant.storetag"

	spexClient, err := client.NewClient(append([]client.Option{
		client.WithInterceptor(ClientVersionReport(version, spexServiceName)),
	}, opts...)...)
	if err != nil {
		return nil, err
	}

	cli := &foodyStoretagClient{
		c:              spexClient,
		microkitClient: NewClient(),
		namespace:      namespace,
	}

	return cli, nil
}

type foodyStoretagClient struct {
	c              client.Client
	microkitClient FoodyStoretagService
	namespace      string
}

func (client *foodyStoretagClient) GetTag(ctx context.Context, in *GetTagRequest, opts ...callopt.Option) (*GetTagResponse, error) {
	if greySwitch(ctx, client.namespace, "get_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetTag(ctx, in, callOpts...)
	}

	out := new(GetTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetTagsByStoreID(ctx context.Context, in *GetTagsByStoreIDRequest, opts ...callopt.Option) (*GetTagsByStoreIDResponse, error) {
	if greySwitch(ctx, client.namespace, "get_tags_by_store_id") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetTagsByStoreID(ctx, in, callOpts...)
	}

	out := new(GetTagsByStoreIDResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_tags_by_store_id", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) CreateTagType(ctx context.Context, in *CreateTagTypeRequest, opts ...callopt.Option) (*CreateTagTypeResponse, error) {
	if greySwitch(ctx, client.namespace, "create_tag_type") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.CreateTagType(ctx, in, callOpts...)
	}

	out := new(CreateTagTypeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_tag_type", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetTagTypes(ctx context.Context, in *GetTagTypesRequest, opts ...callopt.Option) (*GetTagTypesResponse, error) {
	if greySwitch(ctx, client.namespace, "get_tag_types") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetTagTypes(ctx, in, callOpts...)
	}

	out := new(GetTagTypesResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_tag_types", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) UpdateTagType(ctx context.Context, in *UpdateTagTypeRequest, opts ...callopt.Option) (*UpdateTagTypeResponse, error) {
	if greySwitch(ctx, client.namespace, "update_tag_type") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.UpdateTagType(ctx, in, callOpts...)
	}

	out := new(UpdateTagTypeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_tag_type", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MGetTagType(ctx context.Context, in *MGetTagTypeRequest, opts ...callopt.Option) (*MGetTagTypeResponse, error) {
	if greySwitch(ctx, client.namespace, "m_get_tag_type") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MGetTagType(ctx, in, callOpts...)
	}

	out := new(MGetTagTypeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_tag_type", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) DeleteTagType(ctx context.Context, in *DeleteTagTypeRequest, opts ...callopt.Option) (*DeleteTagTypeResponse, error) {
	if greySwitch(ctx, client.namespace, "delete_tag_type") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.DeleteTagType(ctx, in, callOpts...)
	}

	out := new(DeleteTagTypeResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"delete_tag_type", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) CreateTag(ctx context.Context, in *CreateTagRequest, opts ...callopt.Option) (*CreateTagResponse, error) {
	if greySwitch(ctx, client.namespace, "create_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.CreateTag(ctx, in, callOpts...)
	}

	out := new(CreateTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) UpdateTag(ctx context.Context, in *UpdateTagRequest, opts ...callopt.Option) (*UpdateTagResponse, error) {
	if greySwitch(ctx, client.namespace, "update_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.UpdateTag(ctx, in, callOpts...)
	}

	out := new(UpdateTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) DeleteTag(ctx context.Context, in *DeleteTagRequest, opts ...callopt.Option) (*DeleteTagResponse, error) {
	if greySwitch(ctx, client.namespace, "delete_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.DeleteTag(ctx, in, callOpts...)
	}

	out := new(DeleteTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"delete_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) SearchTag(ctx context.Context, in *SearchTagRequest, opts ...callopt.Option) (*SearchTagResponse, error) {
	if greySwitch(ctx, client.namespace, "search_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.SearchTag(ctx, in, callOpts...)
	}

	out := new(SearchTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetStoresByTagID(ctx context.Context, in *GetStoresByTagIDRequest, opts ...callopt.Option) (*GetStoresByTagIDResponse, error) {
	if greySwitch(ctx, client.namespace, "get_stores_by_tag_id") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetStoresByTagID(ctx, in, callOpts...)
	}

	out := new(GetStoresByTagIDResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_stores_by_tag_id", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetStoreIDsByTagID(ctx context.Context, in *GetStoreIDsByTagIDRequest, opts ...callopt.Option) (*GetStoreIDsByTagIDResponse, error) {
	if greySwitch(ctx, client.namespace, "get_store_i_ds_by_tag_id") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetStoreIDsByTagID(ctx, in, callOpts...)
	}

	out := new(GetStoreIDsByTagIDResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_store_i_ds_by_tag_id", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MGetTag(ctx context.Context, in *MGetTagRequest, opts ...callopt.Option) (*MGetTagResponse, error) {
	if greySwitch(ctx, client.namespace, "m_get_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MGetTag(ctx, in, callOpts...)
	}

	out := new(MGetTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MGetAllTag(ctx context.Context, in *MGetAllTagRequest, opts ...callopt.Option) (*MGetAllTagResponse, error) {
	if greySwitch(ctx, client.namespace, "m_get_all_tag") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MGetAllTag(ctx, in, callOpts...)
	}

	out := new(MGetAllTagResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_all_tag", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MGetTagsByStoreIDs(ctx context.Context, in *MGetTagsByStoreIDsRequest, opts ...callopt.Option) (*MGetTagsByStoreIDsResponse, error) {
	if greySwitch(ctx, client.namespace, "m_get_tags_by_store_i_ds") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MGetTagsByStoreIDs(ctx, in, callOpts...)
	}

	out := new(MGetTagsByStoreIDsResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_tags_by_store_i_ds", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetTagTypeByName(ctx context.Context, in *GetTagTypeByNameRequest, opts ...callopt.Option) (*GetTagTypeByNameResponse, error) {
	if greySwitch(ctx, client.namespace, "get_tag_type_by_name") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetTagTypeByName(ctx, in, callOpts...)
	}

	out := new(GetTagTypeByNameResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_tag_type_by_name", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MAddTagBinding(ctx context.Context, in *MAddTagBindingRequest, opts ...callopt.Option) (*MAddTagBindingResponse, error) {
	if greySwitch(ctx, client.namespace, "m_add_tag_binding") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MAddTagBinding(ctx, in, callOpts...)
	}

	out := new(MAddTagBindingResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_add_tag_binding", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) MDeleteTagBinding(ctx context.Context, in *MDeleteTagBindingRequest, opts ...callopt.Option) (*MDeleteTagBindingResponse, error) {
	if greySwitch(ctx, client.namespace, "m_delete_tag_binding") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.MDeleteTagBinding(ctx, in, callOpts...)
	}

	out := new(MDeleteTagBindingResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_delete_tag_binding", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) CreateAsyncTask(ctx context.Context, in *CreateAsyncTaskRequest, opts ...callopt.Option) (*CreateAsyncTaskResponse, error) {
	if greySwitch(ctx, client.namespace, "create_async_task") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.CreateAsyncTask(ctx, in, callOpts...)
	}

	out := new(CreateAsyncTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_async_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyStoretagClient) GetAsyncTasks(ctx context.Context, in *GetAsyncTasksRequest, opts ...callopt.Option) (*GetAsyncTasksResponse, error) {
	if greySwitch(ctx, client.namespace, "get_async_tasks") { // grey related
		callOpts, _ := ctx.Value(callOptsCtxKey{}).([]microclient.CallOption)
		return client.microkitClient.GetAsyncTasks(ctx, in, callOpts...)
	}

	out := new(GetAsyncTasksResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_async_tasks", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}
