package abtest

import (
	"context"
	"strconv"
	"strings"
	"time"

	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	jsoniter "github.com/json-iterator/go"

	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api"
	abConfig "git.garena.com/shopee/experiment-platform/abtest-core/v2/api/config"
	"git.garena.com/shopee/experiment-platform/abtest-core/v2/api/param"
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

var (
	cID               = "ID"
	KEY_TYPE_LayerID  = "LayerID"
	KEY_TYPE_LayerKey = "LayerKey"
	KEY_TYPE_ExpName  = "ExpName"
)

func BuildABTestService(serviceName string) {
	cID = strings.ToUpper(env.GetCID())

	// 这里使用Apollo配置中心对ab service进行控制
	project := apollo.SearchApolloCfg.NewAbConfig.Project
	if len(project) < 1 {
		project = []string{"ShopeeFood"}
	}
	sceneKey := apollo.SearchApolloCfg.NewAbConfig.SceneKey
	if len(sceneKey) < 1 {
		sceneKey = []string{}
	}
	conf := &abConfig.Config{
		ServiceName: serviceName,
		AbtestConfig: abConfig.AbtestConfig{
			ProjectKeys: project,
			SceneKeys:   sceneKey,
			Interval:    1,
		},
	}
	api.Init(api.Config(conf))
	return
}

func GetNewAbKeys(ctx context.Context, scene string, userId uint64, deviceId string, ud map[string]interface{}) (layers map[string]string, groupNames map[string]string, groupIdList []uint64) {
	startTime := time.Now()
	errCode := "0"
	defer func() {
		metric_reporter.ReportClientRequestError(1, "NewABTest", errCode)
	}()
	builder := param.NewExpParamBuilder().SceneKey(scene).Region(cID).UserID(int64(userId)).DeviceID(deviceId).UserParam(ud)
	timeOut := apollo.SearchApolloCfg.NewAbConfig.TimeOut
	if timeOut == 0 {
		timeOut = 1000
	}
	c, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Millisecond)
	defer cancel()
	hit, err := api.GetAbClient().GetExpGroups(c, builder.Build())
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "NewABTest"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "abtest")
	if err != nil {
		errCode = "failed"
		builderJson, _ := jsoniter.MarshalToString(builder)
		logkit.FromContext(ctx).Error("Get New ab flag err", logkit.Err(err), logkit.String("scene", scene), logkit.Uint64("userId", userId), logkit.String("deviceId", deviceId), logkit.Any("ud", ud), logkit.Any("builder", builderJson))
		layers = make(map[string]string, 0)
		groupNames = make(map[string]string, 0)
		groupIdList = make([]uint64, 0)
		return layers, groupNames, groupIdList
	}
	layers = make(map[string]string, len(hit.Hit))
	groupNames = make(map[string]string, len(hit.Hit))
	groupIdList = make([]uint64, 0, len(hit.Hit))

	keyType := apollo.SearchApolloCfg.NewAbConfig.KeyType
	if len(keyType) == 0 {
		keyType = "LayerID"
	}

	key := ""
	for _, each := range hit.Hit {
		switch keyType {
		case KEY_TYPE_LayerID:
			key = strconv.FormatInt(each.LayerID, 10)
		case KEY_TYPE_LayerKey:
			key = each.LayerKey
		default:
			key = each.ExpName
		}
		layerIdStr := strconv.FormatInt(each.LayerID, 10)
		groupIdStr := strconv.FormatInt(each.GroupID, 10)
		layers[layerIdStr] = groupIdStr
		// 命中abtest 黑名单，跳过
		if apollo.SearchApolloCfg.ABTestBlockMap.IsHitABTestBlockList(key) == true {
			continue
		}
		groupNames[key] = each.GroupName
		groupIdList = append(groupIdList, uint64(each.GroupID))
	}
	return layers, groupNames, groupIdList
}
