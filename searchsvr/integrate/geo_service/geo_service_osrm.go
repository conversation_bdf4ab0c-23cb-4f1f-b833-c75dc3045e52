package geo_service

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"io"
	"math"
	"net/http"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/geo_routing"
)

func (s *GeoService) GetRoutingDistancesOnceOsrm(ctx context.Context, origins []*geo_routing.Location, destination *geo_routing.Location) ([]uint64, error) {
	errorCode := "failed"
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("GeoService.GetRoutingDistancesOnceOsrm catch a panic", logkit.Any("err", e))
			errorCode = "failed"
		}
		reporter.ReportClientRequestError(1, "geo.routing.matrix_osrm", errorCode)
	}()
	originsParam := make([]map[string]float64, 0, len(origins))
	for _, i := range origins {
		originsParam = append(originsParam, map[string]float64{
			"longitude": i.GetLon(),
			"latitude":  i.GetLat(),
		})
	}
	destinationsParam := map[string]float64{
		"longitude": destination.GetLon(),
		"latitude":  destination.GetLat(),
	}

	distances, err := getMinimumDistanceFromManySources(ctx, originsParam, destinationsParam)
	if err == nil {
		errorCode = "0"
	}
	return distances, nil

}

func getMinimumDistanceFromManySources(ctx context.Context, restaurants []map[string]float64, destination map[string]float64) ([]uint64, error) {
	var bikeDistances, carDistances []uint64
	var err1, err2 error
	var wg sync.WaitGroup

	wg.Add(2)
	go func(restaurant []map[string]float64) {
		defer wg.Done()

		bikeDistances, err1 = getDistanceFromManySources(ctx, restaurant, destination, "bike")
	}(restaurants)
	go func(restaurant []map[string]float64) {
		defer wg.Done()

		carDistances, err2 = getDistanceFromManySources(ctx, restaurant, destination, "car")
	}(restaurants)
	wg.Wait()

	if err1 == nil && err2 != nil {
		logkit.FromContext(ctx).WithError(err2).Error("getMinimumDistanceFromManySources car failed, return bike")
		return bikeDistances, nil
	}
	if err1 != nil && err2 == nil {
		logkit.FromContext(ctx).WithError(err1).Error("getMinimumDistanceFromManySources bike failed, return car")
		return carDistances, nil
	}
	if err1 != nil && err2 != nil {
		logkit.FromContext(ctx).WithError(err1).Error("getMinimumDistanceFromManySources bike and car both failed")
		return nil, err1
	}

	if bikeDistances == nil && carDistances == nil {
		distances := make([]uint64, len(restaurants))
		for i, restaurant := range restaurants {
			distance := getDistance(restaurant, destination)
			distances[i] = distance
		}
		return distances, nil
	} else if bikeDistances != nil && carDistances != nil {
		return getMinDistance(ctx, bikeDistances, carDistances), nil
	} else {
		if len(bikeDistances) > 0 {
			return bikeDistances, nil
		} else {
			return carDistances, nil
		}
	}
}

func getMinDistance(ctx context.Context, bikeDistances, carDistances []uint64) []uint64 {
	if len(bikeDistances) != len(carDistances) {
		logkit.FromContext(ctx).Error("NOWGEO getMinDistance failed bike_size != car_size")
		return nil
	}
	minDistances := make([]uint64, 0, len(bikeDistances))
	for i := 0; i < len(bikeDistances); i++ {
		if bikeDistances[i] < carDistances[i] {
			minDistances = append(minDistances, bikeDistances[i])
		} else {
			minDistances = append(minDistances, carDistances[i])
		}
	}
	return minDistances
}

func getDistance(source map[string]float64, destination map[string]float64) uint64 {
	latitude1 := source["latitude"]
	longitude1 := source["longitude"]
	latitude2 := destination["latitude"]
	longitude2 := destination["longitude"]

	if latitude1 == 0 || longitude1 == 0 || longitude2 == 0 || latitude2 == 0 {
		return 0
	}

	// 将经纬度转换为弧度
	latitude1 = float64(latitude1) * math.Pi / 180
	longitude1 = float64(longitude1) * math.Pi / 180
	latitude2 = float64(latitude2) * math.Pi / 180
	longitude2 = float64(longitude2) * math.Pi / 180

	// 计算delta_latitude和delta_longitude
	deltaLatitude := (latitude2 - latitude1) / 2
	deltaLongitude := (longitude2 - longitude1) / 2

	// 计算a
	a := math.Pow(math.Sin(deltaLatitude), 2) + math.Cos(latitude1)*math.Cos(latitude2)*math.Pow(math.Sin(deltaLongitude), 2)

	// 计算c
	c := 2.0 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	// 计算地球半径（单位：千米）
	kEarthRadius := 6376.5

	// 转换为米并返回结果（保留两位小数并乘以1000）
	return uint64(round(c*kEarthRadius, 2) * 1000)
}

// 使用内置的math库实现四舍五入函数，保留两位小数
func round(f float64, places int) float64 {
	var round float64
	// 将浮点数转换为字符串，然后进行四舍五入处理，最后再转回浮点数
	round, _ = strconv.ParseFloat(strconv.FormatFloat(f, 'f', places, 64), 64)
	return round
}

type Point struct {
	Longitude float64
	Latitude  float64
}

func getDistanceFromManySources(ctx context.Context, sources []map[string]float64, destination map[string]float64, driveMode string) ([]uint64, error) {
	var url string
	if driveMode == "bike" {
		url = apollo.SearchApolloCfg.VNOsrmDistanceBikeUrl
	} else {
		url = apollo.SearchApolloCfg.VNOsrmDistanceCarUrl
	}

	coordinates := make([][]float64, len(sources))
	for i, point := range sources {
		longitude, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", point["longitude"]), 64)
		latitude, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", point["latitude"]), 64)
		coordinates[i] = []float64{longitude, latitude}
	}
	reqLongitude, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", destination["longitude"]), 64)
	reqLatitude, _ := strconv.ParseFloat(fmt.Sprintf("%.4f", destination["latitude"]), 64)
	coordinates = append(coordinates, []float64{reqLongitude, reqLatitude})

	sourceIndexes := make([]int, 0, len(sources))
	for i := 0; i < len(sources); i++ {
		sourceIndexes = append(sourceIndexes, i)
	}

	requestData := struct {
		Coordinates  [][]float64 `json:"coordinates"`
		Annotations  []string    `json:"annotations"`
		Sources      []int       `json:"sources"`
		Destinations []int       `json:"destinations"`
	}{
		Coordinates:  coordinates,
		Annotations:  []string{"distance"},
		Sources:      sourceIndexes,
		Destinations: []int{len(sources)},
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("getDistanceFromManySources Error encoding request data", logkit.Any("requestData", requestData))
		return nil, err
	}

	// 创建一个 HTTP 客户端，并设置超时时间
	timeoutMs := 300
	if apollo.SearchApolloCfg.VNOsrmDistanceTimeoutMs > 0 {
		timeoutMs = apollo.SearchApolloCfg.VNOsrmDistanceTimeoutMs
	}
	client := &http.Client{
		Timeout: time.Millisecond * time.Duration(timeoutMs),
	}
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("getDistanceFromManySources createRequest failed", logkit.Any("url", url), logkit.Any("req", requestData))
		return nil, err
	}
	// 设置请求头（可选）
	req.Header.Set("Content-Type", "application/json") // 设置请求内容的 MIME 类型为 JSON

	// 发送请求并获取响应
	resp, err := client.Do(req)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("getDistanceFromManySources doRequest failed", logkit.Any("url", url), logkit.Any("req", requestData))
		return nil, err
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("处理 IO close 异常")
		}
	}(resp.Body)

	response, err := io.ReadAll(resp.Body)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("getDistanceFromManySources readBody failed", logkit.Any("url", url), logkit.Any("req", requestData))
		return nil, err
	}

	// 解析JSON响应
	var data struct {
		Distances [][]float64 `json:"distances"`
	}
	responseString := string(response)
	err = json.Unmarshal(response, &data)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("getDistanceFromManySources tranJson failed", logkit.Any("url", url), logkit.Any("req", requestData), logkit.String("resp", responseString))
		return nil, err
	}

	distances := make([]uint64, len(data.Distances), len(data.Distances))
	for i := 0; i < len(data.Distances); i++ {
		distances[i] = uint64(data.Distances[i][0])
	}

	if len(distances) != len(sources) {
		return nil, errors.New(fmt.Sprintf("distance list size not match, %d != %d", len(distances), len(sources)))
	}

	return distances, nil
}
