package geo_service

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/spex"
	"github.com/golang/protobuf/proto"

	"git.garena.com/shopee/platform/golang_splib/client"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/geo_routing"
)

const (
	cmd                            = "geo.routing.matrix"
	_GeoMatrixRspValueTypeDistance = "Distance"
	//_GeoMatrixRspValueTypeDuration = "Duration"
)

type GeoService struct {
	spexCli client.Client
	geoCfg  config2.GeoConfig
}

var GeoServiceClient *GeoService

func NewGeoService(geoCfg config2.GeoConfig) *GeoService {
	if env.GetCID() != cid.VN {
		logkit.Info("skip NewGeoService for no vn region")
		return &GeoService{}
	}
	if len(geoCfg.ClientKey) == 0 {
		logkit.Info("skip NewGeoService for without config", zap.Any("geoConfig", geoCfg))
		return &GeoService{}
	}
	geoClient, _ := client.NewClient(spex.DefaultClientOptions...)
	return &GeoService{
		geoCfg:  geoCfg,
		spexCli: geoClient,
	}
}

func (s *GeoService) GetRoutingDistances(ctx context.Context, traceInfo *traceinfo.TraceInfo, origins []*geo_routing.Location, destination *geo_routing.Location) ([]uint64, error) {
	startTime := time.Now()
	originsLen := len(origins)
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("GeoService.GetRoutingDistances catch a panic", logkit.Any("err", e))
		}
		metric_reporter2.Report3thClientEPS(float64(originsLen), "GeoRoutingToMapEps")
	}()
	if s == nil {
		logkit.FromContext(ctx).Error("GEO Service init fail, is nil to skip")
		return []uint64{}, errors.New(fmt.Sprintf("geo service is nil"))
	}
	if originsLen == 0 {
		logkit.FromContext(ctx).Error("origins is empty, skip")
		return []uint64{}, nil
	}

	batchSize := 50
	if apollo.SearchApolloCfg.GEORoutingMatrixBatchSize != 0 {
		batchSize = apollo.SearchApolloCfg.GEORoutingMatrixBatchSize
	}
	total := originsLen / batchSize
	if originsLen%batchSize > 0 {
		total += 1
	}
	distanceList := make([][]uint64, total)
	wg2 := sync.WaitGroup{}
	wg2.Add(total)
	for i := 0; i < total; i++ {
		var originsTemp []*geo_routing.Location
		if i+1 < total {
			originsTemp = origins[i*batchSize : (i+1)*batchSize]
		} else {
			originsTemp = origins[i*batchSize:]
		}
		goroutine.WithGo(ctx, "GetRoutingDistancesOnce", func(params ...interface{}) {
			defer wg2.Done()
			param := params[0].([]interface{})
			index := param[0].(int)
			tempOrigins := param[1].([]*geo_routing.Location)

			var err error
			var ds []uint64
			if traceInfo.ABTestGroup.IsHitVNOsrmDistance() {
				ds, err = s.GetRoutingDistancesOnceOsrm(ctx, tempOrigins, destination)
				if traceInfo.IsDebug {
					logkit.FromContext(ctx).Info("GetRoutingDistances osrm result", logkit.Any("tempOrigins", tempOrigins), logkit.Any("destination", destination), logkit.Any("rsp", ds))
				}
			} else {
				ds, err = s.GetRoutingDistancesOnce(ctx, traceInfo, tempOrigins, destination)
			}

			if err != nil || len(ds) != len(originsTemp) {
				logkit.FromContext(ctx).WithError(err).Error("GetRoutingDistances fail", zap.String("cost", time.Since(startTime).String()))
				return
			}
			distanceList[index] = ds
		}, i, originsTemp)
	}
	wg2.Wait()
	distances := make([]uint64, 0, 0)
	for _, ds := range distanceList {
		if len(ds) == 0 {
			continue
		}
		distances = append(distances, ds...)
	}
	return distances, nil
}

func (s *GeoService) GetRoutingDistancesOnce(ctx context.Context, traceInfo *traceinfo.TraceInfo, origins []*geo_routing.Location, destination *geo_routing.Location) ([]uint64, error) {
	startTime := time.Now()
	errorCode := "failed"
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("GeoService.GetRoutingDistancesOnce catch a panic", logkit.Any("err", e))
			errorCode = "failed"
		}
		reporter.ReportClientRequestError(1, "geo.routing.matrix", errorCode)
	}()
	source := s.geoCfg.Source
	// toll, highway, ferry
	avoid := []string{"toll", "highway"}
	destinations := []*geo_routing.Location{
		{
			Lon: proto.Float64(destination.GetLon()),
			Lat: proto.Float64(destination.GetLat()),
		},
	}
	req := &geo_routing.MatrixRequest{
		Origins:      origins,              // 最多50个门店经纬度
		Destinations: destinations,         // 1个用户经纬度
		Source:       proto.String(source), // "geo"
		Mode:         proto.String("bike"),
		User:         proto.String(s.geoCfg.User), // "shopeefood"
		ClientKey:    proto.String(s.geoCfg.ClientKey),
		Project:      proto.String(s.geoCfg.Project), // "o2o_intelligence_food_search_rcmd"
		Region:       proto.String(s.geoCfg.Region),  // "vn"
		Avoid:        avoid,
	}

	requestTimeoutMs := int64(100)
	if apollo.SearchApolloCfg.ClientTimeOutConfig.GEORoutingMatrixTimeOut > 0 {
		requestTimeoutMs = int64(apollo.SearchApolloCfg.ClientTimeOutConfig.GEORoutingMatrixTimeOut)
	}
	reqCtx, cancel := context.WithTimeout(ctx, time.Duration(requestTimeoutMs)*time.Millisecond)
	defer cancel()
	rsp := new(geo_routing.MatrixResponse)
	st := time.Now()
	err := s.spexCli.Invoke(reqCtx, cmd, req, rsp)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "geo.routing.matrix"), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeRpc, "", "", "geo.routing.matrix")
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("failed to get geo.routing.matrix", logkit.String("cost", time.Since(startTime).String()))
		return []uint64{}, err
	}
	// todo
	distances, err := getGeoResultValue(rsp, _GeoMatrixRspValueTypeDistance)
	if err != nil {
		logkit.FromContext(ctx).Error("GeoService.GetGeoDetails can't find distance value", logkit.Err(err))
		return []uint64{}, err
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "GeoService.GetGeoDetails succeed",
		logkit.String("source", s.geoCfg.Source),
		logkit.Any("origins", origins),
		logkit.Any("destinations", destinations),
		logkit.Any("distances", distances),
	)
	errorCode = "0"
	return distances, nil

}

func codeToErr(code uint32) error {
	if code == 0 {
		return nil
	}

	if msg, ok := geo_routing.Constant_ErrorCode_name[int32(code)]; ok {
		return fmt.Errorf(msg)
	}
	return spex.CodeToErr(code)
}

func getGeoResultValue(matrixRsp *geo_routing.MatrixResponse, valueType string) ([]uint64, error) {
	if matrixRsp == nil {
		return []uint64{}, errors.New("geo response is nil")
	}

	var matrixRows []*geo_routing.MatrixRow
	if valueType == _GeoMatrixRspValueTypeDistance {
		matrixRows = matrixRsp.GetDistances()
	} else {
		matrixRows = matrixRsp.GetDurations()
	}
	if len(matrixRows) == 0 {
		// 理论可能，保持逻辑完备
		return []uint64{}, errors.New(fmt.Sprintf("geo response %s size is 0", valueType))
	}
	geoValues := make([]uint64, 0, 0)
	for _, matrixRow := range matrixRows {
		elements := matrixRow.GetElements()
		if len(elements) == 0 {
			// 理论可能，保持逻辑完备
			continue
		}
		// 实际只有在origins， destinations 多对多的场景才会出现，单个返回值为nil的情况，实际会以错误码的形式在前面报错了
		// 这里备注一下，后面有多对多的场景可以注意一下
		if elements[0].Value == nil {
			return []uint64{}, errors.New(fmt.Sprintf("geo response %s[0].Elements value is nil", valueType))
		}
		geoValues = append(geoValues, elements[0].GetValue())
	}

	return geoValues, nil
}
