package dish_searcher

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/common_client"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_dishsearcher"
)

const (
	DishSearcherServiceName = "o2oalgo_dishsearcher"
)

type DishSearcherService struct {
	DishSearcherSpexClient o2oalgo_dishsearcher.Client
}

func NewDishSearcherService(config *common_client.MicroClientConfig) *DishSearcherService {
	spexClient, err := o2oalgo_dishsearcher.NewSpexClient(spexcommon.DefaultClientOptions...)
	if err != nil {
		logkit.Fatal("failed to init spex client")
	}
	return &DishSearcherService{
		DishSearcherSpexClient: spexClient,
	}
}
func (s *DishSearcherService) DishSearcherSearch(ctx context.Context, req *o2oalgo_dishsearcher.SearcherRequest) (*o2oalgo_dishsearcher.SearcherResponse, error) {
	return s.DishSearcherSpexClient.Search(ctx, req)
}

func (s *DishSearcherService) DishSearcherSearchByRecallField(ctx context.Context, req *o2oalgo_dishsearcher.SearchRequest) (*o2oalgo_dishsearcher.SearchResponse, error) {
	return s.DishSearcherSpexClient.SearchItem(ctx, req)
}
