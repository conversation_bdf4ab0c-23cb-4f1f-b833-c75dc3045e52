package relevance

import (
	"context"
	"time"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	relevance "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_relevance_server"
	spex "git.garena.com/shopee/o2o-intelligence/common/common-lib/spex_client_gray"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"
)

func GetRelevanceInfos(ctx context.Context, traceInfo *traceinfo.TraceInfo, relevanceInfo *relevance.RelevanceRequest) (*relevance.RelevanceResponse, error) {
	relevance.SetGreySwitch(spex.DefaultSwitchFunc)
	relevanceClient, err := relevance.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.FromContext(ctx).Error("failed to get relevance client")
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.RelevanceTimeOut)*time.Millisecond)
	defer cancel()
	startTime := time.Now()
	resp, err := relevanceClient.GetRelevanceInfo(ctx, relevanceInfo)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "get_relevance"), zap.String("cost", time.Since(startTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "get_relevance")
	if traceInfo.IsDebug {
		logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:relevance server", zap.Any("req", relevanceInfo), zap.Any("res", resp))
	}
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.INTERNAL_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("GetRelevanceInfos request server error", logkit.String("cost", time.Since(startTime).String()))
		return nil, err
	}
	return resp, nil
}

func PackRelevanceRequests(ctx context.Context, traceInfo *traceinfo.TraceInfo, storeIDs model2.StoreInfos) *relevance.RelevanceRequest {
	defer func() {
		if e := recover(); e != nil {
			logkit.FromContext(ctx).Error("PackRelevanceRequestsV2 panic", logkit.Any("traceInfo", traceInfo), logkit.Any("QPResp", traceInfo.QPResponse), logkit.Any("err", e))
			metric_reporter.ReportCounter(metric_reporter.ReportServicePanicMetricName, 1, reporter2.Label{
				Key: "method",
				Val: "PackRelevanceRequestsV2",
			})
		}
	}()
	newQPInfo := relevance.GetQueryProcessingKeywordResp{}
	if traceInfo.QPResponse != nil {
		qpJson, err := proto.Marshal(traceInfo.QPResponse)
		if err == nil {
			err = proto.Unmarshal(qpJson, &newQPInfo)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error(
					"SearchService.GetRelevanceInfos parse QP Infos error",
					zap.String("info", string(qpJson)))
			}
		}
	}

	nStores := len(storeIDs)
	storeItems := make([]*relevance.StoreItem, 0, nStores)
	storeItemInfos := make([]relevance.StoreItem, nStores)
	for index, store := range storeIDs {
		var dishName1, dishName2 string
		var dishId1, dishId2 uint64
		dishName1 = ""
		dishName2 = ""
		dishId1 = 0
		dishId2 = 0
		storeTags := make([]string, 0, len(store.DishInfos))
		for i, dish := range store.DishInfos {
			if i == 0 {
				dishName1 = dish.DishName
				dishId1 = dish.DishId
			}
			if i == 1 {
				dishName2 = dish.DishName
				dishId2 = dish.DishId
			}

			for _, intent := range dish.StoreIntents {
				storeTags = append(storeTags, intent)
			}
			//for _, intent := range dish.DishIntents {
			//	storeTags = append(storeTags, intent)
			//}
		}
		for _, intent := range store.StoreIntents {
			storeTags = append(storeTags, intent)
		}
		itemQuerySet := make([]string, 0)
		itemQueryMap := make(map[string]bool)
		for _, itemQuery := range store.RecallQueries {
			if _, ok := itemQueryMap[itemQuery]; !ok {
				itemQueryMap[itemQuery] = true
				itemQuerySet = append(itemQuerySet, itemQuery)
			}
		}
		if len(itemQuerySet) > 1 {
			//如果该item同时被query和改写query召回，则默认为query去匹配rel得分
			if _, ok := itemQueryMap[traceInfo.QueryKeyword]; ok {
				itemQuerySet = []string{traceInfo.QueryKeyword}
			}
			//特例：如果该item同时新分类和改写query召回，则默认为改写query去匹配rel得分
			for _, recallType := range store.RecallTypes {
				if recallType == foodalgo_search.RecallType_StoreCateExpand.String() {
					for itemQuery := range itemQueryMap {
						if itemQuery != traceInfo.QueryKeyword {
							itemQuerySet = []string{itemQuery}
							break
						}
					}
				}
			}
		}
		storeItemInfos[index].StoreId = proto.Uint64(store.StoreId)
		storeItemInfos[index].StoreName = proto.String(store.StoreName)
		storeItemInfos[index].DishName1 = proto.String(dishName1)
		storeItemInfos[index].DishName2 = proto.String(dishName2)
		storeItemInfos[index].Tags = storeTags
		storeItemInfos[index].DishId1 = proto.Uint64(dishId1)
		storeItemInfos[index].DishId2 = proto.Uint64(dishId2)
		storeItemInfos[index].Query = itemQuerySet
		if traceInfo.IsDebug {
			logger.MyDebug(ctx, traceInfo.IsDebug, "predict debug log:relevance server item", logkit.Any("item feature", storeItemInfos[index]))
		}
		storeItems = append(storeItems, &storeItemInfos[index])
	}
	isHitStoreIntention := false
	isHitDishIntention := false
	if len(traceInfo.QPResult.QueryStoreIntention) != 0 {
		isHitStoreIntention = true
	}
	if len(traceInfo.QPResult.QueryDishIntention) != 0 {
		isHitDishIntention = true
	}
	return &relevance.RelevanceRequest{
		QpInfo:              &newQPInfo,
		Query:               proto.String(traceInfo.QueryKeyword),
		StoreItem:           storeItems,
		IsHitStoreIntention: proto.Bool(isHitStoreIntention),
		IsHitDishIntention:  proto.Bool(isHitDishIntention),
		StoreIntentProb:     proto.Float32(float32(traceInfo.QPResult.QueryStoreIntentionProb)),
		DishIntentProb:      proto.Float32(float32(traceInfo.QPResult.QueryDishIntentionProb)),
		ReqId:               proto.String(traceInfo.TraceRequest.PublishId),
		Uid:                 proto.Uint64(traceInfo.UserId),
		IsDebug:             proto.Bool(traceInfo.IsDebug),
		PredictConfig: &relevance.PredictConfig{
			ReleFusionFunc:     proto.Int32(int32(traceInfo.PredictConfig.ReleFusionFunc)),
			RelevanceModelName: proto.String(traceInfo.PredictConfig.RelevanceModuleName),
			SemanticModelName:  proto.String(traceInfo.PredictConfig.SemanticModuleName),
			RelStrategy:        proto.Int32(int32(traceInfo.PredictConfig.NewStrategy)),
		},
	}
}

func RemoveSameString(strs []string) []string {
	ret := make([]string, 0)
	tmpMap := make(map[string]int)
	for i, str := range strs {
		_, ok := tmpMap[str]
		if !ok {
			ret = append(ret, str)
			tmpMap[str] = i
		}
	}
	return ret
}
