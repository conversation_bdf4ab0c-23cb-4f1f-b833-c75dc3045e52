package es

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
)

const (
	dishAggregation = "dish_aggregation"
)

type ESCount struct {
	MustQuerys    []elastic.Query
	MustNotQuerys []elastic.Query
	ShouldQuerys  []elastic.Query
	Filters       []elastic.Query
}

type ES interface {
	Search(ctx context.Context, esSearch *ESSearch) ([]uint64, []interface{}, uint64, error)
}

var ESClient1 *elastic.Client
var ESClient2 *elastic.Client

var ESClientPool = make(map[string]*ESBase, 4)
var ESClientPoolLock = sync.RWMutex{}

type ESBase struct {
	aliasIndex string
	client     *elastic.Client
}

func NewES(aliasIndex string, client *elastic.Client) *ESBase {
	es := &ESBase{
		aliasIndex: aliasIndex,
		client:     client,
	}
	es.Init()
	return es
}

func (es *ESBase) Init() {
	ctx := context.Background()
	exists, err := es.client.IndexExists(es.aliasIndex).Do(ctx)
	if err != nil {
		logkit.With(logkit.Err(err)).Error("[ES] init", logkit.String("index alias", es.aliasIndex))
		if strings.Contains(es.aliasIndex, "history") || strings.Contains(es.aliasIndex, "order") {
			return
		}
	}

	if !exists {
		if strings.Contains(es.aliasIndex, "history") || strings.Contains(es.aliasIndex, "order") {
			return
		}

		reporter.ReportCounter(reporter.ReportServicePanicMetricName, 1, reporter2.Label{
			Key: "method",
			Val: "index alias " + es.aliasIndex + " not exist",
		})
		fmt.Println("[ES] index alias not exist, panic!:", es.aliasIndex)
		logkit.Fatal("[ES] index alias not exist, panic!", logkit.String("index alias", es.aliasIndex))
		panic(fmt.Sprintf("[ES] index alias %s not exist, panic!", es.aliasIndex))
	}
}

func (es *ESBase) Search(ctx context.Context, esSearch *ESSearch, uid uint64) (uint64, SearchHits, error, int64) {
	if esSearch == nil {
		//logkit.FromContext(ctx).Error("ESBase.Search esSearch is nil")
		return 0, nil, errors.Wrap(errno.ErrParamsInvalid, zap.String("esSearch is nil", "")), 0
	}

	// test, liveish 环境默认打开
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		apollo.SearchApolloCfg.PrintESQueryDSL = true
	}

	var searchService *elastic.SearchService
	query := esSearch.GetQuery(ctx)

	if esSearch.TermsAggregation == nil {
		searchService = es.client.Search().Explain(esSearch.IsNeedEsExplain).Index(es.aliasIndex).Query(query).SortBy(esSearch.Sorters...).FilterPath(esSearch.FilterPath...).Size(int(esSearch.Size))
	} else {
		// dish 召回使用aggregation 桶排序时，结果集在bucket里的hits, 不需要外层的hits的，所以from = 0, size = 0
		searchService = es.client.Search().Explain(esSearch.IsNeedEsExplain).Index(es.aliasIndex).Query(query).
			Aggregation(dishAggregation, esSearch.TermsAggregation).SortBy(esSearch.Sorters...).FilterPath(esSearch.FilterPath...).From(0).Size(0)
	}
	if esSearch.SearchAfter != nil {
		searchService = searchService.SearchAfter(esSearch.SearchAfter...)
	} else if esSearch.From >= 0 {
		searchService = searchService.From(int(esSearch.From))
	}

	if len(esSearch.SourceInclude) != 0 {
		fsc := elastic.NewFetchSourceContext(true).Include(esSearch.SourceInclude...)
		searchService.FetchSourceContext(fsc)
	} else {
		searchService.FetchSource(false)
	}

	if esSearch.ExactTotal != nil {
		searchService.TrackTotalHits(*esSearch.ExactTotal)
	}
	searchService.Preference(strconv.FormatUint(uid, 10))
	if len(esSearch.Routing) > 0 {
		searchService.Routing(esSearch.Routing)
	}
	// 打印查询语句
	if apollo.SearchApolloCfg.PrintESQueryDSL {
		printQuery(ctx, es.aliasIndex, query, esSearch)
	}
	st := time.Now()
	res, err := searchService.Do(ctx)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", es.aliasIndex), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeMiddleware, "", "", es.aliasIndex)
	if err != nil {
		reporter.ReportClientRequestError(1, es.aliasIndex, "failed")
		printQueryRes(esSearch, res, err)
		logkit.FromContext(ctx).WithError(err).Error("ESBase Search searchService.Do", logkit.String("cost", time.Since(st).String()))
		return 0, nil, errors.Wrap(errno.ErrESUnknown, logkit.Any("error", err.Error())), 0
	}
	reporter.ReportClientRequestError(1, es.aliasIndex, "0")

	// 无 aggregation 查询返回结果
	if esSearch.TermsAggregation == nil {
		if res.Hits == nil {
			printQueryRes(esSearch, res, nil)
			return 0, nil, nil, res.TookInMillis
		}
		printQueryRes(esSearch, res, nil)
		return uint64(res.TotalHits()), res.Hits.Hits, nil, res.TookInMillis
	}
	// dish 召回时会使用aggregation 桶排序,需要单独解析 bucket 中的hits
	var aggs struct {
		Buckets []struct {
			StoreID   string `json:"key"`
			TopDishes struct {
				Hits elastic.SearchHits
			} `json:"top_dishes"` // top_dishes 这个需要与SQL 拼接时的报错一致。 见 searchsvr/model/search_request.go
		} `json:"buckets"`
	}
	rawMsgPtr := res.Aggregations[dishAggregation]
	if len(rawMsgPtr) == 0 {
		return 0, nil, nil, res.TookInMillis
	}
	if err = json.Unmarshal(rawMsgPtr, &aggs); err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("ESBase.Search aggregation unmarshal parser", zap.Any("esSearch", esSearch), zap.Error(err), logkit.String("rawMsgPtr", string(rawMsgPtr)))
		printQueryRes(esSearch, res, err)
		return 0, nil, nil, res.TookInMillis
	}
	hits := make([]*elastic.SearchHit, 0)
	for _, bucket := range aggs.Buckets {
		for _, hit := range bucket.TopDishes.Hits.Hits {
			hits = append(hits, hit)
		}
	}
	printQueryRes(esSearch, res, nil)
	return uint64(len(hits)), hits, nil, res.TookInMillis
}

func (es *ESBase) SearchCategoryAgg(ctx context.Context, esSearch *ESSearch, uid uint64) (uint64, []uint32, error) {
	if esSearch == nil {
		//logkit.FromContext(ctx).Error("ESBase.Search esSearch is nil")
		return 0, nil, errors.Wrap(errno.ErrParamsInvalid, zap.String("esSearch is nil", ""))
	}
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.SearchStoreDishL3CategoryTimeOut
	if timeout <= 0 {
		timeout = 1000
	}

	// 如果有配置化定制了 timeout，已定制化的为主
	if esSearch.RecallTimeoutMs > 0 {
		timeout = esSearch.RecallTimeoutMs
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
	defer cancel()
	// test, liveish 环境默认打开
	if env.GetEnv() == "liveish" || env.GetEnv() == "test" {
		apollo.SearchApolloCfg.PrintESQueryDSL = true
	}

	var searchService *elastic.SearchService
	query := esSearch.GetQuery(ctx)

	searchService = es.client.Search().Index(es.aliasIndex).Query(query).
		Aggregation("group_by_l3_category", esSearch.TermsAggregation).
		SortBy(esSearch.Sorters...).FilterPath(esSearch.FilterPath...).
		From(0).Size(0)

	if esSearch.ExactTotal != nil {
		searchService.TrackTotalHits(*esSearch.ExactTotal)
	}
	searchService.Preference(strconv.FormatUint(uid, 10))
	// 打印查询语句
	if apollo.SearchApolloCfg.PrintESQueryDSL {
		printQuery(ctx, es.aliasIndex, query, esSearch)
	}
	st := time.Now()
	res, err := searchService.Do(ctx)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", es.aliasIndex), zap.String("cost", time.Since(st).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(st), metric_reporter2.SearchReportTypeMiddleware, "", "", es.aliasIndex)
	if err != nil {
		reporter.ReportClientRequestError(1, es.aliasIndex, "failed")
		printQueryRes(esSearch, res, err)
		logkit.FromContext(ctx).WithError(err).Error("ESBase SearchCategoryAgg", logkit.String("cost", time.Since(st).String()))
		return 0, nil, errors.Wrap(errno.ErrESUnknown, logkit.Any("error", err.Error()))
	}
	reporter.ReportClientRequestError(1, es.aliasIndex, "0")

	//{
	//    "doc_count_error_upper_bound": 0,
	//    "sum_other_doc_count": 190,
	//    "buckets":
	//    [
	//        {
	//            "key": "0",
	//            "doc_count": 138457
	//        },
	//        {
	//            "key": "6",
	//            "doc_count": 2
	//        }
	//    ]
	//}
	var aggs struct {
		Buckets []struct {
			L3Category string `json:"key"`
			Count      int    `json:"doc_count"`
		} `json:"buckets"`
	}
	rawMsgPtr := res.Aggregations["group_by_l3_category"]
	if err := json.Unmarshal(rawMsgPtr, &aggs); err != nil {
		logkit.FromContext(ctx).With(logkit.Err(err)).Error("ESBase.Search aggregation unmarshal parser", zap.Any("esSearch", esSearch), zap.Error(err))
		printQueryRes(esSearch, res, err)
		return 0, nil, nil
	}
	categories := make([]uint32, 0)
	for _, bucket := range aggs.Buckets {
		id, err1 := strconv.ParseInt(bucket.L3Category, 10, 64)
		if err1 != nil {
			logkit.FromContext(ctx).WithError(err).Error("parse category failed", zap.Any("bucket", bucket))
			continue
		}
		categories = append(categories, uint32(id))
	}
	printQueryRes(esSearch, res, nil)
	return uint64(len(categories)), categories, nil
}

func printQuery(ctx context.Context, indexName string, query elastic.Query, esSearch *ESSearch) {
	var querySql string
	src, err := query.Source()
	if err != nil {
		logkit.FromContext(ctx).Error("es query source failed", zap.Error(err))
		return
	}
	data, err := json.Marshal(src)
	if err != nil {
		logkit.FromContext(ctx).Error("es query source marshal failed", zap.Error(err))
		return
	}

	sortArr := make([]interface{}, 0)
	for _, sorter := range esSearch.Sorters {
		sortSource, err := sorter.Source()
		if err != nil {
			logkit.FromContext(ctx).Error("es sort source failed", zap.Error(err))
			return
		}
		sortArr = append(sortArr, sortSource)
	}
	sorterData, err := json.Marshal(sortArr)
	if err != nil {
		logkit.FromContext(ctx).Error("es query source marshal failed", zap.Error(err))
		return
	}
	if len(esSearch.SourceInclude) == 0 {
		querySql = fmt.Sprintf("{\"query\":%s,\"sort\":%s,\"from\":%d, \"size\":%d,\"_source\":false}", string(data), string(sorterData), esSearch.From, esSearch.Size)
	} else {
		s, _ := json.Marshal(esSearch.SourceInclude)
		ss := string(s)
		querySql = fmt.Sprintf("{\"query\":%s,\"sort\":%s, \"from\":%d, \"size\":%d, \"_source\":{\"includes\":%s}}", string(data), string(sorterData), esSearch.From, esSearch.Size, ss)
	}
	if esSearch.TermsAggregation != nil {
		aggSrc, err := esSearch.TermsAggregation.Source()
		if err != nil {
			logkit.FromContext(ctx).Error("es aggregation source failed", zap.Error(err))
			return
		}
		aggData, err := json.Marshal(aggSrc)
		if err != nil {
			logkit.FromContext(ctx).Error("es aggregation source marshal failed", zap.Error(err))
			return
		}
		querySql = fmt.Sprintf("{\"query\":%s,\"size\": 0,\"from\": 0,\"aggs\": {\"store_id_agg\":%s},\"sort\":%s}", string(data), string(aggData), string(sorterData))
	}

	s := strings.Builder{}
	if len(esSearch.RecallTypeName) > 0 {
		s.WriteString(esSearch.RecallTypeName)
	} else if len(esSearch.RecallType) > 0 {
		s.WriteString(esSearch.RecallType)
	}
	s.WriteString("\t")
	s.WriteString(indexName)
	s.WriteString("\t")
	s.WriteString(querySql)
	s.WriteString("\n")
	fmt.Println(s.String())
}

func printQueryRes(search *ESSearch, res *elastic.SearchResult, err error) {
	if apollo.SearchApolloCfg.PrintESQueryDSL {
		s := strings.Builder{}
		s.WriteString(search.RecallTypeName)
		s.WriteString("\t")

		if err != nil {
			s.WriteString(err.Error())
		} else {
			// 菜品 size
			if res.Aggregations != nil {
				val, ok := res.Aggregations[dishAggregation]
				if ok {
					var val2 = make(map[string]interface{})
					err = json.Unmarshal(val, &val2)
					if err == nil {
						val3, ok3 := val2["buckets"]
						if ok3 {
							val4, ok4 := val3.([]interface{})
							if ok4 {
								s.WriteString(strconv.Itoa(len(val4)))
							} else {
								s.WriteString("val4_0")
							}
						} else {
							s.WriteString("val3_0")
						}
					} else {
						s.WriteString("val2_0")
					}
				}
			} else {
				if res == nil || res.Hits == nil || res.Hits.Hits == nil {
					s.WriteString("0")
				} else {
					s.WriteString(strconv.Itoa(len(res.Hits.Hits)))
				}
			}
		}
		fmt.Println(s.String())
	}
}
