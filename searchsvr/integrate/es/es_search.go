package es

import (
	"context"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"math"
	"strconv"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"
	"go.uber.org/zap"

	"github.com/olivere/elastic/v7"
)

type ESSearchOption func(*ESSearch)

type EsSearchType int32

const (
	ESSearchType_Unknown                   = 0
	ESSearchType_PureBoolQuery             = 1
	ESSearchType_PureScriptScoreQuery      = 2
	ESSearchType_PureFunctionScoreQuery    = 3
	ESSearchType_BoolAndScriptScoreQuery   = 4
	ESSearchType_BoolAndFunctionScoreQuery = 5
)

type ESSearch struct {
	Query           string // 标记该路召回所用的query 词，可能是原始query或者改写query
	RecallTypeName  string // 表示来自哪个 recallType，方便调试, 可以自己随便写
	RecallType      string // 统一定义的召回类型Map str
	RecallId        string
	RecallTimeoutMs int          // 从召回配置化里取的
	ESSearchType    EsSearchType // 配置计算而来
	IsNeedEsExplain bool         // 是否要es 解释

	MinimumShouldMatch    int    // 默认 1 个
	MinimumShouldMatchStr string // 默认 1 个
	MustQueries           []elastic.Query
	MustNotQueries        []elastic.Query
	ShouldQueries         []elastic.Query
	Filters               []elastic.Query
	SearchAfter           []interface{}
	Sorters               []elastic.Sorter
	FilterPath            []string
	Size                  uint64
	From                  int32
	Forward               bool
	ExactTotal            *bool
	Timeout               string
	SourceInclude         []string
	FunctionQuery         *elastic.FunctionScoreQuery
	ScriptScoreQuery      *elastic.ScriptScoreQuery
	TermsAggregation      *elastic.TermsAggregation
	Routing               string // 如果打开配置，es索引全部操作都要走相同的路由

	RecallConfig *apollo.StoreRecallConfig // 用来做配置化反向查询
}

func newDefaultESSearch() *ESSearch {
	return &ESSearch{
		Size:    50,
		From:    -1,
		Forward: true,
		Timeout: "5s",
	}
}

func WithMustQueries(q []elastic.Query) ESSearchOption {
	return func(es *ESSearch) {
		es.MustQueries = q
	}
}

func WithMustNotQueries(q []elastic.Query) ESSearchOption {
	return func(es *ESSearch) {
		es.MustNotQueries = q
	}
}

func WithShouldQueries(q []elastic.Query) ESSearchOption {
	return func(es *ESSearch) {
		es.ShouldQueries = q
	}
}

func WithFilters(q []elastic.Query) ESSearchOption {
	return func(es *ESSearch) {
		es.Filters = q
	}
}

func WithSearchAfter(a []interface{}) ESSearchOption {
	return func(es *ESSearch) {
		es.SearchAfter = a
	}
}

func WithSorters(s []elastic.Sorter) ESSearchOption {
	return func(es *ESSearch) {
		es.Sorters = s
	}
}

func WithFilterPath(p []string) ESSearchOption {
	return func(es *ESSearch) {
		es.FilterPath = p
	}
}

func WithSize(s uint64) ESSearchOption {
	return func(es *ESSearch) {
		es.Size = s
	}
}

func WithFrom(f int32) ESSearchOption {
	return func(es *ESSearch) {
		es.From = f
	}
}

func WithForward(f bool) ESSearchOption {
	return func(es *ESSearch) {
		es.Forward = f
	}
}

func WithExactTotal(e *bool) ESSearchOption {
	return func(es *ESSearch) {
		es.ExactTotal = e
	}
}

func WithTimeout(t string) ESSearchOption {
	return func(es *ESSearch) {
		es.Timeout = t
	}
}

func WithSourceInclude(includes ...string) ESSearchOption {
	return func(es *ESSearch) {
		es.SourceInclude = includes
	}
}

func WithRecallType(recallTypeName string) ESSearchOption {
	return func(es *ESSearch) {
		es.RecallTypeName = recallTypeName
	}
}

func WithRouting(routing string) ESSearchOption {
	return func(es *ESSearch) {
		es.Routing = routing
	}
}

func WithRecallId(recallIdName string) ESSearchOption {
	return func(es *ESSearch) {
		es.RecallId = recallIdName
	}
}

func NewESSearch(opts ...ESSearchOption) *ESSearch {
	es := newDefaultESSearch()
	for _, v := range opts {
		v(es)
	}
	return es
}

type SearchHits []*elastic.SearchHit
type GetResult *elastic.GetResult

func (s SearchHits) Uint64IDs() []uint64 {
	ids := make([]uint64, 0, len(s))
	for _, hit := range s {
		if id, err := strconv.ParseInt(hit.Id, 10, 64); err == nil {
			ids = append(ids, uint64(id))
		}
	}
	return ids
}

func WithFunctionScoreQueries(q *elastic.FunctionScoreQuery) ESSearchOption {
	return func(es *ESSearch) {
		es.FunctionQuery = q
	}
}

func WithTermsAggregation(a *elastic.TermsAggregation) ESSearchOption {
	return func(es *ESSearch) {
		es.TermsAggregation = a
	}
}

func (esSearch *ESSearch) DslString(ctx context.Context) string {
	if esSearch == nil {
		return ""
	}

	query := esSearch.GetQuery(ctx)

	var querySql string
	src, err := query.Source()
	if err != nil {
		return ""
	}
	data, err := json.Marshal(src)
	if err != nil {
		return ""
	}

	sortArr := make([]interface{}, 0)
	for _, sorter := range esSearch.Sorters {
		sortSource, err := sorter.Source()
		if err != nil {
			return ""
		}
		sortArr = append(sortArr, sortSource)
	}
	sorterData, err := json.Marshal(sortArr)
	if err != nil {
		return ""
	}

	if len(esSearch.SourceInclude) == 0 {
		querySql = fmt.Sprintf(
			"{\"query\":%s,\"sort\":%s,\"from\":%d,\"size\":%d,\"_source\":false}",
			string(data), string(sorterData), esSearch.From, esSearch.Size,
		)
	} else {
		s, _ := json.Marshal(esSearch.SourceInclude)
		ss := string(s)
		querySql = fmt.Sprintf(
			"{\"query\":%s,\"sort\":%s,\"from\":%d,\"size\":%d,\"_source\":{\"includes\":%s}}",
			string(data), string(sorterData), esSearch.From, esSearch.Size, ss,
		)
	}

	if esSearch.TermsAggregation != nil {
		aggSrc, err := esSearch.TermsAggregation.Source()
		if err != nil {
			return ""
		}
		aggData, err := json.Marshal(aggSrc)
		if err != nil {
			return ""
		}
		querySql = fmt.Sprintf(
			"{\"query\":%s,\"size\":0,\"from\":0,\"aggs\":{\"store_id_agg\":%s},\"sort\":%s}",
			string(data), string(aggData), string(sorterData),
		)
	}

	return querySql
}

func (esSearch *ESSearch) getBoolQuery() *elastic.BoolQuery {
	boolQ := elastic.NewBoolQuery()

	// 有 boolQuery 的情况
	boolQ.Must(esSearch.MustQueries...)
	boolQ.MustNot(esSearch.MustNotQueries...)
	boolQ.Should(esSearch.ShouldQueries...)
	boolQ.Filter(esSearch.Filters...)

	if len(esSearch.MustQueries) == 0 && len(esSearch.MustNotQueries) == 0 && len(esSearch.ShouldQueries) > 0 {
		boolQ.MinimumShouldMatch("1")
	}
	// 支持 int 的 MinimumShouldMatch
	if esSearch.MinimumShouldMatch > 0 && len(esSearch.ShouldQueries) > 0 {
		boolQ.MinimumNumberShouldMatch(int(math.Min(float64(esSearch.MinimumShouldMatch), float64(len(esSearch.ShouldQueries)))))
	}
	// 支持 str 的MinimumShouldMatch，str的优先级更高
	if len(esSearch.MinimumShouldMatchStr) > 0 && len(esSearch.ShouldQueries) > 0 {
		boolQ.MinimumShouldMatch(esSearch.MinimumShouldMatchStr)
	}
	return boolQ
}

func (esSearch *ESSearch) judgeEsSearchType() EsSearchType {
	if len(esSearch.MustQueries) == 0 && len(esSearch.MustNotQueries) == 0 && len(esSearch.ShouldQueries) == 0 && len(esSearch.Filters) == 0 {
		// 只有一个 script_score 的情况
		if esSearch.ScriptScoreQuery != nil {
			return ESSearchType_PureScriptScoreQuery
		} else if esSearch.FunctionQuery != nil {
			// 只有一个 function_score 的情况
			return ESSearchType_PureFunctionScoreQuery
		}
	} else {
		if esSearch.ScriptScoreQuery != nil {
			return ESSearchType_BoolAndScriptScoreQuery
		} else if esSearch.FunctionQuery != nil {
			// 只有一个 function_score 的情况
			return ESSearchType_BoolAndFunctionScoreQuery
		} else {
			return ESSearchType_PureBoolQuery
		}
	}
	return ESSearchType_PureBoolQuery
}

func (esSearch *ESSearch) GetQuery(ctx context.Context) elastic.Query {
	var query elastic.Query
	boolQ := elastic.NewBoolQuery()

	if esSearch.ESSearchType == ESSearchType_Unknown {
		esSearch.ESSearchType = esSearch.judgeEsSearchType()
	}

	if esSearch.ESSearchType == ESSearchType_PureBoolQuery {
		boolQ = esSearch.getBoolQuery()
		query = boolQ
	} else if esSearch.ESSearchType == ESSearchType_PureScriptScoreQuery {
		query = esSearch.ScriptScoreQuery
	} else if esSearch.ESSearchType == ESSearchType_PureFunctionScoreQuery {
		query = esSearch.FunctionQuery
	} else if esSearch.ESSearchType == ESSearchType_BoolAndFunctionScoreQuery {
		if esSearch.FunctionQuery != nil {
			boolQ = esSearch.getBoolQuery()
			query = esSearch.FunctionQuery.Query(boolQ)
		} else {
			logkit.FromContext(ctx).Error("ESSearch.GetQuery type=BoolAndFunctionScoreQuery but fsq is nil, fallback boolQuery", zap.Any("es search", esSearch))
			boolQ = esSearch.getBoolQuery()
			query = boolQ
		}
	} else if esSearch.ESSearchType == ESSearchType_BoolAndScriptScoreQuery {
		if esSearch.ScriptScoreQuery != nil {
			boolQ = esSearch.getBoolQuery()
			query = esSearch.ScriptScoreQuery.Query(boolQ)
		} else {
			logkit.FromContext(ctx).Error("ESSearch.GetQuery type=BoolAndScriptScoreQuery but ssq is nil, fallback boolQuery", zap.Any("es search", esSearch))
			boolQ = esSearch.getBoolQuery()
			query = boolQ
		}
	} else {
		boolQ = esSearch.getBoolQuery()
		query = boolQ
	}
	return query
}
