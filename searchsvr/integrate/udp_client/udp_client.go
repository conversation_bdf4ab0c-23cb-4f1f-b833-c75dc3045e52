package udp_client

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/account_udp_group"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"github.com/gogo/protobuf/proto"
)

type UdpClient struct {
	client account_udp_group.Client
}

var udpClient *UdpClient

func GetUdpClient() *UdpClient {
	return udpClient
}

func InitUdpClient() {
	c, err := account_udp_group.NewClient(spexcommon.GetDefaultClientOptions()...)
	if err != nil {
		logkit.Fatal(fmt.Sprintf("NewUdpGroupClient client init fail %v", err))
	}
	udpClient = &UdpClient{
		client: c,
	}
}

func (b *UdpClient) CheckUdGroups(ctx context.Context, userId uint64, groupNames []string) ([]bool, error) {
	var errCode = "0"
	startTime := time.Now()
	defer func() {
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "check_udp_group")
		metric_reporter.ReportClientRequestError(1, "check_udp_group", errCode)
	}()
	uid := strconv.Itoa(int(userId))
	udType := account_udp_group.Constant_UD_TYPE_SHOPEE_ACCOUNT
	req := &account_udp_group.CheckUdGroupsRequest{
		GroupNames: groupNames,
		Region:     proto.String(strings.ToUpper(env.GetCID())),
		Ud: &account_udp_group.UD{
			UdId:     &uid,
			UdType:   proto.Uint32(uint32(udType)),
			UdRegion: proto.String(strings.ToUpper(env.GetCID())),
		},
		IgnoreNotFound: proto.Bool(true),
	}
	timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.UdpGroupTimeOut
	if timeout <= 0 {
		timeout = 500
	}
	resp, err := b.client.CheckUdGroups(ctx, req, callopt.WithTimeout(time.Millisecond*time.Duration(timeout)))
	if err != nil {
		errCode = "failed"
		logkit.FromContext(ctx).Error("CheckUdGroups err", logkit.Err(err), logkit.Any("req", req))
		return nil, err
	}
	if len(groupNames) != len(resp.GetIsInGroup()) {
		errCode = "respErr"
		logkit.FromContext(ctx).Error("CheckUdGroups resp not valid", logkit.Err(err), logkit.Any("req", req), logkit.Any("resp", resp))
		return nil, errors.New("udp resp not valid")
	}
	logkit.FromContext(ctx).Info("CheckUdGroups", logkit.Any("req", req), logkit.Any("resp", resp))
	return resp.GetIsInGroup(), nil
}

func (b *UdpClient) GetUdGroups(ctx context.Context, userId uint64, groupNames []string) ([]string, error) {
	ugList := make([]string, 0, len(groupNames))
	if len(groupNames) < 1 {
		logkit.FromContext(ctx).Info("Experiment UserGroup List is nil", logkit.Uint64("UserId", userId))
		return ugList, nil
	}
	ugFlagList, err := b.CheckUdGroups(ctx, userId, groupNames)
	if err != nil {
		return ugList, err
	}
	for i := 0; i < len(groupNames); i++ {
		if ugFlagList[i] {
			ugList = append(ugList, groupNames[i])
		}
	}
	return ugList, nil
}
