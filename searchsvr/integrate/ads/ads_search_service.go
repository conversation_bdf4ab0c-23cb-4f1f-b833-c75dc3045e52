package ads

import (
	"context"
	"encoding/json"
	"time"

	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	qp2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/spexcommon"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/game_platform/go-authsdk/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/protobuf/foodads_adssearchengine"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

type AdsSearchService struct {
	adsService foodads_adssearchengine.Client
}

// always use spex
var greySwitch foodads_adssearchengine.SwitchFunc = func(context.Context, string, foodads_adssearchengine.Method) bool { return false }

func NewAdsSearchService() *AdsSearchService {
	foodads_adssearchengine.SetGreySwitch(greySwitch)
	var client foodads_adssearchengine.Client
	var err error
	if cid.IsVNRegion() {
		client, err = foodads_adssearchengine.NewSpexClientV2(spexcommon.GetDefaultClientOptions()...)
		if err != nil {
			logkit.Fatal("new vn ads search client error", zap.String("err", err.Error()))
		}
	} else {
		client, err = foodads_adssearchengine.NewSpexClient(spexcommon.GetDefaultClientOptions()...)
		if err != nil {
			logkit.Fatal("new ads search client error", zap.String("err", err.Error()))
		}
	}

	return &AdsSearchService{
		adsService: client,
	}
}

func (s *AdsSearchService) ToAdsSearchReq(ctx context.Context, traceInfo *traceinfo.TraceInfo) *foodads_adssearchengine.SearchRequest {
	promotionFilter := make([]foodads_adssearchengine.SearchRequest_PromotionFilterType, 0, len(traceInfo.TraceRequest.GetFilterType().GetPromotionFilter()))
	for _, f := range traceInfo.TraceRequest.GetFilterType().GetPromotionFilter() {
		if env.GetCID() == "vn" {
			if f == foodalgo_search.SearchRequest_StorePromotion || f == foodalgo_search.SearchRequest_FreeShipping {
				if apollo.SearchApolloCfg.VNIsFilterPromotion {
					promotionFilter = append(promotionFilter, foodads_adssearchengine.SearchRequest_PromotionFilterType(f))
				}
			} else {
				// vn 地区的其他过滤项正常处理
				promotionFilter = append(promotionFilter, foodads_adssearchengine.SearchRequest_PromotionFilterType(f))
			}
		} else {
			// 其他地区正常处理
			promotionFilter = append(promotionFilter, foodads_adssearchengine.SearchRequest_PromotionFilterType(f))
		}
	}

	priceRange := make([]*foodads_adssearchengine.SearchRequest_PriceRange, 0, len(traceInfo.TraceRequest.GetFilterType().GetPriceRange()))
	for _, price := range traceInfo.TraceRequest.GetFilterType().GetPriceRange() {
		priceRange = append(priceRange, &foodads_adssearchengine.SearchRequest_PriceRange{
			Floor: proto.Uint64(price.GetFloor()),
			Ceil:  proto.Uint64(price.GetCeil()),
		})
	}

	reqPromotionType := traceInfo.TraceRequest.GetFilterType().GetPromotionTypeFilters()
	promoTypes := make([]*foodads_adssearchengine.SearchRequest_PromotionTypeFilter, 0, len(reqPromotionType))
	for _, p := range reqPromotionType {
		promoLst := make([]foodads_adssearchengine.SearchRequest_PromotionType, 0, len(p.GetPromotionTypes()))
		for _, filter := range p.GetPromotionTypes() {
			promoLst = append(promoLst, foodads_adssearchengine.SearchRequest_PromotionType(filter))
		}
		promoTypes = append(promoTypes, &foodads_adssearchengine.SearchRequest_PromotionTypeFilter{PromotionTypes: promoLst})
	}

	storeTags := traceInfo.TraceRequest.GetFilterType().GetStoreTags()
	adsStoreTags := make([]*foodads_adssearchengine.SearchRequest_StoreTagsFilter, 0, len(storeTags))
	for _, tag := range storeTags {
		adsStoreTags = append(adsStoreTags, &foodads_adssearchengine.SearchRequest_StoreTagsFilter{
			StoreTags:      tag.GetStoreTags(),
			StoreTagsLogic: proto.Int32(tag.GetStoreTagsLogic()),
		})
	}

	collectionFilters := traceInfo.TraceRequest.GetFilterType().GetCollectionFilter()
	var adsCollectionFilter *foodads_adssearchengine.SearchRequest_CollectionFilter
	if collectionFilters != nil {
		adsCollectionFilter = &foodads_adssearchengine.SearchRequest_CollectionFilter{
			PartnerTypes:           collectionFilters.GetPartnerTypes(),
			RatingScoreMin:         collectionFilters.RatingScoreMin,
			RatingScoreMax:         collectionFilters.RatingScoreMax,
			StoreTags:              collectionFilters.GetStoreTags(),
			StoreCategoryLevel2Ids: collectionFilters.GetStoreCategoryLevel2Ids(),
			StoreTagsLogic:         collectionFilters.StoreTagsLogic,
			StoreCategoriesLogic:   collectionFilters.StoreCategoriesLogic,
		}
	}

	halalTypesList := traceInfo.TraceRequest.GetFilterType().GetHalalFilterList()
	adsHalalTypesList := make([]foodads_adssearchengine.HalalType, 0, len(halalTypesList))
	for i := range halalTypesList {
		adsHalalTypesList = append(adsHalalTypesList, foodads_adssearchengine.HalalType(halalTypesList[i]))
	}
	isSelfPickUp := false
	orderTypeList := traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList()
	if len(orderTypeList) > 0 {
		for i := range orderTypeList {
			if orderTypeList[i] == 1 {
				isSelfPickUp = true
				break
			}
		}
	}

	filterType := &foodads_adssearchengine.SearchRequest_FilterType{
		PromotionFilter:      promotionFilter,
		RatingFilter:         (*foodads_adssearchengine.SearchRequest_RatingFilterType)(traceInfo.TraceRequest.GetFilterType().GetRatingFilter().Enum()),
		ShopFilter:           (*foodads_adssearchengine.SearchRequest_ShopFilterType)(traceInfo.TraceRequest.GetFilterType().GetShopFilter().Enum()),
		ShippingDistance:     proto.Uint32(traceInfo.TraceRequest.GetFilterType().GetShippingDistance()),
		PriceRange:           priceRange,
		L2Categories:         traceInfo.TraceRequest.GetFilterType().GetL2Categories(),
		ShippingFee:          proto.Uint64(traceInfo.TraceRequest.GetFilterType().GetShippingFee()),
		StoreTags:            adsStoreTags,
		PromotionTypeFilters: promoTypes,
		CollectionFilter:     adsCollectionFilter,
		HalalFilterList:      adsHalalTypesList,
		IsSelfPickup:         proto.Bool(isSelfPickUp),
		SlashedShippingFee:   proto.Uint64(traceInfo.TraceRequest.GetFilterType().GetSlashedShippingFee()),
	}
	// vn 地区的 isOpening需要开关控制
	if env.GetCID() == "vn" {
		if apollo.SearchApolloCfg.VNIsFilterOpening {
			filterType.IsOpening = proto.Bool(traceInfo.TraceRequest.GetFilterType().GetIsOpening())
		}
	}

	var clientExtraInfo *foodads_adssearchengine.ClientDeviceInfo
	clientExtraInfo = nil
	if traceInfo.TraceRequest.ExtClientInfo != nil {
		clientExtraInfo = &foodads_adssearchengine.ClientDeviceInfo{
			AppVersion:             traceInfo.TraceRequest.ExtClientInfo.AppVersion,
			RnVersion:              traceInfo.TraceRequest.ExtClientInfo.RnVersion,
			Brand:                  traceInfo.TraceRequest.ExtClientInfo.Brand,
			ClientIp:               traceInfo.TraceRequest.ExtClientInfo.ClientIp,
			Country:                traceInfo.TraceRequest.ExtClientInfo.Country,
			DeviceId:               traceInfo.TraceRequest.ExtClientInfo.DeviceId,
			Model:                  traceInfo.TraceRequest.ExtClientInfo.Model,
			Os:                     traceInfo.TraceRequest.ExtClientInfo.Os,
			OsVersion:              traceInfo.TraceRequest.ExtClientInfo.OsVersion,
			Platform:               traceInfo.TraceRequest.ExtClientInfo.Platform,
			PlatformImplementation: traceInfo.TraceRequest.ExtClientInfo.PlatformImplementation,
			Wifi:                   traceInfo.TraceRequest.ExtClientInfo.Wifi,
		}
	}

	var vnPromotionParams *foodads_adssearchengine.SearchRequest_VNPromotionParams
	if cid.IsVNRegion() && len(traceInfo.TraceRequest.GetFilterType().GetPromotionFilter()) > 0 {
		hasStorePromotion := false
		for _, f := range traceInfo.TraceRequest.GetFilterType().GetPromotionFilter() {
			if f == foodalgo_search.SearchRequest_StorePromotion {
				hasStorePromotion = true
			}
		}
		var foodyServiceIDs []uint32
		var isSupportFreeItem bool
		if hasStorePromotion {
			foodyServiceIDs = []uint32{1, 4, 5, 6, 7, 12, 13, 17, 21, 22, 23}
			isSupportFreeItem = true
		}
		vnPromotionParams = &foodads_adssearchengine.SearchRequest_VNPromotionParams{
			FoodyServiceIds:   foodyServiceIDs,
			CategoryIds:       traceInfo.TraceRequest.GetFilterType().GetL1Categories(),
			IsSupportFreeItem: proto.Bool(isSupportFreeItem),
		}
	}

	// 如果 pageSize 不传（totalNum 接口共用该逻辑，它不传），广告不会返回，默认给 20
	pageSize := traceInfo.TraceRequest.PageSize
	if pageSize == 0 {
		pageSize = 20
	}
	keyword := s.preprocessorForAds(ctx, traceInfo, traceInfo.TraceRequest.QueryRaw)
	AdsReq := &foodads_adssearchengine.SearchRequest{
		Keyword:           proto.String(keyword),
		Longitude:         proto.Float32(float32(traceInfo.TraceRequest.Longitude)),
		Latitude:          proto.Float32(float32(traceInfo.TraceRequest.Latitude)),
		PageNum:           proto.Uint32(traceInfo.TraceRequest.PageNum),
		PageSize:          proto.Uint32(pageSize),
		IsDebug:           proto.Bool(traceInfo.IsDebug),
		SearchTime:        proto.Uint64(traceInfo.TraceRequest.SearchTime),
		AbTest:            proto.String(traceInfo.ABTestGroup.GetABTestString()),
		SortType:          (*foodads_adssearchengine.SearchRequest_SortType)(traceInfo.TraceRequest.GetSortType().Enum()),
		FilterType:        filterType,
		BuyerId:           proto.Uint64(traceInfo.UserId),
		PublishId:         proto.String(traceInfo.TraceRequest.PublishId),
		NeedRewrite:       proto.Bool(traceInfo.IsNeedCorrect),
		LocationGroupIds:  traceInfo.TraceRequest.LocationGroupIds,
		ClientExtraInfo:   clientExtraInfo,
		Distance:          proto.Uint32(traceInfo.DistanceLimit),
		SceneId:           proto.Uint32(1), // todo ads need scene id
		FocusServiceId:    proto.Uint32(traceInfo.TraceRequest.FocusServiceId),
		AppType:           proto.Uint32(traceInfo.TraceRequest.AppType),
		CityId:            proto.Uint32(traceInfo.TraceRequest.CityId),
		DistrictIds:       traceInfo.TraceRequest.DistrictIds,
		VnPromotionParams: vnPromotionParams,
	}
	return AdsReq
}

func (s *AdsSearchService) preprocessorForAds(ctx context.Context, traceInfo *traceinfo.TraceInfo, keyword string) string {
	// 调用qp
	if traceInfo.IsNeedCorrect && !(traceInfo.IsDebug && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipCorrection()) {
		errorCode := "0"
		timeout := apollo.SearchApolloCfg.ClientTimeOutConfig.QPClientTimeOut
		if timeout == 0 {
			timeout = 100
		}
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, time.Duration(timeout)*time.Millisecond)
		defer cancel()
		qpReq := &qp.GetQueryProcessingKeywordReq{
			Query:   proto.String(keyword),
			AbTest:  proto.String(traceInfo.ABTestGroup.GetABTestString()),
			Feature: proto.Int32(1), // 1是纠错场景
		}
		startTime := time.Now()
		qpResult, err := qp2.QPServiceClient.Client.GetQueryProcessingKeyword(ctx, qpReq)
		//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "qp-ads"), zap.String("cost", time.Since(startTime).String()))
		metric_reporter2.ReportDurationAndQPS(time.Since(startTime), metric_reporter2.SearchReportTypeRpc, "", "", "ads-qp")
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("failed to get ads qp result", logkit.String("cost", time.Since(startTime).String()))
			errorCode = "failed"
		}
		if qpResult == nil {
			logkit.FromContext(ctx).Info("qp result is nil")
			errorCode = "failed"
		}
		metric_reporter.ReportClientRequestError(1, "qp-ads", errorCode)
		if len(qpResult.GetRewrite().GetCorrected()) > 0 {
			keyword = qpResult.GetRewrite().GetCorrected()[0].GetRewriteQuery()
		}
	}
	// PredefineKeyword
	if !(traceInfo.IsDebug && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipPredefine()) {
		keyword = predefined_keyword.PredefinedKeywordDict.ReplacePredefinedKeyword(ctx, keyword)
		if env.GetCID() == cid.MY {
			// 删除多余标点符号, MY站点
			keyword = util.RemoveAllPunctAndTrim(keyword)
		}
	}
	return keyword
}

// todo linyk3 debuginfo 改造, 广告也使用了debug info
func (s *AdsSearchService) GetAdsSearch(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *foodalgo_search.SearchDebugInfo) (*foodads_adssearchengine.SearchResponse, error) {
	AdsReq := s.ToAdsSearchReq(ctx, traceInfo)
	ctx, cancel := context.WithTimeout(ctx, time.Duration(apollo.SearchApolloCfg.ClientTimeOutConfig.AdsSearchTimeOut)*time.Millisecond)
	defer cancel()
	adsStartTime := time.Now()
	resp, err := s.adsService.Search(ctx, AdsReq)
	//logkit.FromContext(ctx).Info("cost time log", zap.String("method", "AdsSearch"), zap.String("cost", time.Since(adsStartTime).String()))
	metric_reporter2.ReportDurationAndQPS(time.Since(adsStartTime), metric_reporter2.SearchReportTypeRpc, "", "", "ads-search")
	if traceInfo.IsDebug {
		reqStr, _ := json.Marshal(AdsReq)
		resStr, _ := json.Marshal(resp)
		logger.MyDebug(ctx, traceInfo.IsDebug, "diff debug log:GetAdsStores", logkit.String("req", string(reqStr)), logkit.String("resp", string(resStr)))
	}
	if err != nil {
		traceInfo.SlaCode.UpdateErrorCode(sla_code.EXTERN_RPC_ERROR)
		logkit.FromContext(ctx).WithError(err).Error("failed to ads search", logkit.String("cost", time.Since(adsStartTime).String()), zap.String("error", err.Error()))
		metric_reporter.ReportClientRequestError(1, "AdsSearch", "failed")
		return nil, err
	}
	metric_reporter.ReportClientRequestError(1, "AdsSearch", "0")
	if traceInfo.IsDebug && (len(resp.GetIds()) > 0 || len(resp.GetGlobalData()) > 0) {
		BuildAdsDebugStoreInfos(debugInfo, resp)
	}
	traceInfo.ABTestGroupIdList = append(traceInfo.ABTestGroupIdList, resp.GetAbTestGroup()...)
	return resp, nil
}

// todo linyk3 融合未包含ads debuginfo
func BuildAdsDebugStoreInfos(debugInfo *foodalgo_search.SearchDebugInfo, response *foodads_adssearchengine.SearchResponse) {
	if response == nil || debugInfo == nil || response.DebugInfo == nil {
		return
	}
	if env.GetCID() == cid.VN {
		if len(response.DebugInfo.GlobalInfos) == 0 {
			return
		}
		globalInfos := make([]*foodalgo_search.GlobalInfo, 0, len(response.DebugInfo.GlobalInfos))
		for _, info := range response.DebugInfo.GlobalInfos {
			globalInfo := &foodalgo_search.GlobalInfo{
				CategoryType: info.CategoryType,
				TotalNum:     info.TotalNum,
			}
			storeInfos := make([]*foodalgo_search.StoreInfo, 0, len(response.DebugInfo.StoreInfos))
			for _, store := range info.Stores {
				var location *foodalgo_search.Location
				if store.GetLocation() != nil {
					location = &foodalgo_search.Location{
						Latitude:  store.GetLocation().Latitude,
						Longitude: store.GetLocation().Longitude,
					}
				}
				storeInfo := &foodalgo_search.StoreInfo{
					StoreId:                    store.StoreId,
					StoreName:                  store.StoreName,
					RealName:                   store.RealName,
					IsHitStoreIntention:        store.IsHitStoreIntention,
					Logo:                       store.Logo,
					Distance:                   store.Distance,
					OpenStatus:                 store.OpenStatus,
					Segments:                   store.Segments,
					MaxWordSegments:            store.MaxWordSegments,
					StoreIntents:               store.StoreIntents,
					SourceScore:                store.SourceScore,
					RecallType:                 store.RecallType,
					Score:                      store.Score,
					ExactMatch:                 store.ExactMatch,
					NormalStoreSellWeekScore:   store.NormalStoreSellWeekScore,
					NormalStoreSellWeek:        store.NormalStoreSellWeek,
					DistanceScore:              store.DistanceScore,
					StoreRatingScore:           store.StoreRatingScore,
					SortScoreWeight:            store.SortScoreWeight,
					SortDistanceWeight:         store.SortDistanceWeight,
					SortStoreRatingScoreWeight: store.SortStoreRatingScoreWeight,
					SortStoreSellWeekWeight:    store.SortStoreSellWeekWeight,
					PredictCtr:                 store.PredictCtr,
					PredictCvr:                 store.PredictCvr,
					PredictRelevance:           store.PredictRelevance,
					PredictDistScore:           store.PredictDistScore,
					Cvr:                        store.Cvr,
					Ctr:                        store.Ctr,
					ReRankScore:                store.ReRankScore,
					EsRelevanceScore:           store.EsRelevanceScore,
					SemanticRelevanceScore:     store.SemanticRelevanceScore,
					StoreRatingScoreParam:      store.StoreRatingScoreParam,
					PredictRelevanceLevel:      store.PredictRelevanceLevel,
					RepurchaseStrengthening:    store.RepurchaseStrengthening,
					IsIntervention:             store.IsIntervention,
					MainCategory:               store.MainCategory,
					SubCategory:                store.SubCategory,
					BrandId:                    store.BrandId,
					Location:                   location,
					RatingTotal:                store.RatingTotal,
					RatingScore:                store.RatingScore,
					StoreSellWeek:              store.StoreSellWeek,
					DisplayOpeningStatus:       store.DisplayOpeningStatus,
					DisplayDistrictStatus:      store.DisplayDistrictStatus,
					IsPartnerMerchant:          store.IsPartnerMerchant,
					EsScore:                    store.EsScore,
					DistanceGroup_5Km:          store.DistanceGroup_5Km,
					RatingTotalGroup_5:         store.RatingTotalGroup_5,
					OpeningScore:               store.OpeningScore,
					FineTuneRatio:              store.FineTuneRatio,
					FineTuneScore:              store.FineTuneScore,
					RelevanceLevelForSort:      store.RelevanceLevelForSort,
					Price_1:                    store.Price_1,
					Price_2:                    store.Price_2,
					Price_3:                    store.Price_3,
					PredictPrice:               store.PredictPrice,
				}
				dishInfos := make([]*foodalgo_search.DishInfo, 0, len(store.DishInfos))
				for _, dish := range store.DishInfos {
					dishInfo := &foodalgo_search.DishInfo{
						DishId:          dish.DishId,
						DishName:        dish.DishName,
						Picture:         dish.Picture,
						SalesVolume:     dish.SalesVolume,
						Price:           dish.Price,
						Segments:        dish.Segments,
						MaxWordSegments: dish.MaxWordSegments,
						DishIntents:     dish.DishIntents,
						DishSourceScore: dish.DishSourceScore,
						StoreIntents:    dish.StoreIntents,
						HasPicture:      dish.HasPicture,
						EsScore:         dish.EsScore,
					}
					dishInfos = append(dishInfos, dishInfo)
				}
				storeInfo.DishInfos = dishInfos
				storeInfos = append(storeInfos, storeInfo)
			}
			globalInfo.Stores = storeInfos
			globalInfos = append(globalInfos, globalInfo)
		}
		debugInfo.AdsGlobalInfos = globalInfos
	} else {
		if len(response.DebugInfo.StoreInfos) == 0 {
			return
		}
		storeInfos := make([]*foodalgo_search.StoreInfo, 0, len(response.DebugInfo.StoreInfos))
		for _, info := range response.DebugInfo.StoreInfos {

			storeInfo := &foodalgo_search.StoreInfo{
				StoreId:                    info.StoreId,
				StoreName:                  info.StoreName,
				RealName:                   info.RealName,
				IsHitStoreIntention:        info.IsHitStoreIntention,
				Logo:                       info.Logo,
				Distance:                   info.Distance,
				OpenStatus:                 info.OpenStatus,
				Segments:                   info.Segments,
				MaxWordSegments:            info.MaxWordSegments,
				StoreIntents:               info.StoreIntents,
				SourceScore:                info.SourceScore,
				RecallType:                 info.RecallType,
				Score:                      info.Score,
				NormalStoreSellWeekScore:   info.NormalStoreSellWeekScore,
				NormalStoreSellWeek:        info.NormalStoreSellWeek,
				DistanceScore:              info.DistanceScore,
				StoreRatingScore:           info.StoreRatingScore,
				SortScoreWeight:            info.SortScoreWeight,
				SortDistanceWeight:         info.SortDistanceWeight,
				SortStoreRatingScoreWeight: info.SortStoreRatingScoreWeight,
				SortStoreSellWeekWeight:    info.SortStoreSellWeekWeight,
				PredictCtr:                 info.PredictCtr,
				PredictCvr:                 info.PredictCvr,
				PredictRelevance:           info.PredictRelevance,
				PredictDistScore:           info.PredictDistScore,
				Cvr:                        info.Cvr,
				Ctr:                        info.Ctr,
				ReRankScore:                info.ReRankScore,
				EsRelevanceScore:           info.EsRelevanceScore,
				SemanticRelevanceScore:     info.SemanticRelevanceScore,
				StoreRatingScoreParam:      info.StoreRatingScoreParam,
				PredictRelevanceLevel:      info.PredictRelevanceLevel,
				RepurchaseStrengthening:    info.RepurchaseStrengthening,
				IsIntervention:             info.IsIntervention,
				MainCategory:               info.MainCategory,
				SubCategory:                info.SubCategory,
				RelevanceLevelForSort:      info.RelevanceLevelForSort,
				Price_1:                    info.Price_1,
				Price_2:                    info.Price_2,
				Price_3:                    info.Price_3,
				PredictPrice:               info.PredictPrice,
			}
			dishInfos := make([]*foodalgo_search.DishInfo, 0, len(info.DishInfos))
			for _, dish := range info.DishInfos {
				dishInfo := &foodalgo_search.DishInfo{
					DishId:          dish.DishId,
					DishName:        dish.DishName,
					Picture:         dish.Picture,
					SalesVolume:     dish.SalesVolume,
					Price:           dish.Price,
					DishSourceScore: dish.DishSourceScore,
					Segments:        dish.Segments,
					MaxWordSegments: dish.MaxWordSegments,
					StoreIntents:    dish.StoreIntents,
					HasPicture:      dish.HasPicture,
				}
				dishInfos = append(dishInfos, dishInfo)
			}
			storeInfo.DishInfos = dishInfos
			storeInfos = append(storeInfos, storeInfo)
		}
		debugInfo.AdsStoreInfos = storeInfos
	}
}
