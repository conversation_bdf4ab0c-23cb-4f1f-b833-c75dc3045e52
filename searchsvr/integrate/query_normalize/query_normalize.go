package query_normalize

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/normalize"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func InitQueryNormalize(s3Client s3.S3Service) {
	normalize.NewNormalize(normalize.NewOpenCC("t2s", "./searchsvr/conf/opencc"), normalize.NewWhiteDict(s3Client))
}

func Norm(ctx context.Context, traceInfo *traceinfo.TraceInfo) {
	keyword := traceInfo.QueryKeyword
	normKeyword := keyword
	switch env.GetCID() {
	case cid.MY:
		normKeyword = MYQueryNorm(keyword)
	default:
		normKeyword = strings.ToLower(strings.TrimSpace(keyword))
	}
	traceInfo.QueryKeyword = normKeyword
	//traceInfo.UserContext.Query = normKeyword
}

func MYQueryNorm(keyword string) string {
	step1 := normalize.Norm.TraditionalToSimplified(keyword)
	step2 := normalize.Norm.RemoveEmojis(step1)
	step3 := normalize.Norm.ConvertSpecialAlphabet(step2)
	step4 := normalize.Norm.WidenToNarrow(step3)
	step5 := normalize.Norm.ReplacePunctuationExceptWhitelist(step4)
	step6 := normalize.Norm.ReplaceWhitespace(step5)
	step7 := strings.ToLower(step6)
	step8 := strings.TrimSpace(step7)
	if len(step8) == 0 {
		step8 = keyword
	}
	return step8
}
