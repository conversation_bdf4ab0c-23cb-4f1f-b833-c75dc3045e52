package apollo

import (
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	apollo "git.garena.com/shopee/marketing/config-client"
	configCenter "git.garena.com/shopee/o2o-intelligence/common/common-lib/config_center"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

type AlgoApolloConfig struct {
	TruncDistance                float64                      `xml:"TruncDistance"`
	TruncPctr                    float64                      `xml:"TruncPctr"`
	TruncCvr                     float64                      `xml:"TruncCvr"`
	DefaultCtr                   float64                      `xml:"DefaultCtr"`
	DefaultCvr                   float64                      `xml:"DefaultCvr"`
	RatingScoreSmooth            float64                      `xml:"RatingScoreSmooth"`
	RatingScoreNorm              float64                      `xml:"RatingScoreNorm"`
	DefaultAnnESScore            float64                      `xml:"DefaultAnnESScore"`
	AnnESScoreGroup              string                       `xml:"AnnESScoreGroup"`
	PredictMultiFactorConfigList PredictMultiFactorConfigList `xml:"PredictMultiFactorConfigList"`
	PredictCtrConfigList         PredictCtrConfigList         `xml:"PredictCtrConfigList"`
	PredictCvrConfigList         PredictCvrConfigList         `xml:"PredictCvrConfigList"`
	PredictRelevanceConfigList   PredictRelevanceConfigList   `xml:"PredictRelevanceConfigList"`
	PredictRecallConfigList      PredictRecallConfigList      `xml:"PredictRecallConfigList"`
	TopInterventionConfigList    TopInterventionConfigList    `xml:"TopInterventionConfigList"`
	PredictPriceConfigList       PredictPriceConfigList       `xml:"PredictPriceConfigList"`

	DishSupplementRecallConfigList DishSupplementRecallConfigList `xml:"DishSupplementRecallConfigList"`
	StoreA30ReRankConfigList       StoreA30ReRankConfigList       `xml:"StoreA30ReRankConfigList"`
	DishAnnRecallConfigList        DishAnnRecallConfigList        `xml:"DishAnnRecallConfigList"`
	ListWiseGeneratorConfigList    ListWiseGeneratorConfigList    `xml:"ListWiseGeneratorConfigList"`
	ListWiseEvaluatorConfigList    ListWiseEvaluatorConfigList    `xml:"ListWiseEvaluatorConfigList"`

	PredictMultiFactorConfigMap *PredictMultiFactorControl
	PredictCtrControlMap        *PredictCtrControl
	PredictCvrControlMap        *PredictCvrControl
	PredictRelevanceControlMap  *PredictRelevanceControl
	PredictRecallControlMap     *PredictRecallControl
	TopInterventionControlMap   *TopInterventionControl
	PredictPriceControlMap      *PredictPriceControl
	// 补充挂菜remove DishSupplementRecallMap     *DishSupplementRecallControl
	StoreA30ReRankMap    *StoreA30ReRankControl
	DishAnnRecallMap     *DishAnnRecallControl
	ListWiseGeneratorMap *ListWiseGeneratorControl
	ListWiseEvaluatorMap *ListWiseEvaluatorControl
	TruncESScore         float64 `xml:"TruncESScore"`
}

// 启动服务的时候需要初始化Apollo配置
func InitSearchAlgoApolloConfig(group, project, namespace, secret string, region env.RegionCode) error {
	// 1.配置回调函数，实际更新操作。配置转化操作也是放在这里，初始化&更新通知都是调用回调函数
	registerSearchAlgoHandler()
	newClientParam := configCenter.NewClientParam{
		Watcher:     searchAlgoApolloChangeWatcher, // 2.监听，有更新事件触发后，会调用回调函数
		CallbackMap: callAlgoBackHandlerMap,        // 3.设置回调函数
		Group:       group,
		Project:     project,
		Namespace:   namespace,
		Secret:      secret,
	}
	client, err := configCenter.NewConfigCenterClientWithRegion(newClientParam, region, false)
	if err != nil {
		logkit.Error("apollo.NewClient", logkit.Err(err))
		return err
	}

	if err := client.Start(); err != nil {
		logkit.Error("client.Start", logkit.Err(err))
		return err
	}
	logkit.Info("apollo client start ok")
	initParam := configCenter.InitConfigCenterParam{
		Config:      AlgoApolloCfg,
		Client:      client,
		Namespace:   namespace,
		CallbackMap: callAlgoBackHandlerMap,
	}
	configCenter.InitFromConfigCenter(initParam) // 开始，初始化一次。实际是调用回调函数
	return nil
}

func registerSearchAlgoHandler() {
	callAlgoBackHandlerMap = configCenter.ConfigCenterCallbackMap{
		"content": configCenter.CreateCommonCallback(
			func() interface{} { return &AlgoApolloConfig{} },
			func(cfg, value interface{}) {
				mutex.Lock()
				defer mutex.Unlock()
				AlgoApolloCfg = value.(*AlgoApolloConfig)
				// 各种根据配置转化的在这里实现一次，初始化&更新通知都会触发
				BuildPredictMultiFactorConfigControl() //id地区使用新的apollo配置
				BuildTopInterventionConfigControl()    // 置顶词表AB配置
			},
		)}
}

func searchAlgoApolloChangeWatcher(changeEvent *apollo.ChangeEvent) {
	logkit.Info("apollo config change", logkit.Any("namespace", changeEvent.Namespace))
	param := configCenter.UpdateConfigCenterParam{
		ChangeEvent: changeEvent,
		Config:      AlgoApolloCfg,
		CallbackMap: callAlgoBackHandlerMap,
	}
	configCenter.UpdateFromConfigCenter(param)
}

type PredictMultiFactorConfigList struct {
	PredictMultiFactorConfigs []PredictMultiFactorConfig `xml:"PredictMultiFactorConfig"`
}
type PredictCtrConfigList struct {
	PredictCtrConfigs []PredictCtrConfig `xml:"PredictCtrConfig"`
}
type PredictPriceConfigList struct {
	PredictPriceConfigs []PredictPriceConfig `xml:"PredictPriceConfig"`
}
type PredictCvrConfigList struct {
	PredictCvrConfigs []PredictCvrConfig `xml:"PredictCvrConfig"`
}
type PredictRelevanceConfigList struct {
	PredictRelevanceConfigs []PredictRelevanceConfig `xml:"PredictRelevanceConfig"`
}
type PredictRecallConfigList struct {
	PredictRecallConfigs []PredictRecallConfig `xml:"PredictRecallConfig"`
}
type TopInterventionConfigList struct {
	TopInterventionConfigs []TopInterventionConfig `xml:"TopInterventionConfig"`
}
type DishSupplementRecallConfigList struct {
	DishSupplementRecallConfigs []DishSupplementRecallConfig `xml:"DishSupplementRecallConfigs"`
}
type StoreA30ReRankConfigList struct {
	StoreA30ReRankConfigs []StoreA30ReRankConfig `xml:"StoreA30ReRankConfigs"`
}
type DishAnnRecallConfigList struct {
	DishAnnRecallConfigs []DishAnnRecallConfig `xml:"DishAnnRecallConfigs"`
}

type ListWiseGeneratorConfigList struct {
	ListWiseGeneratorConfigs []*ListWiseGeneratorConfig `xml:"ListWiseGeneratorConfig"`
}

type ListWiseEvaluatorConfigList struct {
	ListWiseEvaluatorConfigs []*ListWiseEvaluatorConfig `xml:"ListWiseEvaluatorConfig"`
}

// https://confluence.shopee.io/pages/viewpage.action?pageId=942905579
type PredictMultiFactorConfig struct {
	ExpString         string  `xml:"ExpString"`
	ExpParameters     string  `xml:"ExpParameters"`
	ABTestKey         string  `xml:"ABTestKey"`
	NewABTestKey      string  `xml:"NewABTestKey"`
	ABTestVal         string  `xml:"ABTestVal"`
	ExpFunc           string  `xml:"ExpFunc"`
	Relevance         int     `xml:"Relevance"`
	PriceType         int     `xml:"PriceType"`
	RelevanceUseLevel int     `xml:"RelevanceUseLevel"`
	TruncDistance     float64 `xml:"TruncDistance"`
	ExpParametersMap  map[string]interface{}

	CtrModuleName       string `xml:"CtrModuleName"`
	CvrModuleName       string `xml:"CvrModuleName"`
	RelevanceModuleName string `xml:"RelevanceModuleName"`
	SemanticModuleName  string `xml:"SemanticModuleName"`
	UEModuleName        string `xml:"UEModuleName"`

	IsCtrCvrUnified     bool   `xml:"IsCtrCvrUnified"`
	UnifiedModuleCtrKey string `xml:"UnifiedModuleCtrKey"`
	UnifiedModuleCvrKey string `xml:"UnifiedModuleCvrKey"`

	MaxTruncUE        float64 `xml:"MaxTruncUE"`
	MinTruncUE        float64 `xml:"MinTruncUE"`
	MaxTruncPctr      float64 `xml:"MaxTruncPctr"`
	MinTruncPctr      float64 `xml:"MinTruncPctr"`
	MaxTruncPcvr      float64 `xml:"MaxTruncPcvr"`
	MinTruncPcvr      float64 `xml:"MinTruncPcvr"`
	MaxTruncRelevance float64 `xml:"MaxTruncRelevance"`
	MinTruncRelevance float64 `xml:"MinTruncRelevance"`
	MaxTruncSemantic  float64 `xml:"MaxTruncSemantic"`
	MinTruncSemantic  float64 `xml:"MinTruncSemantic"`
	MaxTruncPrice     float64 `xml:"MaxTruncPrice"`
	MinTruncPrice     float64 `xml:"MinTruncPrice"`
	DistanceFunc      int     `xml:"DistanceFunc"`
	ReleFusionFunc    int     `xml:"ReleFusionFunc"`
	UseNew            int     `xml:"UseNew"`
	// rel新策略
	NewStrategy int `xml:"NewStrategy"`
	//打压系数
	Suppress float64 `xml:"Suppress"`
	//距离，用来定档
	Distance float64 `xml:"Distance"`
	//结果是否要分档
	RelResLayer            bool    `xml:"RelResLayer"`
	UEDefaultVal           float64 `xml:"UEDefaultVal"`
	UERadioDefaultVal      float64 `xml:"UERadioDefaultVal"`
	UserExpParameters      string  `xml:"UserExpParameters"`
	UserExpParametersMap   map[string]map[string]interface{}
	UserExpType            int    `xml:"UserExpType"`
	UserExp                bool   `xml:"UserExp"` //打开user分档实验
	IntentExpParameters    string `xml:"IntentExpParameters"`
	IntentExp              bool   `xml:"IntentExp"` //打开intention实验
	IntentExpParametersMap map[string]map[string]interface{}

	DFModelName     string `xml:"DFModelName"`
	DyFactorList    []float64
	IsPredict       bool    `xml:"IsPredict"`
	StoreIncubation float64 `xml:"StoreIncubation"`
	UsePcFactor     bool    `xml:"UsePcFactor"`

	LtrModuleName              string `xml:"LtrModuleName"`
	UseLtr                     bool   `xml:"UseLtr"`
	CoarseSortExpString        string `xml:"CoarseSortExpString"`
	CoarseSortExpParameters    string `xml:"CoarseSortExpParameters"`
	CoarseSortExpParametersMap map[string]interface{}

	// LTR v1  参数相关
	LTRV1ExpString        string `xml:"LTRV1ExpString"`
	LTRV1ExpParameters    string `xml:"LTRV1ExpParameters"`
	LTRV1ExpParametersMap map[string]interface{}
}
type PredictCtrConfig struct {
	ABTestKey     string  `xml:"ABTestKey"`
	ABTestVal     string  `xml:"ABTestVal"`
	CtrModuleID   int     `xml:"CtrModuleID"`
	CtrModuleName string  `xml:"CtrModuleName"`
	MaxTruncPctr  float64 `xml:"MaxTruncPctr"`
	MinTruncPctr  float64 `xml:"MinTruncPctr"`
}
type PredictPriceConfig struct {
	ABTestKey         string  `xml:"ABTestKey"`
	ABTestVal         string  `xml:"ABTestVal"`
	PriceType         int     `xml:"PriceType"`
	MaxTruncPrice     float64 `xml:"MaxTruncPrice"`
	MinTruncPrice     float64 `xml:"MinTruncPrice"`
	UEDefaultVal      float64 `xml:"UEDefaultVal"`
	UERadioDefaultVal float64 `xml:"UERadioDefaultVal"`
	UEModuleName      string  `xml:"UEModuleName"`
	MaxTruncUE        float64 `xml:"MaxTruncUE"`
	MinTruncUE        float64 `xml:"MinTruncUE"`
}
type PredictCvrConfig struct {
	ABTestKey     string  `xml:"ABTestKey"`
	ABTestVal     string  `xml:"ABTestVal"`
	CvrModuleID   int     `xml:"CvrModuleID"`
	CvrModuleName string  `xml:"CvrModuleName"`
	MaxTruncPcvr  float64 `xml:"MaxTruncPcvr"`
	MinTruncPcvr  float64 `xml:"MinTruncPcvr"`
}
type PredictRelevanceConfig struct {
	ABTestKey           string  `xml:"ABTestKey"`
	ABTestVal           string  `xml:"ABTestVal"`
	RelevanceModuleID   int     `xml:"RelevanceModuleID"`
	RelevanceModuleName string  `xml:"RelevanceModuleName"`
	SemanticModuleID    int     `xml:"SemanticModuleID"`
	SemanticModuleName  string  `xml:"SemanticModuleName"`
	ReleFusionFunc      int     `xml:"ReleFusionFunc"`
	MaxTruncRelevance   float64 `xml:"MaxTruncRelevance"`
	MinTruncRelevance   float64 `xml:"MinTruncRelevance"`
	MaxTruncSemantic    float64 `xml:"MaxTruncSemantic"`
	MinTruncSemantic    float64 `xml:"MinTruncSemantic"`
	// rel新策略
	NewStrategy int `xml:"NewStrategy"`
	//打压系数
	Suppress float64 `xml:"Suppress"`
	//距离，用来定档
	Distance float64 `xml:"Distance"`
	//结果是否要分档
	RelResLayer bool `xml:"RelResLayer"`
}
type PredictRecallConfig struct {
	ABTestKey                                 string `xml:"ABTestKey"`
	ABTestVal                                 string `xml:"ABTestVal"`
	MustAndMatchTypeFieldBoostQueueLocation   string `xml:"MustAndMatchTypeFieldBoostQueueLocation"`
	MustAndMatchTypeFieldBoostQueueDish       string `xml:"MustAndMatchTypeFieldBoostQueueDish"`
	MustAndMatchTypeFieldBoostQueueStore      string `xml:"MustAndMatchTypeFieldBoostQueueStore"`
	MustAndMatchTypeFieldBoostQueueCategory   string `xml:"MustAndMatchTypeFieldBoostQueueCategory"`
	MustAndMatchTypeFieldBoostQueueIngredient string `xml:"MustAndMatchTypeFieldBoostQueueIngredient"`
	MustAndMatchTypeFieldBoostQueueDefault    string `xml:"MustAndMatchTypeFieldBoostQueueDefault"`
	OtherFieldBoostQueueDish                  string `xml:"OtherFieldBoostQueueDish"`
	OtherFieldBoostQueueStore                 string `xml:"OtherFieldBoostQueueStore"`
	OtherFieldBoostQueueCategory              string `xml:"OtherFieldBoostQueueCategory"`
	OtherFieldBoostQueueIngredient            string `xml:"OtherFieldBoostQueueIngredient"`
	OtherFieldBoostQueueDefault               string `xml:"OtherFieldBoostQueueDefault"`
	OtherFieldBoostQueueLocation              string `xml:"OtherFieldBoostQueueLocation"`
	NerRecallTotal                            int    `xml:"NerRecallTotal"`
	MustAndMatchTypeSize                      int    `xml:"MustAndMatchTypeSize"`
	MustOrMatchTypeSize                       int    `xml:"MustOrMatchTypeSize"`
	ShouldMatchTypeSize                       int    `xml:"ShouldMatchTypeSize"`

	// 结构化召回迭代优化
	NerRecallUseScriptSort bool    `xml:"NerRecallUseScriptSort"`
	LocationNerBoost       float64 `xml:"LocationNerBoost"`
	DishNerBoost           float64 `xml:"DishNerBoost"`
	StoreNerBoost          float64 `xml:"StoreNerBoost"`
	CategoryNerBoost       float64 `xml:"CategoryNerBoost"`
	IngredientNerBoost     float64 `xml:"IngredientNerBoost"`
	DefaultNerBoost        float64 `xml:"DefaultNerBoost"`

	// 新分类扩召回
	IsNewCateRecall                   bool    `xml:"IsNewCateRecall"`
	NewCateProbFilterScore            float64 `xml:"NewCateProbFilterScore"`
	NewCateIsKeepGrabfoodTag          bool    `xml:"NewCateIsKeepGrabfoodTag"`
	NewCateIsOnlyRecallStoreIntention bool    `xml:"NewCateIsOnlyRecallStoreIntention"`
	NewCateExpandRecallSize           int     `xml:"NewCateExpandRecallSize"`
	IsNewCateExpandRecallV2           bool    `xml:"IsNewCateExpandRecallV2"`
	NewCateExpandRecallV2DistanceKm   float64 `xml:"NewCateExpandRecallV2DistanceKm"`

	// 排序 ab 开关
	IsOpenLocationSort bool `xml:"IsOpenLocationSort"`
}

type TopInterventionConfig struct {
	ABTestKey   string `xml:"ABTestKey"`
	ABTestVal   string `xml:"ABTestVal"`
	DictVersion string `xml:"DictVersion"`
}

type DishSupplementRecallConfig struct {
	ABTestKey       string `xml:"ABTestKey"`
	ABTestVal       string `xml:"ABTestVal"`
	ExpParameters   string `xml:"ExpParameters"`
	StoreLimit      int    `xml:"StoreLimit"`
	DishRecallLimit int    `xml:"DishRecallLimit"`
	TopSaleRecall   bool   `xml:"TopSaleRecall"`

	ExpParametersMap map[string]float64
}

type StoreA30ReRankConfig struct {
	ABTestKey         string  `xml:"ABTestKey"`
	ABTestVal         string  `xml:"ABTestVal"`
	StoreImp180d      int64   `xml:"StoreImp180d"`
	StoreOrderCnt180d int64   `xml:"StoreOrderCnt180d"`
	StoreCtCvr180d    float64 `xml:"StoreCtCvr180d"`
	Distance          float64 `xml:"Distance"`
	DivBoost          float64 `xml:"DivBoost"`
	TopPos            int     `xml:"TopPos"`
}

type DishAnnRecallConfig struct {
	ABTestKey   string `xml:"ABTestKey"`
	ABTestVal   string `xml:"ABTestVal"`
	RedisPrefix string `xml:"RedisPrefix"`
}

type VectorEngineRecallConfig struct {
	SuppressRecallResultThreshold int  `xml:"SuppressRecallResultThreshold"` // 向量召回打压的召回阈值
	IsSuppressRule1               bool `xml:"IsSuppressRule1"`               //是否打压规则 1：如果Embedding recall的返回结果数目小于设定阈值，则不返回Embedding recall结果
	IsSuppressRule2               bool `xml:"IsSuppressRule2"`               //是否打压规则 2：规则 2：如果Query的NER结果中只有UNKNOWN实体 且 Synonym recall无返回结果，则不返回Embedding recall结果。
	IsSuppressRule3               bool `xml:"IsSuppressRule3"`               // 是否打压规则 3：如果除Embedding recall以外，所有召回都没有返回结果，则不返回Embedding recall结果。
}

type ListWiseGeneratorConfig struct {
	Name          string  `xml:"Name"`
	Type          string  `xml:"Type"`
	Formula       string  `xml:"Formula"` // formula
	Parameters    string  `xml:"Parameters"`
	Lambda        float64 `xml:"Lambda"` // mmr
	RelFormula    string  `xml:"RelFormula"`
	RelParameters string  `xml:"RelParameters"`
	SimFormula    string  `xml:"SimFormula"`
	SimParameters string  `xml:"SimParameters"`
	SimType       string  `xml:"SimType"` // max/min/avg
}

const EvaluatorScoreTypeItem = "item"
const EvaluatorScoreTypeList = "list"

type ListWiseEvaluatorConfig struct {
	Name       string `xml:"Name"`
	Type       string `xml:"Type"`
	Formula    string `xml:"Formula"` // formula
	Parameters string `xml:"Parameters"`
	ModelName  string `xml:"ModelName"`
	ScoreType  string `xml:"ScoresType"` // item/list
	ScoreKeys  string `xml:"ScoresKeys"` // "lw_ctr,lw_cvr" 等等
}

type RecallPriorityConfig struct {
	LimitSize                       int            `json:"LimitSize"`                       // 保留的门店数量
	RecallPriority                  map[string]int `json:"RecallPriority"`                  // RecallType 对应的优先级，0优先级最高
	MinPriority                     int            `json:"MinPriority"`                     // 0 的优先级最高
	IsReRankSkipPriority            int            `json:"IsReRankSkipPriority"`            // 排序层实验
	IsReRankSkipPriorityRecallLayer int            `json:"IsReRankSkipPriorityRecallLayer"` // 为了统计召回层实验效果数据
}

type SortPriorityConfig struct {
	IsUseSortPriority bool           `json:"IsUseSortPriority"`
	SortPriority      map[string]int `json:"RecallPriority"` // RecallType 对应的排序优先级，0优先级最高
}

type DishSearcherRecallConfig struct {
	IsNewDishRecall                bool    `json:"IsNewDishRecall"`
	RecallKeywordLimit             int     `json:"RecallKeywordLimit"`
	RawQueryMultiWeight            float64 `json:"RawQueryMultiWeight"`
	NerRewriteMultiWeight          float64 `json:"NerRewriteMultiWeight"`
	EnlargeRewriteMultiWeight      float64 `json:"EnlargeRewriteMultiWeight"`
	RawQueryUseCatalogRecall       bool    `json:"RawQueryUseCatalogRecall"`
	NerRewriteUseCatalogRecall     bool    `json:"NerRewriteUseCatalogRecall"`
	EnlargeRewriteUseCatalogRecall bool    `json:"EnlargeRewriteUseCatalogRecall"`
	EachStoreRecallDishSize        int     `json:"EachStoreRecallDishSize"`
}

type PredictMultiFactorControl struct {
	PredictMultiFactorControl map[string]*PredictMultiFactorConfig
	ABTestKeyMap              map[string]struct{}
	ABTestGroupMap            map[string]struct{}
	sync.RWMutex
}
type PredictCtrControl struct {
	PredictCtrControl map[string]*PredictCtrConfig
	ABTestKeyMap      map[string]struct{}
	ABTestGroupMap    map[string]struct{}
	sync.RWMutex
}
type PredictCvrControl struct {
	PredictCvrControl map[string]*PredictCvrConfig
	ABTestKeyMap      map[string]struct{}
	ABTestGroupMap    map[string]struct{}
	sync.RWMutex
}
type PredictRelevanceControl struct {
	PredictRelevanceControl map[string]*PredictRelevanceConfig
	ABTestKeyMap            map[string]struct{}
	ABTestGroupMap          map[string]struct{}
	sync.RWMutex
}
type PredictRecallControl struct {
	PredictRecallControl map[string]*PredictRecallConfig
	ABTestKeyMap         map[string]struct{}
	ABTestGroupMap       map[string]struct{}
	sync.RWMutex
}
type TopInterventionControl struct {
	TopInterventionControl map[string]*TopInterventionConfig
	ABTestKeyMap           map[string]struct{}
	ABTestGroupMap         map[string]struct{}
	sync.RWMutex
}

// 补充挂菜remove
// type DishSupplementRecallControl struct {
//	DishSupplementRecallControl map[string]*DishSupplementRecallConfig
//	ABTestKeyMap                map[string]struct{}
//	ABTestGroupMap              map[string]struct{}
//	sync.RWMutex
//}

type StoreA30ReRankControl struct {
	StoreA30ReRankControl map[string]*StoreA30ReRankConfig
	ABTestKeyMap          map[string]struct{}
	ABTestGroupMap        map[string]struct{}
	sync.RWMutex
}

type DishAnnRecallControl struct {
	DishAnnRecallControl map[string]*DishAnnRecallConfig
	ABTestKeyMap         map[string]struct{}
	ABTestGroupMap       map[string]struct{}
	sync.RWMutex
}

type ListWiseGeneratorControl struct {
	ListWiseGeneratorControl map[string]*ListWiseGeneratorConfig
	sync.RWMutex
}

type ListWiseEvaluatorControl struct {
	ListWiseEvaluatorControl map[string]*ListWiseEvaluatorConfig
	sync.RWMutex
}

type PredictPriceControl struct {
	PredictPriceControl map[string]*PredictPriceConfig
	ABTestKeyMap        map[string]struct{}
	ABTestGroupMap      map[string]struct{}
	sync.RWMutex
}

func BuildPredictMultiFactorConfigControl() {
	predictConfigControlTemp := make(map[string]*PredictMultiFactorConfig)
	predictMultiFactorAbtestKey := make(map[string]struct{})
	predictMultiFactorAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictMultiFactorConfigList.PredictMultiFactorConfigs {
		p := AlgoApolloCfg.PredictMultiFactorConfigList.PredictMultiFactorConfigs[i]
		predictMultiFactorAbtestKey[p.NewABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			newKey := p.NewABTestKey + "=" + val
			predictConfigControlTemp[key] = &p
			predictConfigControlTemp[newKey] = &p
			predictMultiFactorAbtestGroup[newKey] = struct{}{}
		}
	}
	predictCtrConfigControlTemp := make(map[string]*PredictCtrConfig)
	predictCtrAbtestKey := make(map[string]struct{})
	predictCtrAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictCtrConfigList.PredictCtrConfigs {
		p := AlgoApolloCfg.PredictCtrConfigList.PredictCtrConfigs[i]
		predictCtrAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			predictCtrConfigControlTemp[key] = &p
			predictCtrAbtestGroup[key] = struct{}{}
		}
	}
	predictCvrConfigControlTemp := make(map[string]*PredictCvrConfig)
	predictCvrAbtestKey := make(map[string]struct{})
	predictCvrAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictCvrConfigList.PredictCvrConfigs {
		p := AlgoApolloCfg.PredictCvrConfigList.PredictCvrConfigs[i]
		predictCvrAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			predictCvrConfigControlTemp[key] = &p
			predictCvrAbtestGroup[key] = struct{}{}
		}
	}
	predictRelevanceConfigControlTemp := make(map[string]*PredictRelevanceConfig)
	predictRelevanceAbtestKey := make(map[string]struct{})
	predictRelevanceAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictRelevanceConfigList.PredictRelevanceConfigs {
		p := AlgoApolloCfg.PredictRelevanceConfigList.PredictRelevanceConfigs[i]
		predictRelevanceAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			predictRelevanceConfigControlTemp[key] = &p
			predictRelevanceAbtestGroup[key] = struct{}{}
		}
	}
	predictRecallConfigControlTemp := make(map[string]*PredictRecallConfig)
	predictRecallAbtestKey := make(map[string]struct{})
	predictRecallAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictRecallConfigList.PredictRecallConfigs {
		p := AlgoApolloCfg.PredictRecallConfigList.PredictRecallConfigs[i]
		predictRecallAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			predictRecallConfigControlTemp[key] = &p
			predictRecallAbtestGroup[key] = struct{}{}
		}
	}
	predictPriceConfigControlTemp := make(map[string]*PredictPriceConfig)
	predictPriceAbtestKey := make(map[string]struct{})
	predictPriceAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.PredictPriceConfigList.PredictPriceConfigs {
		p := AlgoApolloCfg.PredictPriceConfigList.PredictPriceConfigs[i]
		predictPriceAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			predictPriceConfigControlTemp[key] = &p
			predictPriceAbtestGroup[key] = struct{}{}
		}
	}

	dishSupplementRecallControlTemp := make(map[string]*DishSupplementRecallConfig)
	dishSupplementRecallAbtestKey := make(map[string]struct{})
	dishSupplementRecallAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.DishSupplementRecallConfigList.DishSupplementRecallConfigs {
		p := AlgoApolloCfg.DishSupplementRecallConfigList.DishSupplementRecallConfigs[i]

		// 参数解析
		exps := strings.Split(p.ExpParameters, ",")
		p.ExpParametersMap = make(map[string]float64, len(exps))
		for _, exp := range exps {
			keyValue := strings.Split(exp, "=")
			if len(keyValue) < 2 {
				continue
			}
			key := keyValue[0]
			value, err := strconv.ParseFloat(keyValue[1], 64)
			if err != nil {
				continue
			}
			p.ExpParametersMap[key] = value
		}

		dishSupplementRecallAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			dishSupplementRecallControlTemp[key] = &p
			dishSupplementRecallAbtestGroup[key] = struct{}{}
		}
	}

	// store a30
	storeA30ReRankControlTemp := make(map[string]*StoreA30ReRankConfig)
	storeA30ReRankAbtestKey := make(map[string]struct{})
	storeA30ReRankAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.StoreA30ReRankConfigList.StoreA30ReRankConfigs {
		p := AlgoApolloCfg.StoreA30ReRankConfigList.StoreA30ReRankConfigs[i]
		storeA30ReRankAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := make([]string, 0)
		abTestValList = strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			storeA30ReRankControlTemp[key] = &p
			storeA30ReRankAbtestGroup[key] = struct{}{}
		}
	}

	// dish ann recall config
	dishAnnRecallControlTemp := make(map[string]*DishAnnRecallConfig)
	dishAnnRecallAbtestKey := make(map[string]struct{})
	dishAnnRecallAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.DishAnnRecallConfigList.DishAnnRecallConfigs {
		p := AlgoApolloCfg.DishAnnRecallConfigList.DishAnnRecallConfigs[i]
		dishAnnRecallAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := strings.Split(p.ABTestVal, ",")
		for _, val := range abTestValList {
			key := p.ABTestKey + "=" + val
			dishAnnRecallControlTemp[key] = &p
			dishAnnRecallAbtestGroup[key] = struct{}{}
		}
	}

	// listwise generator 配置
	listWiseGeneratorControlTemp := make(map[string]*ListWiseGeneratorConfig)
	for _, conf := range AlgoApolloCfg.ListWiseGeneratorConfigList.ListWiseGeneratorConfigs {
		listWiseGeneratorControlTemp[conf.Name] = conf
	}

	// listwise generator 配置
	listWiseEvaluatorControlTemp := make(map[string]*ListWiseEvaluatorConfig)
	for _, conf := range AlgoApolloCfg.ListWiseEvaluatorConfigList.ListWiseEvaluatorConfigs {
		listWiseEvaluatorControlTemp[conf.Name] = conf
	}

	for i, p := range predictConfigControlTemp {
		predictConfigControlTemp[i].ExpParametersMap = util.BuildExpParameters(p.ExpParameters)
		intentExpParametersMap := make(map[string]map[string]interface{}, 0)
		for _, pair := range strings.Split(p.IntentExpParameters, ";") {
			keyValue := strings.Split(pair, ":")
			if len(keyValue) < 2 {
				continue
			}
			intentExpParametersMap[keyValue[0]] = util.BuildExpParameters(keyValue[1])
		}
		predictConfigControlTemp[i].IntentExpParametersMap = intentExpParametersMap
		userExpParametersMap := make(map[string]map[string]interface{}, 0)
		for _, pair := range strings.Split(p.UserExpParameters, ";") {
			keyValue := strings.Split(pair, ":")
			if len(keyValue) < 2 {
				continue
			}
			userExpParametersMap[keyValue[0]] = util.BuildExpParameters(keyValue[1])
		}
		predictConfigControlTemp[i].UserExpParametersMap = userExpParametersMap
	}

	if AlgoApolloCfg.PredictMultiFactorConfigMap == nil {
		AlgoApolloCfg.PredictMultiFactorConfigMap = &PredictMultiFactorControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.PredictCtrControlMap == nil {
		AlgoApolloCfg.PredictCtrControlMap = &PredictCtrControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.PredictCvrControlMap == nil {
		AlgoApolloCfg.PredictCvrControlMap = &PredictCvrControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.PredictRelevanceControlMap == nil {
		AlgoApolloCfg.PredictRelevanceControlMap = &PredictRelevanceControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.PredictRecallControlMap == nil {
		AlgoApolloCfg.PredictRecallControlMap = &PredictRecallControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.PredictPriceControlMap == nil {
		AlgoApolloCfg.PredictPriceControlMap = &PredictPriceControl{
			RWMutex: sync.RWMutex{},
		}
	}
	// 补充挂菜remove
	//if AlgoApolloCfg.DishSupplementRecallMap == nil {
	//	AlgoApolloCfg.DishSupplementRecallMap = &DishSupplementRecallControl{
	//		RWMutex: sync.RWMutex{},
	//	}
	//}
	if AlgoApolloCfg.StoreA30ReRankMap == nil {
		AlgoApolloCfg.StoreA30ReRankMap = &StoreA30ReRankControl{
			RWMutex: sync.RWMutex{},
		}
	}
	if AlgoApolloCfg.DishAnnRecallMap == nil {
		AlgoApolloCfg.DishAnnRecallMap = &DishAnnRecallControl{
			RWMutex: sync.RWMutex{},
		}
	}

	if AlgoApolloCfg.ListWiseGeneratorMap == nil {
		AlgoApolloCfg.ListWiseGeneratorMap = &ListWiseGeneratorControl{
			RWMutex: sync.RWMutex{},
		}
	}

	if AlgoApolloCfg.ListWiseEvaluatorMap == nil {
		AlgoApolloCfg.ListWiseEvaluatorMap = &ListWiseEvaluatorControl{
			RWMutex: sync.RWMutex{},
		}
	}

	UpdateMultiFactorMap(predictConfigControlTemp, predictMultiFactorAbtestKey, predictMultiFactorAbtestGroup)

	UpdatePredictCtrMap(predictCtrConfigControlTemp, predictCtrAbtestKey, predictCtrAbtestGroup)

	UpdatePredictCvrMap(predictCvrConfigControlTemp, predictCvrAbtestKey, predictCvrAbtestGroup)

	UpdatePredictRelMap(predictRelevanceConfigControlTemp, predictRelevanceAbtestKey, predictRelevanceAbtestGroup)

	UpdateRecallMap(predictRecallConfigControlTemp, predictRecallAbtestKey, predictRecallAbtestGroup)

	UpdatePredictPriceMap(predictPriceConfigControlTemp, predictPriceAbtestKey, predictPriceAbtestGroup)

	//UpdateDishSupplementRecallMap(dishSupplementRecallControlTemp, dishSupplementRecallAbtestKey, dishSupplementRecallAbtestGroup) // 补充挂菜remove

	UpdateStoreA30ReRankMap(storeA30ReRankControlTemp, storeA30ReRankAbtestKey, storeA30ReRankAbtestGroup)

	UpdateDishAnnRecallMap(dishAnnRecallControlTemp, dishAnnRecallAbtestKey, dishAnnRecallAbtestGroup)

	UpdateListWiseGeneratorMap(listWiseGeneratorControlTemp)

	UpdateListWiseEvaluatorMap(listWiseEvaluatorControlTemp)
}

func UpdatePredictPriceMap(predictPriceConfigControlTemp map[string]*PredictPriceConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictPriceControlMap.Lock()
	AlgoApolloCfg.PredictPriceControlMap.PredictPriceControl = predictPriceConfigControlTemp
	AlgoApolloCfg.PredictPriceControlMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictPriceControlMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictPriceControlMap.Unlock()
}

func UpdateStoreA30ReRankMap(StoreA30ReRankControlTemp map[string]*StoreA30ReRankConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.StoreA30ReRankMap.Lock()
	AlgoApolloCfg.StoreA30ReRankMap.StoreA30ReRankControl = StoreA30ReRankControlTemp
	AlgoApolloCfg.StoreA30ReRankMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.StoreA30ReRankMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.StoreA30ReRankMap.Unlock()
}

func UpdateDishAnnRecallMap(DishAnnRecallControlTemp map[string]*DishAnnRecallConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.DishAnnRecallMap.Lock()
	AlgoApolloCfg.DishAnnRecallMap.DishAnnRecallControl = DishAnnRecallControlTemp
	AlgoApolloCfg.DishAnnRecallMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.DishAnnRecallMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.DishAnnRecallMap.Unlock()
}

func UpdateListWiseGeneratorMap(ListWiseGeneratorControlTemp map[string]*ListWiseGeneratorConfig) {
	AlgoApolloCfg.ListWiseGeneratorMap.Lock()
	AlgoApolloCfg.ListWiseGeneratorMap.ListWiseGeneratorControl = ListWiseGeneratorControlTemp
	defer AlgoApolloCfg.ListWiseGeneratorMap.Unlock()
}

func UpdateListWiseEvaluatorMap(ListWiseEvaluatorControlTemp map[string]*ListWiseEvaluatorConfig) {
	AlgoApolloCfg.ListWiseEvaluatorMap.Lock()
	AlgoApolloCfg.ListWiseEvaluatorMap.ListWiseEvaluatorControl = ListWiseEvaluatorControlTemp
	defer AlgoApolloCfg.ListWiseEvaluatorMap.Unlock()
}

func UpdateRecallMap(predictRecallConfigControlTemp map[string]*PredictRecallConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictRecallControlMap.Lock()
	AlgoApolloCfg.PredictRecallControlMap.PredictRecallControl = predictRecallConfigControlTemp
	AlgoApolloCfg.PredictRecallControlMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictRecallControlMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictRecallControlMap.Unlock()
}

func UpdatePredictRelMap(predictRelevanceConfigControlTemp map[string]*PredictRelevanceConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictRelevanceControlMap.Lock()
	AlgoApolloCfg.PredictRelevanceControlMap.PredictRelevanceControl = predictRelevanceConfigControlTemp
	AlgoApolloCfg.PredictRelevanceControlMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictRelevanceControlMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictRelevanceControlMap.Unlock()
}

func UpdatePredictCvrMap(predictCvrConfigControlTemp map[string]*PredictCvrConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictCvrControlMap.Lock()
	AlgoApolloCfg.PredictCvrControlMap.PredictCvrControl = predictCvrConfigControlTemp
	AlgoApolloCfg.PredictCvrControlMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictCvrControlMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictCvrControlMap.Unlock()
}

func UpdatePredictCtrMap(predictCtrConfigControlTemp map[string]*PredictCtrConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictCtrControlMap.Lock()
	AlgoApolloCfg.PredictCtrControlMap.PredictCtrControl = predictCtrConfigControlTemp
	AlgoApolloCfg.PredictCtrControlMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictCtrControlMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictCtrControlMap.Unlock()
}

func UpdateMultiFactorMap(predictConfigControlTemp map[string]*PredictMultiFactorConfig, abtestKey map[string]struct{}, abtestGroups map[string]struct{}) {
	AlgoApolloCfg.PredictMultiFactorConfigMap.Lock()
	AlgoApolloCfg.PredictMultiFactorConfigMap.PredictMultiFactorControl = predictConfigControlTemp
	AlgoApolloCfg.PredictMultiFactorConfigMap.ABTestKeyMap = abtestKey
	AlgoApolloCfg.PredictMultiFactorConfigMap.ABTestGroupMap = abtestGroups
	defer AlgoApolloCfg.PredictMultiFactorConfigMap.Unlock()
}

var recallExpConfMap map[string]map[string]float64

func GetRecallExpConf() map[string]map[string]float64 {
	mutex.RLock()
	defer mutex.RUnlock()
	return recallExpConfMap
}

func BuildTopInterventionConfigControl() {
	TopInterventionControlTemp := make(map[string]*TopInterventionConfig)
	TopInterventionAbtestKey := make(map[string]struct{})
	TopInterventionAbtestGroup := make(map[string]struct{})
	for i := range AlgoApolloCfg.TopInterventionConfigList.TopInterventionConfigs {
		p := AlgoApolloCfg.TopInterventionConfigList.TopInterventionConfigs[i]
		TopInterventionAbtestKey[p.ABTestKey] = struct{}{}
		abTestValList := strings.Split(p.ABTestVal, ",")
		for _, abTestVal := range abTestValList {
			abTestGroup := p.ABTestKey + "=" + abTestVal
			TopInterventionControlTemp[abTestGroup] = &p
			TopInterventionAbtestGroup[abTestGroup] = struct{}{}
		}
	}

	if AlgoApolloCfg.TopInterventionControlMap == nil {
		AlgoApolloCfg.TopInterventionControlMap = &TopInterventionControl{
			RWMutex: sync.RWMutex{},
		}
	}
	AlgoApolloCfg.TopInterventionControlMap.Lock()
	AlgoApolloCfg.TopInterventionControlMap.TopInterventionControl = TopInterventionControlTemp
	AlgoApolloCfg.TopInterventionControlMap.ABTestKeyMap = TopInterventionAbtestKey
	AlgoApolloCfg.TopInterventionControlMap.ABTestGroupMap = TopInterventionAbtestGroup
	defer AlgoApolloCfg.TopInterventionControlMap.Unlock()
}

func GetHitAbTestGroup(abTestGroupRequest map[string]string, abTestKeys map[string]struct{}, abTestGroups map[string]struct{}) string {
	for expKey := range abTestKeys {
		if expGroup, exitGroup := abTestGroupRequest[expKey]; exitGroup {
			keyGroup := expKey + "=" + expGroup
			if _, exitConfig := abTestGroups[keyGroup]; exitConfig {
				return keyGroup
			}
		}
	}
	for expKey := range abTestKeys {
		if _, exitGroup := abTestGroupRequest[expKey]; exitGroup {
			keyGroup := expKey + "=default"
			if _, exitConfig := abTestGroups[keyGroup]; exitConfig {
				return keyGroup
			}
		}
	}
	for expKey, _ := range abTestKeys {
		keyGroup := expKey + "=default"
		if _, exitConfig := abTestGroups[keyGroup]; exitConfig {
			return keyGroup
		}
	}
	return ""
}

func (p *PredictMultiFactorControl) GetPredictMultiFactorControl(abTestGroupMap map[string]string) *PredictMultiFactorConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictMultiFactorControl[abTestGroup]; ok {
		return config
	}
	return nil
}
func (p *PredictCtrControl) GetPredictCtrControl(abTestGroupMap map[string]string) *PredictCtrConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictCtrControl[abTestGroup]; ok {
		return config
	}
	return nil
}
func (p *PredictCvrControl) GetPredictCvrControl(abTestGroupMap map[string]string) *PredictCvrConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictCvrControl[abTestGroup]; ok {
		return config
	}
	return nil
}
func (p *PredictRelevanceControl) GetPredictRelevanceControl(abTestGroupMap map[string]string) *PredictRelevanceConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictRelevanceControl[abTestGroup]; ok {
		return config
	}
	return nil
}
func (p *PredictRecallControl) GetPredictRecallControl(abTestGroupMap map[string]string) *PredictRecallConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictRecallControl[abTestGroup]; ok {
		return config
	}
	return nil
}

func (p *ABTestBlockControl) IsHitABTestBlockList(exp string) bool {
	if p == nil {
		return false // p 为nil, 黑名单配置为空，全部保留不过滤
	}
	exp = strings.TrimSpace(exp)
	if len(exp) == 0 {
		return false
	}
	p.RLock()
	defer p.RUnlock()
	if _, ok := p.ABTestBlockMap[exp]; ok {
		return true
	}
	return false
}

func (p *TopInterventionControl) GetTopInterventionControl(abTestGroupMap map[string]string) *TopInterventionConfig {
	if p == nil {
		logkit.Error("GetTopInterventionControl: TopInterventionControl is nil")
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.TopInterventionControl[abTestGroup]; ok {
		return config
	}
	return nil
}
func (p *PredictPriceControl) GetPredictPriceControl(abTestGroupMap map[string]string) *PredictPriceConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.PredictPriceControl[abTestGroup]; ok {
		return config
	}
	return nil
}

// 补充挂菜remove
//func (p *DishSupplementRecallControl) GetDishSupplementControl(abTestGroupMap map[string]string) *DishSupplementRecallConfig {
//	if p == nil {
//		return nil
//	}
//	p.RLock()
//	defer p.RUnlock()
//	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
//	if config, ok := p.DishSupplementRecallControl[abTestGroup]; ok {
//		return config
//	}
//	return nil
//}

func (p *StoreA30ReRankControl) GetStoreA30ReRankControl(abTestGroupMap map[string]string) *StoreA30ReRankConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.StoreA30ReRankControl[abTestGroup]; ok {
		return config
	}
	return nil
}

func (p *DishAnnRecallControl) GetDishAnnRecallControl(abTestGroupMap map[string]string) *DishAnnRecallConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	abTestGroup := GetHitAbTestGroup(abTestGroupMap, p.ABTestKeyMap, p.ABTestGroupMap)
	if config, ok := p.DishAnnRecallControl[abTestGroup]; ok {
		return config
	}
	return nil
}

func (p *ListWiseGeneratorControl) GetListWiseGenerator(generatorName string) *ListWiseGeneratorConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	if config, ok := p.ListWiseGeneratorControl[generatorName]; ok {
		return config
	}
	return nil
}

func (p *ListWiseEvaluatorControl) GetListWiseEvaluator(evaluatorName string) *ListWiseEvaluatorConfig {
	if p == nil {
		return nil
	}
	p.RLock()
	defer p.RUnlock()
	if config, ok := p.ListWiseEvaluatorControl[evaluatorName]; ok {
		return config
	}
	return nil
}
