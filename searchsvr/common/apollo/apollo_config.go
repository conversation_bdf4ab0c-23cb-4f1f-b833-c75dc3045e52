package apollo

import (
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	configCenter "git.garena.com/shopee/o2o-intelligence/common/common-lib/config_center"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/pass_config"
)

const (
	group              = "foody_and_local_service_intelligence"
	project            = "food_search_and_recommend"
	SecretPrefix       = "search_search_config_secret_"
	SearchEngineModule = "o2oalgo_search_config.xml"
	SearchAlgoModule   = "food_search_algo_config.xml"
	RecallConfigModule = "o2o-search-recall-v2.json"
)

var (
	mutex sync.RWMutex
	// 搜索工程侧配置
	SearchApolloCfg    = &SearchApolloConfig{}
	callBackHandlerMap configCenter.ConfigCenterCallbackMap

	// 搜索算法配置
	AlgoApolloCfg          = &AlgoApolloConfig{}
	callAlgoBackHandlerMap configCenter.ConfigCenterCallbackMap

	// 召回配置化
	SearchRecallConf = &SearchRecallConfig{}
)

func InitApolloConfig() {
	searchEngineSecret := pass_config.GetWithoutPrefix(SecretPrefix + SearchEngineModule)
	searchAlgoSecret := pass_config.GetWithoutPrefix(SecretPrefix + SearchAlgoModule)
	recallConfigSecret := pass_config.GetWithoutPrefix(SecretPrefix + RecallConfigModule)
	// 初始化搜索工程配置
	if err := InitSearchEngineApolloConfig(group, project, SearchEngineModule, searchEngineSecret, env.Region()); err != nil {
		logkit.Error("failed to init apollo config", logkit.Err(err))
		panic(err)
	}
	// 初始化搜索算法配置
	if err := InitSearchAlgoApolloConfig(group, project, SearchAlgoModule, searchAlgoSecret, env.Region()); err != nil {
		logkit.Error("failed to init apollo config", logkit.Err(err))
		panic(err)
	}
	// 初始化召回配置
	if err := InitRecallConfigCenter(group, project, RecallConfigModule, recallConfigSecret, env.Region()); err != nil {
		logkit.Error("failed to init apollo config", logkit.Err(err))
		panic(err)
	}
	logkit.Info("all config center init success")
}
