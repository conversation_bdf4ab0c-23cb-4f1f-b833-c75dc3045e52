package apollo

import (
	"context"
	"encoding/json"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	apollo "git.garena.com/shopee/marketing/config-client"
	configCenter "git.garena.com/shopee/o2o-intelligence/common/common-lib/config_center"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/env"
)

func InitRecallConfigCenter(group, project, namespace, secret string, region env.RegionCode) error {
	// 1.water 是设置到client中的更新回调函数
	newClientParam := configCenter.NewClientParam{
		Watcher:   searchRecallChangeWatcher, // 2.监听，有更新事件触发后，会调用回调函数
		Group:     group,
		Project:   project,
		Namespace: namespace,
		Secret:    secret,
	}
	client, err := configCenter.NewConfigCenterClientWithRegion(newClientParam, region, false)
	if err != nil {
		logkit.Error("apollo.NewClient", logkit.Err(err))
		return err
	}
	if err := client.Start(); err != nil {
		logkit.Error("client.Start", logkit.Err(err))
		return err
	}
	logkit.Info("apollo client start ok")
	// 召回配置化是json, 需要单独解析，不能用公共的CreateCommonCallback(deserializationConfigFunc默认是xml解析)
	content := client.GetStringValueWithNamespace(namespace, "content", "")
	UpdateSearchRecallConfigWithContent(content)
	return nil
}

func UpdateSearchRecallConfigWithContent(content string) {
	if content == "" {
		logkit.FromContext(context.Background()).Error("RecallConfiguration UpdateSearchRecallConfigWithContent empty SearchApolloConfig")
		return
	}
	logkit.Info("RecallConfiguration UpdateSearchRecallConfigWithContent", logkit.String("content", content))

	newCfg := &SearchRecallConfig{}
	err := json.Unmarshal([]byte(content), &newCfg)
	if err != nil {
		logkit.FromContext(context.Background()).Error("RecallConfiguration UpdateSearchRecallConfigWithContent parse SearchApolloConfig fail",
			logkit.String("content", content), logkit.Err(err))
		return
	}
	mutex.Lock()
	defer mutex.Unlock()
	SearchRecallConf = newCfg
}

func searchRecallChangeWatcher(changeEvent *apollo.ChangeEvent) {
	logkit.Info("apollo config change", logkit.Any("namespace", changeEvent.Namespace))
	content := changeEvent.Changes["content"].NewValue
	UpdateSearchRecallConfigWithContent(content)
}

type RecallCommon struct {
	RecallSizeRate   float64               `json:"recallSizeRate"`
	RecallMaxSize    uint64                `json:"recallMaxSize"`
	Source           []string              `json:"source"`
	RecallTimeoutMs  uint64                `json:"recallTimeoutMs"`         // 默认超时
	IsDownGrade      bool                  `json:"isDownGrade"`             // 是否降级
	CommonFilters    []RecallQueryBoolItem `json:"commonFilters,omitempty"` // 公共的过滤条件
	CityDistanceConf *CityDistanceConfig   `json:"cityDistanceConf"`        // 每一路召回的 cityDistanceConf，距离因子配置
}

type CityDistanceConfig struct {
	Default *CityDistance           `json:"default"`
	Cities  map[string]CityDistance `json:"cities"`
}

type CityDistance struct {
	Distance        float64 `json:"distance"`
	OrderThreshold  int32   `json:"orderThreshold"`
	CurrentOrderCnt int32   `json:"currentOrderCnt"`
}

type FunctionScoreConf struct {
	ScoreMode string                `json:"scoreMode"`
	BoostMode string                `json:"boostMode"`
	Queries   []RecallQueryBoolItem `json:"queries,omitempty"`
}

type ScriptScoreConf struct {
	Script ScriptItem  `json:"script"`
	Query  *SearchConf `json:"query,omitempty"`
}

type SearchConf struct {
	MinimumShouldMatchStr *string               `json:"minimumShouldMatchStr"`
	MustQueries           []RecallQueryBoolItem `json:"mustQueries,omitempty"`
	ShouldQueries         []RecallQueryBoolItem `json:"shouldQueries,omitempty"`
	MustNotQueries        []RecallQueryBoolItem `json:"mustNotQueries,omitempty"`
	FilterQueries         []RecallQueryBoolItem `json:"filterQueries,omitempty"`
	FunctionScoreConf     *FunctionScoreConf    `json:"functionScoreConf,omitempty"` //应对 script_score:{query: {function_score: {}}, script: {}} 的情况
}

type RecallQueryBoolItem struct {
	Condition               string                `json:"condition"`
	AbKey                   string                `json:"abKey"`
	OuterMatchType          string                `json:"outerMatchType"`
	MatchType               string                `json:"matchType"`
	MinimumShouldMatchStr   *string               `json:"minimumShouldMatchStr"`
	QuerySource             string                `json:"querySource"`
	QuerySourceType         string                `json:"querySourceType"`
	QueryMode               string                `json:"queryMode"` // direct 直接赋值，比如是1,2,3； params 变量，比如是 Requests_Distance
	EsField                 string                `json:"esField"`
	EsFieldType             string                `json:"esFieldType"` // 和 QuerySourceType 一样，只是用于 es 的 field
	EsFieldMode             string                `json:"esFieldMode"` // 和 QueryMode 一样，只是用于es的 field
	TieBreaker              *float64              `json:"tieBreaker"`
	Operator                *string               `json:"operator"`
	Boost                   *float64              `json:"boost"`
	BoostMode               *string               `json:"boostMode"` // multiply=query score and function score is multiplied (default), replace=only function score is used, the query score is ignored, sum=query score and function score are added, avg=average,  max=max of query score and function score,  min=min of query score and function score
	ScoreMode               *string               `json:"scoreMode"` // multiply=scores are multiplied (default),  sum=scores are summed,  avg=scores are averaged,  fist=the first function that has a matching filter is applied,  max=maximum score is used,   min=minimum score is used
	Slop                    *int                  `json:"slop"`
	MultiMatchType          *string               `json:"multiMatchType"` // 为 multi_match定制 type: best_fields, most_fields, cross_fields， phrase， phrase_prefix， bool_prefix
	Type                    *string               `json:"type"`           // multi_match, query_string 的 type: best_fields, most_fields, cross_fields， phrase， phrase_prefix， bool_prefix
	DefaultField            *string               `json:"defaultField"`
	DefaultOperator         *string               `json:"defaultOperator"`
	Weight                  *float64              `json:"weight"`
	QueryName               *string               `json:"_name"`
	Analyzer                *string               `json:"analyzer"`            // match_bool_prefix 指定terms文本分词器，默认是用mapping阶段指定的分词器
	Fuzziness               *string               `json:"fuzziness"`           // 参数可以使查询的字段具有模糊搜索的特性， 0,1,2 表示最大可允许的莱文斯坦距离， AUTO 会根据词项的长度来产生可编辑距离 AUTO:[low],[high], 最佳实践: fuzziness 在绝大多数场合都应该设置成 AUTO
	CutoffFrequency         *float64              `json:"cutoffFrequency"`     // 查询字符串时的词项会分成低频词（更重要）和高频词（次重要）两类，像前面所说的停用词 （stop word）就属于高频词，它虽然出现频率较高，但在匹配时可能并不太相关。实际上，我们往往是想要文档能尽可能的匹配那些低频词，也就是更重要的词项。  要实现这个需求，只要在查询时配置 cutoff_frequency 参数就可以了。假设我们将 cutoff_frequency 设置成 0.01 就表示  任何词项在文档中超过 1%， 被认为是高频词  其他的词项会被认为低频词
	Lenient                 *bool                 `json:"lenient"`             // 默认值是 false ， 表示用来在查询时如果数据类型不匹配且无法转换时会报错。如果设置成 true 会忽略错误。
	FuzzyTranspositions     *bool                 `json:"fuzzyTranspositions"` // 表示编辑是否包括两个相邻字符的转置（ab → ba）。默认为true。
	MaxExpansions           *int                  `json:"maxExpansions"`       // 允许匹配的最大编辑距离，默认 50，避免在max_expansions参数中使用高值，特别是在prefix_length参数值为0的情况下。max_expansions参数中的高值会因为检查的变体数量多而导致性能差
	FuzzyRewrite            *string               `json:"fuzzyRewrite"`        // 用于重写查询的方法， constant_score（默认）, constant_score_boolean, scoring_boolean,  top_terms_blended_freqs_N, top_terms_boost_N, top_terms_N
	PrefixLength            *int                  `json:"prefixLength"`        // prefix_length 表示不能没模糊化的初始字符数。由于大部分的拼写错误发生在词的结尾，而不是词的开始，使用 prefix_length 就可以完成优化。注意 prefix_length 必须结合 fuzziness 参数使用
	ZeroTermsQuery          *string               `json:"zeroTermsQuery"`      // to be or not to be 这个短语中全部都是停止词，一过滤，就什么也没有了,得不到任何 tokens, 搜索时什么都搜不到。zero_terms_query 就是为了解决这个问题而生的。它的默认值是 none ,就是搜不到停止词（对 stop 分析器字段而言）,如果设置成 all ，它的效果就和 match_all 类似，就可以搜到了。
	Synonyms                *float64              `json:"synonyms"`            // synonyms 是指同义词，只要索引和字段中配置了同义词过滤器，match 查询是支持多词条的同义词扩展的。在应用过滤器后，解析器会对每个多次条同义词创建一个语句查询。
	Queries                 []RecallQueryBoolItem `json:"queries" `
	FunctionScore           *FunctionScore        `json:"functionScore" `
	GeoDistanceType         *string               `json:"geoDistanceType" `         // 用于 geo distance type， arc最慢但是最精确是弧形（arc）计算方式，这种方式把世界当作是球体来处理， plane平面（plane）计算方式，((("plane distance calculation")))把地球当成是平坦的。 这种方式快一些但是精度略逊；在赤道附近位置精度最好，而靠近两极则变差， sloppy_arc 如此命名，是因为它使用了 Lucene 的 SloppyMath 类。 这是一种用精度换取速度的计算方式，它使用 Haversine formula 来计算距离； 它比弧形（arc）计算方式快4~5倍, 并且距离精度达99.9%。这也是默认的计算方式。
	GeoBoundingBoxQueryType *string               `json:"geoBoundingBoxQueryType" ` // Type sets the type of executing the geo bounding box. It can be either memory or indexed. It defaults to memory.
	RangeConf               []RangeConfItem       `json:"rangeConf" `               // 用于 range 相关配置
}

type RangeConfItem struct {
	Condition       string `json:"condition"`
	QuerySource     string `json:"querySource"`
	QuerySourceType string `json:"querySourceType"`
	QueryMode       string `json:"queryMode"`
}

type FunctionScore struct {
	Functions *[]FunctionItem `json:"functions" `
	Boost     *float64        `json:"boost"`
	MaxBoost  *float64        `json:"maxBoost"`
	MinScore  *float64        `json:"minScore"`
	BoostMode *string         `json:"boostMode"` // multiply=query score and function score is multiplied (default), replace=only function score is used, the query score is ignored, sum=query score and function score are added, avg=average,  max=max of query score and function score,  min=min of query score and function score
	ScoreMode *string         `json:"scoreMode"` // multiply=scores are multiplied (default),  sum=scores are summed,  avg=scores are averaged,  fist=the first function that has a matching filter is applied,  max=maximum score is used,   min=minimum score is used
}

type FunctionItem struct {
	Filter           *RecallQueryBoolItem `json:"filter" `
	ScriptScore      *ScriptScoreItem     `json:"scriptScore" `
	RandomScore      *RandomScoreItem     `json:"randomScore" `
	FieldValueFactor *FieldValueFactor    `json:"fieldValueFactor" `
	Weight           *float64             `json:"weight"`
}

type RandomScoreItem struct {
	Seed  *float64 `json:"seed"`
	Field string   `json:"field"`
}

type FieldValueFactor struct {
	Field    string   `json:"field"`    // Field to be extracted from the document.
	Factor   *float64 `json:"factor"`   // Optional factor to multiply the field value with, defaults to 1.
	Modifier *string  `json:"modifier"` // Modifier to apply to the field value, can be one of: none, log, log1p, log2p, ln, ln1p, ln2p, square, sqrt, or reciprocal. Defaults to none
	Missing  *float64 `json:"missing"`  // Value used if the document doesn’t have that field. The modifier and factor are still applied to it as though it were read from the document.
}

type OuterFilterConf struct {
	IsNestFilterByBool bool                  `json:"isNestFilterByBool"` // 是否把 filter 包括在 bool 中
	FilterConf         []RecallQueryBoolItem `json:"filterConf"`
}

type SortConfItem struct {
	Condition       string            `json:"condition"`
	AbKey           string            `json:"abKey"`
	EsField         string            `json:"esField"`
	Order           string            `json:"order"`
	Script          *ScriptItem       `json:"script"`
	DistanceSort    *DistanceSortItem `json:"distanceSort"`
	GeoDistanceType *string           `json:"geoDistanceType" ` // 用于 geo distance type， arc最慢但是最精确是弧形（arc）计算方式，这种方式把世界当作是球体来处理， plane平面（plane）计算方式，((("plane distance calculation")))把地球当成是平坦的。 这种方式快一些但是精度略逊；在赤道附近位置精度最好，而靠近两极则变差， sloppy_arc 如此命名，是因为它使用了 Lucene 的 SloppyMath 类。 这是一种用精度换取速度的计算方式，它使用 Haversine formula 来计算距离； 它比弧形（arc）计算方式快4~5倍, 并且距离精度达99.9%。这也是默认的计算方式。
}

type DistanceSortItem struct {
	FieldName    string `json:"fieldName"`
	DistanceType string `json:"distanceType"`
}
type ScriptScoreItem struct {
	ScriptItem *ScriptItem `json:"script"`
}

type ScriptItem struct {
	Source          string             `json:"source"`
	Lang            *string            `json:"lang"`
	ScriptOrderType string             `json:"scriptOrderType"`
	Order           *string            `json:"order"`
	Params          *[]ScriptParamItem `json:"params"`
}

type ScriptParamItem struct {
	ParamKey        string `json:"paramKey"`
	QuerySource     string `json:"querySource"`
	QuerySourceType string `json:"querySourceType"`
	QueryMode       string `json:"queryMode"`
}

type StoreRecallConfig struct {
	RecallId                 string              `json:"recallId"`
	AbKey                    string              `json:"abKey"`
	RecallType               int32               `json:"recallType"`          // 待废弃：召回 type
	RecallTypeStr            string              `json:"recallTypeStr"`       // 召回 type，优先级高于 RecallType
	DishRecallCondition      string              `json:"dishRecallCondition"` // 该召回是否召菜
	RecallName               string              `json:"recallName"`
	RecallSize               uint64              `json:"recallSize"`
	RelLevelFromRecall       int                 `json:"relLevelFromRecall"` // 相关性档位透传
	RecallCondition          *string             `json:"recallCondition"`
	ReportKey                string              `json:"reportKey"`
	FunctionScoreConf        *FunctionScoreConf  `json:"functionScoreConf"`
	ScriptScoreConf          *ScriptScoreConf    `json:"scriptScoreConf"`
	SearchConf               SearchConf          `json:"searchConf"`
	OuterFilterConf          *OuterFilterConf    `json:"outerFilterConf"`
	SortConf                 []SortConfItem      `json:"sortConf"`
	Source                   *[]string           `json:"source"`
	RecallLogName            string              `json:"recallLogName"` // 用于日志打印，避免多次拼接字符串
	RewriteType              string              `json:"RewriteType"`
	RecallTimeoutMs          *uint64             `json:"recallTimeoutMs"`          // 默认超时，不配置则用公共的
	IsDownGrade              *bool               `json:"isDownGrade"`              // 是否降级，不配置则用公共的
	RecallSizeRate           *float64            `json:"recallSizeRate"`           // 召回比例，不配置则用公共的
	QueryStringMap           map[string][]string `json:"queryStringMap"`           // 每一路召回的 queryStringMap
	AggsConf                 *AggsConfig         `json:"aggsConfig"`               // 菜品召回用到的aggs配置
	RecallSuppressType       uint32              `json:"recallSuppressType"`       // 打压类型标记，0-不进行打压，1-当门店仅从此路召回时进行标记，2-当门店有从此路召回时进行标记
	PipelineTypes            []string            `json:"pipelineTypes"`            // 场景
	DataSource               string              `json:"dataSource"`               // 数据源：es、fs
	FsRecallConfig           *FsRecallConfig     `json:"fsRecallConfig"`           // 添加 FeatureServer 配置字段
	RecallPriority           int                 `json:"recallPriority"`           // 召回优先级
	VectorEngineRecallConfig *VectorEngineConfig `json:"vectorEngineRecallConfig"` // 添加 VectorEngine 配置字段

	// 过滤选项
	FilterByRelevanceScoreSwitch bool    `json:"filterByRelevanceScoreSwitch"` // 是否过滤相关性分数：PRelevanceScore
	FilterByRelevanceScore       float64 `json:"filterByRelevanceScore"`       // 是否过滤相关性分数：PRelevanceScore的阈值
	FilterByDistanceSwitch       bool    `json:"filterByDistanceSwitch"`       // 是否过滤距离
	FilterByDistance             float64 `json:"filterByDistance"`             // 是否过滤距离：距离的阈值
}

// RecallConfig 表示单个召回配置项
type FsRecallConfig struct {
	FeatureGroup       uint32   `json:"featureGroup"`       // 特征组 ID
	Tags               []uint32 `json:"tags"`               // 标签列表
	SpecialProcessType string   `json:"specialProcessType"` // 特殊处理类型，例如 "flash_sale"、"none"、"sku"等
	InputKeyType       string   `json:"inputKeyType"`       // 输入键类型，例如 "store_ids"
	ResultType         string   `json:"resultType"`         // 结果类型，例如 "dish_id"
	DataVersion        string   `json:"dataVersion"`        // 数据版本，为空则表示没有版本区分，例如："v1","v2"..
}

type VectorEngineConfig struct {
	ModelName                   string   `json:"modelName"`                   // 模型平台的模型名字，用于user embedding
	IndexName                   string   `json:"indexName"`                   // hawking平台的index 名字
	ScoreFilterThreshold        float32  `json:"scoreFilterThreshold"`        // 相似度过滤阈值
	DistanceFilterThreshold     float32  `json:"distanceFilterThreshold"`     // 距离过滤阈值（米）
	DefaultReplaceScore         float32  `json:"defaultReplaceScore"`         // 如果配置了,要替换相似度分数
	ModelPlatformFeatureKeyList []string `json:"modelPlatformFeatureKeyList"` //  模型平台，比如ctr, cvr, user_emb, geo_emb等特征的key
	IsGeneralSubCateFilter      bool     `json:"isGeneralSubCateFilter"`      // 是否需要进行泛二级类目过滤
}

func (m *StoreRecallConfig) GetRecallCondition() string {
	if m != nil && m.RecallCondition != nil {
		return *m.RecallCondition
	}
	return ""
}

type AggsConfig struct {
	AggsField                  string              `json:"aggsField"`                  // store_id
	AggsSize                   int32               `json:"aggsSize"`                   // 最多处理 1000 门店的聚合
	SubAggregationName         string              `json:"subAggregationName"`         // top_dishes
	TopHitsAggregationSize     int32               `json:"topHitsAggregationSize"`     // 返回 2 个菜品
	TopHitsAggregationSortConf []SortConfItem      `json:"topHitsAggregationSortConf"` // 菜品内排序
	AggsMaxScoreConfig         *AggsMaxScoreConfig `json:"aggsMaxScoreConfig"`         // 菜品内排序
}

type AggsMaxScoreConfig struct {
	Script  string `json:"script"`
	AggName string `json:"aggName"`
	Order   string `json:"order"`
}

type RecallConfig struct {
	Common       *RecallCommon        `json:"common"`
	StoreRecalls []*StoreRecallConfig `json:"recalls"`
	DishCommon   *RecallCommon        `json:"dishCommon"`
	DishRecalls  []*StoreRecallConfig `json:"dishRecalls"`
}

type SearchRecallConfig struct {
	SearchServerApolloConfigLock sync.RWMutex
	RecallConfig                 RecallConfig `json:"recallConfig"`
}

type MergeConfig struct {
	ExpString     string         `json:"expString"`
	ExpFunc       string         `json:"expFunc"`
	ExpParameters string         `json:"expParameters"`
	RecallMinSize map[string]int `json:"RecallMinSize"`
}
