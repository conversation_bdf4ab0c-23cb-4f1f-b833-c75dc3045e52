package metric_reporter

import (
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"go.uber.org/zap"
)

const (
	ReportClientEPSMetricName                 = "o2oalgo_search_third_part_client_eps"
	ReportSearchFreeCacheStatisticsMetricName = "o2oalgo_search_free_cache_statistics"

	// metric name
	FoodSearchDur99 = "o2oalgo_food_search_dur99"
	FoodSearchNum   = "o2oalgo_food_search_num"
	FoodSearchReq   = "o2oalgo_food_search_req"

	// report_type
	SearchReportTypeServerHandler = "server_handler"
	SearchReportTypeRpc           = "rpc" // 第三方调用
	SearchReportTypeMiddleware    = "middleware"
	SearchReportTypeHttp          = "http"
	SearchReportTypeMainPhrase    = "main_phrase" // 各主阶段
	SearchReportTypePhrase        = "phrase"      // 各子阶段
	SearchReportTypeResponse      = "response"

	reportCronErrorMetricName         = "foodalgo_metrics_cron_error_total"
	reportESSearchClientMetricName    = "foodalgo_metrics_es_search_client_total"
	reportHitRateMetricName           = "foodalgo_metrics_hit_rate"
	reportAbtestConfigErrorMetricName = "foodalgo_metrics_abtest_config_error_total"
)

var HistogramDurationRecallPageBuckets = []float64{
	0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000,
}

func ReportTotalNum(len int, v string) {
	metric_reporter.ReportHistogramWithBuckets("o2oalgo_search_recall_total_num", float64(len), HistogramDurationRecallPageBuckets, reporter.Label{
		Key: "doc",
		Val: v,
	})
}

func Report3thClientEPS(value float64, method string) {
	// qps
	err := reporter.ReportCounter(ReportClientEPSMetricName, value, reporter.Label{Key: "method", Val: method})
	if err != nil {
		logkit.Error("metric_reporter.ReportCounter() failed", logkit.Err(err), logkit.Float64("value", value),
			logkit.String("method", method))
	}
}

// 上报耗时和 QPS
func ReportDurationAndQPS(duration time.Duration, reportType, scene, method, name string) {
	metric_reporter.SearchRawReportSummary(duration, reportType, scene, method, name, "")
	metric_reporter.SearchRawReportReq(reportType, scene, method, name, "")
}

// 上报数量和QPS
func ReportItemNumAndQPS(itemNum int, reportType, scene, method, name string) {
	metric_reporter.SearchRawReportNum(float64(itemNum), reportType, scene, method, name, "")
}

// 上报最终门店、菜品和广告门店、菜品数量和QPS
func ReportFinalItemNum(storeNum, dishNum, adsStoreNum, adsDishNum int, reportType, scene, method string) {
	metric_reporter.SearchRawReportNum(float64(storeNum), reportType, scene, method, constants.FinalStoreNum, "")
	metric_reporter.SearchRawReportNum(float64(dishNum), reportType, scene, method, constants.FinalDishNum, "")
	metric_reporter.SearchRawReportNum(float64(adsStoreNum), reportType, scene, method, constants.FinalAdsStoreNum, "")
	metric_reporter.SearchRawReportNum(float64(adsDishNum), reportType, scene, method, constants.FinalAdsDishNum, "")
}

// SLA 指标统计，上报场景的无结果率, report_type="scene"时， method 和 gateway 接口一一对应
func ReportNoResultWithScene(method string, resultLen int) {
	isNoResult := "1"
	if resultLen > 0 {
		isNoResult = "0"
	}

	err := reporter.ReportCounter("o2oalgo_search_no_result", 1,
		reporter.Label{Key: "report_type", Val: "scene"},
		reporter.Label{Key: "method", Val: method},
		reporter.Label{Key: "isNoResult", Val: isNoResult})
	if err != nil {
		logkit.Error("metric_reporter.ReportCounter() failed", logkit.Err(err), logkit.String("method", method),
			logkit.String("isNoResult", isNoResult))
	}
}

// 上报handler的无结果率, report_type="handler"时， method 和handler type接口一一对应
func ReportNoResultWithHandler(method string, resultLen int) {
	isNoResult := "1"
	if resultLen > 0 {
		isNoResult = "0"
	}

	err := reporter.ReportCounter("o2oalgo_search_no_result", 1,
		reporter.Label{Key: "report_type", Val: "handler"},
		reporter.Label{Key: "method", Val: method},
		reporter.Label{Key: "isNoResult", Val: isNoResult})
	if err != nil {
		logkit.Error("metric_reporter.ReportCounter() failed", logkit.Err(err), logkit.String("method", method),
			logkit.String("isNoResult", isNoResult))
	}
}

// 上报free cache 各项指标
func ReportSearchFreeCacheStatistics(name, method string, value float64) {
	err := reporter.ReportCounter(ReportSearchFreeCacheStatisticsMetricName, value, reporter.Label{Key: "name", Val: name}, reporter.Label{Key: "method", Val: method})
	if err != nil {
		logkit.Error("metric_reporter.ReportCounter() failed", logkit.Err(err), logkit.Float64("value", value),
			logkit.String("name", name), logkit.String("method", method))
	}
}

func ReportCronJobError(value float64, method string) error {
	err := reporter.ReportCounter(reportCronErrorMetricName, value, reporter.Label{Key: "method", Val: method})
	if err != nil {
		logkit.Error("metric_reporter.ReportCronJobError() failed", logkit.Err(err), logkit.Float64("value", value),
			logkit.String("method", method))
		return err
	}

	return nil
}

func ReportESSearchClient(value float64, method string, client *string) error {
	err := reporter.ReportCounter(reportESSearchClientMetricName, value, reporter.Label{Key: "method", Val: method}, reporter.Label{Key: "client", Val: *client})
	if err != nil {
		logkit.Error("metric_reporter.ReportESSearchClient() failed", logkit.Err(err), logkit.Float64("value", value),
			logkit.String("method", method), zap.String("client", *client))
		return err
	}
	return nil
}

// 命中率上报，同时上报total & hit value
func ReportHitRateRequest(totalValue, hitValue int, method string) {
	err := reporter.ReportCounter(reportHitRateMetricName, float64(totalValue), reporter.Label{Key: "method", Val: method}, reporter.Label{Key: "type", Val: "total"})
	if err != nil {
		logkit.Error("metric_reporter.ReportHitRateRequest() failed", logkit.Err(err), logkit.Int("totalValue", totalValue),
			logkit.String("method", method))
	}
	err = reporter.ReportCounter(reportHitRateMetricName, float64(hitValue), reporter.Label{Key: "method", Val: method}, reporter.Label{Key: "type", Val: "hit"})
	if err != nil {
		logkit.Error("metric_reporter.ReportHitRateRequest() failed", logkit.Err(err), logkit.Int("hitValue", hitValue),
			logkit.String("method", method))
	}
}

// 命中率上报，仅上报total, 需要和下面配对
func ReportHitRateTotalRequest(totalValue int, method string) {
	err := reporter.ReportCounter(reportHitRateMetricName, float64(totalValue), reporter.Label{Key: "method", Val: method}, reporter.Label{Key: "type", Val: "total"})
	if err != nil {
		logkit.Error("metric_reporter.ReportHitRateRequest() failed", logkit.Err(err), logkit.Int("totalValue", totalValue),
			logkit.String("method", method))
	}
}

// 命中率上报，仅上报hit, 需要和上面配对
func ReportHitRateHitRequest(hitValue int, method string) {
	err := reporter.ReportCounter(reportHitRateMetricName, float64(hitValue), reporter.Label{Key: "method", Val: method}, reporter.Label{Key: "type", Val: "hit"})
	if err != nil {
		logkit.Error("metric_reporter.ReportHitRateRequest() failed", logkit.Err(err), logkit.Int("hitValue", hitValue),
			logkit.String("method", method))
	}
}

func ReportAbtestConfigError(value float64, method string) error {
	err := reporter.ReportCounter(reportAbtestConfigErrorMetricName, value, reporter.Label{Key: "method", Val: method})
	if err != nil {
		logkit.Error("metric_reporter.ReportAbtestConfigError() failed", logkit.Err(err), logkit.Float64("value", value),
			logkit.String("method", method))
		return err
	}

	return nil
}
