package util

import (
	"context"
	"testing"
)

func TestEvaluateScore(t *testing.T) {
	exp := "expN(listwise_ctr, w1)*expN(listwise_cvr, w2)*expN(pue, w3)*expN(relevance, w4)"
	expression := BuildExpression(context.Background(), exp)

	myMap := make(map[string]interface{})
	myMap["listwise_ctr"] = 0.025889545679092407
	myMap["listwise_cvr"] = 0.016622215509414673
	myMap["pue"] = 0.694241
	myMap["relevance"] = -0.0062328028
	myMap["w1"] = 1
	myMap["w2"] = 1.2
	myMap["w3"] = 0.1
	myMap["w4"] = 0.01

	score, _ := EvaluateScore(context.Background(), expression, myMap)
	println(score)
}
