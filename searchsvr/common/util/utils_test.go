package util

import (
	"strings"
	"testing"
)

func TestCountOverlap(t *testing.T) {
	tests := []struct {
		name     string
		tokens1  []string
		tokens2  []string
		expected int
	}{
		// 基础场景
		{
			name:     "exact_match",
			tokens1:  []string{"a", "b", "c"},
			tokens2:  []string{"a", "b", "c"},
			expected: 3,
		},
		{
			name:     "partial_overlap",
			tokens1:  []string{"apple", "banana"},
			tokens2:  []string{"banana", "orange"},
			expected: 1,
		},
		// 重复元素场景
		{
			name:     "duplicate_tokens",
			tokens1:  []string{"a", "a", "b"},
			tokens2:  []string{"a", "b", "b"},
			expected: 2, // a和b各计1次
		},
		// 顺序无关场景
		{
			name:     "different_order",
			tokens1:  []string{"cat", "dog"},
			tokens2:  []string{"dog", "cat"},
			expected: 2,
		},
		// 空输入场景
		{
			name:     "empty_first_list",
			tokens1:  []string{},
			tokens2:  []string{"a", "b"},
			expected: 0,
		},
		{
			name:     "empty_second_list",
			tokens1:  []string{"a", "b"},
			tokens2:  []string{},
			expected: 0,
		},
		// 大小写敏感场景
		{
			name:     "case_sensitive",
			tokens1:  []string{"Hello"},
			tokens2:  []string{"hello"},
			expected: 0, // 根据实现是否区分大小写
		},
		// 长列表场景
		{
			name:     "long_sequence",
			tokens1:  strings.Split("The quick brown fox jumps over the lazy dog", " "),
			tokens2:  strings.Split("A quick rabbit hops under the energetic dog", " "),
			expected: 3, // quick, the, dog
		},
		// 特殊字符场景
		{
			name:     "special_characters",
			tokens1:  []string{"hello-world", "test@123"},
			tokens2:  []string{"hello_world", "test@123"},
			expected: 1, // 只有test@123匹配
		},
		// 中文场景
		{
			name:     "chinese_tokens",
			tokens1:  []string{"苹果", "香蕉", "橘子"},
			tokens2:  []string{"香蕉", "葡萄", "苹果"},
			expected: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CountOverlap(tt.tokens1, tt.tokens2); got != tt.expected {
				t.Errorf("CountOverlap() = %v, want %v (case: %s)",
					got, tt.expected, tt.name)
			}
		})
	}
}
