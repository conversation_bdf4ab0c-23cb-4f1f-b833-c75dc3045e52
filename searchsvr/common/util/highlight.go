package util

import (
	"regexp"
)

func HighlightMatch(query, text string) [][]int {
	// 编译正则表达式
	query = regexp.QuoteMeta(query)          // 防止
	re := regexp.MustCompile(`(?i)` + query) // 使用 (?i) 来进行不区分大小写匹配

	// 查找所有匹配的位置
	indexes := re.FindAllStringSubmatchIndex(text, -1)

	//// 输出匹配的位置
	//for _, match := range indexes {
	//	fmt.Printf("Start index of matched query: %d, End index of matched query: %d\n", match[0], match[1])
	//}

	return indexes
}
