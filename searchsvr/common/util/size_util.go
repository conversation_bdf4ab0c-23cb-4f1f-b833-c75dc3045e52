package util

import (
	"math"
	"sort"

	common "git.garena.com/shopee/o2o-intelligence/common/common-lib/math"
)

var acc common.Accuracy = func() float64 { return 0.000001 }

func MaxOfFour(a, b, c, d int) int {
	max := math.MinInt32

	if a > max {
		max = a
	}
	if b > max {
		max = b
	}
	if c > max {
		max = c
	}
	if d > max {
		max = d
	}

	return max
}

// PercentileValue 计算并返回数组中指定分位数的值
func PercentileValue(stores []float64, percentile float64) float64 {
	// 0 表示不选择
	if percentile == 0 {
		return 0.0
	}

	// 处理空数组
	if len(stores) == 0 {
		return 0.0 // 或者其他默认值，取决于你的业务逻辑
	}

	// 处理非法百分位数
	if percentile < 0.0 || percentile > 1.0 {
		return 0.0 // 或者其他默认值，取决于你的业务逻辑
	}

	// 对数组进行排序
	sort.Float64s(stores)

	// 计算分位数索引
	index := int(float64(len(stores)) * percentile)

	// 如果索引超出数组范围，返回数组的最后一个元素
	if index >= len(stores) {
		return stores[len(stores)-1]
	}

	// 返回指定分位数的值
	return stores[index]
}

func IsZeroFloat32(a float32) bool {
	return acc.Equal(float64(a), 0.00)
}

func IsZeroFloat64(a float64) bool {
	return acc.Equal(a, 0.00)
}

func IsEqual(a, b float64) bool {
	return acc.Equal(a, b)
}
