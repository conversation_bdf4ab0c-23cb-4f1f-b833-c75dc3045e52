package util

import (
	"fmt"
	"testing"
	"unicode"

	"github.com/stretchr/testify/assert"
	"golang.org/x/text/unicode/norm"
)

func TestCountTermMatchScoreV1(t *testing.T) {
	query1 := StringNormalize("gà rán kfc giâ")
	query2 := StringNormalize("gà rán rán kfc")
	store1 := StringNormalize("Gà Rán KFC Tâm")
	store2 := StringNormalize("Gà Rán Rán KFC")
	assert.Equal(t, CountTermMatchScore(query1, store1), uint32(3))
	assert.Equal(t, CountTermMatchScore(query1, store2), uint32(3))
	assert.Equal(t, CountTermMatchScore(query2, store1), uint32(3))
	assert.Equal(t, CountTermMatchScore(query2, store2), uint32(4))

	query1 = StringNormalize("gà rán kfc")
	query2 = StringNormalize("gà rán kfc - le van sy")
	store1 = StringNormalize("Gà Rán KFC - Lê Văn Sỹ")
	store2 = StringNormalize("Gà Rán KFC - Lê Lai")
	assert.Equal(t, CountTermMatchScore(query1, store1), uint32(3))
	assert.Equal(t, CountTermMatchScore(query1, store2), uint32(3))
	assert.Equal(t, CountTermMatchScore(query2, store1), uint32(6))
	assert.Equal(t, CountTermMatchScore(query2, store2), uint32(4))

}

func TestCountTermMatchScoreV2(t *testing.T) {
	query := StringNormalize("trà sữa gong cha")
	store1 := StringNormalize("Trà Sữa Gong Cha - 貢茶 - Hồ Tùng Mậu")
	store2 := StringNormalize("Trà Sữa Gong Cha - 貢茶 - Saigon Centre")
	store3 := StringNormalize("Trà Sữa Gong Cha - 貢茶 - Bason")

	assert.Equal(t, CountTermMatchScore(query, store1), uint32(4))
	assert.Equal(t, CountTermMatchScore(query, store2), uint32(4))
	assert.Equal(t, CountTermMatchScore(query, store3), uint32(4))

}

func TestVNUnicodeV1(t *testing.T) {
	query1 := "Nhất Tâm - 255 Cầu Giấy" //copy, wrong
	query2 := "Nhất Tâm - 255 Cầu Giấy"    //type, right
	println(query1 == query2)              // false
	q1 := ToAscii(query1)                  // Nha301t Tam - 255 Ca300u Gia301y
	q2 := ToAscii(query2)                  // Nhat Tam - 255 Cau Giay

	println(q1 == q2) // false
	println(q1)
	println(q2)
}

func TestVNUnicodeV2(t *testing.T) {
	query1 := "Nhất" //copy, wrong
	query2 := "Nhất"  //type, right
	println(query1, query2)
	IsNormalString("ấ")

	NormalString("ấ")

}

func IsNormalString(str string) {
	println("NFC:", norm.NFC.IsNormalString(str))
	println("NFD:", norm.NFD.IsNormalString(str))
	println("NFKC:", norm.NFKC.IsNormalString(str))
	println("NFKD:", norm.NFKD.IsNormalString(str))
}

func NormalString(str string) {
	println("NFC:", norm.NFC.IsNormalString(norm.NFC.String(str)))
	println("NFD:", norm.NFD.IsNormalString(norm.NFC.String(str)))
	println("NFKC:", norm.NFKC.IsNormalString(norm.NFKC.String(str)))
	println("NFKD:", norm.NFKD.IsNormalString(norm.NFKD.String(str)))
}

func TestUnicode(t *testing.T) {
	printMnUnicode("Nhất Tâm - 255 Cầu Giấy")
	println()

	println()
	printMnUnicode("Nhất Tâm - 255 Cầu Giấy")

}

func printMnUnicode(s string) {
	for _, r := range s {
		fmt.Printf("[%c] Unicode: %U\n", r, r)
		if unicode.Is(unicode.Mn, r) { // Mn 表示非间距组合字符
			fmt.Printf("%s has an accent\n", string(r))
		}
	}
}

func TestTermThread(t *testing.T) {
	assert.Equal(t, CountTermMatchThread(""), uint32(0))
	assert.Equal(t, CountTermMatchThread("aaa"), uint32(1))
	assert.Equal(t, CountTermMatchThread("aaa bbb"), uint32(2))
	assert.Equal(t, CountTermMatchThread("aaa bbb ccc"), uint32(2))
	assert.Equal(t, CountTermMatchThread("aaa bbb ccc ddd"), uint32(3))
	assert.Equal(t, CountTermMatchThread("aaa bbb ccc ddd eee"), uint32(3))
	assert.Equal(t, CountTermMatchThread("aaa bbb ccc ddd eee fff"), uint32(4))
	assert.Equal(t, CountTermMatchThread("aaa bbb ccc ddd eee fff ggg"), uint32(5))
}

func TestToAsciiWithMapping_Default(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"kfc", "kfc"},
		{"Đ", "d"},
		{"aaa", "aaa"},
		{"đ", "d"},
		{"Đường", "duong"},
		{"đường", "duong"},
		{"áàảãạ", "aaaaa"},
		{"âấầẩẫậ", "aaaaaa"},
		{"ăắằẳẵặ", "aaaaaa"},
		{"éèẻẽẹ", "eeeee"},
		{"êếềểễệ", "eeeeee"},
		{"íìỉĩị", "iiiii"},
		{"óòỏõọ", "ooooo"},
		{"ôốồổỗộ", "oooooo"},
		{"ơớờởỡợ", "oooooo"},
		{"úùủũụ", "uuuuu"},
		{"ưứừửữự", "uuuuuu"},
		{"ýỳỷỹỵ", "yyyyy"},
		{"This is a test.", "this is a test."},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMapping(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

func TestToAsciiWithMapping_VN(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Đường", "duong"},
		{"đường", "duong"},
		{"This is a test.", "this is a test."},
		{"École", "ecole"},
		{"café", "cafe"},
		{"résumé", "resume"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMapping(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

func TestToAsciiWithMappingV2(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"café", "cafe"},
		{"résumé", "resume"},
		{"naïve", "naive"},
		{"über", "uber"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMappingV2(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMapping_TH 测试泰国字符转换为 ASCII
func TestToAsciiWithMapping_TH(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"สวัสดี", "svsd"}, // 示例转换，实际应根据具体需求调整
		{"กรุงเทพมหานคร", "krngtphmhncr"},
		{"ประเทศไทย", "prthchnd"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMapping(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMapping_MY 测试马来西亚字符转换为 ASCII
func TestToAsciiWithMapping_MY(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Selamat datang", "selamat datang"},
		{"Kedah", "kedah"},
		{"Johor", "johor"},
		{"Negeri Sembilan", "negeri sembilan"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMapping(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMapping_ID 测试印度尼西亚字符转换为 ASCII
func TestToAsciiWithMapping_ID(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Selamat pagi", "selamat pagi"},
		{"Jakarta", "jakarta"},
		{"Surabaya", "surabaya"},
		{"Bandung", "bandung"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMapping(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMappingV2_TH 测试泰国字符转换为 ASCII (V2)
func TestToAsciiWithMappingV2_TH(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"สวัสดี", "svsd"}, // 示例转换，实际应根据具体需求调整
		{"กรุงเทพมหานคร", "krngtphmhncr"},
		{"ประเทศไทย", "prthchnd"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMappingV2(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMappingV2_MY 测试马来西亚字符转换为 ASCII (V2)
func TestToAsciiWithMappingV2_MY(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Selamat datang", "selamat datang"},
		{"Kedah", "kedah"},
		{"Johor", "johor"},
		{"Negeri Sembilan", "negeri sembilan"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMappingV2(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}

// TestToAsciiWithMappingV2_ID 测试印度尼西亚字符转换为 ASCII (V2)
func TestToAsciiWithMappingV2_ID(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Selamat pagi", "selamat pagi"},
		{"Jakarta", "jakarta"},
		{"Surabaya", "surabaya"},
		{"Bandung", "bandung"},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("Input: %s", tc.input), func(t *testing.T) {
			result := ToAsciiWithMappingV2(tc.input)
			if result != tc.expected {
				t.Errorf("Expected '%s', got '%s'", tc.expected, result)
			}
		})
	}
}
