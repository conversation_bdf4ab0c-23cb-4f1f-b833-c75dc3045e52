package util

import (
	"context"
	"os"
	"strings"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
)

const (
	cid = "cid"
)

func TTLOfToday() uint64 {
	todayLast := time.Now().Format("2006-01-02") + " 23:59:59"
	todayLastTime, _ := time.ParseInLocation("2006-01-02 15:04:05", todayLast, GetLocation(os.Getenv(cid)))
	return uint64(todayLastTime.Unix() - time.Now().Unix())
}

func GetLocation(cid string) *time.Location {
	cid = strings.ToLower(cid)
	var loc *time.Location
	switch cid {
	case "id", "vn", "th":
		loc = time.FixedZone("UTC-7", 7*60*60)
	case "sg", "tw", "ph", "my":
		loc = time.FixedZone("UTC-8", 8*60*60)
	default:
		loc, _ = time.LoadLocation("Local")
	}
	return loc
}

// GetTimeZone - get time zone
func GetTimeZone(cid string) string {
	if zone, ok := map[string]string{
		"sg": "Asia/Singapore",
		"id": "Asia/Jakarta",
		"th": "Asia/Bangkok",
		"ph": "Asia/Manila",
		"tw": "Asia/Taipei",
		"my": "Asia/Kuala_Lumpur",
		"vn": "Asia/Ho_Chi_Minh",
	}[strings.ToLower(cid)]; ok {
		return zone
	}
	return "Asia/Singapore"
}

func nDaysAfter(nDays int, zeroClock bool) uint64 {
	cid := os.Getenv(cid)
	loc := GetLocation(cid)
	t := time.Now().In(loc)
	td := time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), t.Second(), t.Nanosecond(), loc)
	if zeroClock {
		td = time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc)
	}
	nDaysAfter := td.AddDate(0, 0, nDays)
	return uint64(nDaysAfter.UnixNano() / int64(time.Millisecond))
}

func GetCIDTime(timestamp uint64) time.Time {
	cid := os.Getenv(cid)
	loc := GetLocation(cid)
	t := time.Unix(0, int64(timestamp)*int64(time.Millisecond)).In(loc)
	return t
}

func GetCIDLastTime(timestamp uint64) uint64 {
	t := GetCIDTime(timestamp)
	td := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, GetLocation(os.Getenv(cid)))
	lastTimestamp := uint64(td.UnixNano()) / uint64(time.Millisecond)
	return lastTimestamp
}

func GetCIDZeroTime(timestamp uint64) uint64 {
	t := GetCIDTime(timestamp)
	td := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, GetLocation(os.Getenv(cid)))
	zeroTimestamp := uint64(td.UnixNano()) / uint64(time.Millisecond)
	return zeroTimestamp
}

func GetCIDWeekDay(timestamp uint64) uint64 {
	t := GetCIDTime(timestamp)
	convertMap := map[time.Weekday]uint64{
		time.Sunday:    1,
		time.Monday:    2,
		time.Tuesday:   3,
		time.Wednesday: 4,
		time.Thursday:  5,
		time.Friday:    6,
		time.Saturday:  7,
	}
	return convertMap[t.Weekday()]
}

func GetCIDNowSeconds(timestamp uint64) uint32 {
	cid := os.Getenv(cid)
	loc := GetLocation(cid)
	t := time.Unix(0, int64(timestamp)*int64(time.Millisecond)).In(loc)
	firstTS := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, loc)
	return uint32(t.Unix() - firstTS.Unix())
}

func IsVNNightTime(ctx context.Context, startTimeStr, endTimeStr string) bool {
	if len(startTimeStr) == 0 {
		startTimeStr = "22:00"
	}
	if len(endTimeStr) == 0 {
		endTimeStr = "06:00"
	}

	var now, nowTime, startTime, endTime time.Time
	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err == nil {
		now := time.Now().In(loc)
		nowFormat := now.Format("15:04")
		nowTime, _ = time.ParseInLocation("15:04", nowFormat, loc)

		startTime, _ = time.ParseInLocation("15:04", startTimeStr, loc)
		endTime, _ = time.ParseInLocation("15:04", endTimeStr, loc)
	} else {
		logkit.FromContext(ctx).WithError(err).Error("IsVNNightTime get VN LOCATION failed")
		now = time.Now()
		nowFormat := now.Format("15:04")
		nowTime, _ = time.Parse("15:04", nowFormat)

		startTime, _ = time.Parse("15:04", startTimeStr)
		endTime, _ = time.Parse("15:04", endTimeStr)
	}

	// when start_time and end_time in the same day, if start_time <= current_time <= end_time:  return True
	if (nowTime.Before(endTime) || nowTime.Equal(endTime)) && (nowTime.After(startTime) || nowTime.Equal(startTime)) {
		logger.MyDebug(ctx, false, "IsVNNightTime true", logkit.String("start", startTimeStr), logkit.String("end", endTimeStr))
		return true
	}
	//  when end_time in the next day, if end_time < start_time and (current_time >= start_time or current_time <= end_time): return True
	if endTime.Before(startTime) {
		if (nowTime.After(startTime) || nowTime.Equal(startTime)) || (nowTime.Before(endTime) || nowTime.Equal(endTime)) {
			logger.MyDebug(ctx, false, "IsVNNightTime true", logkit.String("start", startTimeStr), logkit.String("end", endTimeStr))
			return true
		}
	}
	logger.MyDebug(ctx, false, "IsVNNightTime false", logkit.String("start", startTimeStr), logkit.String("end", endTimeStr))
	return false
}

func ParseTime(date string, format string) (time.Time, error) {
	return time.ParseInLocation(date, format, timeutil.GetLocation(""))
}

func GetTodaySeconds() int32 {
	now := timeutil.Now()
	return int32(uint32(now/1000) - timeutil.GetTodayYYYYMMDD())
}

func GetTodaySecondsFromString(date string) int32 {
	now := timeutil.Now()
	t, err := ParseTime(timeutil.Format(now, timeutil.TimeLayout_YYYY_MM_DD)+" "+date, timeutil.TimeFmtDefault)
	if err == nil {
		return int32(t.Hour()*3600 + t.Minute()*60 + t.Second())
	} else {
		return -1
	}
}

func GetStrYYYYMMDD(timestamp uint64) string {
	if timestamp == 0 {
		timestamp = timeutil.Now()
	}
	originDay := timeutil.Timestamp2Time(timestamp) // 根据cid设置时区
	return originDay.Format(timeutil.TimeLayout_YYYY_MM_DD)
}

var globalTimeLocation *time.Location

func GetLocalTimeLocation() *time.Location {
	if globalTimeLocation == nil {
		// lazy init, 不用考虑并发, 避免反复打开文件(系统调用)
		loc, err := time.LoadLocation(timeutil.GetLocName())
		if err != nil {
			logkit.Error("lazy init, LoadLocation failed", logkit.Err(err))
		} else {
			globalTimeLocation = loc
			logkit.Info("lazy LoadLocation success")
		}
	}
	return globalTimeLocation
}

func GetLocalWeekDay() int {
	loc := GetLocalTimeLocation()
	if loc == nil {
		logkit.Error("getLocalHour failed, GetLocalTimeLocation is nil")
		return int(time.Now().Weekday())
	}

	return int(time.Now().In(loc).Weekday())
}
