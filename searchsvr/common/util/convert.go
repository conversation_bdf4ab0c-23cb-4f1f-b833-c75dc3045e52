package util

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"sync"
	"unsafe"
)

// 字节转换成整形
func BytesToInt(b []byte) int32 {
	bytesBuffer := bytes.NewBuffer(b)

	var x int32
	binary.Read(bytesBuffer, binary.BigEndian, &x)

	return x
}
func ByteToString(bytes []byte) string {
	return *(*string)(unsafe.Pointer(&bytes))
}

func ConvertUint64ListToInterfaceSlice(slice []uint64) []interface{} {
	result := make([]interface{}, len(slice))
	for i, v := range slice {
		result[i] = v
	}
	return result
}

// SyncMapToMapInt 将 sync.Map 转换为 map[string]int，类型不匹配的数据会被跳过
func SyncMapToMapInt(sm *sync.Map) map[string]int {
	m := make(map[string]int)
	if sm == nil {
		return m
	}
	sm.Range(func(key, value any) bool {
		// 安全类型断言
		k, kOk := key.(string)
		v, vOk := value.(int)
		if kOk && vOk {
			m[k] = v
		} else {
			fmt.Printf("Skipping invalid key-value pair: key=%v, value=%v\n", key, value)
		}
		return true
	})
	return m
}

// SyncMapToListString 将 sync.Map 转换为 []string，仅保留符合 string 类型的 key
func SyncMapToListString(sm *sync.Map) []string {
	l := make([]string, 0)
	if sm == nil {
		return l
	}
	sm.Range(func(key, value any) bool {
		// 安全类型断言
		if k, ok := key.(string); ok {
			l = append(l, k)
		} else {
			fmt.Printf("Skipping invalid key: %v\n", key)
		}
		return true
	})
	return l
}

func SyncMapToList(sm *sync.Map) []string {
	var result []string
	if sm == nil {
		return result
	}
	sm.Range(func(key, value any) bool {
		if k, ok := key.(string); ok {
			if v, ok := value.(string); ok {
				// 格式化为 "key: value"
				result = append(result, fmt.Sprintf("%s: %s", k, v))
			}
		}
		return true
	})
	return result
}

// DeduplicateStringSlice 对 []string 去重，保持元素首次出现的顺序
func DeduplicateStringSlice(slice []string) []string {
	if len(slice) == 0 {
		return nil
	}

	// 使用 map 记录已存在的元素（struct{} 比 bool 更节省内存）
	exists := make(map[string]struct{}, len(slice))
	result := make([]string, 0, len(slice))

	for _, s := range slice {
		if _, ok := exists[s]; !ok {
			exists[s] = struct{}{}
			result = append(result, s)
		}
	}
	return result
}
