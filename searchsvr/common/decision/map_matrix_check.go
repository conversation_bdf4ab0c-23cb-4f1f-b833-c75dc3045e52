package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

func IsNeedMapMatrix(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() != cid.VN {
		return false
	}
	if apollo.SearchApolloCfg.GEORoutingMatrixSwitch == false {
		return false
	}
	isClosed := traceInfo.AbParamClient.GetParamWithInt("Search.General.RoutingDistance.Closed", 0)
	if isClosed == 1 {
		return false
	}
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearch && traceInfo.IsVnMart {
		return false
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchFoodTotalNum {
		return false
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchStoresForAffiliate {
		return false
	}
	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
		return false
	}
	return true
}
