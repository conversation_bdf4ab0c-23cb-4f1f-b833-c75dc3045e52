package decision

import (
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsUseDishRecallV2(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchStoresWithListingDish || traceInfo.HandlerType == traceinfo.HandlerTypeSearchCollectionWithListingDish {
		isUseDishRecallV2 := traceInfo.AbParamClient.GetParamWithInt("Search.Recall.IsUseDishRecallV2", 0)
		if isUseDishRecallV2 == 1 {
			return true
		}
	}
	return false
}
