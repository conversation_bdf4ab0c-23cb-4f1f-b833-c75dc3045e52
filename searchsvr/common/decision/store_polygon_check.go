package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

func IsNeedStorePolygon(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() == cid.VN {
		return false
	}
	if apollo.SearchApolloCfg.PolygonDistanceFilter == false {
		return false
	}
	if traceInfo.IsDowngradeDataServer {
		return false
	}
	// 用户经纬度为空，不请求多边形数据
	if util.IsZeroFloat64(traceInfo.TraceRequest.Longitude) && util.IsZeroFloat64(traceInfo.TraceRequest.Latitude) {
		return false
	}
	return true
}
