package decision

import "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

func IsSelfPickupFilter(traceInfo *traceinfo.TraceInfo) bool {
	isSelfPickUp := false
	orderTypeList := traceInfo.TraceRequest.GetFilterType().GetOrderTypeFilterList()
	if len(orderTypeList) > 0 {
		for i := range orderTypeList {
			if orderTypeList[i] == 1 {
				isSelfPickUp = true
				break
			}
		}
	}
	return isSelfPickUp
}
