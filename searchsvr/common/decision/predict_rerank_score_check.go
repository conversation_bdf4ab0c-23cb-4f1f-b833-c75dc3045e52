package decision

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedPredictAndReRankScore(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
		return false
	}
	return true
}

// vn 迁移逻辑排序，当前主要用于贯穿实验base组
func IsUseOriginalReRank(traceInfo *traceinfo.TraceInfo) bool {
	if cid.IsVN() {
		if traceInfo.IsVnMart {
			return true
		}
		skipModel := traceInfo.AbParamClient.GetParamWithInt("Search.MultiFactor.UseOriginalReRank", 0)
		if skipModel == 1 {
			return true
		}
	}
	return false
}
