package decision

import (
	"git.garena.com/shopee/o2o-intelligence/shopeefoodads/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// vn 不过滤，且过滤的前提是有filling
func IsNeedStoreDishNumFilter(traceInfo *traceinfo.TraceInfo) bool {
	if cid.IsVN() {
		return false
	}
	if traceInfo.PipelineType != traceinfo.PipelineTypeSearch {
		return false
	}
	if apollo.SearchApolloCfg.FilterWithStoreDishAvailable == false {
		return false
	}
	if traceInfo.IsDowngradeDataServer {
		return false
	}
	return true
}
