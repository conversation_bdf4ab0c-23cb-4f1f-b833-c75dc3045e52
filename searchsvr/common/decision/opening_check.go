package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedVNOpeningFilling(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() != cid.VN {
		return false
	}
	if traceInfo.ShadowFlag {
		return false
	}
	if apollo.SearchApolloCfg.VNIsFilterOpening == false {
		return false
	}
	if traceInfo.TraceRequest.GetFilterType().GetIsOpening() == true {
		return true
	}
	return false
}

func IsNeedOpeningFilter(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.IsDowngradeDataServer {
		return false
	}
	if cid.IsVN() && traceInfo.TraceRequest.GetFilterType().GetIsOpening() == true {
		return true
	}
	// few result 明确需要过滤closed门店
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchFewResult {
		return true
	}
	// 主站明确需要过滤closed门店
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
		if abtest.IsMainSiteFilterClosedStores(traceInfo.AbParamClient) {
			return true
		} else {
			return false
		}
	}
	return false
}
