package decision

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStoreNormalizedScore(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.TraceRequest.GetSortType() == foodalgo_search.SearchRequest_Relevance {
		return false
	}
	return traceInfo.IsNotRelevanceTabUseNewSort
}
