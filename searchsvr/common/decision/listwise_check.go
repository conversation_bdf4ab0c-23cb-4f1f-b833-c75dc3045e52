package decision

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 仅ID站内Relevance，命中实验才执行
func IsNeedListWise(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() != cid.ID {
		return false
	}
	if traceInfo.PipelineType != traceinfo.PipelineTypeSearch {
		return false
	}
	if traceInfo.TraceRequest.GetSortType() != foodalgo_search.SearchRequest_Relevance {
		return false
	}
	isOpenLW := traceInfo.AbParamClient.GetParamWithInt("Search.LWReRank.IsOpenLW", 0)
	if isOpenLW == 1 {
		return true
	}
	return false
}

func IsUseListWiseCache(ctx context.Context, req *foodalgo_search.SearchRequest, traceInfo *traceinfo.TraceInfo) bool {
	if req.GetIsCloseCache() {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseListWiseCache: param false, close cache")
		return false
	}
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseListWiseCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if traceInfo.IsDebug {
		return false
	}
	isUseListWiseCache := IsNeedListWise(ctx, traceInfo)
	return isUseListWiseCache
}
