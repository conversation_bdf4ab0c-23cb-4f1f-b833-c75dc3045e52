package decision

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsDiffCheck(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.SearchDebugReq != nil && traceInfo.SearchDebugReq.GetTrafficFlag() == foodalgo_search.SearchDebugReq_Diff {
		return true
	}
	return false
}

func IsMockModelScore(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.SearchDebugReq != nil && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsMockModelScore() {
		logkit.FromContext(ctx).Info("skip model predict, mock score")
		return true
	}
	return false
}

func IsSkipDishFeaturesCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.SearchDebugReq != nil && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipDishFeaturesCache() {
		logkit.FromContext(ctx).Info("skip dish features cache")
		return true
	}
	return false
}

func IsSkipDishRecallCache(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.SearchDebugReq != nil && traceInfo.SearchDebugReq.GetDebugSwitch().GetIsSkipDishRecallCache() {
		logkit.FromContext(ctx).Info("skip dish recall cache")
		return true
	}
	return false
}
