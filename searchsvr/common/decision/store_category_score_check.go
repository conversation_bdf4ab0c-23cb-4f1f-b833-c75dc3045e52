package decision

import (
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStoreCategoryScore(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.PipelineType == traceinfo.PipelineTypeSearchMainSite {
		return true
	}
	return false
}

func IsNeedStoreCategoryDeboostScore(traceInfo *traceinfo.TraceInfo) bool {
	config := abtest.GetDeboostCategoryConfig(traceInfo.IsDebug, traceInfo.AbParamClient)
	if config == nil {
		return false
	}
	// 实验开启 & 符合条件的才需要进行计算category 打压分数
	if config.DeboostThreshold > 0.0 && config.BoostThreshold > 0.0 && config.TopK > 0 && traceInfo.QPResult.NerRecallResultOnlyDishNer &&
		len(traceInfo.QPResult.QueryCateIntentsManualReviewL1Ids) > 0 && len(traceInfo.QPResult.QueryCateIntentsManualReviewL2Ids) > 0 {
		return true
	}
	return false
}
