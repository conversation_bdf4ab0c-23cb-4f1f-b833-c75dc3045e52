package decision

import (
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 明确需要调用ads的场景，其他的场景不需要
func IsNeedAds(traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.IsSkipAds {
		return false
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearch {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchStoresWithListingDish {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchFood {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMart {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchFoodTotalNum {
		return true
	}
	if traceInfo.HandlerType == traceinfo.HandlerTypeSearchMartTotalNum {
		return true
	}
	return false
}
