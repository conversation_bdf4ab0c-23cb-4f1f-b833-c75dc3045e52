package decision

import "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"

func IsSupportRecallConf(handlerType traceinfo.HandlerType, searchType traceinfo.PipelineType) bool {
	switch handlerType {
	case traceinfo.HandlerTypeSearchGlobalV1, traceinfo.HandlerTypeSearchIdsStores, traceinfo.HandlerTypeSearchIdsDishes,
		traceinfo.HandlerTypeSearchIdsWeb, traceinfo.HandlerTypeSearchIdsFoody:
		return false
	}

	switch searchType {
	case traceinfo.PipelineTypeSearch:
		return true
	case traceinfo.PipelineTypeSearchCollection:
		return true
	case traceinfo.PipelineTypeSearchMainSite:
		return true
	case traceinfo.PipelineTypeSearchFewResult:
		return true
	default:
		return false
	}
}
