package decision

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStorePrice(traceInfo *traceinfo.TraceInfo) bool {
	if cid.IsVN() {
		return false
	}
	if traceInfo.IsDowngradeDataServer {
		return false
	}
	if len(traceInfo.TraceRequest.GetFilterType().GetPriceRange()) > 0 {
		return true
	}
	return false
}
