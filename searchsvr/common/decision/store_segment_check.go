package decision

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStoreSegment(traceInfo *traceinfo.TraceInfo) bool {
	if cid.IsVN() == false {
		return false
	}
	if traceInfo.IsDowngradeDataServer {
		return false
	}
	segmentBoost := traceInfo.AbParamClient.GetParamWithString("Search.MultiFactor.StoreSegmentBoostFactor", "")
	if len(segmentBoost) > 0 {
		return true
	}
	return false
}

func IsStoreSegmentBoostTopN(traceInfo *traceinfo.TraceInfo) bool {
	if cid.IsVN() {
		if traceInfo.PipelineType != traceinfo.PipelineTypeSearch {
			return false
		}
		segmentTopN := traceInfo.AbParamClient.GetParamWithInt("Search.MultiFactor.StoreSegmentBoostTopN", 0)
		if segmentTopN > 0 {
			return true
		}
	}
	return false
}
