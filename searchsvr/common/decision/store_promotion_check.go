package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStorePromotion(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() == cid.VN {
		return false
	}
	if apollo.SearchApolloCfg.DowngradeServerConfig.ClosePromotion {
		return false
	}
	if len(traceInfo.TraceRequest.GetFilterType().GetPromotionTypeFilters()) > 0 {
		return true
	}
	return false
}
