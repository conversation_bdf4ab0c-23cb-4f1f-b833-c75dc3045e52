package decision

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsUseMixProcessCache(ctx context.Context, req *foodalgo_search.SearchRequest, traceInfo *traceinfo.TraceInfo) bool {
	if traceInfo.IsDebug {
		return false
	}
	if req.GetIsCloseCache() {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseMixProcessCache: param false, close cache")
		return false
	}
	if apollo.SearchApolloCfg.DowngradeServerConfig.CloseCodis {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseMixProcessCache: DowngradeServerConfig.CloseCodis is true, close cache")
		return false
	}
	if env.GetCID() == cid.VN && apollo.SearchApolloCfg.UserCacheExpireSeconds == 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "IsUseMixProcessCache: UserCacheExpireSeconds is 0, close cache")
		return false
	}
	switch traceInfo.HandlerType {
	case traceinfo.HandlerTypeSearch:
		return true
	case traceinfo.HandlerTypeSearchFood, traceinfo.HandlerTypeSearchMart:
		return true
	case traceinfo.HandlerTypeSearchFoodTotalNum, traceinfo.HandlerTypeSearchMartTotalNum:
		return true
	case traceinfo.HandlerTypeSearchStoresWithListingDish, traceinfo.HandlerTypeSearchCollectionWithListingDish:
		return true
	default:
		return false
	}
}
