package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedStoreTags(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() == cid.VN {
		return false
	}

	if len(traceInfo.TraceRequest.GetFilterType().GetStoreTags()) > 0 {
		return true
	}
	return false
}
