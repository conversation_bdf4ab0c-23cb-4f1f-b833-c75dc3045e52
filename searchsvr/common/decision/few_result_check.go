package decision

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedFewResult(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	isSkipFewResult := traceInfo.AbParamClient.GetParamWithInt("Search.FewResult.IsSkipFewResult", 1)
	if isSkipFewResult == 1 {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("IsNeedFewResult skip")
		}
		return false
	}

	isSkipFewResultByNer := traceInfo.AbParamClient.GetParamWithInt("Search.FewResult.IsSkipFewResultByNer", 0)
	if isSkipFewResultByNer == 1 {
		if IsOnlyHitStoreNer(ctx, traceInfo) {
			if traceInfo.IsDebug {
				logkit.FromContext(ctx).Info("IsNeedFewResult skip by store ner")
			}
			return false
		}
	}

	return true
}
