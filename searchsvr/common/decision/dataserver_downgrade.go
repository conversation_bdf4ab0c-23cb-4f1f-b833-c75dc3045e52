package decision

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// InitDataServerDowngrade 第一次调用正排时初始化，并赋值给traceInfo.IsDowngradeDataServer
// 第二次可能赋值： 计算正排返回个数， 配合apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeDataServerReturnSizePercent
func InitDataServerDowngrade(traceInfo *traceinfo.TraceInfo) {
	if IsDowngrade(apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeDataServer) {
		traceInfo.IsDowngradeDataServer = true
	}
}

func InitDataServerDowngradeWithReturnSize(ctx context.Context, traceInfo *traceinfo.TraceInfo, searchStoresSize, dataServerReturnStoreSize int) {
	dataServerReturnPercent := float64(dataServerReturnStoreSize) / float64(searchStoresSize)
	DataServerReturnSizePercent := apollo.SearchApolloCfg.DowngradeServerConfig.DowngradeDataServerReturnSizePercent
	if DataServerReturnSizePercent > 0.0 && dataServerReturnPercent < DataServerReturnSizePercent {
		traceInfo.IsDowngradeDataServer = true
		logkit.FromContext(ctx).Error("data server return size not enough", zap.Int("return size", dataServerReturnStoreSize), zap.Int("doc size", searchStoresSize))
	}
}

//// 可以直接使用 traceInfo.IsDowngradeDataServer
//func IsDataServerDowngrade(traceInfo *traceinfo.TraceInfo) bool {
//	return traceInfo.IsDowngradeDataServer
//}

// IsDishMetaDowngrade 菜品正排开关：1.DishMetaSkip  2.IsDowngradeDataServer
func IsDishMetaDowngrade(traceInfo *traceinfo.TraceInfo) bool {
	if apollo.SearchApolloCfg.DishMetaSkip {
		return true
	}
	if traceInfo.IsDowngradeDataServer {
		return true
	}
	return false
}
