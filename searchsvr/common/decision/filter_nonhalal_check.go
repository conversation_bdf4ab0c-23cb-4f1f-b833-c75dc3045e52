package decision

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"time"
)

func IsFilterNonHalal(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	// 判断是否命中ab实验
	if !abtest.GetIsHideNonHalal(traceInfo.IsDebug, traceInfo.AbParamClient) {
		logkit.FromContext(ctx).Debug("IsFilterNonHalal not hit abtest")
		return false
	}

	// 判断是否在时间段内
	if apollo.SearchApolloCfg.HideNonHalalStartUnixTimeSec > 0 && apollo.SearchApolloCfg.HideNonHalalEndUnixTimeSec > 0 {
		nowSec := time.Now().Unix()
		if nowSec >= apollo.SearchApolloCfg.HideNonHalalStartUnixTimeSec && nowSec <= apollo.SearchApolloCfg.HideNonHalalEndUnixTimeSec {
			logkit.FromContext(ctx).Debug("IsFilterNonHalal need to filter",
				logkit.Int64("startUnixTimeSec", apollo.SearchApolloCfg.HideNonHalalStartUnixTimeSec),
				logkit.Int64("endUnixTimeSec", apollo.SearchApolloCfg.HideNonHalalEndUnixTimeSec), logkit.Int64("currentSec", nowSec))
			return true
		} else {
			logkit.FromContext(ctx).Debug("IsFilterNonHalal out of time range, no need to filter",
				logkit.Int64("startUnixTimeSec", apollo.SearchApolloCfg.HideNonHalalStartUnixTimeSec),
				logkit.Int64("endUnixTimeSec", apollo.SearchApolloCfg.HideNonHalalEndUnixTimeSec), logkit.Int64("currentSec", nowSec))
		}
	} else {
		logkit.FromContext(ctx).Debug("IsFilterNonHalal no config start end UnixTimeSec, no need to filter")
	}
	return false
}
