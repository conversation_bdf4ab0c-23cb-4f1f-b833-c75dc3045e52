package decision

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 明确需要调用VectorEngine的场景，其他的场景不需要

func IsOnlyHitStoreNer(ctx context.Context, traceInfo *traceinfo.TraceInfo) bool {
	isUnknown := false
	isStore := false
	isDish := false
	for _, item := range traceInfo.QPResult.NerAndRecallResult {
		for _, item2 := range item.GetNerType() {
			if item2 == qp.NERType_UNKNOWN {
				isUnknown = true
			} else if item2 == qp.NERType_STORE {
				isStore = true
			} else if item2 == qp.NERType_DISH {
				isDish = true
			}
		}
	}
	if !isUnknown && isStore && !isDish {
		if traceInfo.IsDebug {
			logkit.FromContext(ctx).Info("IsOnlyHitStoreNer hit ner protection, give-up")
		}
		return true
	}
	return false
}
