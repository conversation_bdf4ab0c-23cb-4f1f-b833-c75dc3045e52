package decision

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func IsNeedVNCityFilter(traceInfo *traceinfo.TraceInfo) bool {
	if !cid.IsVN() {
		return false
	}
	if traceInfo.HandlerType != traceinfo.HandlerTypeSearchGlobalV1 &&
		traceInfo.HandlerType != traceinfo.HandlerTypeSearchIdsWeb &&
		traceInfo.HandlerType != traceinfo.HandlerTypeSearchIdsFoody {
		return false
	}
	if traceInfo.TraceRequest.CityId <= 0 {
		return false
	}
	return true
}

func IsNeedVNDistrictFilter(traceInfo *traceinfo.TraceInfo) bool {
	if !cid.IsVN() {
		return false
	}
	if traceInfo.HandlerType != traceinfo.HandlerTypeSearchGlobalV1 &&
		traceInfo.HandlerType != traceinfo.HandlerTypeSearchIdsWeb &&
		traceInfo.HandlerType != traceinfo.HandlerTypeSearchIdsFoody {
		return false
	}
	if len(traceInfo.TraceRequest.DistrictIds) <= 0 {
		return false
	}
	return true
}
