package decision

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// 查询运费,调用 shippingfee client
func IsNeedShippingFee(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() != cid.TH {
		return false
	}
	if traceInfo.TraceRequest.GetFilterType().GetShippingFee() <= 0 && traceInfo.TraceRequest.GetFilterType().GetSlashedShippingFee() <= 0 {
		return false
	}
	return true
}

// 查询运费扣减最大优化, 调用Promotion MGetStorePromotion接口
func IsNeedMaxedShippingFeeDiscountAmount(traceInfo *traceinfo.TraceInfo) bool {
	if env.GetCID() != cid.TH {
		return false
	}
	if apollo.SearchApolloCfg.DowngradeServerConfig.ClosePromotion {
		return false
	}
	if traceInfo.TraceRequest.GetFilterType().GetSlashedShippingFee() > 0 {
		return true
	}
	return false
}
