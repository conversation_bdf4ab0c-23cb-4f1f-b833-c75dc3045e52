package decision

import (
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func GetReportHandlerString(traceInfo *traceinfo.TraceInfo) string {
	reportStr := traceInfo.HandlerType.String()
	if IsNeedVNOpeningFilling(traceInfo) {
		reportStr += "_VNOpening"
	}
	if IsNeedVNPromotion(traceInfo) {
		reportStr += "_VNPromotion"
	}
	if IsNeedPromotion(traceInfo) {
		reportStr += "_Promotion"
	}
	if IsNeedStoreTags(traceInfo) {
		reportStr += "_StoreTags"
	}
	if IsNeedShippingFee(traceInfo) {
		reportStr += "_ShippingFee"
	}
	if IsNeedStorePrice(traceInfo) {
		reportStr += "_StorePrice"
	}
	return reportStr
}
