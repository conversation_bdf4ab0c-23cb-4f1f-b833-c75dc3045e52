package traceinfo

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"github.com/micro/go-micro/metadata"
	"go.uber.org/zap"
)

func buildPublishID(ctx context.Context, publishID string, traceInfo *TraceInfo) {
	if len(publishID) > 0 {
		traceInfo.TraceRequest.PublishId = publishID
		return
	}
	// 兜底操作
	metaData, ok := metadata.FromContext(ctx)
	if ok {
		traceInfo.TraceRequest.PublishId = metaData["x-sf-publish-context-id"]
		if len(traceInfo.TraceRequest.PublishId) > 0 {
			logger.MyDebug(ctx, traceInfo.IsDebug, "Fetch Publish ID From context", zap.String("context publish id", traceInfo.TraceRequest.PublishId))
			return
		}
	}
}
