package traceinfo

import (
	"context"
	"fmt"
	"slices"
	"strconv"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/reporter"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/mlplatform"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/udp_client"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/abtest/params_v3"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	abtest2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/abtest"
)

const (
	FoodSearchAbtestProject = "foodsearch"
	CommonAbtestProject     = "foodcommon"
)

// 仅初始化加载
type ABTestGroup struct {
	abTestGroup map[string]string
	abtestStr   string // 内部使用，外部不感知
}

func (ab *ABTestGroup) GetABTestGroup(key string) string {
	if ab == nil {
		return ""
	}
	if val, ok := ab.abTestGroup[key]; ok {
		return key + "=" + val
	}
	return ""
}

func (ab *ABTestGroup) GetABTestGroupValue(key string) string {
	if ab == nil {
		return ""
	}
	if val, ok := ab.abTestGroup[key]; ok {
		return val
	}
	return ""
}

func (ab *ABTestGroup) GetABTestGroupMap() map[string]string {
	if ab == nil || ab.abTestGroup == nil {
		return map[string]string{}
	}
	return ab.abTestGroup
}

func (ab *ABTestGroup) SetABTestGroup(abTestMap map[string]string) {
	ab.abTestGroup = abTestMap
}

func (ab *ABTestGroup) ResolveABTest(abTest string) {
	params := make(map[string]string)
	if len(abTest) == 0 {
		return
	}
	keyValueStrList := strings.Split(abTest, abTestHeaderParamsSep)
	for _, keyValueStr := range keyValueStrList {
		if len(keyValueStr) == 0 || !strings.Contains(keyValueStr, abTestHeaderParamKeyValSep) {
			continue
		}
		keyValue := strings.Split(keyValueStr, abTestHeaderParamKeyValSep)
		params[strings.TrimSpace(keyValue[0])] = strings.TrimSpace(keyValue[1])
	}
	ab.abTestGroup = params
}

func (ab *ABTestGroup) GetABTestString() string {
	if ab == nil || ab.abTestGroup == nil {
		return ""
	}
	if len(ab.abtestStr) > 0 {
		return ab.abtestStr
	}
	abStr := ""
	for key, val := range ab.abTestGroup {
		if len(abStr) > 0 {
			abStr = abStr + "," + key + "=" + val
		} else {
			abStr = abStr + key + "=" + val
		}
	}
	ab.abtestStr = abStr
	return abStr
}

func buildABTest(ctx context.Context, traceInfo *TraceInfo, buyerId uint64, abtestStr string) {
	userParam := make(map[string]interface{})
	if env.GetCID() == cid.VN {
		userKey, userTag, _ := mlplatform.GetVNUserTagFromFeatureServer(ctx, buyerId)
		userParam[userKey] = userTag
	}
	tagMap := make(map[string]struct{}, 4)
	// 新用户相关
	NewUserLabel := fmt.Sprintf("ShopeeFood_New_User_Lifetime_%v", strings.ToUpper(env.GetCID()))
	expLabels := params_v3.GetTagsMap(FoodSearchAbtestProject)
	commonExpLabels := params_v3.GetTagsMap(CommonAbtestProject)
	for k, v := range commonExpLabels {
		expLabels[k] = v
	}
	if len(expLabels) > 0 {
		labelList := make([]string, 0, len(expLabels))
		for k, _ := range expLabels {
			if k == "New_User" {
				k = NewUserLabel
			}
			labelList = append(labelList, k)
		}
		ug, err := udp_client.GetUdpClient().GetUdGroups(ctx, buyerId, labelList)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("Get UserGroup err", logkit.Uint64("UserId", buyerId))
		}
		for _, each := range ug {
			key := each
			if key == NewUserLabel {
				key = "New_User"
			}
			// 上报命中的情况
			metric_reporter.ReportCounter("ABHitGroups", 1, reporter.Label{Key: "scene", Val: key}, reporter.Label{Key: "errorCode", Val: "Success"})
			userParam[key] = "1"
			tagMap[key] = struct{}{}
		}
	}
	allScene := strings.Split(apollo.SearchApolloCfg.NewAbConfig.Scene, ",")
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("buildABTest", logkit.String("allSceneStr", apollo.SearchApolloCfg.NewAbConfig.Scene), logkit.Strings("allScene", allScene))
	}
	layers := make(map[string]string)
	abtestMap := make(map[string]string)
	groupIdList := make([]uint64, 0, 20)
	for _, scene := range allScene {
		layerMap, abtestTmpMap, groupIds := abtest2.GetNewAbKeys(ctx, scene, buyerId, traceInfo.TraceRequest.PublishId, userParam)
		layers = util.MergeMaps(layers, layerMap)
		abtestMap = util.MergeMaps(abtestMap, abtestTmpMap)
		groupIdList = append(groupIdList, groupIds...)
	}

	if len(apollo.SearchApolloCfg.MultiFactorLayerId) > 0 {
		groupIdStr, ok := layers[apollo.SearchApolloCfg.MultiFactorLayerId]
		if ok {
			traceInfo.AckDumpReRankScoreSuffix = groupIdStr + "_" + env.GetCID()
		}
	}

	if len(abtestMap) == 0 {
		abtestMap = make(map[string]string)
		abtestMap["foodsch_recall"] = "group_unknown"
	}
	abtestMap = mergeABTestMap(abtestStr, abtestMap, layers, groupIdList, traceInfo.IsDebug) // 合并req上传ab实验，优先级 req > 实验平台
	traceInfo.ABTestGroupIdList = groupIdList
	traceInfo.ABTestGroup.SetABTestGroup(abtestMap)
	logger.MyDebug(ctx, traceInfo.IsDebug, "buildABTest", zap.String("abtest", traceInfo.ABTestGroup.GetABTestString()))
	traceInfo.SetLayerGroupIdAndTag(layers, tagMap)
	// 获取abtest参数
	params := params_v3.NewParamsMultiClient(FoodSearchAbtestProject, []string{CommonAbtestProject}, "Search", layers, groupIdList, tagMap, true)
	// 对于debug信息，直接替换进去
	if traceInfo.SearchDebugReq.GetAbParamsV2() != nil {
		params.SetDebugParams(traceInfo.SearchDebugReq.GetAbParamsV2())
	}
	traceInfo.AbParamClient = &abtest.SearchParamMultiClient{
		AbParamClient: params,
		SceneType:     traceInfo.PipelineType.String(),
		SortType:      traceInfo.TraceRequest.GetSortType().String(),
	}
}

func buildGroupIdList(groupIdList []uint64) string {
	strGroupIdList := make([]string, 0, len(groupIdList))
	slices.Sort(groupIdList) // 排序，保证一致性
	for _, val := range groupIdList {
		strGroupIdList = append(strGroupIdList, strconv.FormatUint(val, 10))
	}
	return strings.Join(strGroupIdList, ",")
}

// abStr 优先级更高，同样的key会直接替换
func mergeABTestMap(abStr string, abMap, layers map[string]string, groupIdList []uint64, isDebug bool) map[string]string {
	if len(abStr) > 0 {
		keyValueStrList := strings.Split(abStr, ",")
		for _, keyValueStr := range keyValueStrList {
			if len(keyValueStr) == 0 || !strings.Contains(keyValueStr, "=") {
				continue
			}
			keyValue := strings.Split(keyValueStr, "=")
			if len(keyValue) != 2 {
				continue
			}
			// 命中abtest 黑名单，跳过
			if apollo.SearchApolloCfg.ABTestBlockMap.IsHitABTestBlockList(keyValue[0]) == true {
				continue
			}
			abMap[keyValue[0]] = keyValue[1]
			// 补充请求传入的 layerid->groupid的映射关系
			groupId, err := strconv.ParseUint(keyValue[1], 10, 64)
			if err == nil {
				layers[keyValue[0]] = keyValue[1]
				groupIdList = append(groupIdList, groupId)
			}
		}
	}
	return abMap
}

func (ab *ABTestGroup) GetABTestVal(key string) string {
	if ab == nil {
		return ""
	}
	if val, ok := ab.abTestGroup[key]; ok {
		return val
	}
	return ""
}

// abKeyValues:  embedding_recall_exp=group_03,group_04,group_08;vertical_recall_exp=group_01,group_02
func (ab *ABTestGroup) IsHitAbTestMultiKey(abKeyValues string) bool {
	if ab == nil || len(abKeyValues) == 0 {
		return false
	}
	abKeyValueList := strings.Split(abKeyValues, ";")
	for _, abKeyValue := range abKeyValueList {
		abtestTmp := strings.Split(abKeyValue, "=")
		if len(abtestTmp) != 2 {
			continue
		}
		val := ab.GetABTestVal(abtestTmp[0])
		if val == "" {
			continue
		}
		abExps := strings.Split(abtestTmp[1], ",")
		for _, exp := range abExps {
			if val == exp {
				return true
			}
		}
	}
	return false
}

func (ab *ABTestGroup) AnnRecallDefaultScore(group string) bool {
	if ab == nil {
		return false
	}
	groupTmp := strings.Split(group, "=")
	if len(groupTmp) != 2 {
		return false
	}
	key := groupTmp[0]
	val := groupTmp[1]
	if val == ab.GetABTestVal(key) {
		return true
	}

	return false
}

func (ab *ABTestGroup) IsQueryNormExp() bool {
	if ab == nil || len(apollo.SearchApolloCfg.QueryNormExp) == 0 {
		return false
	}
	AbtestTmp := strings.Split(apollo.SearchApolloCfg.QueryNormExp, "=")
	if len(AbtestTmp) != 2 {
		return false
	}
	val := ab.GetABTestVal(AbtestTmp[0])
	if val == "" {
		return false
	}
	QueryNormExps := strings.Split(AbtestTmp[1], ",")
	for _, exp := range QueryNormExps {
		if val == exp {
			return true
		}
	}
	return false
}

func (ab *ABTestGroup) IsHitVNDishRecallWithRewrite() bool {
	if env.GetCID() != cid.VN {
		return false
	}
	var currentGroup = ab.GetABTestVal("foodsch_recall")
	if len(currentGroup) == 0 {
		currentGroup = "group_unknown"
	}

	var rewriteDishRecalls = strings.Split(apollo.SearchApolloCfg.VNDishWithRewriteGroups, ",")
	for _, group := range rewriteDishRecalls {
		if strings.EqualFold(group, currentGroup) {
			return true
		}
	}
	return false
}

func (ab *ABTestGroup) GetTopInterventionDictVersion() string {
	if ab == nil {
		logkit.Error("getTopInterventionDictVersion: ABTestGroup is nil")
		return ""
	}
	topInterventionControlMap := apollo.AlgoApolloCfg.TopInterventionControlMap
	if topInterventionControlMap == nil {
		logkit.Error("getTopInterventionDictVersion: topInterventionControlMap is nil")
		return ""
	}
	topInterventionConfig := topInterventionControlMap.GetTopInterventionControl(ab.GetABTestGroupMap())
	if topInterventionConfig == nil {
		return ""
	}
	return topInterventionConfig.DictVersion
}

func (ab *ABTestGroup) IsHitVNOsrmDistance() bool {
	if ab == nil {
		return false
	}
	if env.GetCID() != cid.VN {
		return false
	}
	if apollo.SearchApolloCfg.VNOsrmDistanceSwitch == 0 {
		return false
	}
	if apollo.SearchApolloCfg.VNOsrmDistanceSwitch == 100 {
		return true
	}
	abKeyGroup := strings.Split(apollo.SearchApolloCfg.VNOsrmDistanceExps, "=")
	if len(abKeyGroup) < 2 {
		return false
	}
	val := ab.GetABTestVal(abKeyGroup[0])
	if val == "" {
		return false
	}
	vnOsrmExps := strings.Split(abKeyGroup[1], ",")
	for _, exp := range vnOsrmExps {
		if val == exp {
			return true
		}
	}
	return false
}

func (ab *ABTestGroup) IsHitDishRecallConfiguration() bool {
	if ab == nil {
		return false
	}
	if apollo.SearchApolloCfg.RecallConfigurationHitDishRecallSwitch == 0 {
		return false
	}
	if apollo.SearchApolloCfg.RecallConfigurationHitDishRecallSwitch == 100 {
		return true
	}
	abKeyGroup := strings.Split(apollo.SearchApolloCfg.RecallConfigurationHitDishRecallExps, "=")
	if len(abKeyGroup) < 2 {
		return false
	}
	val := ab.GetABTestVal(abKeyGroup[0])
	if val == "" {
		return false
	}
	exps := strings.Split(abKeyGroup[1], ",")
	for _, exp := range exps {
		if val == exp {
			return true
		}
	}
	return false
}

func (ab *ABTestGroup) IsHitFoodyDeliveryExp() bool {
	if ab == nil {
		return false
	}
	if apollo.SearchApolloCfg.FoodyDeliverySwitch == 0 {
		return false
	}
	if apollo.SearchApolloCfg.FoodyDeliverySwitch == 100 {
		return true
	}
	if ab.IsHitAbTestMultiKey(apollo.SearchApolloCfg.FoodyDeliveryExp) {
		return true
	}
	return false
}
