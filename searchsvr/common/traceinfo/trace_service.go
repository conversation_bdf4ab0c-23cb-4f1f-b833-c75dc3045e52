package traceinfo

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"

	"git.garena.com/shopee/feed/comm_lib/trace"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	data_management_client "git.garena.com/shopee/o2o-intelligence/common/common-lib/data-management"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sla_code"
	vn_brand_protection_keywords "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_brand_protect"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"

	o2oalgo_queryprocess "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/platform/tracing"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"github.com/Knetic/govaluate"
	"github.com/golang/protobuf/proto"
)

const (
	abTestHeaderParamsSep      = ","
	abTestHeaderParamKeyValSep = "="
	FoodSchRecall              = "foodsch.recall" //索引和召回优化
	Default                    = "default"
	InterventionTypeTop5       = 1
	InterventionTypeTop10      = 2
	InterventionTypeTop20      = 3
	InterventionTypeTop1       = 4
	InterventionTypeFixedSlots = 5

	CategoryTypeFood = 1001
	CategoryTypeMart = 1002

	MethodDownGradeNotConfig = "method_down_grade_not_config"
	MethodDownGradeNoNeed    = "method_down_grade_no_need"
)

type RequestInfo struct {
	// request 相关
	QueryRaw         string // 请求原始词，不做任何改动
	Longitude        float64
	Latitude         float64
	AffiliateId      uint64
	NextPageToken    string
	PublishId        string // 全局唯一publish id
	PageNum          uint32
	PageSize         uint32
	StoreId          uint64 // 门店下搜索菜品
	SortType         *foodalgo_search.SearchRequest_SortType
	FilterType       *foodalgo_search.SearchRequest_FilterType
	City             string   // ID/MY city-name, VN city-id
	State            string   // TH state-name
	CityId           uint32   // VN city ID
	DistrictIds      []uint32 // VN district ids
	LocationGroupIds []string
	AppType          uint32
	FocusServiceId   uint32   // vn 旧接口需要
	FoodyServiceIds  []uint32 // vn 旧接口需要
	ExtClientInfo    *foodalgo_search.ClientDeviceInfo

	SearchTime uint64 // 搜索内部debug + 预约单时vn mart店铺内搜索菜品
}

func (r *RequestInfo) GetFilterType() *foodalgo_search.SearchRequest_FilterType {
	if r != nil {
		return r.FilterType
	}
	return nil
}

func (r *RequestInfo) GetSortType() foodalgo_search.SearchRequest_SortType {
	if r != nil && r.SortType != nil {
		return *r.SortType
	}
	return foodalgo_search.SearchRequest_Relevance
}

// 1.每个字段定义尽量不重复，语义清晰. 需用用到的地方再使用原始字段重新拼装。 尽量避免多个字段雷同，多次赋值和混淆
// 2.相关类型的变量统一定义，必要情况下可以多层嵌套
// 3.非贯穿多个流程的部分尽量单独定义，使用时临时拼装，用后废弃
type TraceInfo struct {
	mutex sync.RWMutex
	// 原始请求,不进行修改赋值。未改变的全部使用origin request, 其他会被修改的统一使用traceInfo 新定义的，例如isNeedCorrect
	//OriginRequest  *foodalgo_search.SearchRequest
	TraceRequest   *RequestInfo // 请求参数相关的，兼容适配各个场景的request
	SearchDebugReq *foodalgo_search.SearchDebugReq

	QueryKeyword                  string           // 实际使用的 keyword(小写、纠错、pk)
	QueryKeywordForDiff           string           // 实际使用的 keyword(小写、纠错、pk) // todo delete after diff
	OriginKeyword                 string           // VN新纠错使用,纠错前keyword
	CorrectKeyword                string           // 纠错后keyword
	SearchSysTime                 time.Time        // 请求时间，入口处初始化
	UserId                        uint64           // 实际用来ab分组的用户id
	IsDebug                       bool             // 是否debug 请求  traceinfo.OriginRequest.GetIsDebug()
	ShadowFlag                    bool             // 压测流量标记
	DowngradeLevel                string           // 分层流量 降级等级标签
	HandlerType                   HandlerType      // search handler 入口分类，共8种
	PipelineType                  PipelineType     // pipeline 内部流程分类，共5种
	UserContext                   *UserContext     // todo linyk3 内部字段保留在外层，使用时拼装
	ABTestGroup                   *ABTestGroup     // 保留。 ab 实验组key-value
	ABTestGroupIdList             []uint64         // 新实验平台 GroupId List,用于返回给业务端
	OptIntervention               *OptIntervention // 保留
	QPResult                      *QPResult        // 保留   没有统一赋值，在查询QP返回结果后，逐一针对字段赋值
	CorrectQPResult               *QPResult
	QPResponse                    *o2oalgo_queryprocess.GetQueryProcessingKeywordResp // todo linyk3 relevance 相关性服务调用需要用到，临时赋值，待确认是否能删除优化
	PredictConfig                 *PredictConfig                                      // 保留
	IsDowngradeDataServer         bool                                                // 是否正排降级, 控制项: 1.降级配置 2.正排返回结果数量
	UnsettledStores               *UnsettledStoreInfo                                 // 未入驻门店，从s3文件根据keyword获取, 保留或者放到internal request中.  不是贯穿全文，可以考虑中间
	RecallConfigurationDataParams map[string]interface{}                              // 召回配置化的，当前请求的数据集
	RecallConfigurationDataSource map[string]DataSourceItem                           // 召回配置化的，当前请求的数据集
	PhraseTimeCost                map[CostPhrase]time.Duration                        // 记录各个阶段的耗时
	PhraseStoreLength             map[string]int                                      // 记录各个阶段的门店数量
	PhraseStoreInfos              map[string][]string                                 // 记录各个阶段的门店的ids,scores
	PhraseStoreESDsl              map[string]string                                   // 记录各个阶段的es store dsl，如果是来自 es 召回的话
	PhraseDishESDsl               map[string]string                                   // 记录各个阶段的es dish dsl，如果是来自 es 召回的话
	PhraseOrderESDsl              *sync.Map                                           // 记录各个阶段的es dish dsl，如果是来自 es 召回的话
	FilteredStores                map[string][]*FilteredStore
	DistanceLimit                 uint32 // 请求级别的距离限制
	SceneId                       uint32

	RspCorrectedType foodalgo_search.SearchResponse_CorrectedType

	// 流程开关、配置类
	IsNeedCorrect               bool   // 纠错
	IsCorrected                 bool   // response 中,是否返回纠错信息, 兼容新老纠错逻辑(VN 新纠错逻辑), QP返回有纠错词,默认为 true. 在 VN 新逻辑中, 可能会改成 false
	IsPredefineKeyword          bool   //
	PredefineKeyword            string // 替换后的值
	IsSkipPredefine             bool   // 是否跳过 PredefineKeyword， debug 功能
	IsSkipIntervention          bool   // 是否跳过干预， debug 功能
	IsSkipModel                 bool
	IsSkipAds                   bool
	IsNeedEsExplain             bool            // 是否debug 请求  下需要 es explain
	EsExplainStoreIds           map[uint64]bool // debug 模式下，需要 es explain 的门店列表
	SearchESClient              string          // "es-client-1", "es-client-2"
	StoreIndexIndexAlias        string
	DishIndexIndexAlias         string
	HistoryOrderIndexIndexAlias string
	IsHitMixResultCache         bool
	IsStoreTopHit               bool
	IsHitBrandProtection        bool // 是否命中品牌保护
	StoreTopKeyword             string
	RecallMaxSize               int `json:"recallMaxSize"` // 召回配置初始化时赋值
	//IsDishSupplement            bool // 补充挂菜remove
	RecallPriorityConfig *apollo.RecallPriorityConfig
	SortPriorityConfig   *apollo.SortPriorityConfig

	DebugInfo                       *foodalgo_search.SearchDebugInfo // todo linyk3 debug info 融合
	RecallsStoreHitAbtest           []string                         // 记录hit abtest 召回的路
	RecallsStoreNotHitAbtest        []string                         // 记录not-hit abtest 召回的路
	RecallsStoreFinal               []string                         // 记录符合 condition 召回的路
	RecallsDishHitAbtest            []string                         // 记录hit abtest 召回的路 for dish 需要并发竞争
	RecallsDishNotHitAbtest         []string                         // 记录not-hit abtest 召回的路 for dish 需要并发竞争
	RecallsDishFinal                []string                         // 记录符合 condition 召回的路 for dish 需要并发竞争
	RecallsDishConditionOkButGiveUp []string                         // 记录符合 condition 召回的路，但是因为只能拿一路导致放弃 for dish
	RecallsOrdersFinal              []string                         // 记录符合 condition 召回的路

	AckDumpReRankScoreSuffix string `json:"ack_dump_re_rank_score_suffix"`
	// 模型相关
	CtrModelInfo         apollo.ModelInfo               `json:"ctr_model_info"`
	CvrModelInfo         apollo.ModelInfo               `json:"cvr_model_info"`
	RelModelInfo         apollo.ModelInfo               `json:"rel_model_info"`
	UEModelInfo          apollo.ModelInfo               `json:"ue_model_info"`
	DFModelInfo          apollo.ModelInfo               `json:"df_model_info"`
	LtrModelInfo         apollo.ModelInfo               `json:"ltr_model_info"`
	Expression           *govaluate.EvaluableExpression `json:"-"` // 每个门店都是一样的，直接保持到traceInfo一份
	CoarseSortExpression *govaluate.EvaluableExpression `json:"-"` // 粗排分数计算表达式
	ContextFeatureBytes  []byte                         `json:"-"` // contextFeature 序列化
	ContextFeature       *food.ContextFeature           `json:"-"` // 模型预估的context特征

	// ATP
	ATP                      int
	StoreCntOfL2Category     map[uint32]uint32
	MetaPool                 *data_management_client.MetaPool
	AbParamClient            *abtest.SearchParamMultiClient // 所有ab参数的集合
	LayerList                []*o2oalgo_queryprocess.AbtestLayer
	TagList                  []string
	ReserveTimeSec           uint64 // 预约时间
	From                     int32  // 缓存和ES 翻页
	IsReRankSkipPriorityFlag bool   //用来标记是否需要在优先级截断之后，再进行一次忽略优先级的重排序
	CoarseModelName          string
	CoarseUserEmbedding      []float64 // 粗排 user embedding
	SlaCode                  *sla_code.SlaCode
	EmbeddingSuppressRule    []string
	EvaluateConf             *apollo.ListWiseEvaluatorConfig `json:"EvaluateConf"`

	// 佣金相关
	StoreCommPlans []uint64
	DishCommPlans  []uint64
	PlanCommRate   map[uint64]uint32

	// 非 relevance tab 页排序实验
	IsNotRelevanceTabUseNewSort bool

	IsVnMart      bool
	IsDishListing bool
}

func (t *TraceInfo) SetLayerGroupIdAndTag(layer map[string]string, tagMap map[string]struct{}) {
	t.LayerList = make([]*o2oalgo_queryprocess.AbtestLayer, 0, len(layer))
	for key, val := range layer {
		t.LayerList = append(t.LayerList, &o2oalgo_queryprocess.AbtestLayer{LayerId: proto.String(key), GroupId: proto.String(val)})
	}
	t.TagList = make([]string, 0, len(tagMap))
	for key, _ := range tagMap {
		t.TagList = append(t.TagList, key)
	}
}

func (t *TraceInfo) MergeTraceInfo(newTraceInfo *TraceInfo, newType string) {
	if newTraceInfo == nil {
		return
	}
	for key, val := range newTraceInfo.FilteredStores {
		t.FilteredStores[newType+key] = val
	}

	for key, val := range newTraceInfo.PhraseStoreESDsl {
		newKey := fmt.Sprintf("%s_%s", newType, key)
		t.PhraseStoreESDsl[newKey] = val
	}

	for key, val := range newTraceInfo.PhraseDishESDsl {
		newKey := fmt.Sprintf("%s_%s", newType, key)
		t.PhraseDishESDsl[newKey] = val
	}

	for key, val := range newTraceInfo.PhraseStoreInfos {
		newKey := fmt.Sprintf("%s_%s", newType, key)
		t.PhraseStoreInfos[newKey] = val
	}

	for key, val := range newTraceInfo.PhraseStoreLength {
		newKey := fmt.Sprintf("%s_%s", newType, key)
		t.PhraseStoreLength[newKey] = val
	}

	for _, val := range newTraceInfo.RecallsStoreHitAbtest {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsStoreHitAbtest = append(t.RecallsStoreHitAbtest, newVal)
	}

	for _, val := range newTraceInfo.RecallsStoreNotHitAbtest {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsStoreNotHitAbtest = append(t.RecallsStoreNotHitAbtest, newVal)
	}

	for _, val := range newTraceInfo.RecallsStoreFinal {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsStoreFinal = append(t.RecallsStoreFinal, newVal)
	}

	for _, val := range newTraceInfo.RecallsDishHitAbtest {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsDishHitAbtest = append(t.RecallsDishHitAbtest, newVal)
	}

	for _, val := range newTraceInfo.RecallsDishNotHitAbtest {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsDishNotHitAbtest = append(t.RecallsDishNotHitAbtest, newVal)
	}

	for _, val := range newTraceInfo.RecallsDishFinal {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsDishFinal = append(t.RecallsDishFinal, newVal)
	}

	for _, val := range newTraceInfo.RecallsDishConditionOkButGiveUp {
		newVal := fmt.Sprintf("%s_%s", newType, val)
		t.RecallsDishConditionOkButGiveUp = append(t.RecallsDishConditionOkButGiveUp, newVal)
	}

	for key, val := range newTraceInfo.PhraseTimeCost {
		newKey := fmt.Sprintf("%s_%s", newType, key)
		t.PhraseTimeCost[CostPhrase(newKey)] = val
	}

}

func NewTraceInfo() *TraceInfo {
	t := &TraceInfo{
		TraceRequest:                    &RequestInfo{},
		SearchSysTime:                   time.Now(),
		UserContext:                     &UserContext{},
		OptIntervention:                 &OptIntervention{},
		QPResult:                        &QPResult{OtherSegments: &OtherSegments{}},
		CorrectQPResult:                 &QPResult{OtherSegments: &OtherSegments{}},
		PredictConfig:                   &PredictConfig{},
		ABTestGroup:                     &ABTestGroup{abTestGroup: make(map[string]string)},
		PhraseTimeCost:                  make(map[CostPhrase]time.Duration, 0),
		PhraseStoreLength:               make(map[string]int),
		PhraseStoreESDsl:                make(map[string]string),
		PhraseDishESDsl:                 make(map[string]string),
		PhraseOrderESDsl:                &sync.Map{},
		PhraseStoreInfos:                make(map[string][]string),
		FilteredStores:                  make(map[string][]*FilteredStore),
		RecallsStoreHitAbtest:           make([]string, 0),
		RecallsStoreNotHitAbtest:        make([]string, 0),
		RecallsStoreFinal:               make([]string, 0),
		RecallsOrdersFinal:              make([]string, 0),
		RecallsDishHitAbtest:            make([]string, 0),
		RecallsDishNotHitAbtest:         make([]string, 0),
		RecallsDishFinal:                make([]string, 0),
		RecallsDishConditionOkButGiveUp: make([]string, 0),
		SlaCode:                         sla_code.NewSlaCode(),
	}
	return t
}

func BuildTraceInfo(ctx context.Context, req *foodalgo_search.SearchRequest, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	//traceInfo.OriginRequest = req
	traceInfo.TraceRequest.PublishId = req.GetPublishId()
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
	traceInfo.TraceRequest.Longitude = float64(req.GetLongitude())
	traceInfo.TraceRequest.Latitude = float64(req.GetLatitude())
	traceInfo.TraceRequest.PageNum = req.GetPageNum()
	traceInfo.TraceRequest.PageSize = req.GetPageSize()
	traceInfo.TraceRequest.SortType = req.GetSortType().Enum()
	traceInfo.TraceRequest.FilterType = req.GetFilterType()
	traceInfo.TraceRequest.SearchTime = req.GetSearchTime()
	traceInfo.TraceRequest.CityId = req.GetCityId()
	traceInfo.TraceRequest.DistrictIds = req.GetDistrictIds()
	traceInfo.TraceRequest.LocationGroupIds = req.GetLocationGroupIds()
	traceInfo.DistanceLimit = constants.MaxDeliveryDistance // 仅vn 会查询peak mode api 修改
	traceInfo.TraceRequest.AppType = req.GetAppType()
	traceInfo.TraceRequest.FocusServiceId = req.GetFocusServiceId()
	traceInfo.TraceRequest.FoodyServiceIds = req.GetVnPromotionParams().GetFoodyServiceIds()
	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()
	traceInfo.SceneId = req.GetSceneId()

	traceInfo.UserId = req.GetBuyerId()
	traceInfo.IsNeedCorrect = req.GetNeedRewrite()
	traceInfo.IsDebug = req.GetIsDebug() || req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	if traceInfo.SearchDebugReq == nil {
		traceInfo.SearchDebugReq = &foodalgo_search.SearchDebugReq{}
	}
	if traceInfo.SearchDebugReq.DebugSwitch == nil && req.GetDebugSwitch() != nil {
		traceInfo.SearchDebugReq.DebugSwitch = req.GetDebugSwitch()
	}

	traceInfo.ShadowFlag = isShadow
	traceInfo.HandlerType = handlerType

	buildUserContext(ctx, req, traceInfo)
	abTest := req.GetAbTest()
	if len(req.GetSearchDebugReq().GetAbTest()) > 0 {
		abTest = req.GetSearchDebugReq().GetAbTest()
	}
	buildABTest(ctx, traceInfo, req.GetBuyerId(), abTest)
	req.AbTest = proto.String(traceInfo.ABTestGroup.GetABTestString())
	traceInfo.AddPredictConfigToTraceInfo()
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000, FlashSaleDishDiscountEnable: true, FlashSaleDishDiscountBufSize: 1024})
	if traceInfo.IsDebug == true && req.GetDebugSwitch() != nil {
		traceInfo.IsSkipModel = req.GetDebugSwitch().GetIsSkipModel()
		traceInfo.IsSkipAds = req.GetDebugSwitch().GetIsSkipAds()
		if len(req.GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
			traceInfo.IsNeedEsExplain = true
			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetDebugSwitch().GetEsExplainStoreIds()))
			for _, storeId := range req.GetDebugSwitch().GetEsExplainStoreIds() {
				traceInfo.EsExplainStoreIds[storeId] = true
			}
		}
	}
	if req.GetDowngrade() != nil {
		downgrade := req.GetDowngrade()
		level := ""
		l1 := downgrade.GetMethodDownGrade_1()
		l2 := downgrade.GetMethodDownGrade_2()
		l3 := downgrade.GetMethodDownGrade_3()
		l4 := downgrade.GetMethodDownGrade_4()
		if l1 != "" && l1 != MethodDownGradeNoNeed && l1 != MethodDownGradeNotConfig {
			level = l1
		} else if l2 != "" && l2 != MethodDownGradeNoNeed && l2 != MethodDownGradeNotConfig {
			level = l2
		} else if l3 != "" && l3 != MethodDownGradeNoNeed && l3 != MethodDownGradeNotConfig {
			level = l3
		} else if l4 != "" && l4 != MethodDownGradeNoNeed && l4 != MethodDownGradeNotConfig {
			level = l4
		}
		traceInfo.DowngradeLevel = level
	}

	// 品牌保护
	if cid.IsVN() {
		traceInfo.IsHitBrandProtection = vn_brand_protection_keywords.GetVnBrandProtectionKeywords(traceInfo.QueryKeyword)
		if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == 1002 {
			traceInfo.IsVnMart = true
		}
	}
	return traceInfo
}

func BuildTraceInfoForMainSite(ctx context.Context, req *foodalgo_search.SearchRequest, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	//traceInfo.OriginRequest = req
	traceInfo.TraceRequest.PublishId = req.GetPublishId()
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
	traceInfo.TraceRequest.Longitude = float64(req.GetLongitude())
	traceInfo.TraceRequest.Latitude = float64(req.GetLatitude())
	traceInfo.TraceRequest.PageNum = req.GetPageNum()
	traceInfo.TraceRequest.PageSize = req.GetPageSize()
	traceInfo.TraceRequest.SortType = req.GetSortType().Enum()
	traceInfo.TraceRequest.FilterType = req.GetFilterType()
	traceInfo.TraceRequest.SearchTime = req.GetSearchTime()
	traceInfo.TraceRequest.CityId = req.GetCityId()
	traceInfo.TraceRequest.DistrictIds = req.GetDistrictIds()
	traceInfo.TraceRequest.LocationGroupIds = req.GetLocationGroupIds()
	traceInfo.TraceRequest.AppType = req.GetAppType()
	traceInfo.TraceRequest.FocusServiceId = req.GetFocusServiceId()
	traceInfo.TraceRequest.FoodyServiceIds = req.GetVnPromotionParams().GetFoodyServiceIds()
	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()
	traceInfo.SceneId = req.GetSceneId()

	if apollo.SearchApolloCfg.MainSiteVNRoutingDistanceLimit == 0 {
		traceInfo.DistanceLimit = constants.MaxDeliveryDistanceForMainSite // 主站默认只要20km
	} else {
		traceInfo.DistanceLimit = uint32(apollo.SearchApolloCfg.MainSiteVNRoutingDistanceLimit)
	}

	traceInfo.UserId = req.GetBuyerId()
	traceInfo.IsNeedCorrect = req.GetNeedRewrite()
	traceInfo.IsDebug = req.GetIsDebug() || req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	if traceInfo.SearchDebugReq == nil {
		traceInfo.SearchDebugReq = &foodalgo_search.SearchDebugReq{}
	}
	if traceInfo.SearchDebugReq.DebugSwitch == nil && req.GetDebugSwitch() != nil {
		traceInfo.SearchDebugReq.DebugSwitch = req.GetDebugSwitch()
	}

	traceInfo.ShadowFlag = isShadow
	traceInfo.HandlerType = handlerType

	buildUserContext(ctx, req, traceInfo)
	abTest := req.GetAbTest()
	if len(req.GetSearchDebugReq().GetAbTest()) > 0 {
		abTest = req.GetSearchDebugReq().GetAbTest()
	}
	buildABTest(ctx, traceInfo, req.GetBuyerId(), abTest)
	req.AbTest = proto.String(traceInfo.ABTestGroup.GetABTestString())
	traceInfo.AddPredictConfigToTraceInfo()
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000, FlashSaleDishDiscountEnable: true, FlashSaleDishDiscountBufSize: 1024})
	if traceInfo.IsDebug == true && req.GetDebugSwitch() != nil {
		traceInfo.IsSkipModel = req.GetDebugSwitch().GetIsSkipModel()
		traceInfo.IsSkipAds = req.GetDebugSwitch().GetIsSkipAds()
		if len(req.GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
			traceInfo.IsNeedEsExplain = true
			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetDebugSwitch().GetEsExplainStoreIds()))
			for _, storeId := range req.GetDebugSwitch().GetEsExplainStoreIds() {
				traceInfo.EsExplainStoreIds[storeId] = true
			}
		}
	}
	if req.GetDowngrade() != nil {
		downgrade := req.GetDowngrade()
		level := ""
		l1 := downgrade.GetMethodDownGrade_1()
		l2 := downgrade.GetMethodDownGrade_2()
		l3 := downgrade.GetMethodDownGrade_3()
		l4 := downgrade.GetMethodDownGrade_4()
		if l1 != "" && l1 != MethodDownGradeNoNeed && l1 != MethodDownGradeNotConfig {
			level = l1
		} else if l2 != "" && l2 != MethodDownGradeNoNeed && l2 != MethodDownGradeNotConfig {
			level = l2
		} else if l3 != "" && l3 != MethodDownGradeNoNeed && l3 != MethodDownGradeNotConfig {
			level = l3
		} else if l4 != "" && l4 != MethodDownGradeNoNeed && l4 != MethodDownGradeNotConfig {
			level = l4
		}
		traceInfo.DowngradeLevel = level
	}

	// 品牌保护
	if cid.IsVN() {
		traceInfo.IsHitBrandProtection = vn_brand_protection_keywords.GetVnBrandProtectionKeywords(traceInfo.QueryKeyword)
		if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == 1002 {
			traceInfo.IsVnMart = true
		}
	}
	return traceInfo
}

func BuildSearchStoresForAffiliateTraceInfo(ctx context.Context, req *foodalgo_search.SearchStoresAffiliateReq, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	traceInfo.IsDebug = req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	traceInfo.ShadowFlag = isShadow
	traceInfo.HandlerType = handlerType

	// 请求参数初始化
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
	traceInfo.TraceRequest.AffiliateId = req.GetAffiliateId()
	traceInfo.UserId = req.GetAffiliateId()

	traceInfo.TraceRequest.NextPageToken = req.GetNextPageToken()
	pubId, pageNum := util.GetTokenParseElem(ctx, req.GetNextPageToken())
	traceInfo.TraceRequest.PublishId = pubId
	traceInfo.TraceRequest.PageNum = pageNum
	traceInfo.TraceRequest.PageSize = req.GetPageSize()

	traceInfo.TraceRequest.SortType = req.GetSortType().Enum()
	traceInfo.TraceRequest.FilterType = req.GetFilterType()
	traceInfo.TraceRequest.City = req.GetCity()
	traceInfo.TraceRequest.State = req.GetState()
	traceInfo.TraceRequest.AppType = req.GetAppType()
	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()
	if cid.IsVN() {
		cityId, err := strconv.Atoi(req.GetCity())
		if err == nil {
			traceInfo.TraceRequest.CityId = uint32(cityId)
		}
		traceInfo.IsHitBrandProtection = vn_brand_protection_keywords.GetVnBrandProtectionKeywords(traceInfo.QueryKeyword)
	}
	buildUserContextForAffiliate(ctx, req, traceInfo)
	buildABTest(ctx, traceInfo, req.GetAffiliateId(), req.GetSearchDebugReq().GetAbTest())
	traceInfo.AddPredictConfigToTraceInfo()
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000})
	return traceInfo
}

func BuildSearchDishesForAffiliateTraceInfo(ctx context.Context, req *foodalgo_search.SearchDishesAffiliateReq, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	traceInfo.IsDebug = req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	traceInfo.ShadowFlag = isShadow
	traceInfo.QueryKeyword = req.GetKeyword()
	traceInfo.HandlerType = handlerType

	// 请求参数初始化
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等

	traceInfo.TraceRequest.NextPageToken = req.GetNextPageToken()
	pubId, pageNum := util.GetTokenParseElem(ctx, req.GetNextPageToken())
	traceInfo.TraceRequest.PublishId = pubId
	traceInfo.TraceRequest.PageNum = pageNum
	traceInfo.TraceRequest.PageSize = req.GetPageSize()

	traceInfo.TraceRequest.AffiliateId = req.GetAffiliateId()
	traceInfo.UserId = req.GetAffiliateId()
	traceInfo.TraceRequest.StoreId = req.GetStoreId()
	traceInfo.TraceRequest.AppType = req.GetAppType()
	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()

	buildABTest(ctx, traceInfo, req.GetAffiliateId(), req.GetSearchDebugReq().GetAbTest())
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000})
	if traceInfo.IsDebug == true && req.GetSearchDebugReq().GetDebugSwitch() != nil {
		traceInfo.IsSkipModel = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipModel()
		traceInfo.IsSkipAds = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipAds()
		if len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
			traceInfo.IsNeedEsExplain = true
			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()))
			for _, storeId := range req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds() {
				traceInfo.EsExplainStoreIds[storeId] = true
			}
		}
	}
	return traceInfo
}

func BuildSSearchStoresWithListingDishTraceInfo(ctx context.Context, req *foodalgo_search.SearchStoresWithListingDishReq, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	traceInfo.IsDebug = req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	traceInfo.ShadowFlag = isShadow
	traceInfo.HandlerType = handlerType

	// 请求参数初始化
	traceInfo.IsDishListing = true
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
	traceInfo.TraceRequest.Longitude = req.GetLongitude()
	traceInfo.TraceRequest.Latitude = req.GetLatitude()

	traceInfo.TraceRequest.NextPageToken = req.GetNextPageToken()
	pubId, pageNum := util.GetTokenParseElem(ctx, req.GetNextPageToken())
	traceInfo.TraceRequest.PublishId = pubId
	traceInfo.TraceRequest.PageNum = pageNum
	traceInfo.TraceRequest.PageSize = req.GetPageSize()

	traceInfo.TraceRequest.SortType = req.GetSortType().Enum()
	traceInfo.TraceRequest.FilterType = req.GetFilterType()
	traceInfo.IsNeedCorrect = req.GetIsNeedCorrected()
	traceInfo.TraceRequest.LocationGroupIds = req.GetLocationGroupIds()
	traceInfo.DistanceLimit = constants.MaxDeliveryDistance // 仅vn 会查询peak mode api 修改
	traceInfo.TraceRequest.AppType = req.GetAppType()
	traceInfo.TraceRequest.ExtClientInfo = req.GetClientExtraInfo()
	traceInfo.TraceRequest.City = req.GetCity()
	if cid.IsVN() {
		if len(req.GetFilterType().GetCategoryType()) == 1 && req.GetFilterType().GetCategoryType()[0] == 1002 {
			traceInfo.IsVnMart = true
		}
		cityId, err := strconv.Atoi(req.GetCity())
		if err == nil {
			traceInfo.TraceRequest.CityId = uint32(cityId)
		}
		traceInfo.IsHitBrandProtection = vn_brand_protection_keywords.GetVnBrandProtectionKeywords(traceInfo.QueryKeyword)
	}
	buildUserContextForStoreListing(ctx, req, traceInfo)
	buyerId := req.GetShopeeUid()
	// vn shopee app 使用 shopee uid 进行分组
	if cid.IsVN() {
		buyerId = req.GetNowUid()
		if req.GetUidType() == 1 && req.GetShopeeUid() > 0 {
			buyerId = req.GetShopeeUid()
		}
	}
	traceInfo.UserId = buyerId
	buildABTest(ctx, traceInfo, buyerId, req.GetSearchDebugReq().GetAbTest())
	traceInfo.AddPredictConfigToTraceInfo() // 后续优化，单独查询配置
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000})
	if traceInfo.IsDebug == true && req.GetSearchDebugReq().GetDebugSwitch() != nil {
		traceInfo.IsSkipModel = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipModel()
		traceInfo.IsSkipAds = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipAds()
		if len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
			traceInfo.IsNeedEsExplain = true
			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()))
			for _, storeId := range req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds() {
				traceInfo.EsExplainStoreIds[storeId] = true
			}
		}
	}
	return traceInfo
}

func BuildSSearchDishRecallTraceInfo(ctx context.Context, req *foodalgo_search.DishRecallReq, handlerType HandlerType) *TraceInfo {
	spanContext := tracing.GetSpanContext(ctx)
	isShadow := tracing.IsSpanContextShadow(spanContext)

	traceInfo := NewTraceInfo()
	traceInfo.IsDebug = req.GetSearchDebugReq().GetTrafficFlag() == foodalgo_search.SearchDebugReq_Debug
	traceInfo.SearchDebugReq = req.GetSearchDebugReq()
	traceInfo.ShadowFlag = isShadow
	traceInfo.HandlerType = handlerType

	// 请求参数初始化
	traceInfo.IsDishListing = true
	traceInfo.TraceRequest.QueryRaw = req.GetKeyword() // 最原始keyword，后续不会改动
	traceInfo.QueryKeyword = req.GetKeyword()          // 实际搜索词，可能有多处修改，例如predefine/纠错/ascii mapping 等等
	traceInfo.TraceRequest.Longitude = req.GetLongitude()
	traceInfo.TraceRequest.Latitude = req.GetLatitude()
	buyerId := req.GetUserId()

	traceInfo.UserId = buyerId
	buildABTest(ctx, traceInfo, buyerId, req.GetSearchDebugReq().GetAbTest())
	traceInfo.AddPredictConfigToTraceInfo() // 后续优化，单独查询配置
	buildEsClientAndIndex(ctx, traceInfo)
	traceInfo.MetaPool = data_management_client.NewMetaPool(data_management_client.MetaPoolConfig{DishMetaEnable: true, DishMetaBufSize: 2000})
	if traceInfo.IsDebug == true && req.GetSearchDebugReq().GetDebugSwitch() != nil {
		traceInfo.IsSkipModel = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipModel()
		traceInfo.IsSkipAds = req.GetSearchDebugReq().GetDebugSwitch().GetIsSkipAds()
		if len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()) > 0 {
			traceInfo.IsNeedEsExplain = true
			traceInfo.EsExplainStoreIds = make(map[uint64]bool, len(req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds()))
			for _, storeId := range req.GetSearchDebugReq().GetDebugSwitch().GetEsExplainStoreIds() {
				traceInfo.EsExplainStoreIds[storeId] = true
			}
		}
	}
	return traceInfo
}

func (t *TraceInfo) CloseTraceInfo() {
	if t == nil {
		return
	}
	t.MetaPool.Close()
	t.ReportFilteredSum()
}

func (t *TraceInfo) BuildRspHeaders(ctx context.Context) []*foodalgo_search.ContextHeader {
	headers := make([]*foodalgo_search.ContextHeader, 0, 0)
	logId := proto.String(trace.RequestIDFromContext(ctx))
	headers = append(headers, &foodalgo_search.ContextHeader{HeaderKey: proto.String("x-log-id"), HeaderValue: logId})
	headers = append(headers, &foodalgo_search.ContextHeader{HeaderKey: proto.String("x-ab-test"), HeaderValue: proto.String(buildGroupIdList(t.ABTestGroupIdList))})
	headers = append(headers, &foodalgo_search.ContextHeader{HeaderKey: proto.String("x-publish-id"), HeaderValue: proto.String(t.TraceRequest.PublishId)})

	// 主站需要额外增加 foody_search_info 埋点
	if t.HandlerType == HandlerTypeSearchMainSite {
		headers = append(headers, &foodalgo_search.ContextHeader{HeaderKey: proto.String("foody_search_info"), HeaderValue: logId})
	}
	return headers
}

type OptIntervention struct {
	PredefinedKeyword string   //运营词典
	QueryStoreTop     []string //门店置顶
	// TH存在扩召回
	NeedExpandRecall       string
	ExpandRecallByCategory string
	ExpandRecallByDishName string
	//干预平台
	IsStoreInterventionRecall     bool
	InterventionRecallStoreIDList []uint64
	InterventionRecallBrandID     []uint64
	InterventionRecallMerchantID  []uint64
	InterventionType              int32
	InterventionRecallStoreTagID  []uint64
	SlotsPosition                 []uint64
	IsNewMerchantsOnly            bool
	IsRotateMerchants             bool
}

type QPResult struct {
	Segments                     []string // query的切词
	SegmentsAscii                []string // query.ascii的切词
	MaxWordSegments              []string
	StoreIntents                 []string
	DishIntents                  []string
	CorrectWord                  string
	IsQPCorrect                  bool
	TopnCandidateSentenceInfo    string
	SentenceInfoLog              string
	CorrectionType               qp.FoodCorrectionType
	OtherSegments                *OtherSegments     //泰国地区会将扩招回的词、dishName、storeName、tags拿去切词
	NerResult                    []*qp.Ner          //qp返回的实体识别的词
	NerAndRecallResult           []*qp.Ner          //qp返回的关于结构化recall实体识别的词
	NerOrRecallResult            []*qp.Ner          //qp返回的关于结构化recall实体识别的词
	NerAsciiResult               []*qp.Ner          //qp返回的实体识别的词_Ascii
	NerAndRecallAsciiResult      []*qp.Ner          //qp返回的关于结构化recall实体识别的词_Ascii
	NerOrRecallAsciiResult       []*qp.Ner          //qp返回的关于结构化recall实体识别的词_Ascii
	RewriteNerResult             [][]*qp.Ner        //qp返回的改写实体识别的词
	RewriteNerOriginResult       []*qp.QueryRewrite //qp返回的改写实体识别的词
	RewriteQuery                 []string           //qp返回的改写query，内容与实体相加一样。VN地区只返回query，不返回改写ner的结果
	IsNeedDishRecall             bool
	CategoryIntentions           []*qp.CategoryIntention     // 分类意图列表
	ProbFilterCategoryIntentions []*qp.CategoryIntention     // 分类意图列表，分值过滤后的
	QueryStoreIntention          string                      //门店意图
	QueryDishIntention           string                      // 菜品意图
	QueryStoreIntentionProb      float64                     //门店意图得分
	QueryDishIntentionProb       float64                     // 菜品意图得分
	QPLog                        string                      // qp的resp string, json 格式，落到日志->hdfs->hive
	QueryStringList              []QueryStringItem           // query string 的配置
	QueryStringListMap           map[int32][]QueryStringItem // recallType: query string 的配置
	TermWeightList               []*qp.TermWeight            // 词权重
	QueryTag                     []string                    // algoTags
	CorrectLevel                 *qp.CorrectionLevel
	QueryGeneralSubCateIds       []int32 // query的泛二级类目id列表
	EnlargeRewriteQuerys         []string
	EnlargeRewriteSegments       []RewriteSegment
	KeywordLabel                 int32            //keyword标签，keyword属于哪种标签：top、中间、尾巴
	RewriteSegments              []RewriteSegment // qp返回的改写词的分词
	OnlyUnknownNer               bool
	DropWordStoreIntents         []string
	DropWordDishIntents          []string

	// category 打压使用
	NerRecallResultOnlyDishNer        bool
	QueryCateIntentsManualReviewL1Ids map[uint32]struct{}
	QueryCateIntentsManualReviewL2Ids map[uint32]struct{}
}

type RewriteSegment struct {
	RewriteQuery string   `json:"rewriteQuery"`
	Segments     []string `json:"segments"`
}

type QueryStringItem struct {
	Fields []string `json:"fields"` // ["store_name^1.000000", "real_store_name^1.000000",]
	Query  string   `json:"query"`  // "pak^0.0715 muh^0.0795"
}

type OtherSegments struct {
	ExpandCallMainTagSegments   []string
	ExpandCallOtherTagsSegments [][]string
	ExpandCallDishNameSegments  []string
}

type UnsettledStoreInfo struct {
	StoreName         string
	Category          []string
	DishName          []string
	CategoryTokenizer []string // for th
	DishNameTokenizer []string // for th
}

type DataSourceItem struct {
	ValueType int         `json:"valueType"`
	Value     interface{} `json:"value"`
}

func (t *TraceInfo) AppendRecallsStoreHitAbtest(recallStatName string) {
	t.mutex.Lock()
	t.RecallsStoreHitAbtest = append(t.RecallsStoreHitAbtest, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsStoreNotHitAbtest(recallStatName string) {
	t.mutex.Lock()
	t.RecallsStoreNotHitAbtest = append(t.RecallsStoreNotHitAbtest, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsStoreFinal(recallStatName string) {
	t.mutex.Lock()
	t.RecallsStoreFinal = append(t.RecallsStoreFinal, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) SetPhraseStoreLength(key string, value int) {
	t.mutex.Lock()
	t.PhraseStoreLength[key] = value
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendPhraseStoreInfos(key string, value []string) {
	t.mutex.Lock()
	t.PhraseStoreInfos[key] = append(t.PhraseStoreInfos[key], value...)
	t.mutex.Unlock()
}

func (t *TraceInfo) SetPhraseStoreESDsl(key string, value string) {
	t.mutex.Lock()
	t.PhraseStoreESDsl[key] = value
	t.mutex.Unlock()
}

func (t *TraceInfo) SetPhraseDishESDsl(key string, value string) {
	t.mutex.Lock()
	t.PhraseDishESDsl[key] = value
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsDishNotHitAbtest(recallStatName string) {
	t.mutex.Lock()
	t.RecallsDishNotHitAbtest = append(t.RecallsDishNotHitAbtest, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsDishHitAbtest(recallStatName string) {
	t.mutex.Lock()
	t.RecallsDishHitAbtest = append(t.RecallsDishHitAbtest, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsDishFinal(recallStatName string) {
	t.mutex.Lock()
	t.RecallsDishFinal = append(t.RecallsDishFinal, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AppendRecallsDishConditionOkButGiveUp(recallStatName string) {
	t.mutex.Lock()
	t.RecallsDishConditionOkButGiveUp = append(t.RecallsDishConditionOkButGiveUp, recallStatName)
	t.mutex.Unlock()
}

func (t *TraceInfo) AddFilteredStore(storeId uint64, filteredType string, ext interface{}) {
	if t == nil || t.FilteredStores == nil {
		return
	}
	if _, exist := t.FilteredStores[filteredType]; !exist {
		t.FilteredStores[filteredType] = make([]*FilteredStore, 0, 0)
	}
	store := &FilteredStore{StoreId: storeId}
	if ext != nil {
		extInfo, _ := json.Marshal(ext)
		store.ExtInfo = string(extInfo)
	}
	t.FilteredStores[filteredType] = append(t.FilteredStores[filteredType], store)
}

func (t *TraceInfo) AddFilteredDish(dishId uint64, filteredType string, ext interface{}) {
	if t == nil || t.FilteredStores == nil {
		return
	}
	t.mutex.Lock()
	if _, exist := t.FilteredStores[filteredType]; !exist {
		t.FilteredStores[filteredType] = make([]*FilteredStore, 0, 0)
	}
	dish := &FilteredStore{DishId: dishId}
	if ext != nil {
		extInfo, _ := json.Marshal(ext)
		dish.ExtInfo = string(extInfo)
	}
	t.FilteredStores[filteredType] = append(t.FilteredStores[filteredType], dish)
	t.mutex.Unlock()
}
