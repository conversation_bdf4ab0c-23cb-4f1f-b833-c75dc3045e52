package traceinfo

import (
	"context"
	"fmt"
	"strings"

	reporter2 "git.garena.com/shopee/feed/comm_lib/reporter"
	reporter "git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
)

const (
	//FilteredTypeNerMergeSizeLimit                   = "FilterByNerMergeSizeLimit"   // Ner 3路合并截断
	FilteredTypeMultiMergeSizeLimit                        = "FilterByMultiMergeSizeLimit" // 门店多路合并截断
	FilteredTypeWithoutStoreMeta                           = "FilterByWithoutStoreMeta"    // 正排未降级且未查到正排信息
	FilteredTypeStoreDistance                              = "FilterByStoreDistance"
	FilteredTypePromotion                                  = "FilterByPromotion"
	FilteredTypePromotionNil                               = "FilterByPromotionNil"
	FilteredTypeStorePromotion                             = "FilterByStorePromotion"
	FilteredTypeStorePromotionNil                          = "FilterByStorePromotionNil"
	FilteredTypeOpening                                    = "FilterByOpening"
	FilteredTypeStoreDishSalesNum                          = "FilterByStoreDishSalesNum"
	FilteredTypeInactiveDistrict                           = "FilterByInactiveDistrict"
	FilteredTypeRatingScore                                = "FilterByRatingScore"
	FilteredTypeShopType                                   = "FilterByShopType"
	FilteredTypeDishSaleTime                               = "FilterByDishSaleTime" // 过滤掉仅通过dish信息召回的门店
	FilteredTypeDuplicateBrand                             = "FilterByDuplicateBrand"
	FilteredTypeDuplicateBrandDetails                      = "FilterByDuplicateBrandDetails"
	FilteredTypeDuplicateMerchant                          = "FilterByDuplicateMerchant"
	FilteredTypeAllDishFilterForMainSite                   = "FilterByAllDishFilterForMainSite" // 主站门店下所有菜品被过滤后，该门店不保留
	FilteredTypeNoDishFilterForMainSite                    = "FilterByNoDishFilterForMainSite"  // 主站门店下没菜品就不要保留门店
	FilterByPriceRange                                     = "FilterByPriceRange"
	FilterByShippingDistance                               = "FilterByShippingDistance"
	FilterByShippingFee                                    = "FilterByShippingFee"
	FilterBySlashedShippingFee                             = "FilterBySlashedShippingFee"
	FilterByL2CategoryId                                   = "FilterByL2CategoryId"
	FilterByCategoryType                                   = "FilterByCategoryType"
	FilterByStoreTags                                      = "FilterByStoreTags"
	FilterByHalalType                                      = "FilterByHalalType"
	FilterByOrderType                                      = "FilterByOrderType"
	FilterByNonHalalWhenRamadan                            = "FilterByNonHalalWhenRamadan"
	FilterDishByNonHalalWhenRamadan                        = "FilterDishByNonHalalWhenRamadan"
	FilterDishByZeroPrice                                  = "FilterDishByZeroPrice"
	FilterByPriorityMerge                                  = "FilterByPriorityMerge"
	FilterByStoreIds                                       = "FilterByStoreIds"
	FilterByDishFilter                                     = "FilterByDishFilter" // 过滤掉的菜品,复用store方法记录被过滤的菜品id
	FilterByCityId                                         = "FilterByCityId"
	FilterByDistrictIds                                    = "FilterByDistrictIds"
	FilterByCoarseRank                                     = "FilterByCoarseRank" // 粗排截断
	FilterByStoreWithoutCommission                         = "FilterByStoreWithoutCommission"
	FilterByStoreCauseOutOfRecallConfigDistance            = "FilterByStoreCauseOutOfRecallConfigDistance"
	FilterByStoreCauseLowerRecallConfigPRelevanceScore     = "FilterByStoreCauseLowerRecallConfigPRelevanceScore"
	FilterByStoreRemainingAfterRecallConfigPRelevanceScore = "FilterByStoreRemainingAfterRecallConfigPRelevanceScore"
	FilterByDishWithoutCommission                          = "FilterByDishWithoutCommission" //
	FilterByCommissionRate                                 = "FilterByCommissionRate"        //
	FilterByStoreBlacklist                                 = "FilterByStoreBlacklist"        //
	//FilterDishByNotOnSaleTime                       = "FilterDishByNotOnSaleTime"                 //
	FilterDishByOutOfStockAndSpecialSaleTime  = "FilterDishByOutOfStockAndSpecialSaleTime"  //
	FilterDishByNotOnSaleTimeAndNoImageDishes = "FilterDishByNotOnSaleTimeAndNoImageDishes" //
	FilteredDishByLowerNotShowSize            = "FilteredDishByLowerNotShowSize"            //
	FilteredDishByMaxSizeCut                  = "FilteredDishByMaxSizeCut"                  //
	FilteredDishByFlashSaleInvalidStatus      = "FilteredDishByFlashSaleInvalidStatus"      //
	FilteredDishByInvalidStatus               = "FilteredDishByInvalidStatus"               //
	FilteredDishByFlashSaleInvalidTimeslot    = "FilteredDishByFlashSaleInvalidTimeslot"    //
	FilteredDishByFlashSaleShowSize           = "FilteredDishByFlashSaleShowSize"           //
	FilteredTypeDupFewResultStore             = "FilteredTypeDupFewResultStore"
	FilteredTypeDupFewResultBrand             = "FilteredTypeDupFewResultBrand"
)

type FilteredStore struct {
	StoreId uint64 `json:"store_id,omitempty" `
	DishId  uint64 `json:"dish_id,omitempty"`
	ExtInfo string `json:"ext_info,omitempty"`
}

func PrintFilteredType(ctx context.Context, traceInfo *TraceInfo) {
	var stringBuilder strings.Builder
	stringBuilder.Grow(10240)
	stringBuilder.WriteString("trace debug log, filter store:")
	for filteredType, stores := range traceInfo.FilteredStores {
		stringBuilder.WriteString(fmt.Sprintf("%s:[", string(filteredType)))
		for _, store := range stores {
			stringBuilder.WriteString(fmt.Sprintf("%d-%s,", store.StoreId, store.ExtInfo))
		}
		stringBuilder.WriteString("],")
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, stringBuilder.String())
}

func (t *TraceInfo) ReportFilteredSum() {
	if t == nil || t.FilteredStores == nil {
		return
	}
	for recallType, stores := range t.FilteredStores {
		reporter.ReportHistogramWithBuckets(reporter.ReportSearchRecallDocNumMetricName,
			float64(len(stores)), HistogramStoreLengthBuckets,
			reporter2.Label{Key: "doc", Val: string(recallType)},
			reporter2.Label{Key: "handler", Val: t.HandlerType.String()},
			reporter2.Label{Key: "pipeline", Val: t.PipelineType.String()})
	}
}
