package traceinfo

import (
	"context"
	"time"

	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
)

type CostPhrase string

const (
	PhraseSearchAds                               CostPhrase = "0_SearchAds"
	PhraseNormalAndFewResultPipeline              CostPhrase = "0_NormalAndFewResultPipeline"
	PhraseNormalPipeline                          CostPhrase = "0_NormalAndFewResultPipeline_Normal"
	PhraseFewResultPipeline                       CostPhrase = "0_NormalAndFewResultPipeline_FewResult"
	PhraseNormalPipelineInternal                  CostPhrase = "0_NormalAndFewResultPipeline_Normal_Internal"
	PhrasePreProcess                              CostPhrase = "1_PreProcess"
	PhrasePreProcessCorrect                       CostPhrase = "1_PreProcess_Correct"
	PhrasePreProcessQP                            CostPhrase = "1_PreProcess_QP"
	PhraseInitDataSource                          CostPhrase = "1_PreProcess_InitDataSource"
	PhraseStoreRecall                             CostPhrase = "2_StoreRecall"
	PhraseStoreRecallMultiSource                  CostPhrase = "2_StoreRecall_MultiSource"
	PhraseStoreRecallMultiSourceES                CostPhrase = "2_StoreRecall_MultiSource_ES"
	PhraseStoreRecallMultiSourceRedis             CostPhrase = "2_StoreRecall_MultiSource_Redis"
	PhraseStoreRecallMultiSourceFS                CostPhrase = "2_StoreRecall_MultiSource_FS"
	PhraseStoreRecallMultiSourceVectorEngine      CostPhrase = "2_StoreRecall_MultiSource_VectorEngine"
	PhraseStoreMerge                              CostPhrase = "2_StoreRecall_StoreMerge"
	PhraseStoreFilling                            CostPhrase = "3_StoreFilling"
	PhraseFillingStoreMeta                        CostPhrase = "3_StoreFilling_StoreMeta"
	PhraseFillingStoreMetaRPC                     CostPhrase = "3_StoreFilling_StoreMeta_RPC"
	PhraseFillingAfterStoreMeta                   CostPhrase = "3_StoreFilling_AfterMeta"
	PhraseFillingStorePolygon                     CostPhrase = "3_StoreFilling_AfterMeta_Polygon"
	PhraseFillingRoutingDistance                  CostPhrase = "3_StoreFilling_AfterMeta_RoutingDistance"
	PhraseFillingVNIsOpening                      CostPhrase = "3_StoreFilling_AfterMeta_VNIsOpening"
	PhraseFillingVNPromotion                      CostPhrase = "3_StoreFilling_AfterMeta_VNPromotion"
	PhraseFillingPromotion                        CostPhrase = "3_StoreFilling_AfterMeta_Promotion"
	PhraseFillingShippingFee                      CostPhrase = "3_StoreFilling_AfterMeta_ShippingFee"
	PhraseFillingStoreMaxShippingFeeDiscount      CostPhrase = "3_StoreFilling_AfterMeta_MaxShippingFeeDiscount"
	PhraseFillingStorePromotion                   CostPhrase = "3_StoreFilling_AfterMeta_StorePromotion"
	PhraseFillingStorePrice                       CostPhrase = "3_StoreFilling_AfterMeta_StorePrice"
	PhraseFillingStoreTextMatchScore              CostPhrase = "3_StoreFilling_AfterMeta_MatchScore"
	PhraseFillingStoreCategoryScore               CostPhrase = "3_StoreFilling_AfterMeta_CategoryScore"
	PhraseFillingPriority                         CostPhrase = "3_StoreFilling_AfterMeta_Priority"
	PhraseFillingStoreCommission                  CostPhrase = "3_StoreFilling_AfterMeta_StoreCommission"
	PhraseFillingStoreSegment                     CostPhrase = "3_StoreFilling_AfterMeta_StoreSegment"
	PhraseFillingStoreNormalizedScore             CostPhrase = "3_StoreFilling_AfterMeta_NormalizedScore"
	PhraseFillingStoreCategoryDeboostScore        CostPhrase = "3_StoreFilling_AfterMeta_CategoryDeboostScore"
	PhraseStoreFilter                             CostPhrase = "4_StoreFilter"
	PhraseStoreMergeTruncate                      CostPhrase = "4_StoreFilter_MergeTruncate"
	PhraseFilterPolygonDistance                   CostPhrase = "4_StoreFilter_PolygonDistance"
	PhraseCoarseRank                              CostPhrase = "5_CoarseRank"
	PhraseCoarseRankUserEmb                       CostPhrase = "5_CoarseRank_UserEmb"
	PhraseCoarseRankPredictorRough                CostPhrase = "5_CoarseRank_PredictorRough"
	PhraseDishListing                             CostPhrase = "6_DishListing"
	PhraseDishListingWithoutCache                 CostPhrase = "6_DishListing_WithoutCache"
	PhraseDishListingMultiRecall                  CostPhrase = "6_DishListing_MultiRecall"
	PhraseDishListingMultiRecallES                CostPhrase = "6_DishListing_MultiRecall_ES"
	PhraseDishListingMultiRecallFS                CostPhrase = "6_DishListing_MultiRecall_FS"
	PhraseDishListingMultiRecallFSGetCache        CostPhrase = "6_DishListing_MultiRecall_FS_GetCache"
	PhraseDishListingMultiRecallFSRPCV2           CostPhrase = "6_DishListing_MultiRecall_FS_RPC"
	PhraseDishListingMultiRecallFSSetCache        CostPhrase = "6_DishListing_MultiRecall_FS_SetCache"
	PhraseDishListingFilling                      CostPhrase = "6_DishListing_Filling"
	PhraseDishListingFillingDishFlashSale         CostPhrase = "6_DishListing_Filling_DishFlashSale"
	PhraseDishListingFillingDishFlashSaleGetCache CostPhrase = "6_DishListing_Filling_DishFlashSale_GetCache"
	PhraseDishListingFillingDishFlashSaleRPC      CostPhrase = "6_DishListing_Filling_DishFlashSale_RPC"
	PhraseDishListingFillingDishFlashSaleRPCOne   CostPhrase = "6_DishListing_Filling_DishFlashSale_RPC_One"
	PhraseDishListingFillingDishFlashSaleSetCache CostPhrase = "6_DishListing_Filling_DishFlashSale_SetCache"
	PhraseDishListingFillingMeta                  CostPhrase = "6_DishListing_Filling_Meta"
	PhraseDishListingFillingMetaMGet              CostPhrase = "6_DishListing_Filling_Meta_MGet"
	PhraseDishListingFillingFeature               CostPhrase = "6_DishListing_Filling_Feature"
	PhraseDishListingFillingFeatureGetCache       CostPhrase = "6_DishListing_Filling_Feature_GetCache"
	PhraseDishListingFillingFeatureRPC            CostPhrase = "6_DishListing_Filling_Feature_RPC"
	PhraseDishListingFillingFeatureSetCache       CostPhrase = "6_DishListing_Filling_Feature_SetCache"
	PhraseFillingDishESRecallPriority             CostPhrase = "6_DishListing_Filling_ESRecallPriority"
	PhraseDishListingMerge                        CostPhrase = "6_DishListing_Merge"
	PhraseDishListingFilter                       CostPhrase = "6_DishListing_Filter"
	PhraseDishListingRank                         CostPhrase = "6_DishListing_Rank"
	PhraseDishListingSplit                        CostPhrase = "6_DishListing_Split"
	PhraseDishListingTruncate                     CostPhrase = "6_DishListing_Truncate"
	PhraseDishRecallV1                            CostPhrase = "6_DishListing_DishRecallV1"
	PhraseDishRecallDishSearcher                  CostPhrase = "6_DishListing_DishRecallV1_DishSearcher"
	PhraseDishRecallDishSearcherCatalog           CostPhrase = "6_DishListing_DishRecallV1_DishSearcherCatalog"
	PhraseDishRecallDishESIndex                   CostPhrase = "6_DishListing_DishRecallV1_ES"
	PhraseFilterDish                              CostPhrase = "6_DishListing_DishFilterV1"
	PhraseFusionRank                              CostPhrase = "7_FusionRank"
	PhraseFusionRankPredict                       CostPhrase = "7_FusionRank_Predict"
	PhraseFusionRankPredictRelevanceServer        CostPhrase = "7_FusionRank_Predict_RelevanceServer"
	PhraseFusionRankPredictCtr                    CostPhrase = "7_FusionRank_Predict_Ctr"
	PhraseFusionRankPredictCvr                    CostPhrase = "7_FusionRank_Predict_Cvr"
	PhraseFusionRankPredictRel                    CostPhrase = "7_FusionRank_Predict_Rel"
	PhraseFusionRankPredictDF                     CostPhrase = "7_FusionRank_Predict_DF"
	PhraseFusionRankPredictUE                     CostPhrase = "7_FusionRank_Predict_UE"
	PhraseFusionRankCalculate                     CostPhrase = "7_FusionRank_Calculate"
	PhraseFusionRankSort                          CostPhrase = "7_FusionRank_Sort"
	PhraseReRank                                  CostPhrase = "8_ReRank"
	PhraseReRankMixer                             CostPhrase = "8_ReRank_Mixer"
	PhraseReRankIntervention                      CostPhrase = "8_ReRank_Intervention"
	PhraseReRankTopKBoost                         CostPhrase = "8_ReRank_TopKBoost"
	PhraseReRankLTR                               CostPhrase = "8_ReRank_LTR"
	PhraseReRankLTRPredict                        CostPhrase = "8_ReRank_LTR_Predict"
	PhraseReRankLTRCalculate                      CostPhrase = "8_ReRank_LTR_Calculate"
	PhraseLWRank                                  CostPhrase = "9_LWRank"
	PhraseLWRankGenerateStoreLists                CostPhrase = "9_LWRank_GenerateStoreLists"
	PhraseLWRankGenFormulaList                    CostPhrase = "9_LWRank_GenFormulaList"
	PhraseLWRankGenMMRList                        CostPhrase = "9_LWRank_GenMMRList"
	PhraseLWRankGenMMRListDoMMR                   CostPhrase = "9_LWRank_GenMMRDoMMR"
	PhraseLWRankGenMMRListDoMMROne                CostPhrase = "9_LWRank_GenMMRDoMMROne"
	PhraseLWRankGenMMRListCalRel                  CostPhrase = "9_LWRank_GenMMRListCalRel"
	PhraseLWRankGenMMRListCalSim                  CostPhrase = "9_LWRank_GenMMRListCalSim"
	PhraseLWRankEvaluateStoreLists                CostPhrase = "9_LWRank_EvaluatorStoreLists"
	PhraseLWRankEvaluatorSelect                   CostPhrase = "9_LWRank_EvaluatorSelect"

	// 其他
	PhraseSearchPipeline                CostPhrase = "0_SearchPipeline"
	PhraseSearchDishesPipeline          CostPhrase = "0_SearchDishesPipeline"
	PhraseSearchMainSitePipeline        CostPhrase = "0_PhraseSearchMainSitePipeline"
	PhraseSearchDishesAffiliatePipeline CostPhrase = "0_SearchDishesAffiliatePipeline"
	PhraseSearchL3CategoryPipeline      CostPhrase = "0_SearchL3CategoryPipeline"
	PhraseHistoryOrderPipeline          CostPhrase = "0_HistoryOrderPipeline"

	PhrasePreProcessCorrectedRecall   CostPhrase = "PreProcess_CorrectedRecall"
	PhraseStoreDishRecallDishes       CostPhrase = "StoreDishRecallDishes"
	PhraseStoreDishRecallCategories   CostPhrase = "StoreDishRecallCategories"
	PhraseESRecallStoresAffiliate     CostPhrase = "ESRecallStoresAffiliate"
	PhraseESRecallDishesAffiliate     CostPhrase = "ESRecallDishesAffiliate"
	PhraseESRecallBrandCountAffiliate CostPhrase = "ESRecallBrandCountAffiliate"
	PhraseESRecallHistoryOrderES      CostPhrase = "ESRecallHistoryOrder_ES"
	PhraseFillingDishCommission       CostPhrase = "FillingDishCommission"
	PhrasePrintDumpLog                CostPhrase = "PrintDumpLog"
	PhrasePrintDumpLogJSON            CostPhrase = "PrintDumpLogJSON"
	PhrasePrintDumpLogForMainSite     CostPhrase = "PrintDumpLogForMainSite"

	PhraseP99FasterCalculatePLtrV1Score CostPhrase = "P99_FasterCalculatePLtrV1Score"
	PhraseP99FasterCalculateReRankScore CostPhrase = "P99_FasterCalculateReRankScore"
)

func (t *TraceInfo) AddPhraseCostTime(ctx context.Context, costType CostPhrase, cost time.Duration) {
	metric_reporter.SearchRawReportSummary(cost, metric_reporter2.SearchReportTypePhrase, t.HandlerType.String(), t.PipelineType.String(), string(costType), "")
}

func (t *TraceInfo) AddMainPhraseCostTime(ctx context.Context, costType CostPhrase, cost time.Duration) {
	metric_reporter.SearchRawReportSummary(cost, metric_reporter2.SearchReportTypeMainPhrase, t.HandlerType.String(), t.PipelineType.String(), string(costType), "")
}
