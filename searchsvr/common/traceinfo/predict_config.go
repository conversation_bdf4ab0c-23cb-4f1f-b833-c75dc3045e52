package traceinfo

import (
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/abtest"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

type PredictConfig struct {
	CtrModuleName       string `xml:"CtrModuleName"`
	CvrModuleName       string `xml:"CvrModuleName"`
	RelevanceModuleName string `xml:"RelevanceModuleName"`
	SemanticModuleName  string `xml:"SemanticModuleName"`
	UEModuleName        string `xml:"UEModuleName"`
	IsCtrCvrUnified     bool   `xml:"IsCtrCvrUnified"`
	UnifiedModuleCtrKey string `xml:"UnifiedModuleCtrKey"`
	UnifiedModuleCvrKey string `xml:"UnifiedModuleCvrKey"`

	MaxTruncPctr      float64 `xml:"MaxTruncPctr"`
	MinTruncPctr      float64 `xml:"MinTruncPctr"`
	MaxTruncPcvr      float64 `xml:"MaxTruncPcvr"`
	MinTruncPcvr      float64 `xml:"MinTruncPcvr"`
	MaxTruncRelevance float64 `xml:"MaxTruncRelevance"`
	MinTruncRelevance float64 `xml:"MinTruncRelevance"`
	MaxTruncPrice     float64 `xml:"MaxTruncPrice"`
	MinTruncPrice     float64 `xml:"MinTruncPrice"`
	MaxTruncSemantic  float64 `xml:"MaxTruncSemantic"`
	MinTruncSemantic  float64 `xml:"MinTruncSemantic"`
	MaxTruncUE        float64 `xml:"MaxTruncUE"`
	MinTruncUE        float64 `xml:"MinTruncUE"`

	ReleFusionFunc   int `xml:"ReleFusionFunc"`
	AckIpAddr        string
	AckIpInstance    string
	RelAckIpAddr     string
	RelAckIpInstance string
	// recall相关，与多因子无关
	MustAndMatchTypeFieldBoostQueueLocation   string
	MustAndMatchTypeFieldBoostQueueDish       string
	MustAndMatchTypeFieldBoostQueueStore      string
	MustAndMatchTypeFieldBoostQueueCategory   string
	MustAndMatchTypeFieldBoostQueueIngredient string
	MustAndMatchTypeFieldBoostQueueDefault    string
	OtherFieldBoostQueueLocation              string
	OtherFieldBoostQueueDish                  string
	OtherFieldBoostQueueStore                 string
	OtherFieldBoostQueueCategory              string
	OtherFieldBoostQueueIngredient            string
	OtherFieldBoostQueueDefault               string

	NerRecallUseScriptSort bool
	LocationNerBoost       float64
	DishNerBoost           float64
	StoreNerBoost          float64
	CategoryNerBoost       float64
	IngredientNerBoost     float64
	DefaultNerBoost        float64

	NerRecallTotal       int `xml:"NerRecallTotal"`
	MustAndMatchTypeSize int `xml:"MustAndMatchTypeSize"`
	MustOrMatchTypeSize  int `xml:"MustOrMatchTypeSize"`
	ShouldMatchTypeSize  int `xml:"ShouldMatchTypeSize"`

	// rel新策略
	NewStrategy int `xml:"NewStrategy"`
	//打压系数
	Suppress float64 `xml:"Suppress"`
	//距离，用来定档
	Distance float64 `xml:"Distance"`
	//结果是否要分档
	RelResLayer bool `xml:"RelResLayer"`

	ExpParametersMap  map[string]interface{}
	ExpString         string  `xml:"ExpString"`
	ExpParameters     string  `xml:"ExpParameters"`
	ExpFunc           string  `xml:"ExpFunc"`
	Relevance         int     `xml:"Relevance"`
	PriceType         int     `xml:"PriceType"`
	UEDefaultVal      float64 `xml:"UEDefaultVal"`
	UERadioDefaultVal float64 `xml:"UERadioDefaultVal"`
	RelevanceUseLevel int     `xml:"RelevanceUseLevel"`
	TruncDistance     float64 `xml:"TruncDistance"`

	// Ltr重排相关
	LtrModuleName string `xml:"LtrModuleName"`
	UseLtr        bool   `xml:"UseLtr"`
	//LtrVersion    int    `xml:"LtrVersion"`
	LtrGroupNum                int    `xml:"LtrGroupNum"`
	CoarseSortExpString        string `xml:"CoarseSortExpString"`
	CoarseSortExpParameters    string `xml:"CoarseSortExpParameters"`
	CoarseSortExpParametersMap map[string]interface{}

	// LTRV1 因子
	LTRV1ExpString        string `xml:"LTRV1ExpString"`
	LTRV1ExpParameters    string `xml:"LTRV1ExpParameters"`
	LTRV1ExpParametersMap map[string]interface{}

	// 新分类应用相关
	IsNewCateRecall                   bool    `xml:"IsNewCateRecall"`
	NewCateProbFilterScore            float64 `xml:"NewCateProbFilterScore"`
	NewCateIsKeepGrabfoodTag          bool    `xml:"NewCateIsKeepGrabfoodTag"`
	NewCateIsOnlyRecallStoreIntention bool    `xml:"NewCateIsOnlyRecallStoreIntention"`
	NewCateExpandRecallSize           int     `xml:"NewCateExpandRecallSize"`
	IsNewCateExpandRecallV2           bool    `xml:"IsNewCateExpandRecallV2"`
	NewCateExpandRecallV2DistanceKm   float64 `xml:"NewCateExpandRecallV2DistanceKm"`

	// location 排序开关
	IsOpenLocationSort     bool   `xml:"IsOpenLocationSort"`
	UserExpParameters      string `xml:"UserExpParameters"`
	UserExpType            int    `xml:"UserExpType"`
	UserExp                bool   `xml:"UserExp"` //打开user分档实验
	IntentExpParameters    string `xml:"IntentExpParameters"`
	IntentExp              bool   `xml:"IntentExp"` //打开intention实验
	UserExpParametersMap   map[string]map[string]interface{}
	IntentExpParametersMap map[string]map[string]interface{}

	DFModelName     string
	DyFactorList    []float64
	IsPredict       bool
	StoreIncubation float64
	UsePcFactor     bool
	AbTestVal       string
}

func (t *TraceInfo) AddPredictConfigToTraceInfo() {
	t.IsNotRelevanceTabUseNewSort = abtest.GetIsNotRelevanceTabUseNewSort(t.IsDebug, t.AbParamClient)

	multiFactorPredictConfig := abtest.GetMultiFactorParams(t.IsDebug, t.AbParamClient)

	if multiFactorPredictConfig != nil {
		// 多因子公式相关赋值
		t.PredictConfig.ExpParametersMap = multiFactorPredictConfig.ExpParametersMap
		t.PredictConfig.ExpString = multiFactorPredictConfig.ExpString
		t.PredictConfig.ExpParameters = multiFactorPredictConfig.ExpParameters
		t.PredictConfig.ExpFunc = multiFactorPredictConfig.ExpFunc
		t.PredictConfig.Relevance = multiFactorPredictConfig.Relevance
		t.PredictConfig.PriceType = multiFactorPredictConfig.PriceType
		t.PredictConfig.RelevanceUseLevel = multiFactorPredictConfig.RelevanceUseLevel
		t.PredictConfig.TruncDistance = multiFactorPredictConfig.TruncDistance
		t.PredictConfig.UserExpParameters = multiFactorPredictConfig.UserExpParameters
		t.PredictConfig.UserExpType = multiFactorPredictConfig.UserExpType
		t.PredictConfig.UserExp = multiFactorPredictConfig.UserExp
		t.PredictConfig.IntentExpParameters = multiFactorPredictConfig.IntentExpParameters
		t.PredictConfig.IntentExp = multiFactorPredictConfig.IntentExp
		t.PredictConfig.UserExpParametersMap = multiFactorPredictConfig.UserExpParametersMap
		t.PredictConfig.IntentExpParametersMap = multiFactorPredictConfig.IntentExpParametersMap
		t.PredictConfig.IsPredict = multiFactorPredictConfig.IsPredict
		t.PredictConfig.StoreIncubation = multiFactorPredictConfig.StoreIncubation
		t.PredictConfig.UsePcFactor = multiFactorPredictConfig.UsePcFactor
		t.PredictConfig.AbTestVal = multiFactorPredictConfig.ABTestVal

		t.PredictConfig.LtrModuleName = multiFactorPredictConfig.LtrModuleName
		t.PredictConfig.UseLtr = multiFactorPredictConfig.UseLtr
		if t.TraceRequest.GetSortType() != foodalgo_search.SearchRequest_Relevance {
			// LTR 只在相关性场景下生效
			t.PredictConfig.UseLtr = false
		}
		t.PredictConfig.DFModelName = multiFactorPredictConfig.DFModelName

		t.PredictConfig.CoarseSortExpString = multiFactorPredictConfig.CoarseSortExpString
		t.PredictConfig.CoarseSortExpParameters = multiFactorPredictConfig.CoarseSortExpParameters
		t.PredictConfig.CoarseSortExpParametersMap = multiFactorPredictConfig.CoarseSortExpParametersMap

		t.PredictConfig.LTRV1ExpString = multiFactorPredictConfig.LTRV1ExpString
		t.PredictConfig.LTRV1ExpParameters = multiFactorPredictConfig.LTRV1ExpParameters
		t.PredictConfig.LTRV1ExpParametersMap = multiFactorPredictConfig.LTRV1ExpParametersMap

		// 各因子赋值
		if multiFactorPredictConfig.ABTestVal == Default {
			//各因子单独控制影响因素
			ctrConfig := abtest.GetCtrConfig(t.IsDebug, t.AbParamClient)
			cvrConfig := abtest.GetCvrConfig(t.IsDebug, t.AbParamClient)
			relevanceConfig := abtest.GetRelevanceConfig(t.IsDebug, t.AbParamClient)
			priceConfig := abtest.GetUeConfig(t.IsDebug, t.AbParamClient)
			if ctrConfig != nil {
				t.PredictConfig.CtrModuleName = ctrConfig.CtrModuleName
				t.PredictConfig.MinTruncPctr = ctrConfig.MinTruncPctr
				t.PredictConfig.MaxTruncPctr = ctrConfig.MaxTruncPctr
			}
			if cvrConfig != nil {
				t.PredictConfig.CvrModuleName = cvrConfig.CvrModuleName
				t.PredictConfig.MinTruncPcvr = cvrConfig.MinTruncPcvr
				t.PredictConfig.MaxTruncPcvr = cvrConfig.MaxTruncPcvr
			}
			if relevanceConfig != nil {
				t.PredictConfig.ReleFusionFunc = relevanceConfig.ReleFusionFunc
				t.PredictConfig.RelevanceModuleName = relevanceConfig.RelevanceModuleName
				t.PredictConfig.SemanticModuleName = relevanceConfig.SemanticModuleName
				t.PredictConfig.MinTruncRelevance = relevanceConfig.MinTruncRelevance
				t.PredictConfig.MaxTruncRelevance = relevanceConfig.MaxTruncRelevance
				t.PredictConfig.MaxTruncSemantic = relevanceConfig.MaxTruncSemantic
				t.PredictConfig.MinTruncSemantic = relevanceConfig.MinTruncSemantic
				t.PredictConfig.NewStrategy = relevanceConfig.NewStrategy
				t.PredictConfig.Suppress = relevanceConfig.Suppress
				t.PredictConfig.Distance = relevanceConfig.Distance
				t.PredictConfig.RelResLayer = relevanceConfig.RelResLayer
			}
			if priceConfig != nil {
				t.PredictConfig.PriceType = priceConfig.PriceType
				t.PredictConfig.MinTruncPrice = priceConfig.MinTruncPrice
				t.PredictConfig.MaxTruncPrice = priceConfig.MaxTruncPrice
				t.PredictConfig.UEDefaultVal = priceConfig.UEDefaultVal
				t.PredictConfig.UERadioDefaultVal = priceConfig.UERadioDefaultVal
				t.PredictConfig.UEModuleName = priceConfig.UEModuleName
				t.PredictConfig.MaxTruncUE = priceConfig.MaxTruncUE
				t.PredictConfig.MinTruncUE = priceConfig.MinTruncUE
			}
			//打压系数会给零档的reRank进行打压
			if t.PredictConfig.Suppress == 0 {
				t.PredictConfig.Suppress = 1
			}
		} else {
			t.PredictConfig.MinTruncPctr = multiFactorPredictConfig.MinTruncPctr
			t.PredictConfig.MaxTruncPctr = multiFactorPredictConfig.MaxTruncPctr
			t.PredictConfig.MinTruncPcvr = multiFactorPredictConfig.MinTruncPcvr
			t.PredictConfig.MaxTruncPcvr = multiFactorPredictConfig.MaxTruncPcvr
			t.PredictConfig.MinTruncRelevance = multiFactorPredictConfig.MinTruncRelevance
			t.PredictConfig.MaxTruncRelevance = multiFactorPredictConfig.MaxTruncRelevance
			t.PredictConfig.MaxTruncSemantic = multiFactorPredictConfig.MaxTruncSemantic
			t.PredictConfig.MinTruncSemantic = multiFactorPredictConfig.MinTruncSemantic
			t.PredictConfig.ReleFusionFunc = multiFactorPredictConfig.ReleFusionFunc
			t.PredictConfig.CtrModuleName = multiFactorPredictConfig.CtrModuleName
			t.PredictConfig.CvrModuleName = multiFactorPredictConfig.CvrModuleName
			t.PredictConfig.RelevanceModuleName = multiFactorPredictConfig.RelevanceModuleName
			t.PredictConfig.SemanticModuleName = multiFactorPredictConfig.SemanticModuleName
			t.PredictConfig.NewStrategy = multiFactorPredictConfig.NewStrategy
			t.PredictConfig.Suppress = multiFactorPredictConfig.Suppress
			t.PredictConfig.Distance = multiFactorPredictConfig.Distance
			t.PredictConfig.RelResLayer = multiFactorPredictConfig.RelResLayer
			t.PredictConfig.PriceType = multiFactorPredictConfig.PriceType
			t.PredictConfig.MinTruncPrice = multiFactorPredictConfig.MinTruncPrice
			t.PredictConfig.MaxTruncPrice = multiFactorPredictConfig.MaxTruncPrice
			t.PredictConfig.UEDefaultVal = multiFactorPredictConfig.UEDefaultVal
			t.PredictConfig.UERadioDefaultVal = multiFactorPredictConfig.UERadioDefaultVal
			t.PredictConfig.UEModuleName = multiFactorPredictConfig.UEModuleName
			t.PredictConfig.MaxTruncUE = multiFactorPredictConfig.MaxTruncUE
			t.PredictConfig.MinTruncUE = multiFactorPredictConfig.MinTruncUE
			t.PredictConfig.IsCtrCvrUnified = multiFactorPredictConfig.IsCtrCvrUnified
			t.PredictConfig.UnifiedModuleCtrKey = multiFactorPredictConfig.UnifiedModuleCtrKey
			t.PredictConfig.UnifiedModuleCvrKey = multiFactorPredictConfig.UnifiedModuleCvrKey

			//打压系数会给零档的reRank进行打压
			if t.PredictConfig.Suppress == 0 {
				t.PredictConfig.Suppress = 1
			}
		}
	}
	//recallConfig := apollo.AlgoApolloCfg.PredictRecallControlMap.GetPredictRecallControl(t.ABTestGroup.GetABTestGroupMap())
	recallConfig := abtest.GetPredictRecallConfig(t.AbParamClient)
	if recallConfig != nil {
		t.PredictConfig.OtherFieldBoostQueueLocation = recallConfig.OtherFieldBoostQueueLocation
		t.PredictConfig.OtherFieldBoostQueueDish = recallConfig.OtherFieldBoostQueueDish
		t.PredictConfig.OtherFieldBoostQueueStore = recallConfig.OtherFieldBoostQueueStore
		t.PredictConfig.OtherFieldBoostQueueCategory = recallConfig.OtherFieldBoostQueueCategory
		t.PredictConfig.OtherFieldBoostQueueIngredient = recallConfig.OtherFieldBoostQueueIngredient
		t.PredictConfig.OtherFieldBoostQueueDefault = recallConfig.OtherFieldBoostQueueDefault
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueDish = recallConfig.MustAndMatchTypeFieldBoostQueueDish
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueStore = recallConfig.MustAndMatchTypeFieldBoostQueueStore
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueCategory = recallConfig.MustAndMatchTypeFieldBoostQueueCategory
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueIngredient = recallConfig.MustAndMatchTypeFieldBoostQueueIngredient
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueDefault = recallConfig.MustAndMatchTypeFieldBoostQueueDefault
		t.PredictConfig.MustAndMatchTypeFieldBoostQueueLocation = recallConfig.MustAndMatchTypeFieldBoostQueueLocation
		t.PredictConfig.MustAndMatchTypeSize = recallConfig.MustAndMatchTypeSize
		t.PredictConfig.ShouldMatchTypeSize = recallConfig.ShouldMatchTypeSize
		t.PredictConfig.MustOrMatchTypeSize = recallConfig.MustOrMatchTypeSize
		t.PredictConfig.NerRecallTotal = recallConfig.NerRecallTotal

		t.PredictConfig.NerRecallUseScriptSort = recallConfig.NerRecallUseScriptSort
		t.PredictConfig.LocationNerBoost = recallConfig.LocationNerBoost
		t.PredictConfig.StoreNerBoost = recallConfig.StoreNerBoost
		t.PredictConfig.DishNerBoost = recallConfig.DishNerBoost
		t.PredictConfig.CategoryNerBoost = recallConfig.CategoryNerBoost
		t.PredictConfig.IngredientNerBoost = recallConfig.IngredientNerBoost
		t.PredictConfig.DefaultNerBoost = recallConfig.DefaultNerBoost

		// 新分类扩召回
		t.PredictConfig.IsNewCateRecall = recallConfig.IsNewCateRecall
		t.PredictConfig.NewCateProbFilterScore = recallConfig.NewCateProbFilterScore
		t.PredictConfig.NewCateIsOnlyRecallStoreIntention = recallConfig.NewCateIsOnlyRecallStoreIntention
		t.PredictConfig.NewCateIsKeepGrabfoodTag = recallConfig.NewCateIsKeepGrabfoodTag
		t.PredictConfig.NewCateExpandRecallSize = recallConfig.NewCateExpandRecallSize
		t.PredictConfig.IsNewCateExpandRecallV2 = recallConfig.IsNewCateExpandRecallV2
		t.PredictConfig.NewCateExpandRecallV2DistanceKm = recallConfig.NewCateExpandRecallV2DistanceKm

		// location 排序
		t.PredictConfig.IsOpenLocationSort = recallConfig.IsOpenLocationSort
	}
	// VN重启实验，需要召回+多因子叠加收益，完成重启后删除此处逻辑
	if cid.IsVN() {
		multiFactorGroup := t.ABTestGroup.GetABTestVal(apollo.SearchApolloCfg.RankExpVariate)
		if len(multiFactorGroup) == 0 {
			multiFactorGroup = "default"
		}
		reRankWeightMap := apollo.GetReRankExpWeight()
		recallGroup := reRankWeightMap[multiFactorGroup]["RecallStrategy"]
		if len(recallGroup) > 0 {
			t.ABTestGroup.abTestGroup[FoodSchRecall] = recallGroup
		}
	}
}
