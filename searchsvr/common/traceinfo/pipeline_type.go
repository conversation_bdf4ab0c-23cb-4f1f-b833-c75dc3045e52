package traceinfo

import "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"

type PipelineType int

const (
	PipelineTypeSearch PipelineType = iota
	PipelineTypeSearchMainSite
	PipelineTypeSearchCollection
	PipelineTypeSearchStoreDishes
	PipelineTypeSearchStoresForAffiliate
	PipelineTypeSearchDishesForAffiliate
	PipelineTypeSearchHistoryOrders
	PipelineTypeSearchFewResult
)

func (s PipelineType) String() string {
	switch s {
	case PipelineTypeSearch:
		return "PipelineSearch"
	case PipelineTypeSearchMainSite:
		return "PipelineSearchMainSite"
	case PipelineTypeSearchCollection:
		return "PipelineSearchCollection"
	case PipelineTypeSearchStoreDishes:
		return "PipelineSearchStoreDishes"
	case PipelineTypeSearchStoresForAffiliate:
		return "PipelineSearchStoresForAffiliate"
	case PipelineTypeSearchDishesForAffiliate:
		return "PipelineSearchDishesForAffiliate"
	case PipelineTypeSearchHistoryOrders:
		return "PipelineHistoryOrders"
	case PipelineTypeSearchFewResult:
		return "PipelineFewResult"
	default:
		return "PipelineUnknown"
	}
}

// vn 类型需要重新调整下
func GetPipelineType(handlerType HandlerType, req *foodalgo_search.SearchRequest) PipelineType {
	switch handlerType {
	case HandlerTypeSearch:
		return PipelineTypeSearch
	case HandlerTypeSearchFood:
		return PipelineTypeSearch
	case HandlerTypeSearchGlobal, HandlerTypeSearchGlobalV1, HandlerTypeSearchIdsStores, HandlerTypeSearchIdsWeb, HandlerTypeSearchIdsFoody, HandlerTypeSearchIdsDishes: // 特殊转化
		return PipelineTypeSearch
	case HandlerTypeSearchMainSite:
		return PipelineTypeSearchMainSite
	case HandlerTypeSearchFoodTotalNum:
		return PipelineTypeSearch // 内部转化为 Food
	case HandlerTypeSearchCollection:
		return PipelineTypeSearchCollection
	case HandlerTypeSearchStoreDishes:
		return PipelineTypeSearchStoreDishes
	case HandlerTypeSearchHistoryOrder:
		return PipelineTypeSearchHistoryOrders
	default:
		return PipelineTypeSearch
	}
}
