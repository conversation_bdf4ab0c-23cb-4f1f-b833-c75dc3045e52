package errno

// Code generated by errno -type ErrNo; DO NOT EDIT.

func Reflect(code uint32) ErrNo {
	err, ok := errnos[code]
	if !ok {
		return Unknown
	}
	return err
}

var errnos = map[uint32]ErrNo{
	0:       Ok,
	1:       Unknown,
	13:      ServerPanic,
	1000:    ErrParamsInvalid,
	1004:    ErrServerUnavailable,
	1008:    ErrInternalServer,
	1100:    ErrRedisUnknown,
	1101:    ErrRedisNil,
	1200:    ErrDBUnknown,
	1300:    ErrESUnknown,
	1401:    ErrS3BucketNotFound,
	2000:    ErrDataTypeConvert,
	3000:    ErrLimitSize,
	1110023: ErrStoreNotExisted,
}
