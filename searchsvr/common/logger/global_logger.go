package logger

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
)

func InitLogkit() {
	var option logkit.Option
	if env.GetEnv() == "live" || env.GetEnv() == "liveish" {
		option = logkit.Level("info")
	} else {
		option = logkit.Level("debug")
	}
	err := logkit.Init(
		option,
		logkit.Path("./log"),
		logkit.EnableCaller(true),
	)
	if err != nil {
		print("Init logkit error", err)
	}
}

func MyDebug(ctx context.Context, isDebug bool, msg string, fields ...logkit.Field) {
	if isDebug {
		logkit.FromContext(ctx).Info(msg, fields...)
	} else {
		if !env.IsLive() {
			logkit.FromContext(ctx).Debug(msg, fields...)
		}
	}
}
