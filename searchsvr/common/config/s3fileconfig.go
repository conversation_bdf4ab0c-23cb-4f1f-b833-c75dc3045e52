package config

import (
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

type S3FileName struct {
	UnsettledStore          string `yaml:"unsettled_store"`
	AlgoTopInterventionText string `yaml:"algo_top_intervention_text"`
	StoreCtrCvr             string `yaml:"offline_algo_factor"`
	UserLevel               string `yaml:"user_level"`
	UeFactorFile            string `yaml:"ue_factor_file"`
	PcFactorFile            string `yaml:"pc_factor_file"`
}

func (s *S3FileName) GetUnSettledStoreFileName() string {
	if s == nil {
		return ""
	}
	return s.UnsettledStore + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".txt"
}

func (s *S3FileName) GetAlgoTopInterventionTextFileName(version string) (string, error) {
	if s == nil {
		return "", fmt.Errorf("S3FileName is nil")
	}
	if s.AlgoTopInterventionText == "" {
		return "", fmt.Errorf("algo_top_intervention_text in s3 config is empty")
	}
	return s.AlgoTopInterventionText + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + "_" + version + ".txt", nil
}

func (s *S3FileName) GetStoreCtrCvr() string {
	if s == nil {
		return ""
	}
	return s.StoreCtrCvr + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".csv"
}

func (s *S3FileName) GetUserLevel() string {
	if s == nil {
		return ""
	}
	return s.UserLevel + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".txt"
}

func (s *S3FileName) GetUeFactorFile() string {
	if s == nil {
		return ""
	}
	return s.UeFactorFile + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".csv"
}

func (s *S3FileName) GetPcFactorFile() string {
	if s == nil {
		return ""
	}
	return s.PcFactorFile + "_" + env.GetCID() + "_" + util.GetEnvLiveish(false)
}
