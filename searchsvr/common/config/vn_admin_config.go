package config

import (
	"context"
	"encoding/json"
	"net/http"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

var VNAdminCfg = &VNAdminConfig{}
var ApiServerHost string

type VNAdminConfig struct {
	client                  *http.Client
	mutex                   sync.RWMutex
	mapAllTimeRangeSettings map[string][]*VNTimeRangeSetting
}

func init() {
	VNAdminCfg.client = &http.Client{
		Timeout: time.Duration(3) * time.Second,
	}
	VNAdminCfg.mapAllTimeRangeSettings = make(map[string][]*VNTimeRangeSetting)
	if cid.IsVN() == false {
		return
	}
	ApiServerHost = getApiServerHost()
	VNAdminCfg.ReloadAllTimeRangeSettings()
	goroutine.WithGo(
		context.Background(),
		"VnAdminConfig",
		func(params ...interface{}) {
			for {
				logkit.Info("vn admin config: reloading")
				time.Sleep(time.Duration(5) * time.Minute)
				VNAdminCfg.ReloadAllTimeRangeSettings()
			}
		},
	)
}

func getApiServerHost() string {
	var apiServerHost string
	switch env.GetEnv() {
	case "live", "liveish":
		apiServerHost = "http://api-server-s2s.shopeefood.vn"
	case "staging":
		apiServerHost = "http://api-server-s2s.staging.shopeefood.vn"
	case "uat":
		apiServerHost = "http://api-server-s2s.uat.shopeefood.vn"
	case "test":
		apiServerHost = "http://api-server-s2s.test.shopeefood.vn"
	default:
		apiServerHost = "http://api-server-s2s.staging.shopeefood.vn"
		logkit.Error("vn admin config: unknown env, using staging api server host", logkit.String("env", env.GetEnv()))
	}
	return apiServerHost
}

func getVNHttpReq(path string) *http.Request {
	url := ApiServerHost + path
	req, err := http.NewRequest("POST", ApiServerHost+path, nil)
	if err != nil {
		logkit.Error("vn admin config: getVNHttpReq fail", logkit.Err(err), logkit.String("url", url))
		metric_reporter.ReportRequestNumber("VNAdmin", "compose http request fail", "", 1, "0", "")
		return nil
	}
	req.Header.Set("x-foody-request-id", "10086")
	req.Header.Set("x-foody-api-version", "5")
	req.Header.Set("x-foody-app-id", "1004")
	req.Header.Set("x-foody-country", "VN")
	req.Header.Set("x-foody-language", "vi")
	return req
}

type VNAllTimeRangeSettings struct {
	Reply struct {
		Settings []*VNTimeRangeSetting `json:"settings"`
	} `json:"reply"`
	Result string `json:"result"`
}

const (
	SettingDayOfWeek  = 1
	SettingCustomDate = 2
	SettingDefault    = 3
)

type VNTimeRangeSetting struct {
	Name           string `json:"name"`
	Value          string `json:"value"`
	Type           int    `json:"type"`
	FoodyServiceId int    `json:"foody_service_id"`
	CityId         int    `json:"city_id"`
	DistrictId     int    `json:"district_id"`
	WardId         int    `json:"ward_id"`
	CustomDate     string `json:"custom_date"`
	StartTime      string `json:"start_time"`
	EndTime        string `json:"end_time"`
	DayOfWeek      int    `json:"day_of_week"`
}

func (c *VNAdminConfig) ReloadAllTimeRangeSettings() {
	if c.client == nil {
		return
	}

	req := getVNHttpReq("/setting/get_all_time_range_settings")
	if req == nil {
		return
	}

	rsp, err := c.client.Do(req)
	if err != nil {
		logkit.Error("vn admin config: ReloadAllTimeRangeSettings http error", logkit.Err(err))
		metric_reporter.ReportRequestNumber("VNAdmin", "get_all_time_range_settings query", "", 1, "0", "")
		return
	}

	defer rsp.Body.Close()
	settings := &VNAllTimeRangeSettings{}
	err = json.NewDecoder(rsp.Body).Decode(settings)
	if err != nil {
		logkit.Error("vn admin config: ReloadAllTimeRangeSettings decode error", logkit.Err(err))
		metric_reporter.ReportRequestNumber("VNAdmin", "get_all_time_range_settings decode", "", 1, "0", "")
		return
	}

	c.mutex.Lock()
	defer c.mutex.Unlock()
	c.mapAllTimeRangeSettings = make(map[string][]*VNTimeRangeSetting, len(c.mapAllTimeRangeSettings))
	for _, item := range settings.Reply.Settings {
		// 原逻辑如此，SettingDefault 直接放过
		if item.Type != SettingDefault {
			nowSecs := util.GetTodaySeconds()
			startSecs := util.GetTodaySecondsFromString(item.StartTime)
			endSecs := util.GetTodaySecondsFromString(item.EndTime)
			if startSecs >= 0 && endSecs >= 0 {
				if nowSecs < startSecs || nowSecs > endSecs {
					continue // 超过配置的时间
				}
			}

			if item.Type == SettingCustomDate {
				if util.GetStrYYYYMMDD(0) != item.CustomDate {
					continue // 日期不匹配
				}
			} else if item.Type == SettingDayOfWeek {
				weekday := util.GetLocalWeekDay()
				if weekday == 0 {
					weekday = 7
				}

				if weekday != item.DayOfWeek {
					continue // 星期不匹配
				}
			}
		}

		if _, ok := c.mapAllTimeRangeSettings[item.Name]; !ok {
			c.mapAllTimeRangeSettings[item.Name] = make([]*VNTimeRangeSetting, 0)
		}

		c.mapAllTimeRangeSettings[item.Name] = append(c.mapAllTimeRangeSettings[item.Name], item)
	}

	logkit.Info("VNAllTimeRangeSettings", logkit.Any("len", len(c.mapAllTimeRangeSettings)))
}

func (c *VNAdminConfig) GetTimeRangeSetting(k string, city, district, ward int) string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	for _, val := range c.mapAllTimeRangeSettings[k] {
		if city > 0 && val.CityId != city {
			continue
		}
		if district > 0 && val.DistrictId != district {
			continue
		}
		if ward > 0 && val.WardId != ward {
			continue
		}

		return val.Value
	}

	return ""
}

func (c *VNAdminConfig) GetTimeRangeSettingForInt(k string, city, district, ward int, default_ int32) int32 {
	v := c.GetTimeRangeSetting(k, city, district, ward)
	if v == "" {
		return default_
	}

	i, err := strconv.ParseInt(v, 10, 32)
	if err != nil {
		return default_
	}

	return int32(i)
}
