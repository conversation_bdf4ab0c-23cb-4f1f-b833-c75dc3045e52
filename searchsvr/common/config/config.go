package config

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"sync"
	"time"

	"gopkg.in/yaml.v2"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/microkit"
	goconfig "github.com/micro/go-micro/config"
	"github.com/micro/go-micro/config/source/file"

	kconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/kafka/config"

	esconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/elastic/config"
	mysqlconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/config"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/common_client"
	redisconfig "git.garena.com/shopee/o2o-intelligence/common/common-lib/redis/config"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"github.com/go-sql-driver/mysql"
)

type StoreRedisConf struct {
	StoreExpire uint64 `yaml:"store_expire"`
}

type GDSKafka kconfig.Kafka

type WatchSchema []string
type DataStreamMysql mysqlconfig.Mysql
type DataStreamMysqlDB sql.DB
type DataPlatformMysql mysqlconfig.Mysql
type DataPlatformMysqlDB sql.DB

type ShopConfig struct {
	BaseConfig        microkit.BaseConfig
	once              sync.Once
	Elastic           esconfig.Elastic                `yaml:"elastic"`
	DataStreamMysql   DataStreamMysql                 `yaml:"datastream_mysql"`
	DataPlatformMysql DataPlatformMysql               `yaml:"dataplatform_mysql"`
	Redis             redisconfig.Redis               `yaml:"redis"`
	S3FileName        *S3FileName                     `yaml:"s3_file_name"`
	S3Config          s3.S3Config                     `yaml:"s3"`
	GDS               GDSKafka                        `yaml:"gds"`
	GdsWatchSchema    WatchSchema                     `yaml:"gdswatch_schema"`
	MicroClientConfig common_client.MicroClientConfig `yaml:"micro_config"`
	ReplicaElastic    esconfig.Elastic                `yaml:"replica_elastic"`
	GeoConfig         GeoConfig                       `yaml:"geo"`
	VnHttpConfig      VNHttpConfig                    `yaml:"vn_http"`
}

func (h *ShopConfig) Scan(configFile string) (err error) {
	h.once.Do(func() {
		goconfig.Load(file.NewSource(
			file.WithPath(configFile),
		))

		conf := goconfig.DefaultConfig
		err = h.BaseConfig.Scan(conf)
		if err != nil {
			logkit.With(logkit.String("configFile", configFile), logkit.Err(err)).Error("scan config failed")
			return
		}

		//Read Values From Config if need
		var serverName string
		{
			serverName = conf.Get("server.name").String("default-svr")
		}
		h.Load(configFile)
		// custom config
		err := conf.Scan(&h)
		if err != nil {
			logkit.With(logkit.String("configFile", configFile), logkit.Err(err)).Error("scan custom config failed")
		}

		// watch change in config file
		go h.watch()

		logkit.With(logkit.String("serverName", serverName)).Info("Scan Config finished")
	})
	return
}

func (h *ShopConfig) watch() {
	w, err := goconfig.Watch("log")
	if err != nil {
		// do something
		logkit.With(logkit.Err(err)).Warn("watch config failed")
	}
	for {
		// wait for next value
		_, err := w.Next()

		if err != nil {
			logkit.With(logkit.Err(err)).Warn("next config failed")
			continue
		}
	}
}

func (h *ShopConfig) Load(configFile string) error {
	data, err := ioutil.ReadFile(configFile)
	if err != nil {
		logkit.Error("Read config parser", logkit.String("filename", configFile), logkit.Err(err))
		return err
	}

	err = yaml.Unmarshal(data, h)
	if err != nil {
		logkit.Error("Unmarshl config parser", logkit.String("filename", configFile), logkit.Err(err))
		return err
	}
	return nil
}

const (
	Chatset             = "utf8mb4"
	DefaultDialTimeout  = 1000
	DefaultReadTimeout  = 3000
	DefaultWriteTimeout = 3000
	DefaultMaxIdleConns = 50
	DefaultMaxOpenConns = 100
	DefaultMaxLifeTime  = 3600000
)

// New mysql client
func NewDataStreamMysqlDB(m *DataStreamMysql) *DataStreamMysqlDB {
	addr := fmt.Sprintf("%v:%v", m.Host, m.Port)
	if m.DialTimeout == 0 {
		m.DialTimeout = DefaultDialTimeout
	}
	if m.ReadTimeout == 0 {
		m.ReadTimeout = DefaultReadTimeout
	}
	if m.WriteTimeout == 0 {
		m.WriteTimeout = DefaultWriteTimeout
	}
	if m.MaxIdleConns == 0 {
		m.MaxIdleConns = DefaultMaxIdleConns
	}
	if m.MaxOpenConns == 0 {
		m.MaxOpenConns = DefaultMaxOpenConns
	}
	if m.MaxLifetime == 0 {
		m.MaxLifetime = DefaultMaxLifeTime
	}

	mysqlParams := make(map[string]string, 1)
	mysqlParams["charset"] = Chatset

	connConfig := mysql.Config{
		User:                 m.User,
		Passwd:               m.Password,
		Addr:                 addr,
		DBName:               m.Schema,
		Params:               mysqlParams,
		Net:                  "tcp",
		MultiStatements:      true,
		AllowNativePasswords: true,
		ReadTimeout:          time.Duration(m.ReadTimeout) * time.Millisecond,
		WriteTimeout:         time.Duration(m.WriteTimeout) * time.Millisecond,
		Timeout:              time.Duration(m.DialTimeout) * time.Millisecond,
	}
	db, err := sql.Open("mysql", connConfig.FormatDSN())
	if err != nil {
		logkit.Fatal("Open mysql failed", logkit.Err(err))
	}
	err = db.Ping()
	if err != nil {
		//logkit.Fatal("Ping mysql failed", logkit.Err(err), logkit.String("addr", addr))
	}
	// configure driver
	db.SetConnMaxLifetime(time.Duration(m.MaxLifetime) * time.Millisecond)
	db.SetMaxIdleConns(m.MaxIdleConns)
	db.SetMaxOpenConns(m.MaxOpenConns)
	return (*DataStreamMysqlDB)(db)
}

// New mysql client
func NewDataPlatformMysqlDB(m *DataPlatformMysql) *DataPlatformMysqlDB {
	addr := fmt.Sprintf("%v:%v", m.Host, m.Port)
	if m.DialTimeout == 0 {
		m.DialTimeout = DefaultDialTimeout
	}
	if m.ReadTimeout == 0 {
		m.ReadTimeout = DefaultReadTimeout
	}
	if m.WriteTimeout == 0 {
		m.WriteTimeout = DefaultWriteTimeout
	}
	if m.MaxIdleConns == 0 {
		m.MaxIdleConns = DefaultMaxIdleConns
	}
	if m.MaxOpenConns == 0 {
		m.MaxOpenConns = DefaultMaxOpenConns
	}
	if m.MaxLifetime == 0 {
		m.MaxLifetime = DefaultMaxLifeTime
	}

	mysqlParams := make(map[string]string, 1)
	mysqlParams["charset"] = Chatset

	connConfig := mysql.Config{
		User:                 m.User,
		Passwd:               m.Password,
		Addr:                 addr,
		DBName:               m.Schema,
		Params:               mysqlParams,
		Net:                  "tcp",
		MultiStatements:      true,
		AllowNativePasswords: true,
		ReadTimeout:          time.Duration(m.ReadTimeout) * time.Millisecond,
		WriteTimeout:         time.Duration(m.WriteTimeout) * time.Millisecond,
		Timeout:              time.Duration(m.DialTimeout) * time.Millisecond,
	}
	db, err := sql.Open("mysql", connConfig.FormatDSN())
	if err != nil {
		logkit.Fatal("Open mysql failed", logkit.Err(err))
	}
	err = db.Ping()
	if err != nil {
		logkit.Error("Ping mysql failed", logkit.Err(err), logkit.String("addr", addr))
	}
	// configure driver
	db.SetConnMaxLifetime(time.Duration(m.MaxLifetime) * time.Millisecond)
	db.SetMaxIdleConns(m.MaxIdleConns)
	db.SetMaxOpenConns(m.MaxOpenConns)
	return (*DataPlatformMysqlDB)(db)
}
