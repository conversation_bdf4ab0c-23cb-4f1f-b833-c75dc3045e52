syntax = "proto2";

import "git.garena.com/shopee/foody/service/pb/foody_base/foody_base.proto";
import "git.garena.com/shopee/foody/service/pb/foody_merchandise/foody_merchandise.proto";
import "git.garena.com/shopee/foody/service/pb/voucher_core/voucher_core.proto";
import "git.garena.com/shopee/foody/service/pb/voucher_task/voucher_task.proto";
import "spex/protobuf/service.proto";

package foody_promotion;

// Code generated by spcli. DO NOT EDIT.
//
// namespace shopeefood.promotion.promotion
//
// commands {
//   shopeefood.promotion.promotion.create_order_discount_campaign
//   shopeefood.promotion.promotion.update_order_discount_campaign
//   shopeefood.promotion.promotion.update_order_discount_campaign_status
//   shopeefood.promotion.promotion.get_order_discount_campaign
//   shopeefood.promotion.promotion.search_order_discount_campaign
//   shopeefood.promotion.promotion.create_item_discount_campaign
//   shopeefood.promotion.promotion.update_item_discount_campaign
//   shopeefood.promotion.promotion.item_discount_campaign_draft
//   shopeefood.promotion.promotion.query_item_discount_campaign_list
//   shopeefood.promotion.promotion.query_item_discount_campaign
//   shopeefood.promotion.promotion.m_create_item_discount_campaign
//   shopeefood.promotion.promotion.m_update_item_discount_campaign
//   shopeefood.promotion.promotion.get_order_promotion_detail
//   shopeefood.promotion.promotion.get_voucher_type_list
//   shopeefood.promotion.promotion.query_available_item_discount_list
//   shopeefood.promotion.promotion.m_disable_item_discount_campaign
//   shopeefood.promotion.promotion.m_get_store_promotion
//   shopeefood.promotion.promotion.get_store_promotion
//   shopeefood.promotion.promotion.m_get_item_discount
//   shopeefood.promotion.promotion.m_get_item_discount_v2
//   shopeefood.promotion.promotion.batch_m_get_item_discount
//   shopeefood.promotion.promotion.batch_m_get_item_discount_v2
//   shopeefood.promotion.promotion.m_get_store_voucher
//   shopeefood.promotion.promotion.m_get_store_voucher_v2
//   shopeefood.promotion.promotion.m_get_store_voucher_brief
//   shopeefood.promotion.promotion.get_recommend_voucher
//   shopeefood.promotion.promotion.get_recommend_promotion
//   shopeefood.promotion.promotion.claim_food_voucher
//   shopeefood.promotion.promotion.get_target_voucher
//   shopeefood.promotion.promotion.query_nearby_available_vouchers
//   shopeefood.promotion.promotion.get_promotion_draft
//   shopeefood.promotion.promotion.use_promotion
//   shopeefood.promotion.promotion.callback_cancel_voucher
//   shopeefood.promotion.promotion.promotion_preview
//   shopeefood.promotion.promotion.commit_promotion_preview
//   shopeefood.promotion.promotion.cancel_promotion_preview
//   shopeefood.promotion.promotion.refund
//   shopeefood.promotion.promotion.part_refund
//   shopeefood.promotion.promotion.m_get_store_promotion_brief
//   shopeefood.promotion.promotion.m_get_flash_sale_item_stock
//   shopeefood.promotion.promotion.m_get_flash_sale_item_discount
//   shopeefood.promotion.promotion.m_check_flash_sale_item_discount_price
//   shopeefood.promotion.promotion.m_get_flash_sale_item
//   shopeefood.promotion.promotion.m_check_item_price
//   shopeefood.promotion.promotion.get_recently_modified
//   shopeefood.promotion.promotion.is_display_red_dot
//   shopeefood.promotion.promotion.check_promotion_limit
//   shopeefood.promotion.promotion.get_promotion_list
//   shopeefood.promotion.promotion.sync_promotion_list
//   shopeefood.promotion.promotion.get_promotion_detail
//   shopeefood.promotion.promotion.get_promotion_tool_list
//   shopeefood.promotion.promotion.m_get_shopee_food_voucher
//   shopeefood.promotion.promotion.check_shopee_food_voucher_user_claim_status
//   shopeefood.promotion.promotion.create_voucher
//   shopeefood.promotion.promotion.update_voucher
//   shopeefood.promotion.promotion.get_voucher
//   shopeefood.promotion.promotion.search_voucher
//   shopeefood.promotion.promotion.create_voucher_dispatch_task
//   shopeefood.promotion.promotion.get_voucher_dispatch_task_list
//   shopeefood.promotion.promotion.create_voucher_codes_task
//   shopeefood.promotion.promotion.update_voucher_codes_task
//   shopeefood.promotion.promotion.create_voucher_template
//   shopeefood.promotion.promotion.update_voucher_template
//   shopeefood.promotion.promotion.get_voucher_template
//   shopeefood.promotion.promotion.search_voucher_template
//   shopeefood.promotion.promotion.get_template_vouchers
//   shopeefood.promotion.promotion.create_voucher_template_update_task
//   shopeefood.promotion.promotion.get_voucher_template_update_task_list
//   shopeefood.promotion.promotion.cancel_voucher_template_update_task
//   shopeefood.promotion.promotion.get_spx_order_promotion_detail
//   shopeefood.promotion.promotion.claim_voucher
//   shopeefood.promotion.promotion.list_applicable_voucher
//   shopeefood.promotion.promotion.spx_get_promotion_draft
//   shopeefood.promotion.promotion.spx_use_promotion
//   shopeefood.promotion.promotion.spx_refund
//   shopeefood.promotion.promotion.get_voucher_stacking
//   shopeefood.promotion.promotion.get_voucher_batch
//   shopeefood.promotion.promotion.spx_callback_cancel_voucher
//   shopeefood.promotion.promotion.spx_callback_cancel_coin
//   shopeefood.promotion.promotion.batch_distribute_voucher
//   shopeefood.promotion.promotion.batch_get_voucher_from_spx_voucher_wallet
//   shopeefood.promotion.promotion.claim_voucher_from_spx_voucher_wallet
//   shopeefood.promotion.promotion.get_co_fund_campaign
//   shopeefood.promotion.promotion.delete_co_fund_campaign
//   shopeefood.promotion.promotion.create_co_fund_campaign
//   shopeefood.promotion.promotion.update_co_fund_campaign
//   shopeefood.promotion.promotion.search_co_fund_campaign
//   shopeefood.promotion.promotion.create_file_upload_async_task
//   shopeefood.promotion.promotion.get_file_upload_async_tasks
//   shopeefood.promotion.promotion.get_promotion_available_payment_method
//   shopeefood.promotion.promotion.get_microsite_target_voucher_component_template
//   shopeefood.promotion.promotion.validate_microsite_target_voucher_component_template
//   shopeefood.promotion.promotion.transform_microsite_target_voucher_component_template
//   shopeefood.promotion.promotion.create_merchandise
//   shopeefood.promotion.promotion.get_merchandise
//   shopeefood.promotion.promotion.search_merchandises
//   shopeefood.promotion.promotion.update_merchandise_status
//   shopeefood.promotion.promotion.m_get_user_merchandise
//   shopeefood.promotion.promotion.query_available_merchandise
//   shopeefood.promotion.promotion.dispatch_merchandise_benefits
//   shopeefood.promotion.promotion.verify_buyer_qualifications
// }

service Promotion {// 模块名
  option (service.service) = {
    servicename: "shopeefood.promotion.promotion" // 添加spex的namespace
  };

  // ShopeeFood
  // admin
  rpc CreateOrderDiscountCampaign(CreateOrderDiscountCampaignRequest) returns (CreateOrderDiscountCampaignResponse) {}
  rpc UpdateOrderDiscountCampaign(UpdateOrderDiscountCampaignRequest) returns (UpdateOrderDiscountCampaignResponse) {}
  rpc UpdateOrderDiscountCampaignStatus(UpdateOrderDiscountCampaignStatusRequest) returns (UpdateOrderDiscountCampaignStatusResponse) {}
  rpc GetOrderDiscountCampaign(GetOrderDiscountCampaignRequest) returns (GetOrderDiscountCampaignResponse) {}
  rpc SearchOrderDiscountCampaign(SearchOrderDiscountCampaignRequest) returns (SearchOrderDiscountCampaignResponse) {}
  rpc CreateItemDiscountCampaign(CreateItemDiscountCampaignRequest) returns (CreateItemDiscountCampaignResponse) {}
  rpc UpdateItemDiscountCampaign(UpdateItemDiscountCampaignRequest) returns (UpdateItemDiscountCampaignResponse) {}
  rpc ItemDiscountCampaignDraft(ItemDiscountCampaignDraftRequest) returns (ItemDiscountCampaignDraftResponse) {}
  rpc QueryItemDiscountCampaignList(QueryItemDiscountCampaignListRequest) returns (QueryItemDiscountCampaignListResponse) {}
  rpc QueryItemDiscountCampaign(QueryItemDiscountCampaignRequest) returns (QueryItemDiscountCampaignResponse) {}
  rpc MCreateItemDiscountCampaign(MCreateItemDiscountCampaignRequest) returns (MCreateItemDiscountCampaignResponse) {}
  rpc MUpdateItemDiscountCampaign(MUpdateItemDiscountCampaignRequest) returns (MUpdateItemDiscountCampaignResponse) {}
  rpc GetOrderPromotionDetail(GetOrderPromotionDetailRequest) returns (GetOrderPromotionDetailResponse) {}
  rpc GetVoucherTypeList(GetVoucherTypeListRequest) returns (GetVoucherTypeListResponse) {}
  rpc QueryAvailableItemDiscountList(QueryItemDiscountRequest) returns (QueryItemDiscountResponse){}
  rpc MDisableItemDiscountCampaign(MDisableItemDiscountCampaignRequest) returns (MDisableItemDiscountCampaignResponse) {}

  // buyer
  rpc MGetStorePromotion(MGetStorePromotionRequest) returns (MGetStorePromotionResponse) {}
  rpc GetStorePromotion(GetStorePromotionRequest) returns (GetStorePromotionResponse) {}
  //Deprecated
  rpc MGetItemDiscount(MGetItemDiscountRequest) returns (MGetItemDiscountResponse) {}
  rpc MGetItemDiscountV2(MGetItemDiscountV2Request) returns (MGetItemDiscountV2Response) {}
  //Deprecated
  rpc BatchMGetItemDiscount(BatchMGetItemDiscountRequest) returns (BatchMGetItemDiscountResponse) {}
  rpc BatchMGetItemDiscountV2(BatchMGetItemDiscountV2Request) returns (BatchMGetItemDiscountV2Response) {}
  //Deprecated
  rpc MGetStoreVoucher(MGetStoreVoucherRequest) returns (MGetStoreVoucherResponse) {}
  rpc MGetStoreVoucherV2(GetVouchersByStoreBatchRequest) returns (GetVouchersByStoreBatchResponse) {}
  rpc MGetStoreVoucherBrief(GetStoreVoucherBriefRequest) returns (GetStoreVoucherBriefResponse) {}
  rpc GetRecommendVoucher(RecommendVouchersRequest) returns (RecommendFoodVouchersResponse) {}
  rpc GetRecommendPromotion(GetRecommendPromotionRequest) returns (GetRecommendPromotionResponse) {}
  rpc ClaimFoodVoucher(ClaimFoodVoucherRequest) returns (ClaimFoodVoucherResponse) {}
  rpc GetTargetVoucher(GetTargetVoucherRequest) returns (GetTargetVoucherResponse) {}
  // 通过获取周边可用的券列表
  // Filtering logic
  //  Within voucher type configured in admin [this info is stored in Food side]
  //  Within claim period or within use period if claimed
  //  Within target user scope (e.g. new user)
  //  Not fully used by this user yet
  //  At least one store will be displayed in store list after click use (see section 2.3 on the criteria)
  //Ranking logic
  //  P1: can claim / can use vouchers > fully claimed vouchers [需要补充设计]
  //  P2: [if above is the same]  Rank by reward type: food vouchers > shipping fee vouchers > coins cashback vouchers
  //  P3: [if above is the same] Rank by discount amount: value > %, DESC (e.g. 50k > 30k > 90% > 80%)
  //  P4: [if above is the same] Rank by cap (if any): value, DESC(e.g. no cap > cap at 30k > cap at 20k)
  //  P5: [if above is the same]  Voucher ID (newly created voucher first)
  rpc QueryNearbyAvailableVouchers(QueryNearbyAvailableVoucherRequest) returns (QueryNearbyAvailableVoucherResponse) {}




  // order
  // buyer place order
  rpc GetPromotionDraft(GetPromotionDraftRequest) returns (GetPromotionDraftResponse) {}
  rpc UsePromotion(UsePromotionRequest) returns (UsePromotionResponse) {}
  rpc CallbackCancelVoucher(CallbackCancelVoucherRequest) returns (CallbackCancelVoucherResponse) {}
  // driver modify order
  rpc PromotionPreview(PromotionPreviewRequest) returns (PromotionPreviewResponse) {}
  rpc CommitPromotionPreview(CommitPromotionPreviewRequest) returns (CommitPromotionPreviewResponse) {}
  rpc CancelPromotionPreview(CancelPromotionPreviewRequest) returns (CancelPromotionPreviewResponse) {}

  rpc Refund(RefundRequest) returns (RefundResponse) {}
  rpc PartRefund(PartRefundRequest) returns (PartRefundResponse) {}


  // algo team
  rpc MGetStorePromotionBrief(MGetStorePromotionBriefRequest) returns (MGetStorePromotionBriefResponse) {}

  // flash sale
  // 获取库存信息
  rpc MGetFlashSaleItemStock(MGetFlashSaleItemStockRequest) returns (MGetFlashSaleItemStockResponse) {}

  // 获取flash sale 菜品详情
  rpc MGetFlashSaleItemDiscount(MGetFlashSaleItemDiscountRequest) returns (MGetFlashSaleItemDiscountResponse) {}
  // 允许&商家修改dish价格时，校验flash sale 菜品价格
  rpc MCheckFlashSaleItemDiscountPrice(MCheckFlashSaleItemDiscountPriceRequest) returns (MCheckFlashSaleItemDiscountPriceResponse) {}
  rpc MGetFlashSaleItem(MGetFlashSaleItemRequest) returns (MGetFlashSaleItemResponse) {}

  rpc MCheckItemPrice(MCheckItemPriceRequest) returns (MCheckItemPriceResponse) {}
  rpc GetRecentlyModified (GetRecentlyModifiedRequest) returns (GetRecentlyModifiedResponse) {}
  rpc IsDisplayRedDot (IsDisplayRedDotRequest) returns (IsDisplayRedDotResponse) {}

  rpc CheckPromotionLimit(CheckPromotionLimitRequest) returns (CheckPromotionLimitResponse) {}

  // 实时拉取混排的 promotion 列表
  rpc GetPromotionList (GetPromotionListRequest) returns (GetPromotionListResponse) {}
  // 同步拉取指定类型的 promotion 数据
  rpc SyncPromotionList (SyncPromotionListRequest) returns (SyncPromotionListResponse) {}
  // 获取指定类型的 promotion 详情
  rpc GetPromotionDetail (GetPromotionDetailRequest) returns (GetPromotionDetailResponse) {}
  // 获取 promotion tool 列表
  rpc GetPromotionToolList (GetPromotionToolListRequest) returns (GetPromotionToolListResponse) {}

  rpc MGetShopeeFoodVoucher(MGetShopeeFoodVoucherRequest) returns (MGetShopeeFoodVoucherResponse) {}
  rpc CheckShopeeFoodVoucherUserClaimStatus(CheckShopeeFoodVoucherUserClaimStatusRequest)returns(CheckShopeeFoodVoucherUserClaimStatusResponse){}

  // admin
  rpc CreateVoucher(CreateVoucherRequest) returns (CreateVoucherResponse) {}
  rpc UpdateVoucher(UpdateVoucherRequest) returns (UpdateVoucherResponse) {}
  rpc GetVoucher(GetVoucherRequest) returns (GetVoucherResponse) {}
  rpc SearchVoucher(SearchVoucherRequest) returns (SearchVoucherResponse) {}
  rpc CreateVoucherDispatchTask(CreateVoucherDispatchTaskRequest) returns (CreateVoucherDispatchTaskResponse) {}
  rpc GetVoucherDispatchTaskList(GetVoucherDispatchTaskListRequest) returns (GetVoucherDispatchTaskListResponse) {}
  rpc CreateVoucherCodesTask(CreateVoucherCodesTaskRequest) returns(CreateVoucherCodesTaskResponse){}
  rpc UpdateVoucherCodesTask(UpdateVoucherCodesTaskRequest) returns(UpdateVoucherCodesTaskResponse){}
  rpc CreateVoucherTemplate(CreateVoucherTemplateRequest) returns (CreateVoucherTemplateResponse) {}
  rpc UpdateVoucherTemplate(UpdateVoucherTemplateRequest) returns (UpdateVoucherTemplateResponse) {}
  rpc GetVoucherTemplate(GetVoucherTemplateRequest) returns (GetVoucherTemplateResponse) {}
  rpc SearchVoucherTemplate(SearchVoucherTemplateRequest) returns (SearchVoucherTemplateResponse) {}
  rpc GetTemplateVouchers(GetTemplateVouchersRequest) returns (GetTemplateVouchersResponse) {}
  rpc CreateVoucherTemplateUpdateTask(CreateVoucherTemplateUpdateTaskRequest) returns (CreateVoucherTemplateUpdateTaskResponse) {}
  rpc GetVoucherTemplateUpdateTaskList(GetVoucherTemplateUpdateTaskListRequest) returns (GetVoucherTemplateUpdateTaskListResponse) {}
  rpc CancelVoucherTemplateUpdateTask(CancelVoucherTemplateUpdateTaskRequest) returns (CancelVoucherTemplateUpdateTaskResponse) {}
  rpc GetSpxOrderPromotionDetail(GetSpxOrderPromotionDetailRequest) returns (GetSpxOrderPromotionDetailResponse) {}
  // buyer
  rpc ClaimVoucher(ClaimVoucherRequest) returns (ClaimVoucherResponse) {}
  rpc ListApplicableVoucher(ListApplicableVoucherRequest) returns (ListApplicableVoucherResponse) {}
  rpc SPXGetPromotionDraft(SPXGetPromotionDraftRequest) returns (SPXGetPromotionDraftResponse) {}
  rpc SPXUsePromotion(SPXUsePromotionRequest) returns (SPXUsePromotionResponse) {}
  rpc SPXRefund(SPXRefundPromotionRequest) returns (SPXRefundPromotionResponse) {}
  rpc GetVoucherStacking(GetVoucherStackingRequest) returns (GetVoucherStackingResponse) {}
  rpc GetVoucherBatch(GetVoucherBatchRequest) returns (GetVoucherBatchResponse){}

  // gateway
  rpc SPXCallbackCancelVoucher(CallbackCancelUseRequest) returns (CallbackCancelUseResponse) {}
  rpc SPXCallbackCancelCoin(CallbackCancelUseRequest) returns (CallbackCancelUseResponse) {}
  rpc BatchDistributeVoucher(BatchDistributeVoucherRequest) returns (BatchDistributeVoucherResponse) {}
  rpc BatchGetVoucherFromSPXVoucherWallet(BatchGetVoucherFromSPXVoucherWalletRequest) returns (BatchGetVoucherFromSPXVoucherWalletResponse) {}
  rpc ClaimVoucherFromSPXVoucherWallet(ClaimVoucherFromSPXVoucherWalletRequest) returns (ClaimVoucherFromSPXVoucherWalletResponse) {}

  //co-found
  rpc GetCoFundCampaign(GetCoFundCampaignRequest) returns (GetCoFundCampaignResponse) {}
  rpc DeleteCoFundCampaign(DeleteCoFundCampaignRequest) returns (DeleteCoFundCampaignResponse) {}
  rpc CreateCoFundCampaign(CreateCoFundCampaignRequest) returns (CreateCoFundCampaignResponse) {}
  rpc UpdateCoFundCampaign(UpdateCoFundCampaignRequest) returns (UpdateCoFundCampaignResponse) {}
  rpc SearchCoFundCampaign(SearchCoFundCampaignRequest) returns (SearchCoFundCampaignResponse) {}
  // file upload async task
  rpc CreateFileUploadAsyncTask(CreateFileUploadAsyncTaskRequest) returns (CreateFileUploadAsyncTaskResponse) {}
  rpc GetFileUploadAsyncTasks(GetFileUploadAsyncTasksRequest) returns (GetFileUploadAsyncTasksResponse) {}
  rpc GetPromotionAvailablePaymentMethod(GetPromotionAvailablePaymentMethodRequest) returns (GetPromotionAvailablePaymentMethodResponse){}

  // microsite
  rpc GetMicrositeTargetVoucherComponentTemplate(GetMicrositeTargetVoucherComponentTemplateRequest) returns(GetMicrositeTargetVoucherComponentTemplateResponse){}
  rpc ValidateMicrositeTargetVoucherComponentTemplate(ValidateMicrositeTargetVoucherComponentTemplateRequest) returns(ValidateMicrositeTargetVoucherComponentTemplateResponse){}
  rpc TransformMicrositeTargetVoucherComponentTemplate(TransformMicrositeTargetVoucherComponentTemplateRequest) returns(TransformMicrositeTargetVoucherComponentTemplateResponse){}

  // package
  rpc CreateMerchandise(CreateMerchandiseRequest) returns (CreateMerchandiseResponse) {}
  rpc GetMerchandise(GetMerchandiseRequest) returns (GetMerchandiseResponse) {}
  rpc SearchMerchandises(SearchMerchandisesRequest) returns (SearchMerchandisesResponse) {}
  rpc UpdateMerchandiseStatus(UpdateMerchandiseStatusRequest) returns (UpdateMerchandiseStatusResponse) {}
  // buyer
  rpc MGetUserMerchandise(MGetUserMerchandiseRequest)returns(MGetUserMerchandiseResponse){}
  rpc QueryAvailableMerchandise(QueryAvailableMerchandiseRequest)returns(QueryAvailableMerchandiseResponse){}
  rpc DispatchMerchandiseBenefits(DispatchMerchandiseBenefitsRequest)returns(DispatchMerchandiseBenefitsResponse){}
  rpc VerifyBuyerQualifications(VerifyBuyerQualificationsRequest)returns(VerifyBuyerQualificationsResponse){}
}

message MGetUserMerchandiseRequest{
  optional uint64 user_id = 1;
  repeated uint64 merchandise_ids = 2;
}

message MGetUserMerchandiseResponse{
  repeated foody_merchandise.Merchandise  merchandise_list = 1;
}

message QueryAvailableMerchandiseRequest{
  optional uint64 user_id = 1;
  optional foody_merchandise.SaleType sale_type = 2;
}

message QueryAvailableMerchandiseResponse{
  repeated foody_merchandise.Merchandise  merchandise_list = 1;
}

message VerifyBuyerQualificationsRequest{
  optional uint64 user_id = 1;
  optional uint64 merchandise_id = 2;
}

message VerifyBuyerQualificationsResponse{
  optional bool result = 1; // true: pass
  optional foody_merchandise.Merchandise merchandise = 2;
}


message DispatchMerchandiseBenefitsRequest{
  optional uint64  merchandise_id  = 1;  // 发放的商品id
  optional uint64  uid = 2; // 发放用户
  optional foody_merchandise.BizType biz_type = 3; // 业务类型
  optional uint64 biz_no = 4;        // 业务序列号,唯一标识某次发放
  optional uint64 benefit_effective_time = 5; // 权益生效时间(时间戳)，重试需保持不变
  optional foody_merchandise.DispatchPriority priority = 6; //发放优先级
}

message DispatchMerchandiseBenefitsResponse{
  optional foody_merchandise.DispatchStatus  dispatch_status = 1;
}

message DispatchTemplateVoucherRequest{
  optional uint64  uid = 1;
  optional CreateVoucherTask.BizType biz_type = 2;
  repeated uint64  biz_ids = 3;
  optional uint64 effective_time = 4;
}

message DispatchTemplateVoucherResponse {
  message Result {
    optional uint32 code =1;
    optional string msg = 2;
    optional uint64 template_id = 3;
    optional uint64 voucher_id = 4;
    optional uint64 biz_id = 5;
  }
  repeated Result dispatch_result = 1;
}



message GetTargetVoucherRequest{
  optional uint64 user_id = 1;
  optional uint64 strategyId = 2;
}

message GetTargetVoucherResponse{
  repeated TargetVoucherInfo target_voucher_info_list = 1;
}

message TargetVoucherInfo {
  optional uint64 scheme_group_id = 1;
  optional foody_base.Voucher voucher = 2;
  optional UserVoucherStatus user_voucher_status = 3;
}

enum UserVoucherStatus {
  NOT_CLAIM = 1;
  CLAIMED = 2;
  USED_OUT = 3;
}

message TransformMicrositeTargetVoucherComponentTemplateRequest {
  optional int32 platform_type = 1;   // reserved naming
  optional string meta = 2;           // reserved naming
  optional string data = 3;           // reserved naming
}

message TransformMicrositeTargetVoucherComponentTemplateResponse {
  repeated string error_messages = 1; // reserved naming
  optional string data = 2;           // reserved naming
}

message GetMicrositeTargetVoucherComponentTemplateRequest {
  optional int32 platform_type = 1;   // reserved naming
}

message GetMicrositeTargetVoucherComponentTemplateResponse {
  repeated string error_messages = 1; // reserved naming
  optional string template = 2;       // reserved naming
}

message ValidateMicrositeTargetVoucherComponentTemplateRequest {
  optional int32 platform_type = 1;   // reserved naming
  optional string meta = 2;           // reserved naming
  optional string data = 3;           // reserved naming
}

message ValidateMicrositeTargetVoucherComponentTemplateResponse {
  repeated string error_messages = 1; // reserved naming
}

enum SkuVoucherSource {
  ALL_VOUCHER = 1;
  STORE_DETAIL_PAGE = 2;
  STORE_LISTING = 3;
  RECOMMEND_VOUCHER = 4;
}

enum CoFundType {
  UNKNOWN = 0;
  PERCENTAGE = 1;
  AMOUNT = 2;
}

message CoFundCampaign {
  enum CampaignStatus {
    UNKNOWN = 0;
    CREATED = 1;
    DELETED = 2;
  }

  enum CalculationMode {
    DEFAULT = 0;
    CUSTOMISE = 1;
  }

  enum CustomiseRewardType {
    NA = 0;
    PERCENTAGE = 1;
    AMOUNT = 2;
  }

  enum ReferenceType {
    UNDEFINED = 0;
    ITEM_VOUCHER = 1;
    SHIPPING_FEE_VOUCHER = 2;
  }

  optional uint64 id = 1;
  optional string campaign_name = 2;
  optional CoFundType co_fund_type = 3;
  optional CampaignStatus campaign_status = 4;
  repeated CoFundScheme co_fund_schemes = 5;
  optional string creator = 6;
  optional uint64 create_time = 7;
  optional uint64 update_time = 8;
  optional CalculationMode calculation_mode = 9;
  optional CustomiseRewardType customise_reward_type = 10;
  optional uint64 customise_reward_percentage = 11;
  optional uint64 customise_reward_amount = 12;
  optional uint64 customise_reward_cap = 13;
  optional ReferenceType co_fund_reference_type = 14;
}

message CoFundScheme {
  enum SchemeStatus {
    STATUS_UNKNOWN = 0;
    CREATED = 1;
    DELETED = 2;
  }
  enum SettlementType {
    TYPE_UNKNOWN = 0;
    INVOICE = 1;
    DIRECT_DEDUCTION = 2;
  }

  optional uint64 id = 1;
  optional uint64 campaign_id = 2;
  optional string scheme_name = 3;
  optional CoFundType co_fund_type = 4;
  optional uint64 percentage = 5;
  optional uint64 amount = 6;
  repeated uint64 store_tag_ids = 7 [packed = true];
  optional SettlementType settlement_type = 8;
  optional SchemeStatus scheme_status = 9;
  optional string creator = 10;
  optional uint64 create_time = 11;
  optional uint64 update_time = 12;
  optional uint64 latest_snapshot_id = 13;
  optional CoFundCampaign.ReferenceType co_fund_reference_type = 14;
}

message CoFundSchemeSnapshot {
  optional uint64 id = 1;
  optional uint64 scheme_id = 2;
  optional uint64 campaign_id = 3;
  optional string scheme_name = 4;
  optional CoFundType co_fund_type = 5;
  optional uint64 percentage = 6;
  optional uint64 amount = 7;
  repeated uint64 store_tag_ids = 8 [packed = true];
  optional CoFundScheme.SettlementType settlement_type = 9;
  optional CoFundScheme.SchemeStatus scheme_status = 10;
  optional CoFundCampaign.CalculationMode calculation_mode = 11;
  optional CoFundCampaign.CustomiseRewardType custom_reward_type = 12;
  optional uint64 custom_reward_percentage = 13;
  optional uint64 custom_reward_amount = 14;
  optional uint64 custom_reward_cap = 15;
  optional uint64 create_time = 16;
  optional string creator = 17;
  optional CoFundCampaign.ReferenceType co_fund_reference_type = 18;
}

message CoFundRecord {
  enum PromotionType {
    UNDEFINED = 0;
    ITEM_VOUCHER = 1;
    SHIPPING_FEE_VOUCHER = 2;
  }

  enum RecordStatus {
    UNKNOWN = 0;
    CREATED = 1;
    DELETED = 2;
  }

  optional uint64 id = 1;
  optional uint64 order_id = 2;
  optional uint64 promotion_id = 3;
  optional PromotionType promotion_type = 4;
  optional uint64 subsidy_amount = 5;
  optional uint64 scheme_snapshot_id = 6;
  optional RecordStatus record_status = 7;
  optional CoFundScheme.SettlementType settlement_type = 8;
  optional uint64 create_time = 9;
}

enum PromotionClass {
  VOUCHER = 1;
  COINS = 2;
  ORDER_DISCOUNT = 3;
  ITEM_DISCOUNT = 4;
}

enum ReturnMode {
  MODE_USER_LIMIT = 0x1;
  MODE_DAY_LIMIT = 0x10;
  MODE_STOCK = 0x100;
}

enum ShopeeFoodScopeType {
  SHOPEE_FOOD_STORE_SCOPE = 1;
  SHOPEE_FOOD_DISH_SCOPE = 2;
}

message OrderDiscountRecord
{
  enum UsedStatus
  {
    NOT_USED = 0;
    USED = 1;
    RETURNED = 2;
    CANCELED = 3;
  }
  optional uint64 id = 1;
  optional uint64 campaign_id = 2;
  optional uint64 order_id = 3;
  optional uint64 store_id = 4;
  optional uint64  buyer_id = 5;
  optional uint64 discount_amount = 6;
  optional UsedStatus used_status = 7;
  optional uint64 create_time = 8;
  optional uint64 update_time = 9;
  optional string promotion_level_name = 10;
}

message CreateOrderDiscountCampaignRequest {
  optional foody_base.OrderDiscountCampaign order_discount_campaign = 1;
}

message CreateOrderDiscountCampaignResponse {
  optional uint64 id = 1;
}

message UpdateOrderDiscountCampaignRequest {
  optional foody_base.OrderDiscountCampaign order_discount_campaign = 1;
}

message UpdateOrderDiscountCampaignResponse {
  optional uint64 id = 1;
}

message UpdateOrderDiscountCampaignStatusRequest {
  optional uint64 id = 1;
  // 修改promotion_status，用于shipping fee类型direct discount的promotion_status修改
  optional foody_base.OrderDiscountCampaign.PromotionStatus promotion_status = 2;
  // 修改campaign_status，用于item subtotal类型的direct discount的campaign_status修改
  optional foody_base.OrderDiscountCampaign.Status campaign_status = 3;
}

message UpdateOrderDiscountCampaignStatusResponse {
  optional uint64 id = 1;
}

message GetOrderDiscountCampaignRequest {
  optional uint64 id = 1;
  optional uint64 store_id = 2;
}

message GetOrderDiscountCampaignResponse {
  optional foody_base.OrderDiscountCampaign order_discount_campaign = 1;
}

message SearchOrderDiscountCampaignRequest {
  message Filter {
    optional uint64 start_time = 1;
    optional uint64 end_time = 2;
    repeated uint64 location_group_ids = 3;
    message Range {
      optional uint64 start = 1;
      optional uint64 end = 2;
    }
    optional foody_base.OrderDiscountCampaign.PromotionType type = 4;
    repeated uint64 merchant_ids = 5;
    optional uint64 campaign_id = 6;
    optional foody_base.OrderDiscountCampaign.Source source = 7;
    optional foody_base.OrderDiscountCampaign.Status status = 8;
    optional Range updated_range = 9;
    optional Range start_range = 10;
    optional Range end_range = 11;
  }
  optional string keyword = 1;
  optional Filter filter = 2;
  optional uint32 page_size = 3;
  optional uint32 page_num = 4;
}

message SearchOrderDiscountCampaignResponse {
  repeated foody_base.OrderDiscountCampaign order_discount_campaigns = 1;
  optional uint64 total_count = 2;
  optional bool has_more = 3;
}

message MGetStorePromotionRequest {
  message Pair {
    optional uint64 store_id = 1;
    optional foody_base.PartnerType partner_type = 2;
  }
  repeated Pair pairs = 1;
  repeated PromotionClass promotion_classes = 2; // the promotion classes that need details
  optional uint64 buyer_id = 3;
  optional ItemDiscountOperationType operation_type = 4;
}

message MultiLangTxt {
  optional string language = 1;
  optional string value = 2;
}

message VoucherDetail {
  optional foody_base.VoucherIdentifier voucher_identifier = 1;
  optional string icon = 2;
  optional string icon_text = 3;
  optional string title = 4;
  optional string label = 5;
  optional uint64 start_time = 6;
  optional uint64 end_time = 7;
  optional bool claimed = 8;
  repeated MultiLangTxt labels = 9;
  optional voucher.core.VoucherDetail vss_voucher_detail = 10;
  repeated int64 voucher_tag_list = 11;
  optional bool is_fused = 12;
  repeated int32 delivery_type = 13;
  optional bool  enable_auto_claim = 14;
}

message StoreVoucherCache {
  repeated VoucherDetail voucher_details = 1;
}

message StoreOrderDiscountCache {
  repeated foody_base.OrderDiscountCampaign order_discount_campaigns = 1;
}

message StoreItemDiscountCache {
  repeated foody_base.ItemDiscountCampaign item_discount_campaigns = 1;
}

message StorePromotion {
  optional uint64 store_id = 1;
  repeated VoucherDetail voucher_details = 2;
  repeated foody_base.OrderDiscountCampaign order_discount_campaigns = 3;
  repeated foody_base.ItemDiscountCampaign item_discount_campaigns = 4;
  optional uint64 maxed_shipping_fee_discount_amount = 5;
}

message MGetStorePromotionResponse {
  repeated StorePromotion promotions = 1;
}

message GetStorePromotionRequest {
  optional uint64 store_id = 1;
  optional foody_base.PartnerType partner_type = 2;
  repeated PromotionClass promotion_classes = 3; // the promotion classes that need details
  repeated uint64 location_group_ids = 4;
  optional uint64 buyer_id = 5;
  optional ItemDiscountOperationType operation_type = 6;
  optional int32 promotion_tool_control = 7;
  optional int32 delivery_type = 8; // 1.merchant or platform delivery 2.self pickup
}

message GetStorePromotionResponse {
  optional StorePromotion promotion = 1;
}

message PaymentInfo {
  optional bool check_payment_info = 1;
  optional uint64 spm_channel_id = 2;
  optional foody_base.PaymentMethod payment_method = 3;
  optional string credit_card_bin = 4;
  optional foody_base.VoucherPaymentType voucher_payment_type = 5;
}

message GetPromotionDraftRequest {
  optional foody_base.Order order = 1;
  optional foody_base.VoucherIdentifier voucher_identifier = 2 [deprecated = true]; // use voucher_identifiers instead
  optional int64 coins = 3;
  optional PaymentInfo payment_info = 4;
  repeated foody_base.VoucherIdentifier voucher_identifiers = 5;
  repeated uint64 location_group_ids = 6;
  optional string ref_id = 7;  // 目前用于标识PaoTang跳转过来的标识ID
  optional bool cal_available_voucher_quantity = 8; //是否需要计算可用券数量，由上游指定。当前场景，用户勾选了券则传false或者不指定，未勾选传true
  optional int32 delivery_type = 9;
  optional bool auto_apply = 10; // 是否计算最优优惠券
  optional int32 promotion_tool_control = 11; // 控制特定优惠是否可用，为了解决RN版本不兼容的问题而增加
}

message GetPromotionDraftResponse {
  message Tip {
    optional uint32 code = 1;
    optional string msg = 2;
    optional foody_base.VoucherIdentifier voucher_identifier = 3;
  }
  message Tips {
    optional Tip voucher = 1 [deprecated = true]; // use vouchers instead
    repeated Tip vouchers = 2;
  }
  optional foody_base.Promotion promotion = 1;
  optional foody_base.EarnCoins no_spend_earn = 2; // 没有使用coins时可以获到的coins
  optional foody_base.EarnCoins spend_earn = 3; // 使用了coins时可以获到的coins
  optional Tips tips = 4; // 提示信息
  optional uint32 available_voucher_quantity =5; // 可用优惠券数量
}

message UsePromotionRequest {
  optional foody_base.Order order = 1;
  optional foody_base.Promotion promotion = 2;
  repeated uint64 location_group_ids = 3;
  optional foody_base.AntiFraud anti_fraud = 999;
}

message UsePromotionResponse {
  optional foody_base.Promotion promotion = 1;
  repeated foody_base.OrderCustomTag custom_tags = 2;
}

message CallbackCancelVoucherRequest {
  optional string reference_id = 1;
}

message CallbackCancelVoucherResponse {
}

message PromotionPreviewRequest {
  optional foody_base.Order order = 1;
  repeated uint64 location_group_ids = 2;
}

message PromotionPreviewResponse {
  optional foody_base.Promotion promotion = 1;
  repeated foody_base.OrderCustomTag custom_tags = 2;
}

message CommitPromotionPreviewRequest {
  optional foody_base.Order order = 1;
  optional foody_base.Promotion promotion = 2;
  repeated uint64 location_group_ids = 3;
  optional foody_base.AntiFraud anti_fraud = 999;
}

message CommitPromotionPreviewResponse {
  optional foody_base.Promotion promotion = 1;
  repeated foody_base.OrderCustomTag custom_tags = 2;
}

message CancelPromotionPreviewRequest {
  optional foody_base.Order order = 1;
  repeated uint64 location_group_ids = 2;
}

message CancelPromotionPreviewResponse {
  optional foody_base.Promotion promotion = 1;
}

message MGetItemDiscountRequest {
  optional uint64 store_id = 1;
  repeated uint64 dish_ids = 2;
}

message MGetItemDiscountResponse {
  repeated foody_base.ItemDiscount discounts = 1;   // 仅包含 ongoing campaigns 下的折扣项
  repeated foody_base.ItemDiscount upcomingDiscounts = 2; // 对于同一个 dish id 只返回最早的 upcoming campaign 下的一个折扣项
}

message ItemDiscountDish {
  optional uint64 dish_id = 1;
  optional uint64 dish_price = 2;
  optional uint64 timeslot_id = 3;
  optional uint64 discount_id = 4;
}

message MGetItemDiscountV2Request {
  optional uint64 store_id = 1;
  repeated ItemDiscountDish dishes = 2;
  optional ItemDiscountOperationType operation_type = 3;
  optional uint64 buyer_id = 4;
}

message MGetItemDiscountV2Response {
  repeated foody_base.ItemDiscount discounts = 1;   // 仅包含 ongoing campaigns 下的折扣项
  repeated foody_base.ItemDiscount upcoming_discounts = 2; // 对于同一个 dish id 只返回最早的 upcoming campaign 下的一个折扣项
}

message MCheckItemPriceRequest {
  optional uint64 store_id = 1;
  repeated ItemDiscountDish dishes = 2;
}

message MCheckItemPriceResponse {
  repeated foody_base.Result results = 1;
}

message BatchMGetItemDiscountParam {
  optional uint64 store_id = 1;
  repeated uint64 dish_ids = 2;
}

message BatchMGetItemDiscountRequest {
  repeated BatchMGetItemDiscountParam params = 1;
}

message BatchMGetItemDiscountItem {
  optional uint64 store_id = 1;
  repeated foody_base.ItemDiscount discounts = 2;
}

message BatchMGetItemDiscountResponse {
  repeated BatchMGetItemDiscountItem items = 1;
}

message BatchMGetItemDiscountParamV2 {
  optional uint64 store_id = 1;
  repeated ItemDiscountDish dishes = 2;
}

message QueryNearbyAvailableVoucherRequest{
  optional uint64 user_id = 1;
  optional float lat = 2;
  optional float lng = 3;
  repeated uint64 voucher_tag_list = 4;
}

message QueryNearbyAvailableVoucherResponse{
  repeated VoucherDetail voucherDetailList = 1;
}

message GetVoucherTypeListRequest{
}

message GetVoucherTypeListResponse{
  repeated Tag tag_list = 1;
}

message Tag{
  optional uint64 Id = 1;
  optional string TagName = 2;
  optional uint64 TagTypeId = 3;
}

message QueryItemDiscountRequest {
  optional uint64 store_id = 1;
  repeated uint64 dish_ids = 2; //传入的数量<=50
}

message QueryItemDiscountResponse {
  repeated foody_base.ItemDiscount discounts = 1;
}

message MDisableItemDiscountCampaignRequest {
  optional uint64 store_id = 1;
  repeated uint64 campaign_ids = 2;
}

message MDisableItemDiscountCampaignResponse {

}

message BatchMGetItemDiscountV2Request {
  repeated BatchMGetItemDiscountParamV2 params = 1;
}

message BatchMGetItemDiscountItemV2 {
  optional uint64 store_id = 1;
  repeated foody_base.ItemDiscount discounts = 2;
}

message BatchMGetItemDiscountV2Response {
  repeated BatchMGetItemDiscountItemV2 items = 1;
}

enum ItemDiscountCampaignShowStatus{
  ONGOING = 1;
  UPCOMING = 2;
  PAST = 3;
}

enum ItemDiscountOperationType {
  ORDER_OPERATION = 1;
  SELLER_DISH_VIEW_OPERATION = 2;
  BUYER_STORE_DISH_VIEW_OPERATION = 3;
  BUYER_STORE_PROMOTION_VIEW_OPERATION = 4;
  OTHER_OPERATION = 5;
}

message QueryItemDiscountCampaignListRequest {
  message Filter{
    optional ItemDiscountCampaignShowStatus show_status = 1;
  }
  optional uint64 store_id = 1;
  optional uint32 page_num = 2;       // 第几页
  optional uint32 page_size = 3;      // 每页数目
  optional Filter filter = 4;
}

message QueryItemDiscountCampaignListResponse {
  repeated foody_base.ItemDiscountCampaign campaigns = 1;
  optional uint64 total_count = 2;
}

message QueryItemDiscountCampaignRequest {
  optional uint64 id = 1;
}

message QueryItemDiscountCampaignResponse {
  optional foody_base.ItemDiscountCampaign campaign = 1;
  repeated foody_base.ItemDiscount discounts = 2;
  optional bool is_available = 3;
}

message GetOrderPromotionDetailRequest {
  optional uint64 order_id = 1;
}

message GetOrderPromotionDetailResponse {
  optional uint64 order_id = 1;
  repeated foody_base.Voucher vouchers = 2;
  repeated foody_base.OrderDiscount order_discounts = 3;
  optional foody_base.EarnCoins earn_coins = 4;
  optional foody_base.SpendCoins spend_coins = 5;
}

message GetSpxOrderPromotionDetailRequest {
  optional uint64 order_id = 1;
}

message GetSpxOrderPromotionDetailResponse {
  optional uint64 order_id = 1;
  repeated foody_base.Voucher vouchers = 2;
}

message CreateItemDiscountCampaignRequest {
  optional string campaign_name = 1;
  optional uint64 store_id = 2;
  optional foody_base.ItemDiscountType campaign_type = 3;
  optional uint64 start_time = 4;
  optional uint64 end_time = 5;
  optional string creator = 6;
  repeated foody_base.ItemDiscount item_discounts = 7;
  optional foody_base.DiscountType discount_type = 8;
  optional foody_base.ItemDiscountCreatorSource creator_source = 9;
  optional string note = 10;
  optional string purpose = 11;
  optional string user_tags = 12;
  optional foody_base.ItemDiscountCampaignAppliedTimes applied_times = 13;
  optional string user_group = 14;
}

message ItemDiscountTip {
  optional uint64 dish_id = 1;
  optional int32 code = 2;
  optional string msg = 3;
}

message CreateItemDiscountCampaignResponse {
  optional uint64 id = 1;
  //repeated uint64 duplicate_dish_ids = 2;
  repeated ItemDiscountTip tips = 3;
}

message UpdateItemDiscountCampaignRequest {
  enum Status {
    DISABLED = 0;
    ENABLED = 1;
  }
  optional uint64 id = 1;
  optional string campaign_name = 2;
  optional foody_base.ItemDiscountType campaign_type = 3;
  optional uint64 start_time = 4;
  optional uint64 end_time = 5;
  optional Status promotion_status = 6;
  optional string operator = 7;
  repeated foody_base.ItemDiscount item_discounts = 8;
  optional foody_base.DiscountType discount_type = 9;
  optional string note = 10;
  optional string purpose = 11;
  optional string user_tags = 12;
  optional foody_base.ItemDiscountCampaignAppliedTimes applied_times = 13;
  optional foody_base.ItemDiscountCreatorSource operator_source = 14;
  optional string user_group = 15;
}

message UpdateItemDiscountCampaignResponse {
  //repeated uint64 duplicate_dish_ids = 1;
  repeated ItemDiscountTip tips = 2;
}

message ItemDiscountCampaignDraftRequest {
  enum Status {
    DISABLED = 0;
    ENABLED = 1;
  }
  enum DraftType {
    CREATED = 0;
    UPDATED = 1;
  }
  optional uint64 id = 1;
  optional string campaign_name = 2;
  optional foody_base.ItemDiscountType campaign_type = 3;
  optional uint64 start_time = 4;
  optional uint64 end_time = 5;
  optional Status promotion_status = 6;
  optional string operator = 7;
  repeated foody_base.ItemDiscount item_discounts = 8;
  optional foody_base.DiscountType discount_type = 9;
  optional uint64 store_id = 10;
  optional foody_base.ItemDiscountCreatorSource creator_source = 11;
  optional DraftType draft_type = 12;
}

message ItemDiscountCampaignDraftResponse {
  repeated ItemDiscountTip tips = 1;
}

message RefundRequest {
  optional uint64 order_id = 1;
  optional foody_base.AntiFraud anti_fraud = 999;
}

message RefundResponse {
}

message PartRefundRequest {
  optional uint64 order_id = 1;
  optional foody_base.SpendCoins refund_spend_coins = 2;
  optional foody_base.EarnCoins refund_earn_coins = 3;
  repeated uint64 voucher_id = 4; // 需要退的订单使用券id
}

message PartRefundResponse {
}

message MUpdateItemDiscountCampaignRequest{
  repeated foody_base.ItemDiscountCampaign itemDiscountCampaigns = 1;
  optional string operator = 2;
  optional foody_base.ItemDiscountCreatorSource source = 3;
}

message MUpdateItemDiscountCampaignResponse{
  message MUpdateItemDiscountCampaignResult{
    repeated ItemDiscountTip tips = 1;
    optional int32 code = 2;
    optional string msg = 3;
  }
  repeated MUpdateItemDiscountCampaignResult results = 1;
}

message MCreateItemDiscountCampaignRequest {
  repeated foody_base.ItemDiscountCampaign itemDiscountCampaigns = 1;
}

message MCreateItemDiscountCampaignResponse {
  message MCreateItemDiscountCampaignResult{
    optional uint64 id = 1;
    repeated ItemDiscountTip tips = 2;
    optional int32 code = 3;
    optional string msg = 4;
  }
  repeated MCreateItemDiscountCampaignResult results = 1;
}

enum PromotionBriefType {
  SHIPPING_FEE_VOUCHER = 1;
  COIN_CASHBACK_VOUCHER = 2;
  FOOD_DISCOUNT_VOUCHER = 3;
  SHIPPING_FEE_DIRECT_DISCOUNT = 4; // shipping_fee的dd
  ORDER_DIRECT_DISCOUNT = 5; // item_subtotal的dd
  FOOD_DIRECT_DISCOUNT = 6; // item_discount
}

message StorePromotionBrief {
  optional uint64 store_id = 1;
  optional bool has_shipping_fee_voucher = 2;
  optional bool has_coin_cashback_voucher = 3;
  optional bool has_food_discount_voucher = 4;
  optional bool has_shipping_fee_direct_discount = 5; // shipping_fee的dd
  optional bool has_order_direct_discount = 6; // item_subtotal的dd
  optional bool has_food_direct_discount = 7; // item_discount
}

message MGetStorePromotionBriefRequest {
  optional uint64 buyer_id = 1;
  repeated uint64 store_ids = 2;
  repeated PromotionBriefType promotion_brief_types = 3;
  optional int32 delivery_type = 4;
}

message MGetStorePromotionBriefResponse {
  repeated StorePromotionBrief promotion_briefs = 1;
}

message GetCoFundCampaignRequest {
  optional uint64 id = 1;
}

message GetCoFundCampaignResponse {
  optional CoFundCampaign co_fund_campaign = 1;
}

message DeleteCoFundCampaignRequest {
  optional uint64 id = 1;
}

message DeleteCoFundCampaignResponse {

}

message CreateCoFundCampaignRequest {
  optional CoFundCampaign co_fund_campaign = 1;
}

message CreateCoFundCampaignResponse {
  optional uint64 id = 1;
}

message UpdateCoFundCampaignRequest {
  optional CoFundCampaign co_fund_campaign = 1;
  optional string operator = 2;
}

message UpdateCoFundCampaignResponse {

}

message SearchCoFundCampaignRequest {
  optional string keyword = 1;
  optional uint32 page_size = 2;
  optional uint32 page_num = 3;
  optional Filter filter = 4;
  message Filter {
    optional CoFundCampaign.ReferenceType co_fund_reference_type = 1;
  }
}

message SearchCoFundCampaignResponse {
  repeated CoFundCampaign co_fund_campaigns = 1;
  optional uint64 total_count = 2;
}

enum RecentlyModifiedType {
  PriceSlash = 1;
}

message GetRecentlyModifiedRequest {
  optional RecentlyModifiedType type = 1;
  optional uint64 id = 2;
}

message GetRecentlyModifiedResponse {
  optional uint64 expiration = 1;
}

message IsDisplayRedDotRequest {
  optional uint64 store_id = 1;
  optional uint64 last_view_time = 2;
}

message IsDisplayRedDotResponse {
  optional bool is_display_ongoing_itemdisocunt_red_dot = 1;
  optional bool is_display_upcoming_itemdisocunt_red_dot = 2;
}

message MGetFlashSaleItemRequest {// 传菜品ID，获取参与flash sale的菜品，当前用于判断菜品有没有近期的折扣
  optional uint64 store_id = 1;
  repeated uint64 dish_ids = 2;
}

message MGetFlashSaleItemResponse {
  repeated foody_base.FlashSaleDishDiscount discounts = 1;           // 菜品 ongoing + upcoming 折扣详情
  repeated foody_base.FlashSaleDishDiscount upcoming_discounts = 2;  // 菜品 ongoing + upcoming 折扣详情
}

message MGetShopeeFoodVoucherRequest {
  repeated uint64 voucher_ids = 1;
}

message MGetShopeeFoodVoucherResponse {
  repeated foody_base.Voucher vouchers = 1;
  repeated InvalidVoucherTip invalid_vouchers = 2;
}


message CheckShopeeFoodVoucherUserClaimStatusRequest{
  optional uint64 buyer_id = 1; // required
  repeated foody_base.VoucherIdentifier voucher_identifiers = 2; // required, max_length=50, otherwise return ERROR_PARAM
}

message CheckShopeeFoodVoucherUserClaimStatusResponse{
  repeated foody_base.VoucherClaimStatus claim_status = 1;
}

enum PromotionLimitType {
  VoucherDailyClaimByCode = 1;
  GenerateMultipleVoucherCode = 2;
  UpdateVoucherQuota = 3;
}

message CheckPromotionLimitRequest {
  optional uint64 buyer_id = 1;
  optional PromotionLimitType promotion_limit_type = 2;
  optional uint64 promotion_id = 3;
  optional uint64 quantity = 4;
  optional uint64 total_limit = 5;
}

message CheckPromotionLimitResponse {
}


message GetPromotionListRequest {
  message GetPromotionListFilter{
    optional uint64 store_id = 1;
    repeated foody_base.PromotionObject.PromotionShowStatus  show_status = 2; //default all 1-pending 2-upcoming 3-ongoing 4-ended
    optional uint64 start_time = 3; // promotion valid start time
    optional uint64 end_time = 4; // promotion valid end time
    repeated foody_base.PromotionObject.PromotionType promotion_types = 5;
  }

  enum OrderBy{
    CREATE_TIME = 1;
    START_TIME = 2;
  }

  optional RequestMeta req_meta = 1;
  optional GetPromotionListFilter filter = 2;
  optional int32 page_size = 3;
  optional int32 page_num = 4;
  optional OrderBy order_by = 5;
  optional bool asc = 6;
}

message GetPromotionListResponse {
  repeated foody_base.PromotionObject promotion_list = 1;
  optional uint64 total_count = 2;
}

message SyncPromotionListRequest {
  message SyncPromotionListFilter{
    optional uint64 start_time = 1;   // the start update_time of Promotion
    optional uint64  end_time = 2;  // the end update_time of Promotion
    optional foody_base.PromotionObject.PromotionType  promotion_type = 3;
  }

  optional RequestMeta req_meta = 1;
  optional SyncPromotionListFilter filter = 2;
  optional int32  page_size = 3;
  optional string last_id = 4;
}

message SyncPromotionListResponse {
  repeated foody_base.PromotionObject promotion_list = 1;
  optional string last_id = 2;
}

message GetPromotionDetailRequest {
  optional RequestMeta req_meta = 1;
  optional uint64  promotion_id = 2;
  optional foody_base.PromotionObject.PromotionType  promotion_type = 3; // 1-item discount 2-flash_sale
}

message GetPromotionDetailResponse {
  optional foody_base.PromotionObject promotion = 1;
}

message GetPromotionToolListRequest {
  optional RequestMeta req_meta = 1;
}

message GetPromotionToolListResponse {
  repeated foody_base.PromotionTool promotion_tool_list = 1;
}


message CreateVoucherRequest {
  optional foody_base.Voucher voucher = 1;
  optional CreateShopeeFoodVoucherRequest create_shopee_food_voucher_request = 2;
}

message CreateVoucherResponse {
  optional uint64 voucher_id = 1;
  optional CreateShopeeFoodVoucherResponse create_shopee_food_voucher_response = 2;
}


message CreateVoucherTemplateRequest {
  optional foody_base.VoucherTemplate voucher_template = 1;
}

message CreateVoucherTemplateResponse {
  optional uint64 template_id = 1;
}

message UpdateVoucherRequest {
  optional foody_base.Voucher voucher = 1;
  optional UpdateShopeeFoodVoucherRequest update_shopee_food_voucher_request = 2;
}

message UpdateVoucherResponse {
  optional uint64 voucher_id = 1;
  optional UpdateShopeeFoodVoucherResponse update_shopee_food_voucher_response = 2;
}

message UpdateVoucherTemplateRequest {
  optional foody_base.VoucherTemplate voucher_template = 1;
}

message UpdateVoucherTemplateResponse {
}

message GetVoucherTemplateRequest {
  optional uint64 template_id = 1;
}

message GetVoucherTemplateResponse {
  optional foody_base.VoucherTemplate voucher_template = 1;
}

message GetTemplateVouchersRequest {
  optional uint64 template_id = 1;
}

message GetTemplateVouchersResponse {
  repeated TemplateVoucher template_vouchers = 1;
}

message CreateVoucherTemplateUpdateTaskRequest {
  optional VoucherTemplateUpdateTask task =1;
}

message CreateVoucherTemplateUpdateTaskResponse {
  optional uint64 task_id = 1;
}

message GetVoucherTemplateUpdateTaskListRequest {
  optional uint64 template_id = 1;
  optional uint32 page_num =2;
  optional uint32 page_size =3;
}

message GetVoucherTemplateUpdateTaskListResponse {
  repeated VoucherTemplateUpdateTask tasks =1;
  optional uint64 total = 2;
}

message CancelVoucherTemplateUpdateTaskRequest{
  optional uint64 task_id = 1;
  optional string operator = 2;
}

message CancelVoucherTemplateUpdateTaskResponse {
}

message VoucherTemplateUpdateTask {
  enum Status {
    UPCOMING = 1;
    COMPLETED = 2;
    WITHDRAWN = 3;
    ABNORMAL = 4;
  }
  optional uint64  id = 1;
  optional uint64  effective_time = 2;
  optional uint64  co_fund_campaign_id = 3;
  optional foody_base.StoreScope store_scope = 4;
  optional uint64 operate_time = 5;
  optional string operator = 6;
  optional uint64 create_time = 7;
  optional uint64 update_time = 8;
  optional Status task_status = 9;
  optional uint64 template_id = 10;
}


message TemplateVoucher {
  optional uint64 voucher_id = 1;
  optional string valid_date = 2;
  optional uint64 biz_id = 3;
}

message GetVoucherRequest {
  optional uint64 voucher_id = 1;
  optional GetPromotionByIdRequest get_shopee_food_voucher_request = 2;
  optional foody_base.ServiceType service_type = 3;
}

message GetVoucherResponse {
  optional foody_base.Voucher voucher = 1;
  optional GetPromotionByIdResponse get_shopee_food_voucher_response = 2;
}

message SearchVoucherFilter {
  optional uint64 voucher_id = 1;
  optional string voucher_name = 2;
  optional string voucher_code = 3;
  optional foody_base.VoucherStatus voucher_status = 4;
  repeated uint64 location_group_ids = 5;
  optional uint64 usage_start_time = 6;
  optional uint64 usage_end_time = 7;
  optional uint64 create_start_time = 8;
  optional uint64 create_end_time = 9;
  optional foody_base.PaymentMethod payment_method = 10;
  optional foody_base.ServiceType service_type = 11;
}

message SearchVoucherRequest {
  optional SearchVoucherFilter filter = 1;
  optional uint32 page_num = 2;
  optional uint32 page_size = 3;
  optional GetPromotionListByListTypeRequest get_promotion_list_by_list_type_request = 4;
}

message SearchVoucherResponse {
  optional uint32 total_count = 1;
  repeated foody_base.Voucher vouchers = 2;
  optional GetPromotionListByListTypeResponse get_promotion_list_by_list_type_response = 3;
}

message SearchVoucherTemplateFilter {
  optional uint64 template_id = 1;
  optional string template_name = 2;
  repeated foody_base.VoucherTemplate.Status template_status = 3;
  repeated uint64 location_group_ids = 4;
  optional uint64 create_start_time = 5;
  optional uint64 create_end_time = 6;
}

message SearchVoucherTemplateRequest {
  optional SearchVoucherTemplateFilter filter = 1;
  optional uint32 page_num = 2;
  optional uint32 page_size = 3;
}

message SearchVoucherTemplateResponse {
  optional uint64 total_count = 1;
  repeated foody_base.VoucherTemplate voucher_templates = 2;
}

message CreateVoucherDispatchTaskRequest {
  optional uint64 voucher_id = 1;
  optional foody_base.VoucherTaskType task_type = 2;
  optional foody_base.UserAttribute user_attribute = 3;
  optional foody_base.VoucherTaskFile voucher_task_file = 4;
  optional string operator = 5;
  optional foody_base.ServiceType service_type = 6;
  optional string user_segment = 9;
  optional CreateDispatchByUserTagTaskRequest create_dispatch_by_user_tag_task_request = 7;
  optional CreateDirectDispatchTaskRequest create_direct_dispatch_task_request = 8;
}

message CreateVoucherDispatchTaskResponse {
  optional uint64 task_id = 1;
  optional CreateDispatchByUserTagTaskResponse create_dispatch_by_user_tag_task_response = 2;
  optional CreateDirectDispatchTaskResponse create_direct_dispatch_task_response = 3;
}

message GetVoucherDispatchTaskListRequest {
  optional uint64 voucher_id = 1;
  optional GetTasksByReferenceRequest get_tasks_by_reference_request = 2;
}

message GetVoucherDispatchTaskListResponse {
  repeated foody_base.VoucherTask voucher_tasks = 1;
  optional GetTasksByReferenceResponse get_tasks_by_reference_response = 2;
}

message CreateVoucherCodesTaskRequest{
  optional CreateGenerateVoucherCodesTaskRequest create_generate_voucher_codes_task_request = 1;
}

message CreateVoucherCodesTaskResponse{
  optional CreateGenerateVoucherCodesTaskResponse create_generate_voucher_codes_task_response = 1;
}

message UpdateVoucherCodesTaskRequest{
  optional CreateUpdateGeneratedVoucherCodesTaskRequest create_update_generate_voucher_codes_task_request = 1;
}

message UpdateVoucherCodesTaskResponse{
  optional CreateUpdateGeneratedVoucherCodesTaskResponse create_update_generate_voucher_codes_task_response = 1;
}

message ClaimVoucherRequest {
  optional uint64 user_id = 1;
  repeated foody_base.VoucherIdentifier voucher_identifiers = 2;
}

message ClaimVoucherResponse {
  repeated foody_base.Voucher user_vouchers = 1;
  repeated InvalidVoucherTip invalid_vouchers = 2;
}

enum DisplayChannel {
  DISPLAY_CHANNEL_LANDING_PAGE = 1;
  DISPLAY_CHANNEL_CHECKOUT_PAGE = 2;
}

message ApplicableVoucher {
  optional foody_base.Voucher unclaimed_voucher = 1;
  optional foody_base.UserVoucher user_voucher = 2;
}

message ListApplicableVoucherRequest {
  optional foody_base.ServiceType service_type = 1;
  optional foody_base.Location location = 2;
  optional uint64 user_id = 3;
  optional DisplayChannel display_channel = 4;
  optional Amount amount = 5;
  optional PaymentInfo payment_info = 6;
}

message ListApplicableVoucherResponse {
  repeated ApplicableVoucher applicable_vouchers = 1;
  repeated InvalidVoucherTip invalid_vouchers = 3;
}

message Amount {
  optional uint64 service_fee = 1;
  optional uint64 shipping_fee = 2;
  optional uint64 insurance_fee = 3;
  optional uint64 insurance_assured_amount = 4;
  optional uint64 total_amount = 5;
}

message SPXGetPromotionDraftRequest {
  optional foody_base.ServiceType service_type = 1;
  optional foody_base.Location location = 2;
  optional Amount amount = 3;
  repeated foody_base.VoucherIdentifier voucher_identifiers = 4;
  optional PaymentInfo payment_info = 5;
  optional uint64 user_id = 6;
}

message SPXGetPromotionDraftResponse {
  optional foody_base.Promotion promotion = 1;
  optional Tips tips = 2;
}

message SPXUsePromotionRequest {
  optional foody_base.ServiceType service_type = 1;
  optional foody_base.SPXOrder spx_order = 2;
  optional foody_base.Promotion promotion = 3;
  repeated uint64 location_group_ids = 4;
  optional uint64 user_id = 5;
  optional foody_base.AntiFraud anti_fraud = 999;
}

message SPXUsePromotionResponse {
  optional foody_base.Promotion promotion = 1;
}

message SPXRefundPromotionRequest {
  optional uint64 order_id = 1;
  optional foody_base.AntiFraud anti_fraud = 999;
}

message SPXRefundPromotionResponse {
  optional uint64 order_id = 1;
  optional ReturnCoins return_coins = 2;
}

message ReturnCoins {
  optional int64 refund_spend_amount = 1;
}

message InvalidVoucherTip {
  optional uint32 code = 1;
  optional string msg = 2;
  optional foody_base.VoucherIdentifier voucher_identifier = 3;
  optional foody_base.Voucher voucher = 4;
  optional foody_base.UserVoucher user_voucher = 5;
}

message Tips {
  repeated InvalidVoucherTip invalid_vouchers = 2;
}

// ref: foody gateway proto UseVouchersTccCallbackRequest && UseCoinTccCallbackRequest
message UsePromotionTccCallbackRequest {
  optional string region = 1;
  optional uint64 order_id = 2;
}

message CallbackCancelUseRequest {
  optional uint64 order_id = 1;
}

message CallbackCancelUseResponse {
}

message MGetFlashSaleItemStockRequest {
  optional uint64 buyer_id = 1;
  optional uint64 store_id = 2;
  repeated ItemDiscountDish dishes = 3;
}

message MGetFlashSaleItemStockResponse {
  optional uint64 buyer_id = 1;
  optional uint64 store_id = 2;
  repeated foody_base.FlashSaleCartItemDetail flash_sale_cart_item = 3;
}

// Flash sale
message MGetFlashSaleItemDiscountRequest {// 需要传菜品原价，后续可能有些优惠价格，需要原价来计算，用于需要展示优惠价的场景
  optional uint64 store_id = 1;
  repeated ItemDiscountDish dishes = 2;
}

message MGetFlashSaleItemDiscountResponse {
  repeated foody_base.FlashSaleDishDiscount discounts = 1;           // 菜品 ongoing + upcoming 折扣详情
  repeated foody_base.FlashSaleDishDiscount upcoming_discounts = 2;  // 菜品 ongoing + upcoming 折扣详情
}

message MCheckFlashSaleItemDiscountPriceRequest {
  optional uint64 store_id = 1;
  repeated ItemDiscountDish dishes = 2;
}

message MCheckFlashSaleItemDiscountPriceResponse {
  repeated foody_base.Result result = 1;
}

message RequestMeta {
  optional uint32 caller_source = 1;
  optional string request_id = 2;
}

message ResponseMeta {
  optional int32 error_code = 1;
  optional string debug_message = 2;
}

message DistributeVoucherRequest {
  optional foody_base.VoucherIdentifier voucher_identifier = 1;
  optional uint64 user_id = 2;
}

message DistributeVoucherResponse {
  optional ResponseMeta resp_meta = 1;
  optional uint64 user_id = 2;
  optional foody_base.VoucherIdentifier voucher_identifier = 3;
}

message BatchDistributeVoucherRequest {
  repeated DistributeVoucherRequest requests = 1;
}

message BatchDistributeVoucherResponse {
  repeated DistributeVoucherResponse responses = 1;
}

message BatchGetVoucherFromSPXVoucherWalletRequest {
  optional RequestMeta req_meta = 1;
  repeated uint64 voucher_ids = 2;
}

message BatchGetVoucherFromSPXVoucherWalletResponse {
  repeated foody_base.Voucher vouchers = 1;
}

message ClaimVoucherFromSPXVoucherWalletRequest {
  optional RequestMeta req_meta = 1;
  optional uint64 user_id = 2;
  optional foody_base.VoucherIdentifier voucher_identifier = 3;
}

message ClaimVoucherFromSPXVoucherWalletResponse {
  optional foody_base.Voucher voucher = 1;
}

message GetVoucherStackingRequest {
  optional uint64 user_id = 1;
}
message CreateShopeeFoodVoucherRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required
  optional string name = 3; // can be empty, we don't care, it's only for creator to see
  optional string voucher_code = 4;
  optional int64 min_spend = 5; // minimum basket price
  optional voucher.core.ShopeeFoodStoreScope store_scope = 6;

  optional int32 reward_type = 7; // ref: VoucherRewardType
  optional voucher.core.Discount discount = 8;
  optional voucher.core.CoinCashback coin_cashback = 9;
  optional voucher.core.Discount shipping_fee_discount = 10;

  optional int32 total_count = 11; // max number of users can claim
  optional int32 total_usage = 12; // max number of usage
  optional int32 usage_limit_per_user = 13; // how many times a user can use the voucher, default is 1

  optional int64 start_time = 14; // voucher valid start_time
  optional int64 end_time = 15; // voucher valid end_time

  optional int64 claim_start_time = 16; // only after this time, user can claim voucher
  optional int64 stop_dispatch_time = 17; // after this time, dispatching voucher will fail
  optional int32 valid_days = 18; // specific to each user, start_time=the time user receive the voucher, end_time=start_time+valid_days*86400

  optional voucher.core.Int64List spm_channels = 19; // deprecated, move inside ShopeeFoodPaymentScope

  optional string icon_hash = 20;
  optional string icon_text = 21;
  optional string description = 22; // use for T&C section

  optional voucher.core.UserAttributeList user_attributes = 23;
  optional voucher.core.VoucherCustomisedLabelList customised_labels = 24;
  optional voucher.core.VoucherCustomisedLabelList browse_list_tags = 25;

  optional bool non_claimable_by_code = 26;
  optional bool display_on_store_page = 27;

  optional voucher.core.ShopeeFoodLocationScope location_scope = 28;
  optional voucher.core.VoucherTAndCList tc_section_list = 29; // voucher T&C sections

  optional uint64 co_fund_id = 30;
  optional voucher.core.ShopeeFoodPaymentScope payment_scope = 31;

  optional uint32 business_budget_id = 32; // Foody - for operators to store which internal budget to go to
  optional string use_link = 33; // url link when clicking "Use" button

  // rules
  optional uint64 service_type = 34; // Ref:voucher_core.ShopeeFoodServiceType
  optional uint64 schedule_type = 35; // Ref:voucher_core.ShopeeFoodScheduleType
  optional uint64 shipping_method = 36; // Ref:voucher_core.ShopeeFoodShippingMethod
  repeated int32 user_agent_types = 37; // ref: DeviceType
  repeated voucher.core.WeekdayTimePeriod valid_usage_periods = 38; // must fullfil start_time - end_time first, then check for this periods
  optional voucher.core.PromotionItemList item_list = 39; // set itemid only
  optional voucher.core.PromotionItemList disable_item_list = 40; // set itemid only

  optional int32 suffix_length = 41;
  optional string user_segment = 42;  // integrated with UserPlatform system
  optional uint32 priority = 43;
  optional uint32 dish_scope_type = 44; // Refer to voucher.core.ShopeeFoodDishScopeType for enums
  optional uint32 scope_type = 45; // Refer to ShopeeFoodScopeType for enums

  // 以下字段不存在 vss ,vss 对应的协议也没有
  repeated uint64 voucher_tag_list = 1001;
}

message CreateShopeeFoodVoucherResponse {
  optional string debug_msg = 1;
  optional VssPromotion promotion = 2;
}


message UpdateShopeeFoodVoucherRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required
  optional int64 promotionid = 3; // required

  optional string name = 4; // can be empty, we don't care, it's only for creator to see
  optional string voucher_code = 5;
  optional int64 min_spend = 6; // minimum basket price
  optional voucher.core.ShopeeFoodStoreScope store_scope = 7;

  optional int32 reward_type = 8; // ref: VoucherRewardType
  optional voucher.core.Discount discount = 9;
  optional voucher.core.CoinCashback coin_cashback = 10;
  optional voucher.core.Discount shipping_fee_discount = 11;

  optional int32 total_count = 12; // max number of users can claim
  optional int32 total_usage = 13; // max number of usage
  optional int32 usage_limit_per_user = 14; // how many times a user can use the voucher, default is 1

  optional int64 start_time = 15; // voucher valid start_time
  optional int64 end_time = 16; // voucher valid end_time

  optional int64 claim_start_time = 17; // only after this time, user can claim voucher
  optional int64 stop_dispatch_time = 18; // after this time, dispatching voucher will fail
  optional int32 valid_days = 19; // specific to each user, start_time=the time user receive the voucher, end_time=start_time+valid_days*86400

  optional voucher.core.Int64List spm_channels = 20; // deprecated, move inside ShopeeFoodPaymentScope

  optional string icon_hash = 21;
  optional string icon_text = 22;
  optional string description = 23; // use for T&C section

  optional int32 status = 24; // Status_PROMOTION_x, 0: disabled, 1: enabled, 2 : deleted
  optional voucher.core.UserAttributeList user_attributes = 25;
  optional voucher.core.VoucherCustomisedLabelList customised_labels = 26;
  optional voucher.core.VoucherCustomisedLabelList browse_list_tags = 27;

  optional bool non_claimable_by_code = 28;
  optional bool display_on_store_page = 29;

  optional voucher.core.ShopeeFoodLocationScope location_scope = 30;
  optional voucher.core.VoucherTAndCList tc_section_list = 31; // voucher T&C sections

  optional uint64 co_fund_id = 32;
  optional voucher.core.ShopeeFoodPaymentScope payment_scope = 33;
  optional uint32 business_budget_id = 34; // Foody - for operators to store which internal budget to go to
  optional string use_link = 35; // url link when clicking "Use" button

  // rules
  optional uint64 service_type = 36; // Ref:voucher_core.ShopeeFoodServiceType
  optional uint64 schedule_type = 37; // Ref:voucher_core.ShopeeFoodScheduleType
  optional uint64 shipping_method = 38; // Ref:voucher_core.ShopeeFoodShipingMethod
  repeated int32 user_agent_types = 39; // ref: DeviceType
  repeated voucher.core.WeekdayTimePeriod valid_usage_periods = 40; // must fullfil start_time - end_time first, then check for this periods
  optional voucher.core.PromotionItemList item_list = 41; // set by seller center only, only when voucher is applicable to selected items in the shop
  optional voucher.core.PromotionItemList disable_item_list = 42; // set by seller center only, only when voucher is applicable to selected items in the shop
  optional string user_segment = 43;  // integrated with UserPlatform system
  optional uint32 priority = 44;
  optional uint32 dish_scope_type = 45;
  optional uint32 scope_type = 46;

  // 以下字段不存在 vss ,vss 对应的协议也没有
  repeated uint64 voucher_tag_list = 1001;
}

message UpdateShopeeFoodVoucherResponse {
  optional string debug_msg = 1;
  optional VssPromotion promotion = 2;
}

message GetPromotionByIdRequest {
  optional string region = 1; // required
  optional int64 promotion_id = 2; // required
  optional int64 shop_id = 3; // seller center please always set this shop_id param!
  optional bool need_deleted = 4; // need deleted record or not, default is false
  optional int32 source = 5; // required, ref: CallerSource
}

message GetPromotionByIdResponse {
  optional string debug_msg = 1;
  optional VssPromotion promotion = 2;
}

message VssPromotion {
  optional int64 promotionid = 1;
  optional string name = 2;
  optional string url = 3;
  optional int64 start_time = 4;
  optional int64 end_time = 5;
  optional int64 notice_start_time = 6; // deprecated
  optional int64 notice_end_time = 7; // deprecated
  optional int32 discount = 8; // For percentage discount voucher, this is the amount of percentage discount (out of 100) to be multiplied to order price to make the discounted amount. e.g. discount = 30, order = 200, discounted order = 200 - (200 * 30 / 100) = 140
  optional int64 value = 9; // For absolute discount, this is the amount of absolute discount to be provided. value = 30, order = 200, discounted order = 200 - 30 = 170
  optional string banner = 10;
  optional bytes rule = 11; // ref: PBData.beeshop.db.PromotionRuleSet
  optional string prefix = 12; // voucher code for private voucher, voucher code prefix for public voucher
  optional int32 length = 13; // Length needed for voucher code. If length > len(prefix), will pad voucher code with random characters
  optional int32 usage_limit = 14; // This is just a storage place for BE to retreive the value and set it in public voucher. Coreserver does not use this value at all to do checks.
  optional int32 total_count = 15; // Limit on how many private vouchers can be distributed. Check against distributed_count during private voucher dispatching
  optional string country = 16;
  optional int64 min_price = 17; // cart item total price > this can use the voucher
  optional int32 status = 18; // Status_PROMOTION_x, 0: disabled, 1: enabled, 2 : deleted
  optional int32 distributed_count = 19; // Number of private voucher code already distributed (dispatched) to users
  optional int32 use_type = 20; // ref: PBData.beeshop.db.VoucherUseType
  optional int32 total_usage = 21; // How many times this promotion can be used
  optional int32 current_usage = 22; // Actual usage count of voucher. If voucher is returned/cancelled, this number is also reduced.
  optional int64 ctime = 23;
  optional int64 mtime = 24;
  optional string description = 25; // voucher T&C, usage term
  optional bool action_trigger = 26; // true: send PN when dispatching voucher
  optional string action_content = 27;
  optional int64 stop_dispatch_time = 28; //After this time, dispatching voucher will fail
  optional string action_title = 29;
  optional string push_content = 30;
  optional int64 max_value = 31; //Max amount of voucher discount (useful for percentage discount types). 0 to indicate no max.
  optional int32 allow_seller_promotion = 32; // 1: allow. If not set on creation, will be auto set to 1
  optional int32 shopid = 33; //0: Shopee Voucher created by backend.
  optional int32 voucher_market_type = 34; // bitflag value, ref: PBData.beeshop.db.VoucherMarketType
  optional string customer_reference_id = 35; // unique voucher identification displayed on FE for buyers to communicate with customer service

  // 以下字段不存在 vss ,vss 对应的协议也没有
  repeated uint64 voucher_tag_list = 1001;
  repeated uint64 exclude_store_tag_ids = 1002;
  repeated int32 delivery_type = 1003;
  optional foody_base.ClaimRules claim_rules = 1004;
  optional foody_base.ApplyRules apply_rules = 1005;
}

// get_promotion_list_by_list_type
message GetPromotionListByListTypeRequest {
  optional string region = 1; // required
  optional int64 shop_id = 2; // seller center please always set this shop_id param!
  optional int32 list_type = 3; // ref: PromotionListType
  optional voucher.core.PromotionQueryCondition condition = 4;
  repeated voucher.core.QueryOrderBy order_bys = 5;
  optional int32 resp_type = 6; // ref: PromotionQueryRespType, default is PROMOTION_QUERY_RESP_TYPE_FULL
  optional voucher.core.Pagination pagination = 7;
  optional bool need_deleted = 8; // need deleted record or not, default is false
  optional bool need_product_scope = 9; // default is false, explicitly set this to true if you need voucher product scope rules; TODO: ask seller center to modify accordingly, so that we don't have any hidden logic or inconsistent logic for seller vouchers
  optional int32 source = 10; // required, ref: CallerSource
}

message GetPromotionListByListTypeResponse {
  optional string debug_msg = 1;
  optional int64 total_count = 2; // valid only when request.resp_type=PROMOTION_QUERY_RESP_TYPE_COUNT_ONLY
  repeated foody_promotion.VssPromotion promotions = 3;
  optional voucher.core.Pagination pagination = 4;
}

message CreateDispatchByUserTagTaskRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required, eg: "SG", "ID", "VN", ...
  optional int64 promotion_id = 3;
  optional string operator = 4;
  optional voucher.task.UserAttribute user_attribute = 5;
  optional int32 business_domain = 6; // ref:Constant.BusinessDomain
  optional string user_segment = 7;
}

message CreateDispatchByUserTagTaskResponse {
  optional string debug_msg = 1;
  optional int64 task_id = 2;
}


message CreateDirectDispatchTaskRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required, eg: "SG", "ID", "VN", ...
  optional int64 promotion_id = 3;
  optional voucher.task.File file = 4; // file download url
  optional string operator = 5;
  optional int32 business_domain = 6; // ref:Constant.BusinessDomain
}

message CreateDirectDispatchTaskResponse {
  optional string debug_msg = 1;
  optional int64 task_id = 2;
}

message GetTasksByReferenceRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required, eg: "SG", "ID", "VN", ...
  optional int32 task_type = 3; // optional, ref: TaskType
  optional string reference_id = 4; // required, the ID which was used to create the task, in this use case it's promotionId
  repeated int32 task_commands = 5; // optional, ref: TaskCommand; OR logic for a list of task_commands.
}

message GetTasksByReferenceResponse {
  optional string debug_msg = 1;
  repeated VssTask tasks = 2;
}

message VssTask {
  optional int64 task_id = 1;
  optional int32 type = 2; // ref:pb.TaskType
  optional int32 command = 3; // internal command ,ref: pb.TaskCommand
  optional int32 status = 4; // ref:DispatchTaskStatus
  optional int32 success_count = 5; // the count of successful execution
  optional int32 fail_count = 6; // the count of failed execution
  optional int32 execute_count = 7; // the count of total execution
  optional int32 total_count = 8; // total task count
  optional int64 start_time = 9; // task start time
  optional int64 finish_time = 10; // task end time
  optional string operator = 11; // task creator
  optional voucher.task.File upload_file = 12;
  repeated voucher.task.File result_files = 13;
  optional voucher.task.UserAttribute user_attribute = 14;
  optional voucher.task.DispatchToAllTaskData dispatch_to_all_task_data = 15;
  optional string name = 16;
  optional voucher.task.GenerateVoucherCodesTaskData generate_voucher_codes_task_data = 17;
}

message CreateGenerateVoucherCodesTaskRequest {
  optional int32 source = 1;
  optional string region = 2;
  optional int64 voucher_id = 3;
  optional string operator = 4;
  optional int64 quantity = 5;
  optional int64 usage_limit = 6;
  optional string prefix = 7;
  optional int32 suffix_length = 8;
  optional string name = 9;
  optional int32 business_domain = 10; // ref:Constant.BusinessDomain
}

message CreateGenerateVoucherCodesTaskResponse {
  optional string debug_msg = 1;
  optional int64 task_id = 2;
  optional voucher.task.GenerateVoucherCodesTaskErrorData error_data = 3; // nil if no additional error data is available
}

message CreateUpdateGeneratedVoucherCodesTaskRequest {
  optional int32 source = 1; // required, ref: CallerSource
  optional string region = 2; // required, eg: "SG", "ID", "VN", ...
  optional int64 promotion_id = 3;
  optional string operator = 4;
  optional int32 business_domain = 5; // ref:Constant.BusinessDomain
  optional int64 generate_voucher_codes_task_id = 6;
  optional int32 status = 7;
}

message CreateUpdateGeneratedVoucherCodesTaskResponse {
  optional string debug_msg = 1;
  optional int64 task_id = 2;
}

message MGetStoreVoucherRequest {
  optional voucher.core.GetShopeeFoodVouchersByStoreBatchRequest vssReq = 1;
}

message MGetStoreVoucherResponse {
  optional voucher.core.GetShopeeFoodVouchersByStoreBatchResponse vssRsp = 1;
}

message GetVoucherStackingResponse {
  optional VoucherStackingLogic stacking_logic = 1;
}

message VoucherStackingLogic {
  optional uint64 total_voucher_num = 1;
  optional uint64 food_discount_voucher_num = 2;
  optional uint64 shipping_fee_voucher_num = 3;
  optional uint64 coins_cashback_voucher_num = 4;
}

message GetRecommendPromotionRequest {
  optional uint64 user_id = 1; // required
  optional uint64 store_id =2;
  optional string paotangRefId = 4; //paotangRefId
  optional int32  delivery_type = 5;
}

message GetRecommendPromotionResponse {
  repeated foody_base.OrderDiscountCampaign order_discounts = 5;
}

message RecommendVouchersRequest {
  optional int64 user_id = 1; // required
  optional voucher.core.ShopeeFoodOrder order = 2;
  optional int32 sorting_flag = 3; // optional, refer: enum VoucherRecommendSortFlag
  optional string paotangRefId = 4; //paotangRefId
  optional int32 delivery_type = 5;
  repeated foody_base.OrderDiscount order_discounts = 6; // optional 传入订单满减活动信息
}

message RecommendFoodVouchersResponse {
  repeated RecommendVouchers recommend_voucher = 1;
}

message ClaimFoodVoucherRequest {
  optional int64 user_id = 1; // required
  optional int64 promotion_id = 2;
  optional string voucher_code =3;
  repeated foody_base.VoucherIdentifier voucher_identifiers=4;
  optional bool auto_claim = 5;
}

message ClaimFoodVoucherResponse {
  message ClaimError{
    optional uint64 promotion_id = 1;
    optional string debug_msg = 2;
  }
  optional string debug_msg = 1;
  optional int32  claim_error = 2;
  repeated uint64 claimed_voucher_ids = 3;
  repeated ClaimError claim_errors = 4;
}



message GetVoucherBatchRequest {
  repeated foody_base.VoucherIdentifier voucher_identifiers = 1;
}

message GetVoucherBatchResponse {
  optional string debug_msg = 1;
  repeated VoucherDetail voucher_list = 2;
}

message RecommendVouchers {
  optional voucher.core.ValidateVoucherResult voucher = 1;
  optional bool is_fused = 2;
  optional uint64 gap_amount = 3;
  optional bool stackable = 4;
}

message GetVouchersByStoreBatchRequest {
  optional int64 limit_per_store = 1;
  repeated voucher.core.ShopeeFoodStoreInfo store_list = 2;
  optional int64 user_id = 3;
  optional uint64 service_type = 4;
  optional bool filter_for_non_logged_in_user = 5;
  optional string abtest_info = 6;
  optional int32 delivery_type = 7;
  optional bool auto_claim = 8;
}

message GetVouchersByStoreBatchResponse {
  repeated StoreVoucherList voucher_lists = 1;
  repeated int64 claimed_promotion_ids = 2;
  optional uint32 voucher_display_quantity =3;
}

message GetStoreVoucherBriefRequest {
  optional int64 limit_per_store = 1;
  repeated voucher.core.ShopeeFoodStoreInfo store_list = 2;
  optional int64 user_id = 3;
  repeated int32 reward_type = 4; //ref VoucherRewardType  0 food voucher 1 coin cash back 6 shipping fee
}

message GetStoreVoucherBriefResponse {
  repeated StoreVoucherList voucher_lists = 1;
}

message StoreVoucherList {
  optional uint64 store_id = 1;
  repeated VoucherDetail voucher_list = 2;
  optional uint64 maxed_shipping_fee_discount_amount = 3;
}

message CreateFileUploadAsyncTaskRequest {
  optional string file_name = 1;
  optional string file_link = 2;
  optional foody_base.AsyncTask.Type task_type = 3;
  optional string operator = 4;
  optional uint64 reference_id = 5;
  optional uint64 effective_time = 6;
  optional foody_base.AsyncTask.Status status = 7;
  optional uint32 succeed_cnt = 8;
  optional uint32 failed_cnt = 9;
}

message CreateFileUploadAsyncTaskResponse {
  optional uint64 task_id = 1;
}

message GetFileUploadAsyncTasksRequest {
  optional uint64 reference_id = 1;
  repeated foody_base.AsyncTask.Type task_type = 2;
  optional uint32 page_num = 3;
  optional uint32 page_size = 4;
}

message GetFileUploadAsyncTasksResponse {
  repeated foody_base.AsyncTask tasks = 1;
  optional uint64 total = 2;
}

message GetPromotionAvailablePaymentMethodRequest {
}

message GetPromotionAvailablePaymentMethodResponse {
  repeated foody_base.PromotionPaymentMethod voucher_payment_methods = 1;
  repeated foody_base.PromotionPaymentMethod spx_voucher_payment_methods = 2;
  repeated foody_base.PromotionPaymentMethod direct_discount_payment_methods = 3;
}


message MGenerateCreateVoucherTaskRequest {
  repeated CreateVoucherTask task = 1;
}

message CancelCreateVoucherTaskRequest {
  optional uint64  id = 1;
  optional CreateVoucherTask.BizType biz_type = 2;
  optional uint64  biz_id = 3;
}



message CreateVoucherTask {
  enum BizType {
    PACKAGE = 1;
  }
  enum Status{
    ENABLE =1;
    DISABLE=2;
  }
  optional uint64  id = 1;
  optional BizType biz_type = 2;
  optional uint64  biz_id = 3;
  optional uint64  template_id = 4;
  optional uint64  task_start_time =5;
  optional uint64  task_end_time = 6;
  optional uint32  create_interval = 7; // 生成券间隔,单位day
  optional uint32  advanced_time = 8; // 提前生成券的时间,单位day
  optional Status task_status = 9;
  optional CustomVoucherRule custom_voucher_rule = 10;
  optional uint64  next_create_time = 11;
}

message CustomVoucherRule{
  message UsageTime {
    optional uint64 usage_duration = 1;
    optional int64 start_time_offset = 2;
    optional int64 end_time_offset = 3;
  }
  message ClaimTime {
    optional int64 start_time_offset = 1;
    optional int64 end_time_offset = 2;
  }
  optional  UsageTime usage_time = 1;
  optional  ClaimTime claim_time = 2;
  optional foody_base.LocationScope location_scope =3;
  optional foody_base.UserRules user_rules= 4;
  optional uint64 usage_per_user_limit = 5;
  optional string revalidator = 6;
  optional string name_prefix = 7;
}

message MGenerateCreateVoucherTaskResponse {
  repeated uint64 task_id = 1;
}
message CreateMerchandiseRequest {
  required foody_merchandise.Merchandise merchandise = 1;
}

message CreateMerchandiseResponse {
  required uint64 id = 1;
}

message GetMerchandiseRequest {
  required uint64 id = 1;
}

message GetMerchandiseResponse {
  required foody_merchandise.Merchandise merchandise = 1;
}

message SearchMerchandisesRequest {
  message Filter {
    optional string keywords = 1;
    optional foody_merchandise.MerchandiseStatus status = 2;
    repeated uint64 location_groups = 3;
    optional foody_merchandise.TimeRange valid_start_time_range = 4;
    optional foody_merchandise.TimeRange valid_end_time_range = 5;
    optional foody_merchandise.TimeRange create_time_range = 6;
  }
  required uint32 page_num = 1;
  required uint32 page_size = 2;
  optional Filter filter = 3;
  optional foody_merchandise.PackageStatus status = 4;
  optional foody_merchandise.TimeRange valid_time_range = 5;
}

message SearchMerchandisesResponse {
  repeated foody_merchandise.Merchandise merchandises = 1;
  optional uint64 total_count = 2;
}

message UpdateMerchandiseStatusRequest {
  required uint64 id = 1;
  required foody_merchandise.MerchandiseStatus status = 2;
}

message UpdateMerchandiseStatusResponse {
}
