// Code generated by protoc-gen-microkit. DO NOT EDIT.
// source: foody_promotion.proto

/*
Package foody_promotion is a generated protocol buffer package.

It is generated from these files:

	foody_promotion.proto

It has these top-level messages:

	CoFundCampaign
	CoFundScheme
	CoFundSchemeSnapshot
	CoFundRecord
	OrderDiscountRecord
	CreateOrderDiscountCampaignRequest
	CreateOrderDiscountCampaignResponse
	UpdateOrderDiscountCampaignRequest
	UpdateOrderDiscountCampaignResponse
	UpdateOrderDiscountCampaignStatusRequest
	UpdateOrderDiscountCampaignStatusResponse
	GetOrderDiscountCampaignRequest
	GetOrderDiscountCampaignResponse
	SearchOrderDiscountCampaignRequest
	SearchOrderDiscountCampaignResponse
	MGetStorePromotionRequest
	MultiLangTxt
	VoucherDetail
	StoreVoucherCache
	StoreOrderDiscountCache
	StoreItemDiscountCache
	StorePromotion
	MGetStorePromotionResponse
	GetStorePromotionRequest
	GetStorePromotionResponse
	PaymentInfo
	GetPromotionDraftRequest
	GetPromotionDraftResponse
	UsePromotionRequest
	UsePromotionResponse
	CallbackCancelVoucherRequest
	CallbackCancelVoucherResponse
	PromotionPreviewRequest
	PromotionPreviewResponse
	CommitPromotionPreviewRequest
	CommitPromotionPreviewResponse
	CancelPromotionPreviewRequest
	CancelPromotionPreviewResponse
	MGetItemDiscountRequest
	MGetItemDiscountResponse
	ItemDiscountDish
	MGetItemDiscountV2Request
	MGetItemDiscountV2Response
	MCheckItemPriceRequest
	MCheckItemPriceResponse
	BatchMGetItemDiscountParam
	BatchMGetItemDiscountRequest
	BatchMGetItemDiscountItem
	BatchMGetItemDiscountResponse
	BatchMGetItemDiscountParamV2
	QueryNearbyAvailableVoucherRequest
	QueryNearbyAvailableVoucherResponse
	GetVoucherTypeListRequest
	GetVoucherTypeListResponse
	Tag
	BatchMGetItemDiscountV2Request
	BatchMGetItemDiscountItemV2
	BatchMGetItemDiscountV2Response
	QueryItemDiscountCampaignListRequest
	QueryItemDiscountCampaignListResponse
	QueryItemDiscountCampaignRequest
	QueryItemDiscountCampaignResponse
	GetOrderPromotionDetailRequest
	GetOrderPromotionDetailResponse
	GetSpxOrderPromotionDetailRequest
	GetSpxOrderPromotionDetailResponse
	CreateItemDiscountCampaignRequest
	ItemDiscountTip
	CreateItemDiscountCampaignResponse
	UpdateItemDiscountCampaignRequest
	UpdateItemDiscountCampaignResponse
	ItemDiscountCampaignDraftRequest
	ItemDiscountCampaignDraftResponse
	RefundRequest
	RefundResponse
	PartRefundRequest
	PartRefundResponse
	MUpdateItemDiscountCampaignRequest
	MUpdateItemDiscountCampaignResponse
	MCreateItemDiscountCampaignRequest
	MCreateItemDiscountCampaignResponse
	StorePromotionBrief
	MGetStorePromotionBriefRequest
	MGetStorePromotionBriefResponse
	GetCoFundCampaignRequest
	GetCoFundCampaignResponse
	DeleteCoFundCampaignRequest
	DeleteCoFundCampaignResponse
	CreateCoFundCampaignRequest
	CreateCoFundCampaignResponse
	UpdateCoFundCampaignRequest
	UpdateCoFundCampaignResponse
	SearchCoFundCampaignRequest
	SearchCoFundCampaignResponse
	GetRecentlyModifiedRequest
	GetRecentlyModifiedResponse
	IsDisplayRedDotRequest
	IsDisplayRedDotResponse
	MGetFlashSaleItemRequest
	MGetFlashSaleItemResponse
	MGetShopeeFoodVoucherRequest
	MGetShopeeFoodVoucherResponse
	CheckShopeeFoodVoucherUserClaimStatusRequest
	CheckShopeeFoodVoucherUserClaimStatusResponse
	CheckPromotionLimitRequest
	CheckPromotionLimitResponse
	GetPromotionListRequest
	GetPromotionListResponse
	SyncPromotionListRequest
	SyncPromotionListResponse
	GetPromotionDetailRequest
	GetPromotionDetailResponse
	GetPromotionToolListRequest
	GetPromotionToolListResponse
	CreateVoucherRequest
	CreateVoucherResponse
	UpdateVoucherRequest
	UpdateVoucherResponse
	GetVoucherRequest
	GetVoucherResponse
	SearchVoucherFilter
	SearchVoucherRequest
	SearchVoucherResponse
	CreateVoucherDispatchTaskRequest
	CreateVoucherDispatchTaskResponse
	GetVoucherDispatchTaskListRequest
	GetVoucherDispatchTaskListResponse
	CreateVoucherCodesTaskRequest
	CreateVoucherCodesTaskResponse
	UpdateVoucherCodesTaskRequest
	UpdateVoucherCodesTaskResponse
	ClaimVoucherRequest
	ClaimVoucherResponse
	ApplicableVoucher
	ListApplicableVoucherRequest
	ListApplicableVoucherResponse
	Amount
	SPXGetPromotionDraftRequest
	SPXGetPromotionDraftResponse
	SPXUsePromotionRequest
	SPXUsePromotionResponse
	SPXRefundPromotionRequest
	SPXRefundPromotionResponse
	ReturnCoins
	InvalidVoucherTip
	Tips
	UsePromotionTccCallbackRequest
	CallbackCancelUseRequest
	CallbackCancelUseResponse
	MGetFlashSaleItemStockRequest
	MGetFlashSaleItemStockResponse
	MGetFlashSaleItemDiscountRequest
	MGetFlashSaleItemDiscountResponse
	MCheckFlashSaleItemDiscountPriceRequest
	MCheckFlashSaleItemDiscountPriceResponse
	RequestMeta
	ResponseMeta
	DistributeVoucherRequest
	DistributeVoucherResponse
	BatchDistributeVoucherRequest
	BatchDistributeVoucherResponse
	BatchGetVoucherFromSPXVoucherWalletRequest
	BatchGetVoucherFromSPXVoucherWalletResponse
	ClaimVoucherFromSPXVoucherWalletRequest
	ClaimVoucherFromSPXVoucherWalletResponse
	GetVoucherStackingRequest
	CreateShopeeFoodVoucherRequest
	CreateShopeeFoodVoucherResponse
	UpdateShopeeFoodVoucherRequest
	UpdateShopeeFoodVoucherResponse
	GetPromotionByIdRequest
	GetPromotionByIdResponse
	VssPromotion
	GetPromotionListByListTypeRequest
	GetPromotionListByListTypeResponse
	CreateDispatchByUserTagTaskRequest
	CreateDispatchByUserTagTaskResponse
	CreateDirectDispatchTaskRequest
	CreateDirectDispatchTaskResponse
	GetTasksByReferenceRequest
	GetTasksByReferenceResponse
	VssTask
	CreateGenerateVoucherCodesTaskRequest
	CreateGenerateVoucherCodesTaskResponse
	CreateUpdateGeneratedVoucherCodesTaskRequest
	CreateUpdateGeneratedVoucherCodesTaskResponse
	MGetStoreVoucherRequest
	MGetStoreVoucherResponse
	GetVoucherStackingResponse
	VoucherStackingLogic
	RecommendVouchersRequest
	RecommendFoodVouchersResponse
	ClaimFoodVoucherRequest
	ClaimFoodVoucherResponse
	RecommendVouchers
	GetVouchersByStoreBatchRequest
	GetVouchersByStoreBatchResponse
	StoreVoucherList
*/
package foody_promotion

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
import _ "git.garena.com/shopee/foody/service/pb/foody_base"
import _ "git.garena.com/shopee/foody/service/pb/voucher_core"
import _ "git.garena.com/shopee/foody/service/pb/voucher_task"
import _ "git.garena.com/shopee/sp_protocol/desc/pb/spex/protobuf/service"

import (
	context "context"
	microkit1 "git.garena.com/shopee/feed/microkit"
	_ "git.garena.com/shopee/feed/microkit/protoc-gen-microkit/descriptor"
	micro "github.com/micro/go-micro"
	client "github.com/micro/go-micro/client"
	server "github.com/micro/go-micro/server"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ client.Option
var _ server.Option

// custom serviceName
const (
	serviceName = "foody_promotion"
)

// Client API for FoodyPromotion service

type FoodyPromotionService interface {
	// ShopeeFood
	// admin
	CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, opts ...client.CallOption) (*CreateOrderDiscountCampaignResponse, error)
	UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, opts ...client.CallOption) (*UpdateOrderDiscountCampaignResponse, error)
	UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, opts ...client.CallOption) (*UpdateOrderDiscountCampaignStatusResponse, error)
	GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, opts ...client.CallOption) (*GetOrderDiscountCampaignResponse, error)
	SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, opts ...client.CallOption) (*SearchOrderDiscountCampaignResponse, error)
	CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, opts ...client.CallOption) (*CreateItemDiscountCampaignResponse, error)
	UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, opts ...client.CallOption) (*UpdateItemDiscountCampaignResponse, error)
	ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, opts ...client.CallOption) (*ItemDiscountCampaignDraftResponse, error)
	QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, opts ...client.CallOption) (*QueryItemDiscountCampaignListResponse, error)
	QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, opts ...client.CallOption) (*QueryItemDiscountCampaignResponse, error)
	MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, opts ...client.CallOption) (*MCreateItemDiscountCampaignResponse, error)
	MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, opts ...client.CallOption) (*MUpdateItemDiscountCampaignResponse, error)
	GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, opts ...client.CallOption) (*GetOrderPromotionDetailResponse, error)
	GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, opts ...client.CallOption) (*GetVoucherTypeListResponse, error)
	// buyer
	MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, opts ...client.CallOption) (*MGetStorePromotionResponse, error)
	GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, opts ...client.CallOption) (*GetStorePromotionResponse, error)
	MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, opts ...client.CallOption) (*MGetItemDiscountResponse, error)
	MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, opts ...client.CallOption) (*MGetItemDiscountV2Response, error)
	BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, opts ...client.CallOption) (*BatchMGetItemDiscountResponse, error)
	BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, opts ...client.CallOption) (*BatchMGetItemDiscountV2Response, error)
	MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, opts ...client.CallOption) (*MGetStoreVoucherResponse, error)
	MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, opts ...client.CallOption) (*GetVouchersByStoreBatchResponse, error)
	GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, opts ...client.CallOption) (*RecommendFoodVouchersResponse, error)
	ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, opts ...client.CallOption) (*ClaimFoodVoucherResponse, error)
	// 通过获取周边可用的券列表
	// Filtering logic
	//  Within voucher type configured in admin [this info is stored in Food side]
	//  Within claim period or within use period if claimed
	//  Within target user scope (e.g. new user)
	//  Not fully used by this user yet
	//  At least one store will be displayed in store list after click use (see section 2.3 on the criteria)
	// Ranking logic
	//  P1: can claim / can use vouchers > fully claimed vouchers [需要补充设计]
	//  P2: [if above is the same]  Rank by reward type: food vouchers > shipping fee vouchers > coins cashback vouchers
	//  P3: [if above is the same] Rank by discount amount: value > %, DESC (e.g. 50k > 30k > 90% > 80%)
	//  P4: [if above is the same] Rank by cap (if any): value, DESC(e.g. no cap > cap at 30k > cap at 20k)
	//  P5: [if above is the same]  Voucher ID (newly created voucher first)
	QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, opts ...client.CallOption) (*QueryNearbyAvailableVoucherResponse, error)
	// order
	// buyer place order
	GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, opts ...client.CallOption) (*GetPromotionDraftResponse, error)
	UsePromotion(ctx context.Context, in *UsePromotionRequest, opts ...client.CallOption) (*UsePromotionResponse, error)
	CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, opts ...client.CallOption) (*CallbackCancelVoucherResponse, error)
	// driver modify order
	PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, opts ...client.CallOption) (*PromotionPreviewResponse, error)
	CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, opts ...client.CallOption) (*CommitPromotionPreviewResponse, error)
	CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, opts ...client.CallOption) (*CancelPromotionPreviewResponse, error)
	Refund(ctx context.Context, in *RefundRequest, opts ...client.CallOption) (*RefundResponse, error)
	PartRefund(ctx context.Context, in *PartRefundRequest, opts ...client.CallOption) (*PartRefundResponse, error)
	// algo team
	MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, opts ...client.CallOption) (*MGetStorePromotionBriefResponse, error)
	// flash sale
	// 获取库存信息
	MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, opts ...client.CallOption) (*MGetFlashSaleItemStockResponse, error)
	// 获取flash sale 菜品详情
	MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, opts ...client.CallOption) (*MGetFlashSaleItemDiscountResponse, error)
	// 允许&商家修改dish价格时，校验flash sale 菜品价格
	MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, opts ...client.CallOption) (*MCheckFlashSaleItemDiscountPriceResponse, error)
	MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, opts ...client.CallOption) (*MGetFlashSaleItemResponse, error)
	MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, opts ...client.CallOption) (*MCheckItemPriceResponse, error)
	GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, opts ...client.CallOption) (*GetRecentlyModifiedResponse, error)
	IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, opts ...client.CallOption) (*IsDisplayRedDotResponse, error)
	CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, opts ...client.CallOption) (*CheckPromotionLimitResponse, error)
	// 实时拉取混排的 promotion 列表
	GetPromotionList(ctx context.Context, in *GetPromotionListRequest, opts ...client.CallOption) (*GetPromotionListResponse, error)
	// 同步拉取指定类型的 promotion 数据
	SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, opts ...client.CallOption) (*SyncPromotionListResponse, error)
	// 获取指定类型的 promotion 详情
	GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, opts ...client.CallOption) (*GetPromotionDetailResponse, error)
	// 获取 promotion tool 列表
	GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, opts ...client.CallOption) (*GetPromotionToolListResponse, error)
	MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, opts ...client.CallOption) (*MGetShopeeFoodVoucherResponse, error)
	CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, opts ...client.CallOption) (*CheckShopeeFoodVoucherUserClaimStatusResponse, error)
	// admin
	CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...client.CallOption) (*CreateVoucherResponse, error)
	UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...client.CallOption) (*UpdateVoucherResponse, error)
	GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...client.CallOption) (*GetVoucherResponse, error)
	SearchVoucher(ctx context.Context, in *SearchVoucherRequest, opts ...client.CallOption) (*SearchVoucherResponse, error)
	CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, opts ...client.CallOption) (*CreateVoucherDispatchTaskResponse, error)
	GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, opts ...client.CallOption) (*GetVoucherDispatchTaskListResponse, error)
	CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, opts ...client.CallOption) (*CreateVoucherCodesTaskResponse, error)
	UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, opts ...client.CallOption) (*UpdateVoucherCodesTaskResponse, error)
	GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, opts ...client.CallOption) (*GetSpxOrderPromotionDetailResponse, error)
	// buyer
	ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, opts ...client.CallOption) (*ClaimVoucherResponse, error)
	ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, opts ...client.CallOption) (*ListApplicableVoucherResponse, error)
	SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, opts ...client.CallOption) (*SPXGetPromotionDraftResponse, error)
	SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, opts ...client.CallOption) (*SPXUsePromotionResponse, error)
	SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, opts ...client.CallOption) (*SPXRefundPromotionResponse, error)
	GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, opts ...client.CallOption) (*GetVoucherStackingResponse, error)
	// gateway
	SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, opts ...client.CallOption) (*CallbackCancelUseResponse, error)
	SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, opts ...client.CallOption) (*CallbackCancelUseResponse, error)
	BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, opts ...client.CallOption) (*BatchDistributeVoucherResponse, error)
	BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, opts ...client.CallOption) (*BatchGetVoucherFromSPXVoucherWalletResponse, error)
	ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, opts ...client.CallOption) (*ClaimVoucherFromSPXVoucherWalletResponse, error)
	// co-found
	GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, opts ...client.CallOption) (*GetCoFundCampaignResponse, error)
	DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, opts ...client.CallOption) (*DeleteCoFundCampaignResponse, error)
	CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, opts ...client.CallOption) (*CreateCoFundCampaignResponse, error)
	UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, opts ...client.CallOption) (*UpdateCoFundCampaignResponse, error)
	SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, opts ...client.CallOption) (*SearchCoFundCampaignResponse, error)
}

type foodyPromotionService struct {
	c    client.Client
	name string
}

func newFoodyPromotionService(name string, c client.Client) FoodyPromotionService {
	if c == nil {
		c = client.NewClient()
	}
	if len(name) == 0 {
		name = serviceName
	}
	return &foodyPromotionService{
		c:    c,
		name: name,
	}
}

func (c *foodyPromotionService) CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, opts ...client.CallOption) (*CreateOrderDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateOrderDiscountCampaign", in)
	out := new(CreateOrderDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, opts ...client.CallOption) (*UpdateOrderDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateOrderDiscountCampaign", in)
	out := new(UpdateOrderDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, opts ...client.CallOption) (*UpdateOrderDiscountCampaignStatusResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateOrderDiscountCampaignStatus", in)
	out := new(UpdateOrderDiscountCampaignStatusResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, opts ...client.CallOption) (*GetOrderDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetOrderDiscountCampaign", in)
	out := new(GetOrderDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, opts ...client.CallOption) (*SearchOrderDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SearchOrderDiscountCampaign", in)
	out := new(SearchOrderDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, opts ...client.CallOption) (*CreateItemDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateItemDiscountCampaign", in)
	out := new(CreateItemDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, opts ...client.CallOption) (*UpdateItemDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateItemDiscountCampaign", in)
	out := new(UpdateItemDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, opts ...client.CallOption) (*ItemDiscountCampaignDraftResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.ItemDiscountCampaignDraft", in)
	out := new(ItemDiscountCampaignDraftResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, opts ...client.CallOption) (*QueryItemDiscountCampaignListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.QueryItemDiscountCampaignList", in)
	out := new(QueryItemDiscountCampaignListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, opts ...client.CallOption) (*QueryItemDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.QueryItemDiscountCampaign", in)
	out := new(QueryItemDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, opts ...client.CallOption) (*MCreateItemDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MCreateItemDiscountCampaign", in)
	out := new(MCreateItemDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, opts ...client.CallOption) (*MUpdateItemDiscountCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MUpdateItemDiscountCampaign", in)
	out := new(MUpdateItemDiscountCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, opts ...client.CallOption) (*GetOrderPromotionDetailResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetOrderPromotionDetail", in)
	out := new(GetOrderPromotionDetailResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, opts ...client.CallOption) (*GetVoucherTypeListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetVoucherTypeList", in)
	out := new(GetVoucherTypeListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, opts ...client.CallOption) (*MGetStorePromotionResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetStorePromotion", in)
	out := new(MGetStorePromotionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, opts ...client.CallOption) (*GetStorePromotionResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetStorePromotion", in)
	out := new(GetStorePromotionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, opts ...client.CallOption) (*MGetItemDiscountResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetItemDiscount", in)
	out := new(MGetItemDiscountResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, opts ...client.CallOption) (*MGetItemDiscountV2Response, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetItemDiscountV2", in)
	out := new(MGetItemDiscountV2Response)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, opts ...client.CallOption) (*BatchMGetItemDiscountResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.BatchMGetItemDiscount", in)
	out := new(BatchMGetItemDiscountResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, opts ...client.CallOption) (*BatchMGetItemDiscountV2Response, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.BatchMGetItemDiscountV2", in)
	out := new(BatchMGetItemDiscountV2Response)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, opts ...client.CallOption) (*MGetStoreVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetStoreVoucher", in)
	out := new(MGetStoreVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, opts ...client.CallOption) (*GetVouchersByStoreBatchResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetStoreVoucherV2", in)
	out := new(GetVouchersByStoreBatchResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, opts ...client.CallOption) (*RecommendFoodVouchersResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetRecommendVoucher", in)
	out := new(RecommendFoodVouchersResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, opts ...client.CallOption) (*ClaimFoodVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.ClaimFoodVoucher", in)
	out := new(ClaimFoodVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, opts ...client.CallOption) (*QueryNearbyAvailableVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.QueryNearbyAvailableVouchers", in)
	out := new(QueryNearbyAvailableVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, opts ...client.CallOption) (*GetPromotionDraftResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetPromotionDraft", in)
	out := new(GetPromotionDraftResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UsePromotion(ctx context.Context, in *UsePromotionRequest, opts ...client.CallOption) (*UsePromotionResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UsePromotion", in)
	out := new(UsePromotionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, opts ...client.CallOption) (*CallbackCancelVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CallbackCancelVoucher", in)
	out := new(CallbackCancelVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, opts ...client.CallOption) (*PromotionPreviewResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.PromotionPreview", in)
	out := new(PromotionPreviewResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, opts ...client.CallOption) (*CommitPromotionPreviewResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CommitPromotionPreview", in)
	out := new(CommitPromotionPreviewResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, opts ...client.CallOption) (*CancelPromotionPreviewResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CancelPromotionPreview", in)
	out := new(CancelPromotionPreviewResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) Refund(ctx context.Context, in *RefundRequest, opts ...client.CallOption) (*RefundResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.Refund", in)
	out := new(RefundResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) PartRefund(ctx context.Context, in *PartRefundRequest, opts ...client.CallOption) (*PartRefundResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.PartRefund", in)
	out := new(PartRefundResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, opts ...client.CallOption) (*MGetStorePromotionBriefResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetStorePromotionBrief", in)
	out := new(MGetStorePromotionBriefResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, opts ...client.CallOption) (*MGetFlashSaleItemStockResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetFlashSaleItemStock", in)
	out := new(MGetFlashSaleItemStockResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, opts ...client.CallOption) (*MGetFlashSaleItemDiscountResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetFlashSaleItemDiscount", in)
	out := new(MGetFlashSaleItemDiscountResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, opts ...client.CallOption) (*MCheckFlashSaleItemDiscountPriceResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MCheckFlashSaleItemDiscountPrice", in)
	out := new(MCheckFlashSaleItemDiscountPriceResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, opts ...client.CallOption) (*MGetFlashSaleItemResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetFlashSaleItem", in)
	out := new(MGetFlashSaleItemResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, opts ...client.CallOption) (*MCheckItemPriceResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MCheckItemPrice", in)
	out := new(MCheckItemPriceResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, opts ...client.CallOption) (*GetRecentlyModifiedResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetRecentlyModified", in)
	out := new(GetRecentlyModifiedResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, opts ...client.CallOption) (*IsDisplayRedDotResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.IsDisplayRedDot", in)
	out := new(IsDisplayRedDotResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, opts ...client.CallOption) (*CheckPromotionLimitResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CheckPromotionLimit", in)
	out := new(CheckPromotionLimitResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetPromotionList(ctx context.Context, in *GetPromotionListRequest, opts ...client.CallOption) (*GetPromotionListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetPromotionList", in)
	out := new(GetPromotionListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, opts ...client.CallOption) (*SyncPromotionListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SyncPromotionList", in)
	out := new(SyncPromotionListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, opts ...client.CallOption) (*GetPromotionDetailResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetPromotionDetail", in)
	out := new(GetPromotionDetailResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, opts ...client.CallOption) (*GetPromotionToolListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetPromotionToolList", in)
	out := new(GetPromotionToolListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, opts ...client.CallOption) (*MGetShopeeFoodVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.MGetShopeeFoodVoucher", in)
	out := new(MGetShopeeFoodVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, opts ...client.CallOption) (*CheckShopeeFoodVoucherUserClaimStatusResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CheckShopeeFoodVoucherUserClaimStatus", in)
	out := new(CheckShopeeFoodVoucherUserClaimStatusResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...client.CallOption) (*CreateVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateVoucher", in)
	out := new(CreateVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...client.CallOption) (*UpdateVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateVoucher", in)
	out := new(UpdateVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...client.CallOption) (*GetVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetVoucher", in)
	out := new(GetVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SearchVoucher(ctx context.Context, in *SearchVoucherRequest, opts ...client.CallOption) (*SearchVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SearchVoucher", in)
	out := new(SearchVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, opts ...client.CallOption) (*CreateVoucherDispatchTaskResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateVoucherDispatchTask", in)
	out := new(CreateVoucherDispatchTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, opts ...client.CallOption) (*GetVoucherDispatchTaskListResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetVoucherDispatchTaskList", in)
	out := new(GetVoucherDispatchTaskListResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, opts ...client.CallOption) (*CreateVoucherCodesTaskResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateVoucherCodesTask", in)
	out := new(CreateVoucherCodesTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, opts ...client.CallOption) (*UpdateVoucherCodesTaskResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateVoucherCodesTask", in)
	out := new(UpdateVoucherCodesTaskResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, opts ...client.CallOption) (*GetSpxOrderPromotionDetailResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetSpxOrderPromotionDetail", in)
	out := new(GetSpxOrderPromotionDetailResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, opts ...client.CallOption) (*ClaimVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.ClaimVoucher", in)
	out := new(ClaimVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, opts ...client.CallOption) (*ListApplicableVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.ListApplicableVoucher", in)
	out := new(ListApplicableVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, opts ...client.CallOption) (*SPXGetPromotionDraftResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SPXGetPromotionDraft", in)
	out := new(SPXGetPromotionDraftResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, opts ...client.CallOption) (*SPXUsePromotionResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SPXUsePromotion", in)
	out := new(SPXUsePromotionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, opts ...client.CallOption) (*SPXRefundPromotionResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SPXRefund", in)
	out := new(SPXRefundPromotionResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, opts ...client.CallOption) (*GetVoucherStackingResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetVoucherStacking", in)
	out := new(GetVoucherStackingResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, opts ...client.CallOption) (*CallbackCancelUseResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SPXCallbackCancelVoucher", in)
	out := new(CallbackCancelUseResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, opts ...client.CallOption) (*CallbackCancelUseResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SPXCallbackCancelCoin", in)
	out := new(CallbackCancelUseResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, opts ...client.CallOption) (*BatchDistributeVoucherResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.BatchDistributeVoucher", in)
	out := new(BatchDistributeVoucherResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, opts ...client.CallOption) (*BatchGetVoucherFromSPXVoucherWalletResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.BatchGetVoucherFromSPXVoucherWallet", in)
	out := new(BatchGetVoucherFromSPXVoucherWalletResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, opts ...client.CallOption) (*ClaimVoucherFromSPXVoucherWalletResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.ClaimVoucherFromSPXVoucherWallet", in)
	out := new(ClaimVoucherFromSPXVoucherWalletResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, opts ...client.CallOption) (*GetCoFundCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.GetCoFundCampaign", in)
	out := new(GetCoFundCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, opts ...client.CallOption) (*DeleteCoFundCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.DeleteCoFundCampaign", in)
	out := new(DeleteCoFundCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, opts ...client.CallOption) (*CreateCoFundCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.CreateCoFundCampaign", in)
	out := new(CreateCoFundCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, opts ...client.CallOption) (*UpdateCoFundCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.UpdateCoFundCampaign", in)
	out := new(UpdateCoFundCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *foodyPromotionService) SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, opts ...client.CallOption) (*SearchCoFundCampaignResponse, error) {
	req := c.c.NewRequest(c.name, "FoodyPromotion.SearchCoFundCampaign", in)
	out := new(SearchCoFundCampaignResponse)
	err := c.c.Call(ctx, req, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for FoodyPromotion service

type FoodyPromotionHandler interface {
	// ShopeeFood
	// admin
	CreateOrderDiscountCampaign(context.Context, *CreateOrderDiscountCampaignRequest, *CreateOrderDiscountCampaignResponse) error
	UpdateOrderDiscountCampaign(context.Context, *UpdateOrderDiscountCampaignRequest, *UpdateOrderDiscountCampaignResponse) error
	UpdateOrderDiscountCampaignStatus(context.Context, *UpdateOrderDiscountCampaignStatusRequest, *UpdateOrderDiscountCampaignStatusResponse) error
	GetOrderDiscountCampaign(context.Context, *GetOrderDiscountCampaignRequest, *GetOrderDiscountCampaignResponse) error
	SearchOrderDiscountCampaign(context.Context, *SearchOrderDiscountCampaignRequest, *SearchOrderDiscountCampaignResponse) error
	CreateItemDiscountCampaign(context.Context, *CreateItemDiscountCampaignRequest, *CreateItemDiscountCampaignResponse) error
	UpdateItemDiscountCampaign(context.Context, *UpdateItemDiscountCampaignRequest, *UpdateItemDiscountCampaignResponse) error
	ItemDiscountCampaignDraft(context.Context, *ItemDiscountCampaignDraftRequest, *ItemDiscountCampaignDraftResponse) error
	QueryItemDiscountCampaignList(context.Context, *QueryItemDiscountCampaignListRequest, *QueryItemDiscountCampaignListResponse) error
	QueryItemDiscountCampaign(context.Context, *QueryItemDiscountCampaignRequest, *QueryItemDiscountCampaignResponse) error
	MCreateItemDiscountCampaign(context.Context, *MCreateItemDiscountCampaignRequest, *MCreateItemDiscountCampaignResponse) error
	MUpdateItemDiscountCampaign(context.Context, *MUpdateItemDiscountCampaignRequest, *MUpdateItemDiscountCampaignResponse) error
	GetOrderPromotionDetail(context.Context, *GetOrderPromotionDetailRequest, *GetOrderPromotionDetailResponse) error
	GetVoucherTypeList(context.Context, *GetVoucherTypeListRequest, *GetVoucherTypeListResponse) error
	// buyer
	MGetStorePromotion(context.Context, *MGetStorePromotionRequest, *MGetStorePromotionResponse) error
	GetStorePromotion(context.Context, *GetStorePromotionRequest, *GetStorePromotionResponse) error
	MGetItemDiscount(context.Context, *MGetItemDiscountRequest, *MGetItemDiscountResponse) error
	MGetItemDiscountV2(context.Context, *MGetItemDiscountV2Request, *MGetItemDiscountV2Response) error
	BatchMGetItemDiscount(context.Context, *BatchMGetItemDiscountRequest, *BatchMGetItemDiscountResponse) error
	BatchMGetItemDiscountV2(context.Context, *BatchMGetItemDiscountV2Request, *BatchMGetItemDiscountV2Response) error
	MGetStoreVoucher(context.Context, *MGetStoreVoucherRequest, *MGetStoreVoucherResponse) error
	MGetStoreVoucherV2(context.Context, *GetVouchersByStoreBatchRequest, *GetVouchersByStoreBatchResponse) error
	GetRecommendVoucher(context.Context, *RecommendVouchersRequest, *RecommendFoodVouchersResponse) error
	ClaimFoodVoucher(context.Context, *ClaimFoodVoucherRequest, *ClaimFoodVoucherResponse) error
	// 通过获取周边可用的券列表
	// Filtering logic
	//  Within voucher type configured in admin [this info is stored in Food side]
	//  Within claim period or within use period if claimed
	//  Within target user scope (e.g. new user)
	//  Not fully used by this user yet
	//  At least one store will be displayed in store list after click use (see section 2.3 on the criteria)
	// Ranking logic
	//  P1: can claim / can use vouchers > fully claimed vouchers [需要补充设计]
	//  P2: [if above is the same]  Rank by reward type: food vouchers > shipping fee vouchers > coins cashback vouchers
	//  P3: [if above is the same] Rank by discount amount: value > %, DESC (e.g. 50k > 30k > 90% > 80%)
	//  P4: [if above is the same] Rank by cap (if any): value, DESC(e.g. no cap > cap at 30k > cap at 20k)
	//  P5: [if above is the same]  Voucher ID (newly created voucher first)
	QueryNearbyAvailableVouchers(context.Context, *QueryNearbyAvailableVoucherRequest, *QueryNearbyAvailableVoucherResponse) error
	// order
	// buyer place order
	GetPromotionDraft(context.Context, *GetPromotionDraftRequest, *GetPromotionDraftResponse) error
	UsePromotion(context.Context, *UsePromotionRequest, *UsePromotionResponse) error
	CallbackCancelVoucher(context.Context, *CallbackCancelVoucherRequest, *CallbackCancelVoucherResponse) error
	// driver modify order
	PromotionPreview(context.Context, *PromotionPreviewRequest, *PromotionPreviewResponse) error
	CommitPromotionPreview(context.Context, *CommitPromotionPreviewRequest, *CommitPromotionPreviewResponse) error
	CancelPromotionPreview(context.Context, *CancelPromotionPreviewRequest, *CancelPromotionPreviewResponse) error
	Refund(context.Context, *RefundRequest, *RefundResponse) error
	PartRefund(context.Context, *PartRefundRequest, *PartRefundResponse) error
	// algo team
	MGetStorePromotionBrief(context.Context, *MGetStorePromotionBriefRequest, *MGetStorePromotionBriefResponse) error
	// flash sale
	// 获取库存信息
	MGetFlashSaleItemStock(context.Context, *MGetFlashSaleItemStockRequest, *MGetFlashSaleItemStockResponse) error
	// 获取flash sale 菜品详情
	MGetFlashSaleItemDiscount(context.Context, *MGetFlashSaleItemDiscountRequest, *MGetFlashSaleItemDiscountResponse) error
	// 允许&商家修改dish价格时，校验flash sale 菜品价格
	MCheckFlashSaleItemDiscountPrice(context.Context, *MCheckFlashSaleItemDiscountPriceRequest, *MCheckFlashSaleItemDiscountPriceResponse) error
	MGetFlashSaleItem(context.Context, *MGetFlashSaleItemRequest, *MGetFlashSaleItemResponse) error
	MCheckItemPrice(context.Context, *MCheckItemPriceRequest, *MCheckItemPriceResponse) error
	GetRecentlyModified(context.Context, *GetRecentlyModifiedRequest, *GetRecentlyModifiedResponse) error
	IsDisplayRedDot(context.Context, *IsDisplayRedDotRequest, *IsDisplayRedDotResponse) error
	CheckPromotionLimit(context.Context, *CheckPromotionLimitRequest, *CheckPromotionLimitResponse) error
	// 实时拉取混排的 promotion 列表
	GetPromotionList(context.Context, *GetPromotionListRequest, *GetPromotionListResponse) error
	// 同步拉取指定类型的 promotion 数据
	SyncPromotionList(context.Context, *SyncPromotionListRequest, *SyncPromotionListResponse) error
	// 获取指定类型的 promotion 详情
	GetPromotionDetail(context.Context, *GetPromotionDetailRequest, *GetPromotionDetailResponse) error
	// 获取 promotion tool 列表
	GetPromotionToolList(context.Context, *GetPromotionToolListRequest, *GetPromotionToolListResponse) error
	MGetShopeeFoodVoucher(context.Context, *MGetShopeeFoodVoucherRequest, *MGetShopeeFoodVoucherResponse) error
	CheckShopeeFoodVoucherUserClaimStatus(context.Context, *CheckShopeeFoodVoucherUserClaimStatusRequest, *CheckShopeeFoodVoucherUserClaimStatusResponse) error
	// admin
	CreateVoucher(context.Context, *CreateVoucherRequest, *CreateVoucherResponse) error
	UpdateVoucher(context.Context, *UpdateVoucherRequest, *UpdateVoucherResponse) error
	GetVoucher(context.Context, *GetVoucherRequest, *GetVoucherResponse) error
	SearchVoucher(context.Context, *SearchVoucherRequest, *SearchVoucherResponse) error
	CreateVoucherDispatchTask(context.Context, *CreateVoucherDispatchTaskRequest, *CreateVoucherDispatchTaskResponse) error
	GetVoucherDispatchTaskList(context.Context, *GetVoucherDispatchTaskListRequest, *GetVoucherDispatchTaskListResponse) error
	CreateVoucherCodesTask(context.Context, *CreateVoucherCodesTaskRequest, *CreateVoucherCodesTaskResponse) error
	UpdateVoucherCodesTask(context.Context, *UpdateVoucherCodesTaskRequest, *UpdateVoucherCodesTaskResponse) error
	GetSpxOrderPromotionDetail(context.Context, *GetSpxOrderPromotionDetailRequest, *GetSpxOrderPromotionDetailResponse) error
	// buyer
	ClaimVoucher(context.Context, *ClaimVoucherRequest, *ClaimVoucherResponse) error
	ListApplicableVoucher(context.Context, *ListApplicableVoucherRequest, *ListApplicableVoucherResponse) error
	SPXGetPromotionDraft(context.Context, *SPXGetPromotionDraftRequest, *SPXGetPromotionDraftResponse) error
	SPXUsePromotion(context.Context, *SPXUsePromotionRequest, *SPXUsePromotionResponse) error
	SPXRefund(context.Context, *SPXRefundPromotionRequest, *SPXRefundPromotionResponse) error
	GetVoucherStacking(context.Context, *GetVoucherStackingRequest, *GetVoucherStackingResponse) error
	// gateway
	SPXCallbackCancelVoucher(context.Context, *CallbackCancelUseRequest, *CallbackCancelUseResponse) error
	SPXCallbackCancelCoin(context.Context, *CallbackCancelUseRequest, *CallbackCancelUseResponse) error
	BatchDistributeVoucher(context.Context, *BatchDistributeVoucherRequest, *BatchDistributeVoucherResponse) error
	BatchGetVoucherFromSPXVoucherWallet(context.Context, *BatchGetVoucherFromSPXVoucherWalletRequest, *BatchGetVoucherFromSPXVoucherWalletResponse) error
	ClaimVoucherFromSPXVoucherWallet(context.Context, *ClaimVoucherFromSPXVoucherWalletRequest, *ClaimVoucherFromSPXVoucherWalletResponse) error
	// co-found
	GetCoFundCampaign(context.Context, *GetCoFundCampaignRequest, *GetCoFundCampaignResponse) error
	DeleteCoFundCampaign(context.Context, *DeleteCoFundCampaignRequest, *DeleteCoFundCampaignResponse) error
	CreateCoFundCampaign(context.Context, *CreateCoFundCampaignRequest, *CreateCoFundCampaignResponse) error
	UpdateCoFundCampaign(context.Context, *UpdateCoFundCampaignRequest, *UpdateCoFundCampaignResponse) error
	SearchCoFundCampaign(context.Context, *SearchCoFundCampaignRequest, *SearchCoFundCampaignResponse) error
}

func registerFoodyPromotionHandler(s server.Server, hdlr FoodyPromotionHandler, opts ...server.HandlerOption) error {
	type foodyPromotion interface {
		CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, out *CreateOrderDiscountCampaignResponse) error
		UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, out *UpdateOrderDiscountCampaignResponse) error
		UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, out *UpdateOrderDiscountCampaignStatusResponse) error
		GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, out *GetOrderDiscountCampaignResponse) error
		SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, out *SearchOrderDiscountCampaignResponse) error
		CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, out *CreateItemDiscountCampaignResponse) error
		UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, out *UpdateItemDiscountCampaignResponse) error
		ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, out *ItemDiscountCampaignDraftResponse) error
		QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, out *QueryItemDiscountCampaignListResponse) error
		QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, out *QueryItemDiscountCampaignResponse) error
		MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, out *MCreateItemDiscountCampaignResponse) error
		MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, out *MUpdateItemDiscountCampaignResponse) error
		GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, out *GetOrderPromotionDetailResponse) error
		GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, out *GetVoucherTypeListResponse) error
		MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, out *MGetStorePromotionResponse) error
		GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, out *GetStorePromotionResponse) error
		MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, out *MGetItemDiscountResponse) error
		MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, out *MGetItemDiscountV2Response) error
		BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, out *BatchMGetItemDiscountResponse) error
		BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, out *BatchMGetItemDiscountV2Response) error
		MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, out *MGetStoreVoucherResponse) error
		MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, out *GetVouchersByStoreBatchResponse) error
		GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, out *RecommendFoodVouchersResponse) error
		ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, out *ClaimFoodVoucherResponse) error
		QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, out *QueryNearbyAvailableVoucherResponse) error
		GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, out *GetPromotionDraftResponse) error
		UsePromotion(ctx context.Context, in *UsePromotionRequest, out *UsePromotionResponse) error
		CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, out *CallbackCancelVoucherResponse) error
		PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, out *PromotionPreviewResponse) error
		CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, out *CommitPromotionPreviewResponse) error
		CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, out *CancelPromotionPreviewResponse) error
		Refund(ctx context.Context, in *RefundRequest, out *RefundResponse) error
		PartRefund(ctx context.Context, in *PartRefundRequest, out *PartRefundResponse) error
		MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, out *MGetStorePromotionBriefResponse) error
		MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, out *MGetFlashSaleItemStockResponse) error
		MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, out *MGetFlashSaleItemDiscountResponse) error
		MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, out *MCheckFlashSaleItemDiscountPriceResponse) error
		MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, out *MGetFlashSaleItemResponse) error
		MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, out *MCheckItemPriceResponse) error
		GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, out *GetRecentlyModifiedResponse) error
		IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, out *IsDisplayRedDotResponse) error
		CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, out *CheckPromotionLimitResponse) error
		GetPromotionList(ctx context.Context, in *GetPromotionListRequest, out *GetPromotionListResponse) error
		SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, out *SyncPromotionListResponse) error
		GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, out *GetPromotionDetailResponse) error
		GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, out *GetPromotionToolListResponse) error
		MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, out *MGetShopeeFoodVoucherResponse) error
		CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, out *CheckShopeeFoodVoucherUserClaimStatusResponse) error
		CreateVoucher(ctx context.Context, in *CreateVoucherRequest, out *CreateVoucherResponse) error
		UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, out *UpdateVoucherResponse) error
		GetVoucher(ctx context.Context, in *GetVoucherRequest, out *GetVoucherResponse) error
		SearchVoucher(ctx context.Context, in *SearchVoucherRequest, out *SearchVoucherResponse) error
		CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, out *CreateVoucherDispatchTaskResponse) error
		GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, out *GetVoucherDispatchTaskListResponse) error
		CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, out *CreateVoucherCodesTaskResponse) error
		UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, out *UpdateVoucherCodesTaskResponse) error
		GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, out *GetSpxOrderPromotionDetailResponse) error
		ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, out *ClaimVoucherResponse) error
		ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, out *ListApplicableVoucherResponse) error
		SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, out *SPXGetPromotionDraftResponse) error
		SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, out *SPXUsePromotionResponse) error
		SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, out *SPXRefundPromotionResponse) error
		GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, out *GetVoucherStackingResponse) error
		SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, out *CallbackCancelUseResponse) error
		SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, out *CallbackCancelUseResponse) error
		BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, out *BatchDistributeVoucherResponse) error
		BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, out *BatchGetVoucherFromSPXVoucherWalletResponse) error
		ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, out *ClaimVoucherFromSPXVoucherWalletResponse) error
		GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, out *GetCoFundCampaignResponse) error
		DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, out *DeleteCoFundCampaignResponse) error
		CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, out *CreateCoFundCampaignResponse) error
		UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, out *UpdateCoFundCampaignResponse) error
		SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, out *SearchCoFundCampaignResponse) error
	}
	type FoodyPromotion struct {
		foodyPromotion
	}
	h := &foodyPromotionHandler{hdlr}
	return s.Handle(s.NewHandler(&FoodyPromotion{h}, opts...))
}

type foodyPromotionHandler struct {
	FoodyPromotionHandler
}

// Default service
var (
	service micro.Service
	cli     FoodyPromotionService
)

func GetServiceName() string {
	return serviceName
}

func NewService() micro.Service {
	service = micro.NewService(microkit1.DefaultOptions...)
	return service
}

func NewClient() FoodyPromotionService {
	cli = newFoodyPromotionService(serviceName, microkit1.DefaultClient)
	return cli
}

func RegisterFoodyPromotionHandler(hdlr FoodyPromotionHandler, opts ...server.HandlerOption) error {
	return registerFoodyPromotionHandler(service.Server(), hdlr, opts...)
}

func (h *foodyPromotionHandler) CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, out *CreateOrderDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.CreateOrderDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, out *UpdateOrderDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.UpdateOrderDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, out *UpdateOrderDiscountCampaignStatusResponse) error {
	return h.FoodyPromotionHandler.UpdateOrderDiscountCampaignStatus(ctx, in, out)
}

func (h *foodyPromotionHandler) GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, out *GetOrderDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.GetOrderDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, out *SearchOrderDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.SearchOrderDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, out *CreateItemDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.CreateItemDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, out *UpdateItemDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.UpdateItemDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, out *ItemDiscountCampaignDraftResponse) error {
	return h.FoodyPromotionHandler.ItemDiscountCampaignDraft(ctx, in, out)
}

func (h *foodyPromotionHandler) QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, out *QueryItemDiscountCampaignListResponse) error {
	return h.FoodyPromotionHandler.QueryItemDiscountCampaignList(ctx, in, out)
}

func (h *foodyPromotionHandler) QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, out *QueryItemDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.QueryItemDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, out *MCreateItemDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.MCreateItemDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, out *MUpdateItemDiscountCampaignResponse) error {
	return h.FoodyPromotionHandler.MUpdateItemDiscountCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, out *GetOrderPromotionDetailResponse) error {
	return h.FoodyPromotionHandler.GetOrderPromotionDetail(ctx, in, out)
}

func (h *foodyPromotionHandler) GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, out *GetVoucherTypeListResponse) error {
	return h.FoodyPromotionHandler.GetVoucherTypeList(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, out *MGetStorePromotionResponse) error {
	return h.FoodyPromotionHandler.MGetStorePromotion(ctx, in, out)
}

func (h *foodyPromotionHandler) GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, out *GetStorePromotionResponse) error {
	return h.FoodyPromotionHandler.GetStorePromotion(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, out *MGetItemDiscountResponse) error {
	return h.FoodyPromotionHandler.MGetItemDiscount(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, out *MGetItemDiscountV2Response) error {
	return h.FoodyPromotionHandler.MGetItemDiscountV2(ctx, in, out)
}

func (h *foodyPromotionHandler) BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, out *BatchMGetItemDiscountResponse) error {
	return h.FoodyPromotionHandler.BatchMGetItemDiscount(ctx, in, out)
}

func (h *foodyPromotionHandler) BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, out *BatchMGetItemDiscountV2Response) error {
	return h.FoodyPromotionHandler.BatchMGetItemDiscountV2(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, out *MGetStoreVoucherResponse) error {
	return h.FoodyPromotionHandler.MGetStoreVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, out *GetVouchersByStoreBatchResponse) error {
	return h.FoodyPromotionHandler.MGetStoreVoucherV2(ctx, in, out)
}

func (h *foodyPromotionHandler) GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, out *RecommendFoodVouchersResponse) error {
	return h.FoodyPromotionHandler.GetRecommendVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, out *ClaimFoodVoucherResponse) error {
	return h.FoodyPromotionHandler.ClaimFoodVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, out *QueryNearbyAvailableVoucherResponse) error {
	return h.FoodyPromotionHandler.QueryNearbyAvailableVouchers(ctx, in, out)
}

func (h *foodyPromotionHandler) GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, out *GetPromotionDraftResponse) error {
	return h.FoodyPromotionHandler.GetPromotionDraft(ctx, in, out)
}

func (h *foodyPromotionHandler) UsePromotion(ctx context.Context, in *UsePromotionRequest, out *UsePromotionResponse) error {
	return h.FoodyPromotionHandler.UsePromotion(ctx, in, out)
}

func (h *foodyPromotionHandler) CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, out *CallbackCancelVoucherResponse) error {
	return h.FoodyPromotionHandler.CallbackCancelVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, out *PromotionPreviewResponse) error {
	return h.FoodyPromotionHandler.PromotionPreview(ctx, in, out)
}

func (h *foodyPromotionHandler) CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, out *CommitPromotionPreviewResponse) error {
	return h.FoodyPromotionHandler.CommitPromotionPreview(ctx, in, out)
}

func (h *foodyPromotionHandler) CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, out *CancelPromotionPreviewResponse) error {
	return h.FoodyPromotionHandler.CancelPromotionPreview(ctx, in, out)
}

func (h *foodyPromotionHandler) Refund(ctx context.Context, in *RefundRequest, out *RefundResponse) error {
	return h.FoodyPromotionHandler.Refund(ctx, in, out)
}

func (h *foodyPromotionHandler) PartRefund(ctx context.Context, in *PartRefundRequest, out *PartRefundResponse) error {
	return h.FoodyPromotionHandler.PartRefund(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, out *MGetStorePromotionBriefResponse) error {
	return h.FoodyPromotionHandler.MGetStorePromotionBrief(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, out *MGetFlashSaleItemStockResponse) error {
	return h.FoodyPromotionHandler.MGetFlashSaleItemStock(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, out *MGetFlashSaleItemDiscountResponse) error {
	return h.FoodyPromotionHandler.MGetFlashSaleItemDiscount(ctx, in, out)
}

func (h *foodyPromotionHandler) MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, out *MCheckFlashSaleItemDiscountPriceResponse) error {
	return h.FoodyPromotionHandler.MCheckFlashSaleItemDiscountPrice(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, out *MGetFlashSaleItemResponse) error {
	return h.FoodyPromotionHandler.MGetFlashSaleItem(ctx, in, out)
}

func (h *foodyPromotionHandler) MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, out *MCheckItemPriceResponse) error {
	return h.FoodyPromotionHandler.MCheckItemPrice(ctx, in, out)
}

func (h *foodyPromotionHandler) GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, out *GetRecentlyModifiedResponse) error {
	return h.FoodyPromotionHandler.GetRecentlyModified(ctx, in, out)
}

func (h *foodyPromotionHandler) IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, out *IsDisplayRedDotResponse) error {
	return h.FoodyPromotionHandler.IsDisplayRedDot(ctx, in, out)
}

func (h *foodyPromotionHandler) CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, out *CheckPromotionLimitResponse) error {
	return h.FoodyPromotionHandler.CheckPromotionLimit(ctx, in, out)
}

func (h *foodyPromotionHandler) GetPromotionList(ctx context.Context, in *GetPromotionListRequest, out *GetPromotionListResponse) error {
	return h.FoodyPromotionHandler.GetPromotionList(ctx, in, out)
}

func (h *foodyPromotionHandler) SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, out *SyncPromotionListResponse) error {
	return h.FoodyPromotionHandler.SyncPromotionList(ctx, in, out)
}

func (h *foodyPromotionHandler) GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, out *GetPromotionDetailResponse) error {
	return h.FoodyPromotionHandler.GetPromotionDetail(ctx, in, out)
}

func (h *foodyPromotionHandler) GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, out *GetPromotionToolListResponse) error {
	return h.FoodyPromotionHandler.GetPromotionToolList(ctx, in, out)
}

func (h *foodyPromotionHandler) MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, out *MGetShopeeFoodVoucherResponse) error {
	return h.FoodyPromotionHandler.MGetShopeeFoodVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, out *CheckShopeeFoodVoucherUserClaimStatusResponse) error {
	return h.FoodyPromotionHandler.CheckShopeeFoodVoucherUserClaimStatus(ctx, in, out)
}

func (h *foodyPromotionHandler) CreateVoucher(ctx context.Context, in *CreateVoucherRequest, out *CreateVoucherResponse) error {
	return h.FoodyPromotionHandler.CreateVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, out *UpdateVoucherResponse) error {
	return h.FoodyPromotionHandler.UpdateVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) GetVoucher(ctx context.Context, in *GetVoucherRequest, out *GetVoucherResponse) error {
	return h.FoodyPromotionHandler.GetVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) SearchVoucher(ctx context.Context, in *SearchVoucherRequest, out *SearchVoucherResponse) error {
	return h.FoodyPromotionHandler.SearchVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, out *CreateVoucherDispatchTaskResponse) error {
	return h.FoodyPromotionHandler.CreateVoucherDispatchTask(ctx, in, out)
}

func (h *foodyPromotionHandler) GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, out *GetVoucherDispatchTaskListResponse) error {
	return h.FoodyPromotionHandler.GetVoucherDispatchTaskList(ctx, in, out)
}

func (h *foodyPromotionHandler) CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, out *CreateVoucherCodesTaskResponse) error {
	return h.FoodyPromotionHandler.CreateVoucherCodesTask(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, out *UpdateVoucherCodesTaskResponse) error {
	return h.FoodyPromotionHandler.UpdateVoucherCodesTask(ctx, in, out)
}

func (h *foodyPromotionHandler) GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, out *GetSpxOrderPromotionDetailResponse) error {
	return h.FoodyPromotionHandler.GetSpxOrderPromotionDetail(ctx, in, out)
}

func (h *foodyPromotionHandler) ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, out *ClaimVoucherResponse) error {
	return h.FoodyPromotionHandler.ClaimVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, out *ListApplicableVoucherResponse) error {
	return h.FoodyPromotionHandler.ListApplicableVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, out *SPXGetPromotionDraftResponse) error {
	return h.FoodyPromotionHandler.SPXGetPromotionDraft(ctx, in, out)
}

func (h *foodyPromotionHandler) SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, out *SPXUsePromotionResponse) error {
	return h.FoodyPromotionHandler.SPXUsePromotion(ctx, in, out)
}

func (h *foodyPromotionHandler) SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, out *SPXRefundPromotionResponse) error {
	return h.FoodyPromotionHandler.SPXRefund(ctx, in, out)
}

func (h *foodyPromotionHandler) GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, out *GetVoucherStackingResponse) error {
	return h.FoodyPromotionHandler.GetVoucherStacking(ctx, in, out)
}

func (h *foodyPromotionHandler) SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, out *CallbackCancelUseResponse) error {
	return h.FoodyPromotionHandler.SPXCallbackCancelVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, out *CallbackCancelUseResponse) error {
	return h.FoodyPromotionHandler.SPXCallbackCancelCoin(ctx, in, out)
}

func (h *foodyPromotionHandler) BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, out *BatchDistributeVoucherResponse) error {
	return h.FoodyPromotionHandler.BatchDistributeVoucher(ctx, in, out)
}

func (h *foodyPromotionHandler) BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, out *BatchGetVoucherFromSPXVoucherWalletResponse) error {
	return h.FoodyPromotionHandler.BatchGetVoucherFromSPXVoucherWallet(ctx, in, out)
}

func (h *foodyPromotionHandler) ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, out *ClaimVoucherFromSPXVoucherWalletResponse) error {
	return h.FoodyPromotionHandler.ClaimVoucherFromSPXVoucherWallet(ctx, in, out)
}

func (h *foodyPromotionHandler) GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, out *GetCoFundCampaignResponse) error {
	return h.FoodyPromotionHandler.GetCoFundCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, out *DeleteCoFundCampaignResponse) error {
	return h.FoodyPromotionHandler.DeleteCoFundCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, out *CreateCoFundCampaignResponse) error {
	return h.FoodyPromotionHandler.CreateCoFundCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, out *UpdateCoFundCampaignResponse) error {
	return h.FoodyPromotionHandler.UpdateCoFundCampaign(ctx, in, out)
}

func (h *foodyPromotionHandler) SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, out *SearchCoFundCampaignResponse) error {
	return h.FoodyPromotionHandler.SearchCoFundCampaign(ctx, in, out)
}
