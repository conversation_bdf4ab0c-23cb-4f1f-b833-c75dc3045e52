package foody_promotion

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/desc"
	sp_errors "git.garena.com/shopee/platform/golang_splib/errors"
	sp_common "git.garena.com/shopee/sp_protocol/golang/common.pb"
)

type SpexFoodyPromotionHandler interface {
	// ShopeeFood
	// admin
	CreateOrderDiscountCampaign(context.Context, *CreateOrderDiscountCampaignRequest, *CreateOrderDiscountCampaignResponse) error

	UpdateOrderDiscountCampaign(context.Context, *UpdateOrderDiscountCampaignRequest, *UpdateOrderDiscountCampaignResponse) error

	UpdateOrderDiscountCampaignStatus(context.Context, *UpdateOrderDiscountCampaignStatusRequest, *UpdateOrderDiscountCampaignStatusResponse) error

	GetOrderDiscountCampaign(context.Context, *GetOrderDiscountCampaignRequest, *GetOrderDiscountCampaignResponse) error

	SearchOrderDiscountCampaign(context.Context, *SearchOrderDiscountCampaignRequest, *SearchOrderDiscountCampaignResponse) error

	CreateItemDiscountCampaign(context.Context, *CreateItemDiscountCampaignRequest, *CreateItemDiscountCampaignResponse) error

	UpdateItemDiscountCampaign(context.Context, *UpdateItemDiscountCampaignRequest, *UpdateItemDiscountCampaignResponse) error

	ItemDiscountCampaignDraft(context.Context, *ItemDiscountCampaignDraftRequest, *ItemDiscountCampaignDraftResponse) error

	QueryItemDiscountCampaignList(context.Context, *QueryItemDiscountCampaignListRequest, *QueryItemDiscountCampaignListResponse) error

	QueryItemDiscountCampaign(context.Context, *QueryItemDiscountCampaignRequest, *QueryItemDiscountCampaignResponse) error

	MCreateItemDiscountCampaign(context.Context, *MCreateItemDiscountCampaignRequest, *MCreateItemDiscountCampaignResponse) error

	MUpdateItemDiscountCampaign(context.Context, *MUpdateItemDiscountCampaignRequest, *MUpdateItemDiscountCampaignResponse) error

	GetOrderPromotionDetail(context.Context, *GetOrderPromotionDetailRequest, *GetOrderPromotionDetailResponse) error

	GetVoucherTypeList(context.Context, *GetVoucherTypeListRequest, *GetVoucherTypeListResponse) error

	QueryAvailableItemDiscountList(context.Context, *QueryItemDiscountRequest, *QueryItemDiscountResponse) error

	MDisableItemDiscountCampaign(context.Context, *MDisableItemDiscountCampaignRequest, *MDisableItemDiscountCampaignResponse) error

	// buyer
	MGetStorePromotion(context.Context, *MGetStorePromotionRequest, *MGetStorePromotionResponse) error

	GetStorePromotion(context.Context, *GetStorePromotionRequest, *GetStorePromotionResponse) error

	// Deprecated
	MGetItemDiscount(context.Context, *MGetItemDiscountRequest, *MGetItemDiscountResponse) error

	MGetItemDiscountV2(context.Context, *MGetItemDiscountV2Request, *MGetItemDiscountV2Response) error

	// Deprecated
	BatchMGetItemDiscount(context.Context, *BatchMGetItemDiscountRequest, *BatchMGetItemDiscountResponse) error

	BatchMGetItemDiscountV2(context.Context, *BatchMGetItemDiscountV2Request, *BatchMGetItemDiscountV2Response) error

	// Deprecated
	MGetStoreVoucher(context.Context, *MGetStoreVoucherRequest, *MGetStoreVoucherResponse) error

	MGetStoreVoucherV2(context.Context, *GetVouchersByStoreBatchRequest, *GetVouchersByStoreBatchResponse) error

	MGetStoreVoucherBrief(context.Context, *GetStoreVoucherBriefRequest, *GetStoreVoucherBriefResponse) error

	GetRecommendVoucher(context.Context, *RecommendVouchersRequest, *RecommendFoodVouchersResponse) error

	GetRecommendPromotion(context.Context, *GetRecommendPromotionRequest, *GetRecommendPromotionResponse) error

	ClaimFoodVoucher(context.Context, *ClaimFoodVoucherRequest, *ClaimFoodVoucherResponse) error

	GetTargetVoucher(context.Context, *GetTargetVoucherRequest, *GetTargetVoucherResponse) error

	// 通过获取周边可用的券列表
	// Filtering logic
	//  Within voucher type configured in admin [this info is stored in Food side]
	//  Within claim period or within use period if claimed
	//  Within target user scope (e.g. new user)
	//  Not fully used by this user yet
	//  At least one store will be displayed in store list after click use (see section 2.3 on the criteria)
	// Ranking logic
	//  P1: can claim / can use vouchers > fully claimed vouchers [需要补充设计]
	//  P2: [if above is the same]  Rank by reward type: food vouchers > shipping fee vouchers > coins cashback vouchers
	//  P3: [if above is the same] Rank by discount amount: value > %, DESC (e.g. 50k > 30k > 90% > 80%)
	//  P4: [if above is the same] Rank by cap (if any): value, DESC(e.g. no cap > cap at 30k > cap at 20k)
	//  P5: [if above is the same]  Voucher ID (newly created voucher first)
	QueryNearbyAvailableVouchers(context.Context, *QueryNearbyAvailableVoucherRequest, *QueryNearbyAvailableVoucherResponse) error

	// order
	// buyer place order
	GetPromotionDraft(context.Context, *GetPromotionDraftRequest, *GetPromotionDraftResponse) error

	UsePromotion(context.Context, *UsePromotionRequest, *UsePromotionResponse) error

	CallbackCancelVoucher(context.Context, *CallbackCancelVoucherRequest, *CallbackCancelVoucherResponse) error

	// driver modify order
	PromotionPreview(context.Context, *PromotionPreviewRequest, *PromotionPreviewResponse) error

	CommitPromotionPreview(context.Context, *CommitPromotionPreviewRequest, *CommitPromotionPreviewResponse) error

	CancelPromotionPreview(context.Context, *CancelPromotionPreviewRequest, *CancelPromotionPreviewResponse) error

	Refund(context.Context, *RefundRequest, *RefundResponse) error

	PartRefund(context.Context, *PartRefundRequest, *PartRefundResponse) error

	// algo team
	MGetStorePromotionBrief(context.Context, *MGetStorePromotionBriefRequest, *MGetStorePromotionBriefResponse) error

	// flash sale
	// 获取库存信息
	MGetFlashSaleItemStock(context.Context, *MGetFlashSaleItemStockRequest, *MGetFlashSaleItemStockResponse) error

	// 获取flash sale 菜品详情
	MGetFlashSaleItemDiscount(context.Context, *MGetFlashSaleItemDiscountRequest, *MGetFlashSaleItemDiscountResponse) error

	// 允许&商家修改dish价格时，校验flash sale 菜品价格
	MCheckFlashSaleItemDiscountPrice(context.Context, *MCheckFlashSaleItemDiscountPriceRequest, *MCheckFlashSaleItemDiscountPriceResponse) error

	MGetFlashSaleItem(context.Context, *MGetFlashSaleItemRequest, *MGetFlashSaleItemResponse) error

	MCheckItemPrice(context.Context, *MCheckItemPriceRequest, *MCheckItemPriceResponse) error

	GetRecentlyModified(context.Context, *GetRecentlyModifiedRequest, *GetRecentlyModifiedResponse) error

	IsDisplayRedDot(context.Context, *IsDisplayRedDotRequest, *IsDisplayRedDotResponse) error

	CheckPromotionLimit(context.Context, *CheckPromotionLimitRequest, *CheckPromotionLimitResponse) error

	// 实时拉取混排的 promotion 列表
	GetPromotionList(context.Context, *GetPromotionListRequest, *GetPromotionListResponse) error

	// 同步拉取指定类型的 promotion 数据
	SyncPromotionList(context.Context, *SyncPromotionListRequest, *SyncPromotionListResponse) error

	// 获取指定类型的 promotion 详情
	GetPromotionDetail(context.Context, *GetPromotionDetailRequest, *GetPromotionDetailResponse) error

	// 获取 promotion tool 列表
	GetPromotionToolList(context.Context, *GetPromotionToolListRequest, *GetPromotionToolListResponse) error

	MGetShopeeFoodVoucher(context.Context, *MGetShopeeFoodVoucherRequest, *MGetShopeeFoodVoucherResponse) error

	CheckShopeeFoodVoucherUserClaimStatus(context.Context, *CheckShopeeFoodVoucherUserClaimStatusRequest, *CheckShopeeFoodVoucherUserClaimStatusResponse) error

	// admin
	CreateVoucher(context.Context, *CreateVoucherRequest, *CreateVoucherResponse) error

	UpdateVoucher(context.Context, *UpdateVoucherRequest, *UpdateVoucherResponse) error

	GetVoucher(context.Context, *GetVoucherRequest, *GetVoucherResponse) error

	SearchVoucher(context.Context, *SearchVoucherRequest, *SearchVoucherResponse) error

	CreateVoucherDispatchTask(context.Context, *CreateVoucherDispatchTaskRequest, *CreateVoucherDispatchTaskResponse) error

	GetVoucherDispatchTaskList(context.Context, *GetVoucherDispatchTaskListRequest, *GetVoucherDispatchTaskListResponse) error

	CreateVoucherCodesTask(context.Context, *CreateVoucherCodesTaskRequest, *CreateVoucherCodesTaskResponse) error

	UpdateVoucherCodesTask(context.Context, *UpdateVoucherCodesTaskRequest, *UpdateVoucherCodesTaskResponse) error

	CreateVoucherTemplate(context.Context, *CreateVoucherTemplateRequest, *CreateVoucherTemplateResponse) error

	UpdateVoucherTemplate(context.Context, *UpdateVoucherTemplateRequest, *UpdateVoucherTemplateResponse) error

	GetVoucherTemplate(context.Context, *GetVoucherTemplateRequest, *GetVoucherTemplateResponse) error

	SearchVoucherTemplate(context.Context, *SearchVoucherTemplateRequest, *SearchVoucherTemplateResponse) error

	GetTemplateVouchers(context.Context, *GetTemplateVouchersRequest, *GetTemplateVouchersResponse) error

	CreateVoucherTemplateUpdateTask(context.Context, *CreateVoucherTemplateUpdateTaskRequest, *CreateVoucherTemplateUpdateTaskResponse) error

	GetVoucherTemplateUpdateTaskList(context.Context, *GetVoucherTemplateUpdateTaskListRequest, *GetVoucherTemplateUpdateTaskListResponse) error

	CancelVoucherTemplateUpdateTask(context.Context, *CancelVoucherTemplateUpdateTaskRequest, *CancelVoucherTemplateUpdateTaskResponse) error

	GetSpxOrderPromotionDetail(context.Context, *GetSpxOrderPromotionDetailRequest, *GetSpxOrderPromotionDetailResponse) error

	// buyer
	ClaimVoucher(context.Context, *ClaimVoucherRequest, *ClaimVoucherResponse) error

	ListApplicableVoucher(context.Context, *ListApplicableVoucherRequest, *ListApplicableVoucherResponse) error

	SPXGetPromotionDraft(context.Context, *SPXGetPromotionDraftRequest, *SPXGetPromotionDraftResponse) error

	SPXUsePromotion(context.Context, *SPXUsePromotionRequest, *SPXUsePromotionResponse) error

	SPXRefund(context.Context, *SPXRefundPromotionRequest, *SPXRefundPromotionResponse) error

	GetVoucherStacking(context.Context, *GetVoucherStackingRequest, *GetVoucherStackingResponse) error

	GetVoucherBatch(context.Context, *GetVoucherBatchRequest, *GetVoucherBatchResponse) error

	// gateway
	SPXCallbackCancelVoucher(context.Context, *CallbackCancelUseRequest, *CallbackCancelUseResponse) error

	SPXCallbackCancelCoin(context.Context, *CallbackCancelUseRequest, *CallbackCancelUseResponse) error

	BatchDistributeVoucher(context.Context, *BatchDistributeVoucherRequest, *BatchDistributeVoucherResponse) error

	BatchGetVoucherFromSPXVoucherWallet(context.Context, *BatchGetVoucherFromSPXVoucherWalletRequest, *BatchGetVoucherFromSPXVoucherWalletResponse) error

	ClaimVoucherFromSPXVoucherWallet(context.Context, *ClaimVoucherFromSPXVoucherWalletRequest, *ClaimVoucherFromSPXVoucherWalletResponse) error

	// co-found
	GetCoFundCampaign(context.Context, *GetCoFundCampaignRequest, *GetCoFundCampaignResponse) error

	DeleteCoFundCampaign(context.Context, *DeleteCoFundCampaignRequest, *DeleteCoFundCampaignResponse) error

	CreateCoFundCampaign(context.Context, *CreateCoFundCampaignRequest, *CreateCoFundCampaignResponse) error

	UpdateCoFundCampaign(context.Context, *UpdateCoFundCampaignRequest, *UpdateCoFundCampaignResponse) error

	SearchCoFundCampaign(context.Context, *SearchCoFundCampaignRequest, *SearchCoFundCampaignResponse) error

	// file upload async task
	CreateFileUploadAsyncTask(context.Context, *CreateFileUploadAsyncTaskRequest, *CreateFileUploadAsyncTaskResponse) error

	GetFileUploadAsyncTasks(context.Context, *GetFileUploadAsyncTasksRequest, *GetFileUploadAsyncTasksResponse) error

	GetPromotionAvailablePaymentMethod(context.Context, *GetPromotionAvailablePaymentMethodRequest, *GetPromotionAvailablePaymentMethodResponse) error

	// microsite
	GetMicrositeTargetVoucherComponentTemplate(context.Context, *GetMicrositeTargetVoucherComponentTemplateRequest, *GetMicrositeTargetVoucherComponentTemplateResponse) error

	ValidateMicrositeTargetVoucherComponentTemplate(context.Context, *ValidateMicrositeTargetVoucherComponentTemplateRequest, *ValidateMicrositeTargetVoucherComponentTemplateResponse) error

	TransformMicrositeTargetVoucherComponentTemplate(context.Context, *TransformMicrositeTargetVoucherComponentTemplateRequest, *TransformMicrositeTargetVoucherComponentTemplateResponse) error

	// package
	CreateMerchandise(context.Context, *CreateMerchandiseRequest, *CreateMerchandiseResponse) error

	GetMerchandise(context.Context, *GetMerchandiseRequest, *GetMerchandiseResponse) error

	SearchMerchandises(context.Context, *SearchMerchandisesRequest, *SearchMerchandisesResponse) error

	UpdateMerchandiseStatus(context.Context, *UpdateMerchandiseStatusRequest, *UpdateMerchandiseStatusResponse) error

	// buyer
	MGetUserMerchandise(context.Context, *MGetUserMerchandiseRequest, *MGetUserMerchandiseResponse) error

	QueryAvailableMerchandise(context.Context, *QueryAvailableMerchandiseRequest, *QueryAvailableMerchandiseResponse) error

	DispatchMerchandiseBenefits(context.Context, *DispatchMerchandiseBenefitsRequest, *DispatchMerchandiseBenefitsResponse) error

	VerifyBuyerQualifications(context.Context, *VerifyBuyerQualificationsRequest, *VerifyBuyerQualificationsResponse) error
}

type spexFoodyPromotionServiceImpl struct {
	methods []desc.Method
	handler SpexFoodyPromotionHandler
}

func (s *spexFoodyPromotionServiceImpl) Name() string {
	return "FoodyPromotion"
}

func (s *spexFoodyPromotionServiceImpl) Methods() []desc.Method {
	return s.methods
}

func (s *spexFoodyPromotionServiceImpl) Backend() desc.Backend {
	return desc.SPEX
}

type method struct {
	handler      desc.Handler
	command      string
	requestType  interface{}
	responseType interface{}
}

func newMethod(command string, handler desc.Handler, reqType interface{}, respType interface{}) *method {
	return &method{
		command:      command,
		handler:      handler,
		requestType:  reqType,
		responseType: respType,
	}
}

func (m *method) Command() string {
	return m.command
}

func (m *method) RequestType() interface{} {
	return m.requestType
}

func (m *method) ResponseType() interface{} {
	return m.responseType
}

func (m *method) Handler() desc.Handler {
	return m.handler
}

func newService(handler SpexFoodyPromotionHandler) desc.Service {
	namespace := "shopeefood.promotion.promotion"

	hs := spexFoodyPromotionServiceImpl{
		handler: handler,
	}
	hs.methods = append(hs.methods,
		newMethod(namespace+"."+"create_order_discount_campaign", hs.CreateOrderDiscountCampaign, &CreateOrderDiscountCampaignRequest{}, &CreateOrderDiscountCampaignResponse{}),
		newMethod(namespace+"."+"update_order_discount_campaign", hs.UpdateOrderDiscountCampaign, &UpdateOrderDiscountCampaignRequest{}, &UpdateOrderDiscountCampaignResponse{}),
		newMethod(namespace+"."+"update_order_discount_campaign_status", hs.UpdateOrderDiscountCampaignStatus, &UpdateOrderDiscountCampaignStatusRequest{}, &UpdateOrderDiscountCampaignStatusResponse{}),
		newMethod(namespace+"."+"get_order_discount_campaign", hs.GetOrderDiscountCampaign, &GetOrderDiscountCampaignRequest{}, &GetOrderDiscountCampaignResponse{}),
		newMethod(namespace+"."+"search_order_discount_campaign", hs.SearchOrderDiscountCampaign, &SearchOrderDiscountCampaignRequest{}, &SearchOrderDiscountCampaignResponse{}),
		newMethod(namespace+"."+"create_item_discount_campaign", hs.CreateItemDiscountCampaign, &CreateItemDiscountCampaignRequest{}, &CreateItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"update_item_discount_campaign", hs.UpdateItemDiscountCampaign, &UpdateItemDiscountCampaignRequest{}, &UpdateItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"item_discount_campaign_draft", hs.ItemDiscountCampaignDraft, &ItemDiscountCampaignDraftRequest{}, &ItemDiscountCampaignDraftResponse{}),
		newMethod(namespace+"."+"query_item_discount_campaign_list", hs.QueryItemDiscountCampaignList, &QueryItemDiscountCampaignListRequest{}, &QueryItemDiscountCampaignListResponse{}),
		newMethod(namespace+"."+"query_item_discount_campaign", hs.QueryItemDiscountCampaign, &QueryItemDiscountCampaignRequest{}, &QueryItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"m_create_item_discount_campaign", hs.MCreateItemDiscountCampaign, &MCreateItemDiscountCampaignRequest{}, &MCreateItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"m_update_item_discount_campaign", hs.MUpdateItemDiscountCampaign, &MUpdateItemDiscountCampaignRequest{}, &MUpdateItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"get_order_promotion_detail", hs.GetOrderPromotionDetail, &GetOrderPromotionDetailRequest{}, &GetOrderPromotionDetailResponse{}),
		newMethod(namespace+"."+"get_voucher_type_list", hs.GetVoucherTypeList, &GetVoucherTypeListRequest{}, &GetVoucherTypeListResponse{}),
		newMethod(namespace+"."+"query_available_item_discount_list", hs.QueryAvailableItemDiscountList, &QueryItemDiscountRequest{}, &QueryItemDiscountResponse{}),
		newMethod(namespace+"."+"m_disable_item_discount_campaign", hs.MDisableItemDiscountCampaign, &MDisableItemDiscountCampaignRequest{}, &MDisableItemDiscountCampaignResponse{}),
		newMethod(namespace+"."+"m_get_store_promotion", hs.MGetStorePromotion, &MGetStorePromotionRequest{}, &MGetStorePromotionResponse{}),
		newMethod(namespace+"."+"get_store_promotion", hs.GetStorePromotion, &GetStorePromotionRequest{}, &GetStorePromotionResponse{}),
		newMethod(namespace+"."+"m_get_item_discount", hs.MGetItemDiscount, &MGetItemDiscountRequest{}, &MGetItemDiscountResponse{}),
		newMethod(namespace+"."+"m_get_item_discount_v2", hs.MGetItemDiscountV2, &MGetItemDiscountV2Request{}, &MGetItemDiscountV2Response{}),
		newMethod(namespace+"."+"batch_m_get_item_discount", hs.BatchMGetItemDiscount, &BatchMGetItemDiscountRequest{}, &BatchMGetItemDiscountResponse{}),
		newMethod(namespace+"."+"batch_m_get_item_discount_v2", hs.BatchMGetItemDiscountV2, &BatchMGetItemDiscountV2Request{}, &BatchMGetItemDiscountV2Response{}),
		newMethod(namespace+"."+"m_get_store_voucher", hs.MGetStoreVoucher, &MGetStoreVoucherRequest{}, &MGetStoreVoucherResponse{}),
		newMethod(namespace+"."+"m_get_store_voucher_v2", hs.MGetStoreVoucherV2, &GetVouchersByStoreBatchRequest{}, &GetVouchersByStoreBatchResponse{}),
		newMethod(namespace+"."+"m_get_store_voucher_brief", hs.MGetStoreVoucherBrief, &GetStoreVoucherBriefRequest{}, &GetStoreVoucherBriefResponse{}),
		newMethod(namespace+"."+"get_recommend_voucher", hs.GetRecommendVoucher, &RecommendVouchersRequest{}, &RecommendFoodVouchersResponse{}),
		newMethod(namespace+"."+"get_recommend_promotion", hs.GetRecommendPromotion, &GetRecommendPromotionRequest{}, &GetRecommendPromotionResponse{}),
		newMethod(namespace+"."+"claim_food_voucher", hs.ClaimFoodVoucher, &ClaimFoodVoucherRequest{}, &ClaimFoodVoucherResponse{}),
		newMethod(namespace+"."+"get_target_voucher", hs.GetTargetVoucher, &GetTargetVoucherRequest{}, &GetTargetVoucherResponse{}),
		newMethod(namespace+"."+"query_nearby_available_vouchers", hs.QueryNearbyAvailableVouchers, &QueryNearbyAvailableVoucherRequest{}, &QueryNearbyAvailableVoucherResponse{}),
		newMethod(namespace+"."+"get_promotion_draft", hs.GetPromotionDraft, &GetPromotionDraftRequest{}, &GetPromotionDraftResponse{}),
		newMethod(namespace+"."+"use_promotion", hs.UsePromotion, &UsePromotionRequest{}, &UsePromotionResponse{}),
		newMethod(namespace+"."+"callback_cancel_voucher", hs.CallbackCancelVoucher, &CallbackCancelVoucherRequest{}, &CallbackCancelVoucherResponse{}),
		newMethod(namespace+"."+"promotion_preview", hs.PromotionPreview, &PromotionPreviewRequest{}, &PromotionPreviewResponse{}),
		newMethod(namespace+"."+"commit_promotion_preview", hs.CommitPromotionPreview, &CommitPromotionPreviewRequest{}, &CommitPromotionPreviewResponse{}),
		newMethod(namespace+"."+"cancel_promotion_preview", hs.CancelPromotionPreview, &CancelPromotionPreviewRequest{}, &CancelPromotionPreviewResponse{}),
		newMethod(namespace+"."+"refund", hs.Refund, &RefundRequest{}, &RefundResponse{}),
		newMethod(namespace+"."+"part_refund", hs.PartRefund, &PartRefundRequest{}, &PartRefundResponse{}),
		newMethod(namespace+"."+"m_get_store_promotion_brief", hs.MGetStorePromotionBrief, &MGetStorePromotionBriefRequest{}, &MGetStorePromotionBriefResponse{}),
		newMethod(namespace+"."+"m_get_flash_sale_item_stock", hs.MGetFlashSaleItemStock, &MGetFlashSaleItemStockRequest{}, &MGetFlashSaleItemStockResponse{}),
		newMethod(namespace+"."+"m_get_flash_sale_item_discount", hs.MGetFlashSaleItemDiscount, &MGetFlashSaleItemDiscountRequest{}, &MGetFlashSaleItemDiscountResponse{}),
		newMethod(namespace+"."+"m_check_flash_sale_item_discount_price", hs.MCheckFlashSaleItemDiscountPrice, &MCheckFlashSaleItemDiscountPriceRequest{}, &MCheckFlashSaleItemDiscountPriceResponse{}),
		newMethod(namespace+"."+"m_get_flash_sale_item", hs.MGetFlashSaleItem, &MGetFlashSaleItemRequest{}, &MGetFlashSaleItemResponse{}),
		newMethod(namespace+"."+"m_check_item_price", hs.MCheckItemPrice, &MCheckItemPriceRequest{}, &MCheckItemPriceResponse{}),
		newMethod(namespace+"."+"get_recently_modified", hs.GetRecentlyModified, &GetRecentlyModifiedRequest{}, &GetRecentlyModifiedResponse{}),
		newMethod(namespace+"."+"is_display_red_dot", hs.IsDisplayRedDot, &IsDisplayRedDotRequest{}, &IsDisplayRedDotResponse{}),
		newMethod(namespace+"."+"check_promotion_limit", hs.CheckPromotionLimit, &CheckPromotionLimitRequest{}, &CheckPromotionLimitResponse{}),
		newMethod(namespace+"."+"get_promotion_list", hs.GetPromotionList, &GetPromotionListRequest{}, &GetPromotionListResponse{}),
		newMethod(namespace+"."+"sync_promotion_list", hs.SyncPromotionList, &SyncPromotionListRequest{}, &SyncPromotionListResponse{}),
		newMethod(namespace+"."+"get_promotion_detail", hs.GetPromotionDetail, &GetPromotionDetailRequest{}, &GetPromotionDetailResponse{}),
		newMethod(namespace+"."+"get_promotion_tool_list", hs.GetPromotionToolList, &GetPromotionToolListRequest{}, &GetPromotionToolListResponse{}),
		newMethod(namespace+"."+"m_get_shopee_food_voucher", hs.MGetShopeeFoodVoucher, &MGetShopeeFoodVoucherRequest{}, &MGetShopeeFoodVoucherResponse{}),
		newMethod(namespace+"."+"check_shopee_food_voucher_user_claim_status", hs.CheckShopeeFoodVoucherUserClaimStatus, &CheckShopeeFoodVoucherUserClaimStatusRequest{}, &CheckShopeeFoodVoucherUserClaimStatusResponse{}),
		newMethod(namespace+"."+"create_voucher", hs.CreateVoucher, &CreateVoucherRequest{}, &CreateVoucherResponse{}),
		newMethod(namespace+"."+"update_voucher", hs.UpdateVoucher, &UpdateVoucherRequest{}, &UpdateVoucherResponse{}),
		newMethod(namespace+"."+"get_voucher", hs.GetVoucher, &GetVoucherRequest{}, &GetVoucherResponse{}),
		newMethod(namespace+"."+"search_voucher", hs.SearchVoucher, &SearchVoucherRequest{}, &SearchVoucherResponse{}),
		newMethod(namespace+"."+"create_voucher_dispatch_task", hs.CreateVoucherDispatchTask, &CreateVoucherDispatchTaskRequest{}, &CreateVoucherDispatchTaskResponse{}),
		newMethod(namespace+"."+"get_voucher_dispatch_task_list", hs.GetVoucherDispatchTaskList, &GetVoucherDispatchTaskListRequest{}, &GetVoucherDispatchTaskListResponse{}),
		newMethod(namespace+"."+"create_voucher_codes_task", hs.CreateVoucherCodesTask, &CreateVoucherCodesTaskRequest{}, &CreateVoucherCodesTaskResponse{}),
		newMethod(namespace+"."+"update_voucher_codes_task", hs.UpdateVoucherCodesTask, &UpdateVoucherCodesTaskRequest{}, &UpdateVoucherCodesTaskResponse{}),
		newMethod(namespace+"."+"create_voucher_template", hs.CreateVoucherTemplate, &CreateVoucherTemplateRequest{}, &CreateVoucherTemplateResponse{}),
		newMethod(namespace+"."+"update_voucher_template", hs.UpdateVoucherTemplate, &UpdateVoucherTemplateRequest{}, &UpdateVoucherTemplateResponse{}),
		newMethod(namespace+"."+"get_voucher_template", hs.GetVoucherTemplate, &GetVoucherTemplateRequest{}, &GetVoucherTemplateResponse{}),
		newMethod(namespace+"."+"search_voucher_template", hs.SearchVoucherTemplate, &SearchVoucherTemplateRequest{}, &SearchVoucherTemplateResponse{}),
		newMethod(namespace+"."+"get_template_vouchers", hs.GetTemplateVouchers, &GetTemplateVouchersRequest{}, &GetTemplateVouchersResponse{}),
		newMethod(namespace+"."+"create_voucher_template_update_task", hs.CreateVoucherTemplateUpdateTask, &CreateVoucherTemplateUpdateTaskRequest{}, &CreateVoucherTemplateUpdateTaskResponse{}),
		newMethod(namespace+"."+"get_voucher_template_update_task_list", hs.GetVoucherTemplateUpdateTaskList, &GetVoucherTemplateUpdateTaskListRequest{}, &GetVoucherTemplateUpdateTaskListResponse{}),
		newMethod(namespace+"."+"cancel_voucher_template_update_task", hs.CancelVoucherTemplateUpdateTask, &CancelVoucherTemplateUpdateTaskRequest{}, &CancelVoucherTemplateUpdateTaskResponse{}),
		newMethod(namespace+"."+"get_spx_order_promotion_detail", hs.GetSpxOrderPromotionDetail, &GetSpxOrderPromotionDetailRequest{}, &GetSpxOrderPromotionDetailResponse{}),
		newMethod(namespace+"."+"claim_voucher", hs.ClaimVoucher, &ClaimVoucherRequest{}, &ClaimVoucherResponse{}),
		newMethod(namespace+"."+"list_applicable_voucher", hs.ListApplicableVoucher, &ListApplicableVoucherRequest{}, &ListApplicableVoucherResponse{}),
		newMethod(namespace+"."+"spx_get_promotion_draft", hs.SPXGetPromotionDraft, &SPXGetPromotionDraftRequest{}, &SPXGetPromotionDraftResponse{}),
		newMethod(namespace+"."+"spx_use_promotion", hs.SPXUsePromotion, &SPXUsePromotionRequest{}, &SPXUsePromotionResponse{}),
		newMethod(namespace+"."+"spx_refund", hs.SPXRefund, &SPXRefundPromotionRequest{}, &SPXRefundPromotionResponse{}),
		newMethod(namespace+"."+"get_voucher_stacking", hs.GetVoucherStacking, &GetVoucherStackingRequest{}, &GetVoucherStackingResponse{}),
		newMethod(namespace+"."+"get_voucher_batch", hs.GetVoucherBatch, &GetVoucherBatchRequest{}, &GetVoucherBatchResponse{}),
		newMethod(namespace+"."+"spx_callback_cancel_voucher", hs.SPXCallbackCancelVoucher, &CallbackCancelUseRequest{}, &CallbackCancelUseResponse{}),
		newMethod(namespace+"."+"spx_callback_cancel_coin", hs.SPXCallbackCancelCoin, &CallbackCancelUseRequest{}, &CallbackCancelUseResponse{}),
		newMethod(namespace+"."+"batch_distribute_voucher", hs.BatchDistributeVoucher, &BatchDistributeVoucherRequest{}, &BatchDistributeVoucherResponse{}),
		newMethod(namespace+"."+"batch_get_voucher_from_spx_voucher_wallet", hs.BatchGetVoucherFromSPXVoucherWallet, &BatchGetVoucherFromSPXVoucherWalletRequest{}, &BatchGetVoucherFromSPXVoucherWalletResponse{}),
		newMethod(namespace+"."+"claim_voucher_from_spx_voucher_wallet", hs.ClaimVoucherFromSPXVoucherWallet, &ClaimVoucherFromSPXVoucherWalletRequest{}, &ClaimVoucherFromSPXVoucherWalletResponse{}),
		newMethod(namespace+"."+"get_co_fund_campaign", hs.GetCoFundCampaign, &GetCoFundCampaignRequest{}, &GetCoFundCampaignResponse{}),
		newMethod(namespace+"."+"delete_co_fund_campaign", hs.DeleteCoFundCampaign, &DeleteCoFundCampaignRequest{}, &DeleteCoFundCampaignResponse{}),
		newMethod(namespace+"."+"create_co_fund_campaign", hs.CreateCoFundCampaign, &CreateCoFundCampaignRequest{}, &CreateCoFundCampaignResponse{}),
		newMethod(namespace+"."+"update_co_fund_campaign", hs.UpdateCoFundCampaign, &UpdateCoFundCampaignRequest{}, &UpdateCoFundCampaignResponse{}),
		newMethod(namespace+"."+"search_co_fund_campaign", hs.SearchCoFundCampaign, &SearchCoFundCampaignRequest{}, &SearchCoFundCampaignResponse{}),
		newMethod(namespace+"."+"create_file_upload_async_task", hs.CreateFileUploadAsyncTask, &CreateFileUploadAsyncTaskRequest{}, &CreateFileUploadAsyncTaskResponse{}),
		newMethod(namespace+"."+"get_file_upload_async_tasks", hs.GetFileUploadAsyncTasks, &GetFileUploadAsyncTasksRequest{}, &GetFileUploadAsyncTasksResponse{}),
		newMethod(namespace+"."+"get_promotion_available_payment_method", hs.GetPromotionAvailablePaymentMethod, &GetPromotionAvailablePaymentMethodRequest{}, &GetPromotionAvailablePaymentMethodResponse{}),
		newMethod(namespace+"."+"get_microsite_target_voucher_component_template", hs.GetMicrositeTargetVoucherComponentTemplate, &GetMicrositeTargetVoucherComponentTemplateRequest{}, &GetMicrositeTargetVoucherComponentTemplateResponse{}),
		newMethod(namespace+"."+"validate_microsite_target_voucher_component_template", hs.ValidateMicrositeTargetVoucherComponentTemplate, &ValidateMicrositeTargetVoucherComponentTemplateRequest{}, &ValidateMicrositeTargetVoucherComponentTemplateResponse{}),
		newMethod(namespace+"."+"transform_microsite_target_voucher_component_template", hs.TransformMicrositeTargetVoucherComponentTemplate, &TransformMicrositeTargetVoucherComponentTemplateRequest{}, &TransformMicrositeTargetVoucherComponentTemplateResponse{}),
		newMethod(namespace+"."+"create_merchandise", hs.CreateMerchandise, &CreateMerchandiseRequest{}, &CreateMerchandiseResponse{}),
		newMethod(namespace+"."+"get_merchandise", hs.GetMerchandise, &GetMerchandiseRequest{}, &GetMerchandiseResponse{}),
		newMethod(namespace+"."+"search_merchandises", hs.SearchMerchandises, &SearchMerchandisesRequest{}, &SearchMerchandisesResponse{}),
		newMethod(namespace+"."+"update_merchandise_status", hs.UpdateMerchandiseStatus, &UpdateMerchandiseStatusRequest{}, &UpdateMerchandiseStatusResponse{}),
		newMethod(namespace+"."+"m_get_user_merchandise", hs.MGetUserMerchandise, &MGetUserMerchandiseRequest{}, &MGetUserMerchandiseResponse{}),
		newMethod(namespace+"."+"query_available_merchandise", hs.QueryAvailableMerchandise, &QueryAvailableMerchandiseRequest{}, &QueryAvailableMerchandiseResponse{}),
		newMethod(namespace+"."+"dispatch_merchandise_benefits", hs.DispatchMerchandiseBenefits, &DispatchMerchandiseBenefitsRequest{}, &DispatchMerchandiseBenefitsResponse{}),
		newMethod(namespace+"."+"verify_buyer_qualifications", hs.VerifyBuyerQualifications, &VerifyBuyerQualificationsRequest{}, &VerifyBuyerQualificationsResponse{}),
	)

	return &hs
}

func (s *spexFoodyPromotionServiceImpl) CreateOrderDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateOrderDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateOrderDiscountCampaignResponse{}

	appErr := s.handler.CreateOrderDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateOrderDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateOrderDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateOrderDiscountCampaignResponse{}

	appErr := s.handler.UpdateOrderDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateOrderDiscountCampaignStatus(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateOrderDiscountCampaignStatusRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateOrderDiscountCampaignStatusResponse{}

	appErr := s.handler.UpdateOrderDiscountCampaignStatus(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetOrderDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetOrderDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetOrderDiscountCampaignResponse{}

	appErr := s.handler.GetOrderDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SearchOrderDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchOrderDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchOrderDiscountCampaignResponse{}

	appErr := s.handler.SearchOrderDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateItemDiscountCampaignResponse{}

	appErr := s.handler.CreateItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateItemDiscountCampaignResponse{}

	appErr := s.handler.UpdateItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ItemDiscountCampaignDraft(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ItemDiscountCampaignDraftRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ItemDiscountCampaignDraftResponse{}

	appErr := s.handler.ItemDiscountCampaignDraft(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) QueryItemDiscountCampaignList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*QueryItemDiscountCampaignListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := QueryItemDiscountCampaignListResponse{}

	appErr := s.handler.QueryItemDiscountCampaignList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) QueryItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*QueryItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := QueryItemDiscountCampaignResponse{}

	appErr := s.handler.QueryItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MCreateItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MCreateItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MCreateItemDiscountCampaignResponse{}

	appErr := s.handler.MCreateItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MUpdateItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MUpdateItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MUpdateItemDiscountCampaignResponse{}

	appErr := s.handler.MUpdateItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetOrderPromotionDetail(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetOrderPromotionDetailRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetOrderPromotionDetailResponse{}

	appErr := s.handler.GetOrderPromotionDetail(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherTypeList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherTypeListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherTypeListResponse{}

	appErr := s.handler.GetVoucherTypeList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) QueryAvailableItemDiscountList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*QueryItemDiscountRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := QueryItemDiscountResponse{}

	appErr := s.handler.QueryAvailableItemDiscountList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MDisableItemDiscountCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MDisableItemDiscountCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MDisableItemDiscountCampaignResponse{}

	appErr := s.handler.MDisableItemDiscountCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetStorePromotion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetStorePromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetStorePromotionResponse{}

	appErr := s.handler.MGetStorePromotion(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetStorePromotion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetStorePromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetStorePromotionResponse{}

	appErr := s.handler.GetStorePromotion(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetItemDiscount(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetItemDiscountRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetItemDiscountResponse{}

	appErr := s.handler.MGetItemDiscount(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetItemDiscountV2(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetItemDiscountV2Request)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetItemDiscountV2Response{}

	appErr := s.handler.MGetItemDiscountV2(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) BatchMGetItemDiscount(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*BatchMGetItemDiscountRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := BatchMGetItemDiscountResponse{}

	appErr := s.handler.BatchMGetItemDiscount(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) BatchMGetItemDiscountV2(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*BatchMGetItemDiscountV2Request)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := BatchMGetItemDiscountV2Response{}

	appErr := s.handler.BatchMGetItemDiscountV2(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetStoreVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetStoreVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetStoreVoucherResponse{}

	appErr := s.handler.MGetStoreVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetStoreVoucherV2(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVouchersByStoreBatchRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVouchersByStoreBatchResponse{}

	appErr := s.handler.MGetStoreVoucherV2(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetStoreVoucherBrief(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetStoreVoucherBriefRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetStoreVoucherBriefResponse{}

	appErr := s.handler.MGetStoreVoucherBrief(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetRecommendVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*RecommendVouchersRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := RecommendFoodVouchersResponse{}

	appErr := s.handler.GetRecommendVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetRecommendPromotion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetRecommendPromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetRecommendPromotionResponse{}

	appErr := s.handler.GetRecommendPromotion(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ClaimFoodVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ClaimFoodVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ClaimFoodVoucherResponse{}

	appErr := s.handler.ClaimFoodVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetTargetVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTargetVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTargetVoucherResponse{}

	appErr := s.handler.GetTargetVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) QueryNearbyAvailableVouchers(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*QueryNearbyAvailableVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := QueryNearbyAvailableVoucherResponse{}

	appErr := s.handler.QueryNearbyAvailableVouchers(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetPromotionDraft(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetPromotionDraftRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetPromotionDraftResponse{}

	appErr := s.handler.GetPromotionDraft(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UsePromotion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UsePromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UsePromotionResponse{}

	appErr := s.handler.UsePromotion(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CallbackCancelVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CallbackCancelVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CallbackCancelVoucherResponse{}

	appErr := s.handler.CallbackCancelVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) PromotionPreview(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*PromotionPreviewRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := PromotionPreviewResponse{}

	appErr := s.handler.PromotionPreview(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CommitPromotionPreview(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CommitPromotionPreviewRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CommitPromotionPreviewResponse{}

	appErr := s.handler.CommitPromotionPreview(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CancelPromotionPreview(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CancelPromotionPreviewRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CancelPromotionPreviewResponse{}

	appErr := s.handler.CancelPromotionPreview(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) Refund(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*RefundRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := RefundResponse{}

	appErr := s.handler.Refund(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) PartRefund(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*PartRefundRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := PartRefundResponse{}

	appErr := s.handler.PartRefund(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetStorePromotionBrief(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetStorePromotionBriefRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetStorePromotionBriefResponse{}

	appErr := s.handler.MGetStorePromotionBrief(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetFlashSaleItemStock(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetFlashSaleItemStockRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetFlashSaleItemStockResponse{}

	appErr := s.handler.MGetFlashSaleItemStock(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetFlashSaleItemDiscount(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetFlashSaleItemDiscountRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetFlashSaleItemDiscountResponse{}

	appErr := s.handler.MGetFlashSaleItemDiscount(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MCheckFlashSaleItemDiscountPrice(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MCheckFlashSaleItemDiscountPriceRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MCheckFlashSaleItemDiscountPriceResponse{}

	appErr := s.handler.MCheckFlashSaleItemDiscountPrice(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetFlashSaleItem(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetFlashSaleItemRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetFlashSaleItemResponse{}

	appErr := s.handler.MGetFlashSaleItem(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MCheckItemPrice(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MCheckItemPriceRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MCheckItemPriceResponse{}

	appErr := s.handler.MCheckItemPrice(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetRecentlyModified(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetRecentlyModifiedRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetRecentlyModifiedResponse{}

	appErr := s.handler.GetRecentlyModified(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) IsDisplayRedDot(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*IsDisplayRedDotRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := IsDisplayRedDotResponse{}

	appErr := s.handler.IsDisplayRedDot(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CheckPromotionLimit(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CheckPromotionLimitRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CheckPromotionLimitResponse{}

	appErr := s.handler.CheckPromotionLimit(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetPromotionList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetPromotionListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetPromotionListResponse{}

	appErr := s.handler.GetPromotionList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SyncPromotionList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SyncPromotionListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SyncPromotionListResponse{}

	appErr := s.handler.SyncPromotionList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetPromotionDetail(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetPromotionDetailRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetPromotionDetailResponse{}

	appErr := s.handler.GetPromotionDetail(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetPromotionToolList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetPromotionToolListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetPromotionToolListResponse{}

	appErr := s.handler.GetPromotionToolList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetShopeeFoodVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetShopeeFoodVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetShopeeFoodVoucherResponse{}

	appErr := s.handler.MGetShopeeFoodVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CheckShopeeFoodVoucherUserClaimStatusRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CheckShopeeFoodVoucherUserClaimStatusResponse{}

	appErr := s.handler.CheckShopeeFoodVoucherUserClaimStatus(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateVoucherResponse{}

	appErr := s.handler.CreateVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateVoucherResponse{}

	appErr := s.handler.UpdateVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherResponse{}

	appErr := s.handler.GetVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SearchVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchVoucherResponse{}

	appErr := s.handler.SearchVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateVoucherDispatchTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateVoucherDispatchTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateVoucherDispatchTaskResponse{}

	appErr := s.handler.CreateVoucherDispatchTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherDispatchTaskList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherDispatchTaskListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherDispatchTaskListResponse{}

	appErr := s.handler.GetVoucherDispatchTaskList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateVoucherCodesTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateVoucherCodesTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateVoucherCodesTaskResponse{}

	appErr := s.handler.CreateVoucherCodesTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateVoucherCodesTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateVoucherCodesTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateVoucherCodesTaskResponse{}

	appErr := s.handler.UpdateVoucherCodesTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateVoucherTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateVoucherTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateVoucherTemplateResponse{}

	appErr := s.handler.CreateVoucherTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateVoucherTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateVoucherTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateVoucherTemplateResponse{}

	appErr := s.handler.UpdateVoucherTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherTemplateResponse{}

	appErr := s.handler.GetVoucherTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SearchVoucherTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchVoucherTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchVoucherTemplateResponse{}

	appErr := s.handler.SearchVoucherTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetTemplateVouchers(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetTemplateVouchersRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetTemplateVouchersResponse{}

	appErr := s.handler.GetTemplateVouchers(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateVoucherTemplateUpdateTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateVoucherTemplateUpdateTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateVoucherTemplateUpdateTaskResponse{}

	appErr := s.handler.CreateVoucherTemplateUpdateTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherTemplateUpdateTaskList(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherTemplateUpdateTaskListRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherTemplateUpdateTaskListResponse{}

	appErr := s.handler.GetVoucherTemplateUpdateTaskList(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CancelVoucherTemplateUpdateTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CancelVoucherTemplateUpdateTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CancelVoucherTemplateUpdateTaskResponse{}

	appErr := s.handler.CancelVoucherTemplateUpdateTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetSpxOrderPromotionDetail(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetSpxOrderPromotionDetailRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetSpxOrderPromotionDetailResponse{}

	appErr := s.handler.GetSpxOrderPromotionDetail(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ClaimVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ClaimVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ClaimVoucherResponse{}

	appErr := s.handler.ClaimVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ListApplicableVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ListApplicableVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ListApplicableVoucherResponse{}

	appErr := s.handler.ListApplicableVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SPXGetPromotionDraft(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SPXGetPromotionDraftRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SPXGetPromotionDraftResponse{}

	appErr := s.handler.SPXGetPromotionDraft(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SPXUsePromotion(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SPXUsePromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SPXUsePromotionResponse{}

	appErr := s.handler.SPXUsePromotion(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SPXRefund(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SPXRefundPromotionRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SPXRefundPromotionResponse{}

	appErr := s.handler.SPXRefund(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherStacking(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherStackingRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherStackingResponse{}

	appErr := s.handler.GetVoucherStacking(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetVoucherBatch(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetVoucherBatchRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetVoucherBatchResponse{}

	appErr := s.handler.GetVoucherBatch(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SPXCallbackCancelVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CallbackCancelUseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CallbackCancelUseResponse{}

	appErr := s.handler.SPXCallbackCancelVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SPXCallbackCancelCoin(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CallbackCancelUseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CallbackCancelUseResponse{}

	appErr := s.handler.SPXCallbackCancelCoin(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) BatchDistributeVoucher(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*BatchDistributeVoucherRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := BatchDistributeVoucherResponse{}

	appErr := s.handler.BatchDistributeVoucher(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*BatchGetVoucherFromSPXVoucherWalletRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := BatchGetVoucherFromSPXVoucherWalletResponse{}

	appErr := s.handler.BatchGetVoucherFromSPXVoucherWallet(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ClaimVoucherFromSPXVoucherWallet(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ClaimVoucherFromSPXVoucherWalletRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ClaimVoucherFromSPXVoucherWalletResponse{}

	appErr := s.handler.ClaimVoucherFromSPXVoucherWallet(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetCoFundCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetCoFundCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetCoFundCampaignResponse{}

	appErr := s.handler.GetCoFundCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) DeleteCoFundCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*DeleteCoFundCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := DeleteCoFundCampaignResponse{}

	appErr := s.handler.DeleteCoFundCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateCoFundCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateCoFundCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateCoFundCampaignResponse{}

	appErr := s.handler.CreateCoFundCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateCoFundCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateCoFundCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateCoFundCampaignResponse{}

	appErr := s.handler.UpdateCoFundCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SearchCoFundCampaign(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchCoFundCampaignRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchCoFundCampaignResponse{}

	appErr := s.handler.SearchCoFundCampaign(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateFileUploadAsyncTask(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateFileUploadAsyncTaskRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateFileUploadAsyncTaskResponse{}

	appErr := s.handler.CreateFileUploadAsyncTask(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetFileUploadAsyncTasks(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetFileUploadAsyncTasksRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetFileUploadAsyncTasksResponse{}

	appErr := s.handler.GetFileUploadAsyncTasks(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetPromotionAvailablePaymentMethod(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetPromotionAvailablePaymentMethodRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetPromotionAvailablePaymentMethodResponse{}

	appErr := s.handler.GetPromotionAvailablePaymentMethod(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetMicrositeTargetVoucherComponentTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetMicrositeTargetVoucherComponentTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetMicrositeTargetVoucherComponentTemplateResponse{}

	appErr := s.handler.GetMicrositeTargetVoucherComponentTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) ValidateMicrositeTargetVoucherComponentTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*ValidateMicrositeTargetVoucherComponentTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := ValidateMicrositeTargetVoucherComponentTemplateResponse{}

	appErr := s.handler.ValidateMicrositeTargetVoucherComponentTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) TransformMicrositeTargetVoucherComponentTemplate(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*TransformMicrositeTargetVoucherComponentTemplateRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := TransformMicrositeTargetVoucherComponentTemplateResponse{}

	appErr := s.handler.TransformMicrositeTargetVoucherComponentTemplate(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) CreateMerchandise(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*CreateMerchandiseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := CreateMerchandiseResponse{}

	appErr := s.handler.CreateMerchandise(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) GetMerchandise(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*GetMerchandiseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := GetMerchandiseResponse{}

	appErr := s.handler.GetMerchandise(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) SearchMerchandises(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*SearchMerchandisesRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := SearchMerchandisesResponse{}

	appErr := s.handler.SearchMerchandises(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) UpdateMerchandiseStatus(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*UpdateMerchandiseStatusRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := UpdateMerchandiseStatusResponse{}

	appErr := s.handler.UpdateMerchandiseStatus(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) MGetUserMerchandise(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*MGetUserMerchandiseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := MGetUserMerchandiseResponse{}

	appErr := s.handler.MGetUserMerchandise(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) QueryAvailableMerchandise(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*QueryAvailableMerchandiseRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := QueryAvailableMerchandiseResponse{}

	appErr := s.handler.QueryAvailableMerchandise(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) DispatchMerchandiseBenefits(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*DispatchMerchandiseBenefitsRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := DispatchMerchandiseBenefitsResponse{}

	appErr := s.handler.DispatchMerchandiseBenefits(ctx, req, &resp)

	return &resp, appErr
}

func (s *spexFoodyPromotionServiceImpl) VerifyBuyerQualifications(ctx context.Context, request interface{}) (interface{}, error) {
	req, ok := request.(*VerifyBuyerQualificationsRequest)
	if !ok {
		return nil, sp_errors.FromCode(uint32(sp_common.Constant_ERROR_PARAMS))
	}

	resp := VerifyBuyerQualificationsResponse{}

	appErr := s.handler.VerifyBuyerQualifications(ctx, req, &resp)

	return &resp, appErr
}
