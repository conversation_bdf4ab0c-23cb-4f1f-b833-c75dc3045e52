package foody_promotion

import (
	"context"

	"git.garena.com/shopee/platform/golang_splib/client"
	"git.garena.com/shopee/platform/golang_splib/client/callopt"
)

type Client interface {
	// ShopeeFood
	// admin
	CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, opts ...callopt.Option) (*CreateOrderDiscountCampaignResponse, error)

	UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, opts ...callopt.Option) (*UpdateOrderDiscountCampaignResponse, error)

	UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, opts ...callopt.Option) (*UpdateOrderDiscountCampaignStatusResponse, error)

	GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, opts ...callopt.Option) (*GetOrderDiscountCampaignResponse, error)

	SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, opts ...callopt.Option) (*SearchOrderDiscountCampaignResponse, error)

	CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, opts ...callopt.Option) (*CreateItemDiscountCampaignResponse, error)

	UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, opts ...callopt.Option) (*UpdateItemDiscountCampaignResponse, error)

	ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, opts ...callopt.Option) (*ItemDiscountCampaignDraftResponse, error)

	QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, opts ...callopt.Option) (*QueryItemDiscountCampaignListResponse, error)

	QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, opts ...callopt.Option) (*QueryItemDiscountCampaignResponse, error)

	MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, opts ...callopt.Option) (*MCreateItemDiscountCampaignResponse, error)

	MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, opts ...callopt.Option) (*MUpdateItemDiscountCampaignResponse, error)

	GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, opts ...callopt.Option) (*GetOrderPromotionDetailResponse, error)

	GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, opts ...callopt.Option) (*GetVoucherTypeListResponse, error)

	QueryAvailableItemDiscountList(ctx context.Context, in *QueryItemDiscountRequest, opts ...callopt.Option) (*QueryItemDiscountResponse, error)

	MDisableItemDiscountCampaign(ctx context.Context, in *MDisableItemDiscountCampaignRequest, opts ...callopt.Option) (*MDisableItemDiscountCampaignResponse, error)

	// buyer
	MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, opts ...callopt.Option) (*MGetStorePromotionResponse, error)

	GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, opts ...callopt.Option) (*GetStorePromotionResponse, error)

	// Deprecated
	MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, opts ...callopt.Option) (*MGetItemDiscountResponse, error)

	MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, opts ...callopt.Option) (*MGetItemDiscountV2Response, error)

	// Deprecated
	BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, opts ...callopt.Option) (*BatchMGetItemDiscountResponse, error)

	BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, opts ...callopt.Option) (*BatchMGetItemDiscountV2Response, error)

	// Deprecated
	MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, opts ...callopt.Option) (*MGetStoreVoucherResponse, error)

	MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, opts ...callopt.Option) (*GetVouchersByStoreBatchResponse, error)

	MGetStoreVoucherBrief(ctx context.Context, in *GetStoreVoucherBriefRequest, opts ...callopt.Option) (*GetStoreVoucherBriefResponse, error)

	GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, opts ...callopt.Option) (*RecommendFoodVouchersResponse, error)

	GetRecommendPromotion(ctx context.Context, in *GetRecommendPromotionRequest, opts ...callopt.Option) (*GetRecommendPromotionResponse, error)

	ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, opts ...callopt.Option) (*ClaimFoodVoucherResponse, error)

	GetTargetVoucher(ctx context.Context, in *GetTargetVoucherRequest, opts ...callopt.Option) (*GetTargetVoucherResponse, error)

	// 通过获取周边可用的券列表
	// Filtering logic
	//  Within voucher type configured in admin [this info is stored in Food side]
	//  Within claim period or within use period if claimed
	//  Within target user scope (e.g. new user)
	//  Not fully used by this user yet
	//  At least one store will be displayed in store list after click use (see section 2.3 on the criteria)
	// Ranking logic
	//  P1: can claim / can use vouchers > fully claimed vouchers [需要补充设计]
	//  P2: [if above is the same]  Rank by reward type: food vouchers > shipping fee vouchers > coins cashback vouchers
	//  P3: [if above is the same] Rank by discount amount: value > %, DESC (e.g. 50k > 30k > 90% > 80%)
	//  P4: [if above is the same] Rank by cap (if any): value, DESC(e.g. no cap > cap at 30k > cap at 20k)
	//  P5: [if above is the same]  Voucher ID (newly created voucher first)
	QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, opts ...callopt.Option) (*QueryNearbyAvailableVoucherResponse, error)

	// order
	// buyer place order
	GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, opts ...callopt.Option) (*GetPromotionDraftResponse, error)

	UsePromotion(ctx context.Context, in *UsePromotionRequest, opts ...callopt.Option) (*UsePromotionResponse, error)

	CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, opts ...callopt.Option) (*CallbackCancelVoucherResponse, error)

	// driver modify order
	PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, opts ...callopt.Option) (*PromotionPreviewResponse, error)

	CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, opts ...callopt.Option) (*CommitPromotionPreviewResponse, error)

	CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, opts ...callopt.Option) (*CancelPromotionPreviewResponse, error)

	Refund(ctx context.Context, in *RefundRequest, opts ...callopt.Option) (*RefundResponse, error)

	PartRefund(ctx context.Context, in *PartRefundRequest, opts ...callopt.Option) (*PartRefundResponse, error)

	// algo team
	MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, opts ...callopt.Option) (*MGetStorePromotionBriefResponse, error)

	// flash sale
	// 获取库存信息
	MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, opts ...callopt.Option) (*MGetFlashSaleItemStockResponse, error)

	// 获取flash sale 菜品详情
	MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, opts ...callopt.Option) (*MGetFlashSaleItemDiscountResponse, error)

	// 允许&商家修改dish价格时，校验flash sale 菜品价格
	MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, opts ...callopt.Option) (*MCheckFlashSaleItemDiscountPriceResponse, error)

	MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, opts ...callopt.Option) (*MGetFlashSaleItemResponse, error)

	MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, opts ...callopt.Option) (*MCheckItemPriceResponse, error)

	GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, opts ...callopt.Option) (*GetRecentlyModifiedResponse, error)

	IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, opts ...callopt.Option) (*IsDisplayRedDotResponse, error)

	CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, opts ...callopt.Option) (*CheckPromotionLimitResponse, error)

	// 实时拉取混排的 promotion 列表
	GetPromotionList(ctx context.Context, in *GetPromotionListRequest, opts ...callopt.Option) (*GetPromotionListResponse, error)

	// 同步拉取指定类型的 promotion 数据
	SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, opts ...callopt.Option) (*SyncPromotionListResponse, error)

	// 获取指定类型的 promotion 详情
	GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, opts ...callopt.Option) (*GetPromotionDetailResponse, error)

	// 获取 promotion tool 列表
	GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, opts ...callopt.Option) (*GetPromotionToolListResponse, error)

	MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, opts ...callopt.Option) (*MGetShopeeFoodVoucherResponse, error)

	CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, opts ...callopt.Option) (*CheckShopeeFoodVoucherUserClaimStatusResponse, error)

	// admin
	CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...callopt.Option) (*CreateVoucherResponse, error)

	UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...callopt.Option) (*UpdateVoucherResponse, error)

	GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...callopt.Option) (*GetVoucherResponse, error)

	SearchVoucher(ctx context.Context, in *SearchVoucherRequest, opts ...callopt.Option) (*SearchVoucherResponse, error)

	CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, opts ...callopt.Option) (*CreateVoucherDispatchTaskResponse, error)

	GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, opts ...callopt.Option) (*GetVoucherDispatchTaskListResponse, error)

	CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, opts ...callopt.Option) (*CreateVoucherCodesTaskResponse, error)

	UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, opts ...callopt.Option) (*UpdateVoucherCodesTaskResponse, error)

	CreateVoucherTemplate(ctx context.Context, in *CreateVoucherTemplateRequest, opts ...callopt.Option) (*CreateVoucherTemplateResponse, error)

	UpdateVoucherTemplate(ctx context.Context, in *UpdateVoucherTemplateRequest, opts ...callopt.Option) (*UpdateVoucherTemplateResponse, error)

	GetVoucherTemplate(ctx context.Context, in *GetVoucherTemplateRequest, opts ...callopt.Option) (*GetVoucherTemplateResponse, error)

	SearchVoucherTemplate(ctx context.Context, in *SearchVoucherTemplateRequest, opts ...callopt.Option) (*SearchVoucherTemplateResponse, error)

	GetTemplateVouchers(ctx context.Context, in *GetTemplateVouchersRequest, opts ...callopt.Option) (*GetTemplateVouchersResponse, error)

	CreateVoucherTemplateUpdateTask(ctx context.Context, in *CreateVoucherTemplateUpdateTaskRequest, opts ...callopt.Option) (*CreateVoucherTemplateUpdateTaskResponse, error)

	GetVoucherTemplateUpdateTaskList(ctx context.Context, in *GetVoucherTemplateUpdateTaskListRequest, opts ...callopt.Option) (*GetVoucherTemplateUpdateTaskListResponse, error)

	CancelVoucherTemplateUpdateTask(ctx context.Context, in *CancelVoucherTemplateUpdateTaskRequest, opts ...callopt.Option) (*CancelVoucherTemplateUpdateTaskResponse, error)

	GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, opts ...callopt.Option) (*GetSpxOrderPromotionDetailResponse, error)

	// buyer
	ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, opts ...callopt.Option) (*ClaimVoucherResponse, error)

	ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, opts ...callopt.Option) (*ListApplicableVoucherResponse, error)

	SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, opts ...callopt.Option) (*SPXGetPromotionDraftResponse, error)

	SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, opts ...callopt.Option) (*SPXUsePromotionResponse, error)

	SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, opts ...callopt.Option) (*SPXRefundPromotionResponse, error)

	GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, opts ...callopt.Option) (*GetVoucherStackingResponse, error)

	GetVoucherBatch(ctx context.Context, in *GetVoucherBatchRequest, opts ...callopt.Option) (*GetVoucherBatchResponse, error)

	// gateway
	SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, opts ...callopt.Option) (*CallbackCancelUseResponse, error)

	SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, opts ...callopt.Option) (*CallbackCancelUseResponse, error)

	BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, opts ...callopt.Option) (*BatchDistributeVoucherResponse, error)

	BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, opts ...callopt.Option) (*BatchGetVoucherFromSPXVoucherWalletResponse, error)

	ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, opts ...callopt.Option) (*ClaimVoucherFromSPXVoucherWalletResponse, error)

	// co-found
	GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, opts ...callopt.Option) (*GetCoFundCampaignResponse, error)

	DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, opts ...callopt.Option) (*DeleteCoFundCampaignResponse, error)

	CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, opts ...callopt.Option) (*CreateCoFundCampaignResponse, error)

	UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, opts ...callopt.Option) (*UpdateCoFundCampaignResponse, error)

	SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, opts ...callopt.Option) (*SearchCoFundCampaignResponse, error)

	// file upload async task
	CreateFileUploadAsyncTask(ctx context.Context, in *CreateFileUploadAsyncTaskRequest, opts ...callopt.Option) (*CreateFileUploadAsyncTaskResponse, error)

	GetFileUploadAsyncTasks(ctx context.Context, in *GetFileUploadAsyncTasksRequest, opts ...callopt.Option) (*GetFileUploadAsyncTasksResponse, error)

	GetPromotionAvailablePaymentMethod(ctx context.Context, in *GetPromotionAvailablePaymentMethodRequest, opts ...callopt.Option) (*GetPromotionAvailablePaymentMethodResponse, error)

	// microsite
	GetMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *GetMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*GetMicrositeTargetVoucherComponentTemplateResponse, error)

	ValidateMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *ValidateMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*ValidateMicrositeTargetVoucherComponentTemplateResponse, error)

	TransformMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *TransformMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*TransformMicrositeTargetVoucherComponentTemplateResponse, error)

	// package
	CreateMerchandise(ctx context.Context, in *CreateMerchandiseRequest, opts ...callopt.Option) (*CreateMerchandiseResponse, error)

	GetMerchandise(ctx context.Context, in *GetMerchandiseRequest, opts ...callopt.Option) (*GetMerchandiseResponse, error)

	SearchMerchandises(ctx context.Context, in *SearchMerchandisesRequest, opts ...callopt.Option) (*SearchMerchandisesResponse, error)

	UpdateMerchandiseStatus(ctx context.Context, in *UpdateMerchandiseStatusRequest, opts ...callopt.Option) (*UpdateMerchandiseStatusResponse, error)

	// buyer
	MGetUserMerchandise(ctx context.Context, in *MGetUserMerchandiseRequest, opts ...callopt.Option) (*MGetUserMerchandiseResponse, error)

	QueryAvailableMerchandise(ctx context.Context, in *QueryAvailableMerchandiseRequest, opts ...callopt.Option) (*QueryAvailableMerchandiseResponse, error)

	DispatchMerchandiseBenefits(ctx context.Context, in *DispatchMerchandiseBenefitsRequest, opts ...callopt.Option) (*DispatchMerchandiseBenefitsResponse, error)

	VerifyBuyerQualifications(ctx context.Context, in *VerifyBuyerQualificationsRequest, opts ...callopt.Option) (*VerifyBuyerQualificationsResponse, error)
}

func NewSpexClient(opts ...client.Option) (Client, error) {
	namespace := "shopeefood.promotion.promotion"

	spexClient, err := client.NewClient(append([]client.Option{
		client.WithInterceptor(ClientVersionReport(version, spexServiceName)),
	}, opts...)...)
	if err != nil {
		return nil, err
	}

	cli := &foodyPromotionClient{
		c:         spexClient,
		namespace: namespace,
	}

	return cli, nil
}

type foodyPromotionClient struct {
	c         client.Client
	namespace string
}

func (client *foodyPromotionClient) CreateOrderDiscountCampaign(ctx context.Context, in *CreateOrderDiscountCampaignRequest, opts ...callopt.Option) (*CreateOrderDiscountCampaignResponse, error) {
	out := new(CreateOrderDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_order_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateOrderDiscountCampaign(ctx context.Context, in *UpdateOrderDiscountCampaignRequest, opts ...callopt.Option) (*UpdateOrderDiscountCampaignResponse, error) {
	out := new(UpdateOrderDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_order_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateOrderDiscountCampaignStatus(ctx context.Context, in *UpdateOrderDiscountCampaignStatusRequest, opts ...callopt.Option) (*UpdateOrderDiscountCampaignStatusResponse, error) {
	out := new(UpdateOrderDiscountCampaignStatusResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_order_discount_campaign_status", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetOrderDiscountCampaign(ctx context.Context, in *GetOrderDiscountCampaignRequest, opts ...callopt.Option) (*GetOrderDiscountCampaignResponse, error) {
	out := new(GetOrderDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_order_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SearchOrderDiscountCampaign(ctx context.Context, in *SearchOrderDiscountCampaignRequest, opts ...callopt.Option) (*SearchOrderDiscountCampaignResponse, error) {
	out := new(SearchOrderDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_order_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateItemDiscountCampaign(ctx context.Context, in *CreateItemDiscountCampaignRequest, opts ...callopt.Option) (*CreateItemDiscountCampaignResponse, error) {
	out := new(CreateItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateItemDiscountCampaign(ctx context.Context, in *UpdateItemDiscountCampaignRequest, opts ...callopt.Option) (*UpdateItemDiscountCampaignResponse, error) {
	out := new(UpdateItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ItemDiscountCampaignDraft(ctx context.Context, in *ItemDiscountCampaignDraftRequest, opts ...callopt.Option) (*ItemDiscountCampaignDraftResponse, error) {
	out := new(ItemDiscountCampaignDraftResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"item_discount_campaign_draft", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) QueryItemDiscountCampaignList(ctx context.Context, in *QueryItemDiscountCampaignListRequest, opts ...callopt.Option) (*QueryItemDiscountCampaignListResponse, error) {
	out := new(QueryItemDiscountCampaignListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"query_item_discount_campaign_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) QueryItemDiscountCampaign(ctx context.Context, in *QueryItemDiscountCampaignRequest, opts ...callopt.Option) (*QueryItemDiscountCampaignResponse, error) {
	out := new(QueryItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"query_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MCreateItemDiscountCampaign(ctx context.Context, in *MCreateItemDiscountCampaignRequest, opts ...callopt.Option) (*MCreateItemDiscountCampaignResponse, error) {
	out := new(MCreateItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_create_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MUpdateItemDiscountCampaign(ctx context.Context, in *MUpdateItemDiscountCampaignRequest, opts ...callopt.Option) (*MUpdateItemDiscountCampaignResponse, error) {
	out := new(MUpdateItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_update_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetOrderPromotionDetail(ctx context.Context, in *GetOrderPromotionDetailRequest, opts ...callopt.Option) (*GetOrderPromotionDetailResponse, error) {
	out := new(GetOrderPromotionDetailResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_order_promotion_detail", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherTypeList(ctx context.Context, in *GetVoucherTypeListRequest, opts ...callopt.Option) (*GetVoucherTypeListResponse, error) {
	out := new(GetVoucherTypeListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_type_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) QueryAvailableItemDiscountList(ctx context.Context, in *QueryItemDiscountRequest, opts ...callopt.Option) (*QueryItemDiscountResponse, error) {
	out := new(QueryItemDiscountResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"query_available_item_discount_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MDisableItemDiscountCampaign(ctx context.Context, in *MDisableItemDiscountCampaignRequest, opts ...callopt.Option) (*MDisableItemDiscountCampaignResponse, error) {
	out := new(MDisableItemDiscountCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_disable_item_discount_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetStorePromotion(ctx context.Context, in *MGetStorePromotionRequest, opts ...callopt.Option) (*MGetStorePromotionResponse, error) {
	out := new(MGetStorePromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_promotion", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetStorePromotion(ctx context.Context, in *GetStorePromotionRequest, opts ...callopt.Option) (*GetStorePromotionResponse, error) {
	out := new(GetStorePromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_store_promotion", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetItemDiscount(ctx context.Context, in *MGetItemDiscountRequest, opts ...callopt.Option) (*MGetItemDiscountResponse, error) {
	out := new(MGetItemDiscountResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_item_discount", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetItemDiscountV2(ctx context.Context, in *MGetItemDiscountV2Request, opts ...callopt.Option) (*MGetItemDiscountV2Response, error) {
	out := new(MGetItemDiscountV2Response)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_item_discount_v2", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) BatchMGetItemDiscount(ctx context.Context, in *BatchMGetItemDiscountRequest, opts ...callopt.Option) (*BatchMGetItemDiscountResponse, error) {
	out := new(BatchMGetItemDiscountResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"batch_m_get_item_discount", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) BatchMGetItemDiscountV2(ctx context.Context, in *BatchMGetItemDiscountV2Request, opts ...callopt.Option) (*BatchMGetItemDiscountV2Response, error) {
	out := new(BatchMGetItemDiscountV2Response)

	err := client.c.Invoke(ctx, client.namespace+"."+"batch_m_get_item_discount_v2", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetStoreVoucher(ctx context.Context, in *MGetStoreVoucherRequest, opts ...callopt.Option) (*MGetStoreVoucherResponse, error) {
	out := new(MGetStoreVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetStoreVoucherV2(ctx context.Context, in *GetVouchersByStoreBatchRequest, opts ...callopt.Option) (*GetVouchersByStoreBatchResponse, error) {
	out := new(GetVouchersByStoreBatchResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_voucher_v2", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetStoreVoucherBrief(ctx context.Context, in *GetStoreVoucherBriefRequest, opts ...callopt.Option) (*GetStoreVoucherBriefResponse, error) {
	out := new(GetStoreVoucherBriefResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_voucher_brief", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetRecommendVoucher(ctx context.Context, in *RecommendVouchersRequest, opts ...callopt.Option) (*RecommendFoodVouchersResponse, error) {
	out := new(RecommendFoodVouchersResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_recommend_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetRecommendPromotion(ctx context.Context, in *GetRecommendPromotionRequest, opts ...callopt.Option) (*GetRecommendPromotionResponse, error) {
	out := new(GetRecommendPromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_recommend_promotion", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ClaimFoodVoucher(ctx context.Context, in *ClaimFoodVoucherRequest, opts ...callopt.Option) (*ClaimFoodVoucherResponse, error) {
	out := new(ClaimFoodVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"claim_food_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetTargetVoucher(ctx context.Context, in *GetTargetVoucherRequest, opts ...callopt.Option) (*GetTargetVoucherResponse, error) {
	out := new(GetTargetVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_target_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) QueryNearbyAvailableVouchers(ctx context.Context, in *QueryNearbyAvailableVoucherRequest, opts ...callopt.Option) (*QueryNearbyAvailableVoucherResponse, error) {
	out := new(QueryNearbyAvailableVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"query_nearby_available_vouchers", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetPromotionDraft(ctx context.Context, in *GetPromotionDraftRequest, opts ...callopt.Option) (*GetPromotionDraftResponse, error) {
	out := new(GetPromotionDraftResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_promotion_draft", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UsePromotion(ctx context.Context, in *UsePromotionRequest, opts ...callopt.Option) (*UsePromotionResponse, error) {
	out := new(UsePromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"use_promotion", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CallbackCancelVoucher(ctx context.Context, in *CallbackCancelVoucherRequest, opts ...callopt.Option) (*CallbackCancelVoucherResponse, error) {
	out := new(CallbackCancelVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"callback_cancel_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) PromotionPreview(ctx context.Context, in *PromotionPreviewRequest, opts ...callopt.Option) (*PromotionPreviewResponse, error) {
	out := new(PromotionPreviewResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"promotion_preview", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CommitPromotionPreview(ctx context.Context, in *CommitPromotionPreviewRequest, opts ...callopt.Option) (*CommitPromotionPreviewResponse, error) {
	out := new(CommitPromotionPreviewResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"commit_promotion_preview", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CancelPromotionPreview(ctx context.Context, in *CancelPromotionPreviewRequest, opts ...callopt.Option) (*CancelPromotionPreviewResponse, error) {
	out := new(CancelPromotionPreviewResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"cancel_promotion_preview", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) Refund(ctx context.Context, in *RefundRequest, opts ...callopt.Option) (*RefundResponse, error) {
	out := new(RefundResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"refund", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) PartRefund(ctx context.Context, in *PartRefundRequest, opts ...callopt.Option) (*PartRefundResponse, error) {
	out := new(PartRefundResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"part_refund", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetStorePromotionBrief(ctx context.Context, in *MGetStorePromotionBriefRequest, opts ...callopt.Option) (*MGetStorePromotionBriefResponse, error) {
	out := new(MGetStorePromotionBriefResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_store_promotion_brief", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetFlashSaleItemStock(ctx context.Context, in *MGetFlashSaleItemStockRequest, opts ...callopt.Option) (*MGetFlashSaleItemStockResponse, error) {
	out := new(MGetFlashSaleItemStockResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_flash_sale_item_stock", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetFlashSaleItemDiscount(ctx context.Context, in *MGetFlashSaleItemDiscountRequest, opts ...callopt.Option) (*MGetFlashSaleItemDiscountResponse, error) {
	out := new(MGetFlashSaleItemDiscountResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_flash_sale_item_discount", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MCheckFlashSaleItemDiscountPrice(ctx context.Context, in *MCheckFlashSaleItemDiscountPriceRequest, opts ...callopt.Option) (*MCheckFlashSaleItemDiscountPriceResponse, error) {
	out := new(MCheckFlashSaleItemDiscountPriceResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_check_flash_sale_item_discount_price", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetFlashSaleItem(ctx context.Context, in *MGetFlashSaleItemRequest, opts ...callopt.Option) (*MGetFlashSaleItemResponse, error) {
	out := new(MGetFlashSaleItemResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_flash_sale_item", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MCheckItemPrice(ctx context.Context, in *MCheckItemPriceRequest, opts ...callopt.Option) (*MCheckItemPriceResponse, error) {
	out := new(MCheckItemPriceResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_check_item_price", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetRecentlyModified(ctx context.Context, in *GetRecentlyModifiedRequest, opts ...callopt.Option) (*GetRecentlyModifiedResponse, error) {
	out := new(GetRecentlyModifiedResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_recently_modified", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) IsDisplayRedDot(ctx context.Context, in *IsDisplayRedDotRequest, opts ...callopt.Option) (*IsDisplayRedDotResponse, error) {
	out := new(IsDisplayRedDotResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"is_display_red_dot", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CheckPromotionLimit(ctx context.Context, in *CheckPromotionLimitRequest, opts ...callopt.Option) (*CheckPromotionLimitResponse, error) {
	out := new(CheckPromotionLimitResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"check_promotion_limit", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetPromotionList(ctx context.Context, in *GetPromotionListRequest, opts ...callopt.Option) (*GetPromotionListResponse, error) {
	out := new(GetPromotionListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_promotion_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SyncPromotionList(ctx context.Context, in *SyncPromotionListRequest, opts ...callopt.Option) (*SyncPromotionListResponse, error) {
	out := new(SyncPromotionListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"sync_promotion_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetPromotionDetail(ctx context.Context, in *GetPromotionDetailRequest, opts ...callopt.Option) (*GetPromotionDetailResponse, error) {
	out := new(GetPromotionDetailResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_promotion_detail", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetPromotionToolList(ctx context.Context, in *GetPromotionToolListRequest, opts ...callopt.Option) (*GetPromotionToolListResponse, error) {
	out := new(GetPromotionToolListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_promotion_tool_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetShopeeFoodVoucher(ctx context.Context, in *MGetShopeeFoodVoucherRequest, opts ...callopt.Option) (*MGetShopeeFoodVoucherResponse, error) {
	out := new(MGetShopeeFoodVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_shopee_food_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CheckShopeeFoodVoucherUserClaimStatus(ctx context.Context, in *CheckShopeeFoodVoucherUserClaimStatusRequest, opts ...callopt.Option) (*CheckShopeeFoodVoucherUserClaimStatusResponse, error) {
	out := new(CheckShopeeFoodVoucherUserClaimStatusResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"check_shopee_food_voucher_user_claim_status", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateVoucher(ctx context.Context, in *CreateVoucherRequest, opts ...callopt.Option) (*CreateVoucherResponse, error) {
	out := new(CreateVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateVoucher(ctx context.Context, in *UpdateVoucherRequest, opts ...callopt.Option) (*UpdateVoucherResponse, error) {
	out := new(UpdateVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucher(ctx context.Context, in *GetVoucherRequest, opts ...callopt.Option) (*GetVoucherResponse, error) {
	out := new(GetVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SearchVoucher(ctx context.Context, in *SearchVoucherRequest, opts ...callopt.Option) (*SearchVoucherResponse, error) {
	out := new(SearchVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateVoucherDispatchTask(ctx context.Context, in *CreateVoucherDispatchTaskRequest, opts ...callopt.Option) (*CreateVoucherDispatchTaskResponse, error) {
	out := new(CreateVoucherDispatchTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_voucher_dispatch_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherDispatchTaskList(ctx context.Context, in *GetVoucherDispatchTaskListRequest, opts ...callopt.Option) (*GetVoucherDispatchTaskListResponse, error) {
	out := new(GetVoucherDispatchTaskListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_dispatch_task_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateVoucherCodesTask(ctx context.Context, in *CreateVoucherCodesTaskRequest, opts ...callopt.Option) (*CreateVoucherCodesTaskResponse, error) {
	out := new(CreateVoucherCodesTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_voucher_codes_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateVoucherCodesTask(ctx context.Context, in *UpdateVoucherCodesTaskRequest, opts ...callopt.Option) (*UpdateVoucherCodesTaskResponse, error) {
	out := new(UpdateVoucherCodesTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_voucher_codes_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateVoucherTemplate(ctx context.Context, in *CreateVoucherTemplateRequest, opts ...callopt.Option) (*CreateVoucherTemplateResponse, error) {
	out := new(CreateVoucherTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_voucher_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateVoucherTemplate(ctx context.Context, in *UpdateVoucherTemplateRequest, opts ...callopt.Option) (*UpdateVoucherTemplateResponse, error) {
	out := new(UpdateVoucherTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_voucher_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherTemplate(ctx context.Context, in *GetVoucherTemplateRequest, opts ...callopt.Option) (*GetVoucherTemplateResponse, error) {
	out := new(GetVoucherTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SearchVoucherTemplate(ctx context.Context, in *SearchVoucherTemplateRequest, opts ...callopt.Option) (*SearchVoucherTemplateResponse, error) {
	out := new(SearchVoucherTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_voucher_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetTemplateVouchers(ctx context.Context, in *GetTemplateVouchersRequest, opts ...callopt.Option) (*GetTemplateVouchersResponse, error) {
	out := new(GetTemplateVouchersResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_template_vouchers", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateVoucherTemplateUpdateTask(ctx context.Context, in *CreateVoucherTemplateUpdateTaskRequest, opts ...callopt.Option) (*CreateVoucherTemplateUpdateTaskResponse, error) {
	out := new(CreateVoucherTemplateUpdateTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_voucher_template_update_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherTemplateUpdateTaskList(ctx context.Context, in *GetVoucherTemplateUpdateTaskListRequest, opts ...callopt.Option) (*GetVoucherTemplateUpdateTaskListResponse, error) {
	out := new(GetVoucherTemplateUpdateTaskListResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_template_update_task_list", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CancelVoucherTemplateUpdateTask(ctx context.Context, in *CancelVoucherTemplateUpdateTaskRequest, opts ...callopt.Option) (*CancelVoucherTemplateUpdateTaskResponse, error) {
	out := new(CancelVoucherTemplateUpdateTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"cancel_voucher_template_update_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetSpxOrderPromotionDetail(ctx context.Context, in *GetSpxOrderPromotionDetailRequest, opts ...callopt.Option) (*GetSpxOrderPromotionDetailResponse, error) {
	out := new(GetSpxOrderPromotionDetailResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_spx_order_promotion_detail", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ClaimVoucher(ctx context.Context, in *ClaimVoucherRequest, opts ...callopt.Option) (*ClaimVoucherResponse, error) {
	out := new(ClaimVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"claim_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ListApplicableVoucher(ctx context.Context, in *ListApplicableVoucherRequest, opts ...callopt.Option) (*ListApplicableVoucherResponse, error) {
	out := new(ListApplicableVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"list_applicable_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SPXGetPromotionDraft(ctx context.Context, in *SPXGetPromotionDraftRequest, opts ...callopt.Option) (*SPXGetPromotionDraftResponse, error) {
	out := new(SPXGetPromotionDraftResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"spx_get_promotion_draft", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SPXUsePromotion(ctx context.Context, in *SPXUsePromotionRequest, opts ...callopt.Option) (*SPXUsePromotionResponse, error) {
	out := new(SPXUsePromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"spx_use_promotion", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SPXRefund(ctx context.Context, in *SPXRefundPromotionRequest, opts ...callopt.Option) (*SPXRefundPromotionResponse, error) {
	out := new(SPXRefundPromotionResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"spx_refund", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherStacking(ctx context.Context, in *GetVoucherStackingRequest, opts ...callopt.Option) (*GetVoucherStackingResponse, error) {
	out := new(GetVoucherStackingResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_stacking", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetVoucherBatch(ctx context.Context, in *GetVoucherBatchRequest, opts ...callopt.Option) (*GetVoucherBatchResponse, error) {
	out := new(GetVoucherBatchResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_voucher_batch", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SPXCallbackCancelVoucher(ctx context.Context, in *CallbackCancelUseRequest, opts ...callopt.Option) (*CallbackCancelUseResponse, error) {
	out := new(CallbackCancelUseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"spx_callback_cancel_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SPXCallbackCancelCoin(ctx context.Context, in *CallbackCancelUseRequest, opts ...callopt.Option) (*CallbackCancelUseResponse, error) {
	out := new(CallbackCancelUseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"spx_callback_cancel_coin", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) BatchDistributeVoucher(ctx context.Context, in *BatchDistributeVoucherRequest, opts ...callopt.Option) (*BatchDistributeVoucherResponse, error) {
	out := new(BatchDistributeVoucherResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"batch_distribute_voucher", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) BatchGetVoucherFromSPXVoucherWallet(ctx context.Context, in *BatchGetVoucherFromSPXVoucherWalletRequest, opts ...callopt.Option) (*BatchGetVoucherFromSPXVoucherWalletResponse, error) {
	out := new(BatchGetVoucherFromSPXVoucherWalletResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"batch_get_voucher_from_spx_voucher_wallet", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ClaimVoucherFromSPXVoucherWallet(ctx context.Context, in *ClaimVoucherFromSPXVoucherWalletRequest, opts ...callopt.Option) (*ClaimVoucherFromSPXVoucherWalletResponse, error) {
	out := new(ClaimVoucherFromSPXVoucherWalletResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"claim_voucher_from_spx_voucher_wallet", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetCoFundCampaign(ctx context.Context, in *GetCoFundCampaignRequest, opts ...callopt.Option) (*GetCoFundCampaignResponse, error) {
	out := new(GetCoFundCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_co_fund_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) DeleteCoFundCampaign(ctx context.Context, in *DeleteCoFundCampaignRequest, opts ...callopt.Option) (*DeleteCoFundCampaignResponse, error) {
	out := new(DeleteCoFundCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"delete_co_fund_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateCoFundCampaign(ctx context.Context, in *CreateCoFundCampaignRequest, opts ...callopt.Option) (*CreateCoFundCampaignResponse, error) {
	out := new(CreateCoFundCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_co_fund_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateCoFundCampaign(ctx context.Context, in *UpdateCoFundCampaignRequest, opts ...callopt.Option) (*UpdateCoFundCampaignResponse, error) {
	out := new(UpdateCoFundCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_co_fund_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SearchCoFundCampaign(ctx context.Context, in *SearchCoFundCampaignRequest, opts ...callopt.Option) (*SearchCoFundCampaignResponse, error) {
	out := new(SearchCoFundCampaignResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_co_fund_campaign", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateFileUploadAsyncTask(ctx context.Context, in *CreateFileUploadAsyncTaskRequest, opts ...callopt.Option) (*CreateFileUploadAsyncTaskResponse, error) {
	out := new(CreateFileUploadAsyncTaskResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_file_upload_async_task", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetFileUploadAsyncTasks(ctx context.Context, in *GetFileUploadAsyncTasksRequest, opts ...callopt.Option) (*GetFileUploadAsyncTasksResponse, error) {
	out := new(GetFileUploadAsyncTasksResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_file_upload_async_tasks", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetPromotionAvailablePaymentMethod(ctx context.Context, in *GetPromotionAvailablePaymentMethodRequest, opts ...callopt.Option) (*GetPromotionAvailablePaymentMethodResponse, error) {
	out := new(GetPromotionAvailablePaymentMethodResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_promotion_available_payment_method", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *GetMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*GetMicrositeTargetVoucherComponentTemplateResponse, error) {
	out := new(GetMicrositeTargetVoucherComponentTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_microsite_target_voucher_component_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) ValidateMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *ValidateMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*ValidateMicrositeTargetVoucherComponentTemplateResponse, error) {
	out := new(ValidateMicrositeTargetVoucherComponentTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"validate_microsite_target_voucher_component_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) TransformMicrositeTargetVoucherComponentTemplate(ctx context.Context, in *TransformMicrositeTargetVoucherComponentTemplateRequest, opts ...callopt.Option) (*TransformMicrositeTargetVoucherComponentTemplateResponse, error) {
	out := new(TransformMicrositeTargetVoucherComponentTemplateResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"transform_microsite_target_voucher_component_template", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) CreateMerchandise(ctx context.Context, in *CreateMerchandiseRequest, opts ...callopt.Option) (*CreateMerchandiseResponse, error) {
	out := new(CreateMerchandiseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"create_merchandise", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) GetMerchandise(ctx context.Context, in *GetMerchandiseRequest, opts ...callopt.Option) (*GetMerchandiseResponse, error) {
	out := new(GetMerchandiseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"get_merchandise", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) SearchMerchandises(ctx context.Context, in *SearchMerchandisesRequest, opts ...callopt.Option) (*SearchMerchandisesResponse, error) {
	out := new(SearchMerchandisesResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"search_merchandises", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) UpdateMerchandiseStatus(ctx context.Context, in *UpdateMerchandiseStatusRequest, opts ...callopt.Option) (*UpdateMerchandiseStatusResponse, error) {
	out := new(UpdateMerchandiseStatusResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"update_merchandise_status", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) MGetUserMerchandise(ctx context.Context, in *MGetUserMerchandiseRequest, opts ...callopt.Option) (*MGetUserMerchandiseResponse, error) {
	out := new(MGetUserMerchandiseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"m_get_user_merchandise", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) QueryAvailableMerchandise(ctx context.Context, in *QueryAvailableMerchandiseRequest, opts ...callopt.Option) (*QueryAvailableMerchandiseResponse, error) {
	out := new(QueryAvailableMerchandiseResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"query_available_merchandise", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) DispatchMerchandiseBenefits(ctx context.Context, in *DispatchMerchandiseBenefitsRequest, opts ...callopt.Option) (*DispatchMerchandiseBenefitsResponse, error) {
	out := new(DispatchMerchandiseBenefitsResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"dispatch_merchandise_benefits", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func (client *foodyPromotionClient) VerifyBuyerQualifications(ctx context.Context, in *VerifyBuyerQualificationsRequest, opts ...callopt.Option) (*VerifyBuyerQualificationsResponse, error) {
	out := new(VerifyBuyerQualificationsResponse)

	err := client.c.Invoke(ctx, client.namespace+"."+"verify_buyer_qualifications", in, out, opts...)
	if err != nil {
		return nil, err
	}

	return out, nil
}
