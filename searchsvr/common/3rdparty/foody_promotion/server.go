package foody_promotion

import (
	"git.garena.com/shopee/platform/golang_splib/server"
)

type Server = server.Server

func NewServer(impl SpexFoodyPromotionHandler, originOpts ...server.Option) (Server, error) {
	service := newService(impl)

	opts := make([]server.Option, 0, len(originOpts))
	opts = append(opts, server.WithName("shopeefood.promotion.promotion"))
	opts = append(opts, server.WithInterceptor(ServerVersionReport(version)))
	opts = append(opts, originOpts...)

	s, err := server.NewServer(opts...)
	if err != nil {
		return nil, err
	}

	if err := s.RegisterService(service); err != nil {
		return nil, err
	}

	return s, nil
}
