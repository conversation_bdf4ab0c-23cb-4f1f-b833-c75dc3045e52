package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetA30ReRankConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.StoreA30ReRankConfig {
	a30Config := &apollo.StoreA30ReRankConfig{}

	a30Config.Distance = abParamClient.GetParamWithFloat("Search.ReRank.StoreA30ReRank.Distance", 0.0)
	a30Config.DivBoost = abParamClient.GetParamWithFloat("Search.ReRank.StoreA30ReRank.DivBoost", 0.0)
	a30Config.TopPos = abParamClient.GetParamWithInt("Search.ReRank.StoreA30ReRank.TopPos", 0)
	a30Config.StoreCtCvr180d = abParamClient.GetParamWithFloat("Search.ReRank.StoreA30ReRank.StoreCtCvr180d", 0.0)
	a30Config.StoreImp180d = abParamClient.GetParamWithInt64("Search.ReRank.StoreA30ReRank.StoreImp180d", 0)
	a30Config.StoreOrderCnt180d = abParamClient.GetParamWithInt64("Search.ReRank.StoreA30ReRank.StoreOrderCnt180d", 0)

	logger.MyDebug(context.Background(), isDebug, "a30 from abtest params platform", zap.Any("a30 config", a30Config))

	return a30Config
}
