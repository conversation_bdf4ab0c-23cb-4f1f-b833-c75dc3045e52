package abtest

import (
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall2/recallconstant"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

func GetOneRecallConfig(recallName string, abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.Recall.ES."+recallName, 0))
}

func GetOneRecallConfigV2(recallName string, abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.Recall.ESV2."+recallName, 0))
}

func GetPredictRecallConfig(abParamClient *SearchParamMultiClient) *apollo.PredictRecallConfig {
	pRecallConfig := &apollo.PredictRecallConfig{}

	pRecallConfig.MustAndMatchTypeFieldBoostQueueLocation = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueLocation", "")
	pRecallConfig.MustAndMatchTypeFieldBoostQueueDish = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueDish", "")
	pRecallConfig.MustAndMatchTypeFieldBoostQueueStore = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueStore", "")
	pRecallConfig.MustAndMatchTypeFieldBoostQueueCategory = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueCategory", "")
	pRecallConfig.MustAndMatchTypeFieldBoostQueueIngredient = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueIngredient", "")
	pRecallConfig.MustAndMatchTypeFieldBoostQueueDefault = abParamClient.GetParamWithString("Search.PredictRecall.MustAndMatchTypeFieldBoostQueueDefault", "")
	pRecallConfig.OtherFieldBoostQueueDish = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueDish", "")
	pRecallConfig.OtherFieldBoostQueueStore = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueStore", "")
	pRecallConfig.OtherFieldBoostQueueCategory = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueCategory", "")
	pRecallConfig.OtherFieldBoostQueueIngredient = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueIngredient", "")
	pRecallConfig.OtherFieldBoostQueueDefault = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueDefault", "")
	pRecallConfig.OtherFieldBoostQueueLocation = abParamClient.GetParamWithString("Search.PredictRecall.OtherFieldBoostQueueLocation", "")
	pRecallConfig.NerRecallTotal = abParamClient.GetParamWithInt("Search.PredictRecall.NerRecallTotal", 0)
	pRecallConfig.MustAndMatchTypeSize = abParamClient.GetParamWithInt("Search.PredictRecall.MustAndMatchTypeSize", 0)
	pRecallConfig.MustOrMatchTypeSize = abParamClient.GetParamWithInt("Search.PredictRecall.MustOrMatchTypeSize", 0)
	pRecallConfig.ShouldMatchTypeSize = abParamClient.GetParamWithInt("Search.PredictRecall.ShouldMatchTypeSize", 0)

	pRecallConfig.NerRecallUseScriptSort = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.NerRecallUseScriptSort", 0))
	pRecallConfig.LocationNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.LocationNerBoost", 0.0)
	pRecallConfig.DishNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.DishNerBoost", 0.0)
	pRecallConfig.StoreNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.StoreNerBoost", 0.0)
	pRecallConfig.CategoryNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.CategoryNerBoost", 0.0)
	pRecallConfig.IngredientNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.IngredientNerBoost", 0.0)
	pRecallConfig.DefaultNerBoost = abParamClient.GetParamWithFloat("Search.PredictRecall.DefaultNerBoost", 0.0)

	pRecallConfig.IsNewCateRecall = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.IsNewCateRecall", 0))
	pRecallConfig.NewCateProbFilterScore = abParamClient.GetParamWithFloat("Search.PredictRecall.NewCateProbFilterScore", 0.0)
	pRecallConfig.NewCateIsKeepGrabfoodTag = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.NewCateIsKeepGrabfoodTag", 0))
	pRecallConfig.NewCateIsOnlyRecallStoreIntention = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.NewCateIsOnlyRecallStoreIntention", 0))
	pRecallConfig.NewCateExpandRecallSize = abParamClient.GetParamWithInt("Search.PredictRecall.NewCateExpandRecallSize", 0)
	pRecallConfig.IsNewCateExpandRecallV2 = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.IsNewCateExpandRecallV2", 0))
	pRecallConfig.NewCateExpandRecallV2DistanceKm = abParamClient.GetParamWithFloat("Search.PredictRecall.NewCateExpandRecallV2DistanceKm", 0.0)

	pRecallConfig.IsOpenLocationSort = getBool(abParamClient.GetParamWithInt("Search.PredictRecall.IsOpenLocationSort", 0))

	return pRecallConfig
}

func GetOneRecallConfigWithDataSourceAndDomain(recallName string, dataSource string, recallDomain string, abParamClient *SearchParamMultiClient) bool {
	// Search.Recall.ES.Dish.DishRecallConfiguration
	// Search.Recall.ES.Store.HotStore
	// Search.Recall.FS.Dish.SKU

	// dataSource = ES, FS
	// recallDomain = Store, Dish

	if recallDomain == recallconstant.RecallDomainStore {
		recallDomain = "Store"
	}
	if recallDomain == recallconstant.RecallDomainDish {
		recallDomain = "Dish"
	}
	if dataSource == recallconstant.RecallDataSourceES {
		dataSource = "ES"
	}
	if dataSource == recallconstant.RecallDataSourceFS {
		dataSource = "FS"
	}
	if dataSource == recallconstant.RecallDataSourceVectorEngine {
		dataSource = "VEC"
	}

	key := fmt.Sprintf("Search.Recall.%s.%s.%s", dataSource, recallDomain, recallName)
	return getBool(abParamClient.GetParamWithInt(key, 0))
}
