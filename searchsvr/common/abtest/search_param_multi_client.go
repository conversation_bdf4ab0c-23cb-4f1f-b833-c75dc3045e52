package abtest

import (
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/abtest/params_v3"
	"go.uber.org/zap"
	"strconv"
	"strings"
)

type SearchParamMultiClient struct {
	AbParamClient *params_v3.ParamMultiClient
	SceneType     string
	SortType      string
}

func (p *SearchParamMultiClient) GetAbTestNewKeyWithSceneTypeSortType(expKey string) string {
	return p.SceneType + "." + p.SortType + "." + expKey
}

func (p *SearchParamMultiClient) GetAbTestNewKey(expKey string) string {
	return p.SceneType + "." + expKey
}

func (p *SearchParamMultiClient) GetAbTestNewKeyWithSortType(expKey string) string {
	return "Search" + "." + p.SortType + "." + expKey
}

func (p *SearchParamMultiClient) GetRealExpKey(expKey string) string {
	if expKey == "" {
		return ""
	}
	_, suffix, found := strings.Cut(expKey, ".")
	if found {
		return suffix
	} else {
		logkit.Error("Error abtest expKey", zap.String("key", expKey))
	}
	return expKey
}

func (p *SearchParamMultiClient) GetParamVal(expKey string) string {
	relExpKey := p.GetRealExpKey(expKey)
	// 优先获取当前场景 + sortType 参数, key = {sceneType}.{sortType}.xxx.xxx.xxx
	val := p.AbParamClient.GetParamWithString(p.GetAbTestNewKeyWithSceneTypeSortType(relExpKey), "")
	if len(val) == 0 {
		// 再获取当前场景参数, key = Search.{sortType}.xxx.xxx.xxx
		val = p.AbParamClient.GetParamWithString(p.GetAbTestNewKeyWithSortType(relExpKey), "")
	}
	if len(val) == 0 {
		// 再获取当前场景参数, key = {sceneType}.xxx.xxx.xxx
		val = p.AbParamClient.GetParamWithString(p.GetAbTestNewKey(relExpKey), "")
	}
	if len(val) == 0 {
		// 最后降级获取默认参数, 当前线上的, key = Search.xxx.xxx.xxx
		val = p.AbParamClient.GetParamWithString(expKey, "")
	}
	return val
}

func (p *SearchParamMultiClient) GetParamWithInt(expKey string, defaultValue int) int {
	val := p.GetParamVal(expKey)
	if len(val) == 0 {
		return defaultValue
	}

	intVal, err := strconv.Atoi(val)
	if err != nil {
		logkit.Error("abtest params parse int fail", zap.String("expKey", expKey), zap.String("val", val))
		return defaultValue
	}
	return intVal
}

func (p *SearchParamMultiClient) GetParamWithInt64(expKey string, defaultValue int64) int64 {
	val := p.GetParamVal(expKey)
	if len(val) == 0 {
		return defaultValue
	}

	intVal, err := strconv.ParseInt(val, 10, 64)
	if err != nil {
		logkit.Error("abtest params parse int fail", zap.String("expKey", expKey), zap.String("val", val))
		return defaultValue
	}
	return intVal
}

func (p *SearchParamMultiClient) GetParamWithFloat(expKey string, defaultValue float64) float64 {
	val := p.GetParamVal(expKey)
	if len(val) == 0 {
		return defaultValue
	}

	intVal, err := strconv.ParseFloat(val, 64)
	if err != nil {
		logkit.Error("abtest params parse int fail", zap.String("expKey", expKey), zap.String("val", val))
		return defaultValue
	}
	return intVal
}

func (p *SearchParamMultiClient) GetParamWithString(expKey string, defaultValue string) string {
	val := p.GetParamVal(expKey)
	if len(val) == 0 {
		return defaultValue
	}

	return val
}
