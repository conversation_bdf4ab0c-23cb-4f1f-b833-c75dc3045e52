package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetVectorEngineRecallConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.VectorEngineRecallConfig {
	vectorRecallConfig := &apollo.VectorEngineRecallConfig{}
	vectorRecallConfig.SuppressRecallResultThreshold = abParamClient.GetParamWithInt("Search.Recall.VectorEngineRecall.SuppressRecallResultThreshold", 0)
	vectorRecallConfig.IsSuppressRule1 = getBool(abParamClient.GetParamWithInt("Search.Recall.VectorEngineRecall.IsSuppressRule1", 0))
	vectorRecallConfig.IsSuppressRule2 = getBool(abParamClient.GetParamWithInt("Search.Recall.VectorEngineRecall.IsSuppressRule2", 0))
	vectorRecallConfig.IsSuppressRule3 = getBool(abParamClient.GetParamWithInt("Search.Recall.VectorEngineRecall.IsSuppressRule3", 0))
	logger.MyDebug(context.Background(), isDebug, "vector engine from abtest params platform", zap.Any("vector engine config", vectorRecallConfig))

	return vectorRecallConfig
}
