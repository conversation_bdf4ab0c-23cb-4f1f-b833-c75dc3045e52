package abtest

import (
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
)

func GetDishSearcherRecallConfig(abParamClient *SearchParamMultiClient) apollo.DishSearcherRecallConfig {
	config := apollo.DishSearcherRecallConfig{}

	config.IsNewDishRecall = getBool(abParamClient.GetParamWithInt("Search.Recall.DishSearcher.IsNewDishRecall", 0))
	config.RecallKeywordLimit = abParamClient.GetParamWithInt("Search.Recall.DishSearcher.RecallKeywordLimit", 0)
	config.RawQueryMultiWeight = abParamClient.GetParamWithFloat("Search.Recall.DishSearcher.RawQueryMultiWeight", 2)
	config.NerRewriteMultiWeight = abParamClient.GetParamWithFloat("Search.Recall.DishSearcher.NerRewriteMultiWeight", 0.5)
	config.EnlargeRewriteMultiWeight = abParamClient.GetParamWithFloat("Search.Recall.DishSearcher.EnlargeRewriteMultiWeight", 0.1)

	config.RawQueryUseCatalogRecall = getBool(abParamClient.GetParamWithInt("Search.Recall.DishSearcher.RawQueryUseCatalogRecall", 0))
	config.NerRewriteUseCatalogRecall = getBool(abParamClient.GetParamWithInt("Search.Recall.DishSearcher.NerRewriteUseCatalogRecall", 0))
	config.EnlargeRewriteUseCatalogRecall = getBool(abParamClient.GetParamWithInt("Search.Recall.DishSearcher.EnlargeRewriteUseCatalogRecall", 0))
	config.EachStoreRecallDishSize = abParamClient.GetParamWithInt("Search.Recall.DishSearcher.EachStoreRecallDishSize", 2)
	return config
}
