package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetCvrConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.PredictCvrConfig {
	cvrConfig := &apollo.PredictCvrConfig{}
	cvrConfig.CvrModuleID = abParamClient.GetParamWithInt("Search.PredictCvr.CvrModuleID", 0)
	cvrConfig.CvrModuleName = abParamClient.GetParamWithString("Search.PredictCvr.CvrModuleName", "")
	cvrConfig.MaxTruncPcvr = abParamClient.GetParamWithFloat("Search.PredictCvr.MaxTruncPcvr", 0.0)
	cvrConfig.MinTruncPcvr = abParamClient.GetParamWithFloat("Search.PredictCvr.MinTruncPcvr", 0.0)

	logger.MyDebug(context.Background(), isDebug, "cvr from abtest params platform", zap.Any("cvr config", cvrConfig))
	return cvrConfig
}
