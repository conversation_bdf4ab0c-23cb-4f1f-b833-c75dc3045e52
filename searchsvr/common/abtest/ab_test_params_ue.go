package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetUeConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.PredictPriceConfig {
	ueConfig := &apollo.PredictPriceConfig{}

	ueConfig.PriceType = abParamClient.GetParamWithInt("Search.PredictUe.PriceType", 0)
	ueConfig.UEModuleName = abParamClient.GetParamWithString("Search.PredictUe.UEModuleName", "")
	ueConfig.MaxTruncPrice = abParamClient.GetParamWithFloat("Search.PredictUe.MaxTruncPrice", 0.0)
	ueConfig.MinTruncPrice = abParamClient.GetParamWithFloat("Search.PredictUe.MinTruncPrice", 0.0)
	ueConfig.MaxTruncUE = abParamClient.GetParamWithFloat("Search.PredictUe.MaxTruncUE", 0.0)
	ueConfig.MinTruncUE = abParamClient.GetParamWithFloat("Search.PredictUe.MinTruncUE", 0.0)
	ueConfig.UEDefaultVal = abParamClient.GetParamWithFloat("Search.PredictUe.UEDefaultVal", 0.0)
	ueConfig.UERadioDefaultVal = abParamClient.GetParamWithFloat("Search.PredictUe.UERadioDefaultVal", 0.0)

	logger.MyDebug(context.Background(), isDebug, "ue from abtest params platform", zap.Any("ue config", ueConfig))

	return ueConfig
}
