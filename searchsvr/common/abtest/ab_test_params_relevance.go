package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetRelevanceConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.PredictRelevanceConfig {
	relevanceConfig := &apollo.PredictRelevanceConfig{}

	relevanceConfig.NewStrategy = abParamClient.GetParamWithInt("Search.PredictRelevance.NewStrategy", 0)
	relevanceConfig.ReleFusionFunc = abParamClient.GetParamWithInt("Search.PredictRelevance.ReleFusionFunc", 0)
	relevanceConfig.MaxTruncRelevance = abParamClient.GetParamWithFloat("Search.PredictRelevance.MaxTruncRelevance", 0.0)
	relevanceConfig.MinTruncRelevance = abParamClient.GetParamWithFloat("Search.PredictRelevance.MinTruncRelevance", 0.0)
	relevanceConfig.MaxTruncSemantic = abParamClient.GetParamWithFloat("Search.PredictRelevance.MaxTruncSemantic", 0.0)
	relevanceConfig.MinTruncSemantic = abParamClient.GetParamWithFloat("Search.PredictRelevance.MinTruncSemantic", 0.0)
	relevanceConfig.RelevanceModuleID = abParamClient.GetParamWithInt("Search.PredictRelevance.RelevanceModuleID", 0)
	relevanceConfig.RelevanceModuleName = abParamClient.GetParamWithString("Search.PredictRelevance.RelevanceModuleName", "")
	relevanceConfig.Distance = abParamClient.GetParamWithFloat("Search.PredictRelevance.Distance", 0)
	relevanceConfig.RelResLayer = getBool(abParamClient.GetParamWithInt("Search.PredictRelevance.RelResLayer", 0))
	relevanceConfig.SemanticModuleID = abParamClient.GetParamWithInt("Search.PredictRelevance.SemanticModuleID", 0)
	relevanceConfig.SemanticModuleName = abParamClient.GetParamWithString("Search.PredictRelevance.SemanticModuleName", "")
	relevanceConfig.Suppress = abParamClient.GetParamWithFloat("Search.PredictRelevance.Suppress", 0.0)

	logger.MyDebug(context.Background(), isDebug, "relevance from abtest params platform", zap.Any("relevance config", relevanceConfig))

	return relevanceConfig
}

func GetDefaultPredictRelevanceScore(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.MultiFactor.DefaultPredictRelevanceScore", 0)
}
