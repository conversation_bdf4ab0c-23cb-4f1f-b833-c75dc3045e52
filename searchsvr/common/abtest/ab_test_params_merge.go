package abtest

import (
	"context"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetMergeConfig(ctx context.Context, isDebug bool, abParamClient *SearchParamMultiClient) *apollo.MergeConfig {
	mergeConfig := &apollo.MergeConfig{}
	mergeConfig.RecallMinSize = make(map[string]int)
	mergeConfig.ExpString = abParamClient.GetParamWithString("Search.Merge.ExpString", "")
	mergeConfig.ExpFunc = abParamClient.GetParamWithString("Search.Merge.ExpFunc", "")
	mergeConfig.ExpParameters = abParamClient.GetParamWithString("Search.Merge.ExpParameters", "")
	recallTypesStr := abParamClient.GetParamWithString("Search.Merge.RecallTypes", "")
	recallTypes := strings.Split(recallTypesStr, ",")
	mergeConfig.RecallMinSize = make(map[string]int, len(recallTypes))
	for _, recallType := range recallTypes {
		mergeConfig.RecallMinSize[recallType] = abParamClient.GetParamWithInt("Search.Merge.RecallMinSize."+recallType, 0)
	}
	logger.MyDebug(ctx, isDebug, "merge config from abtest params platform", zap.Any("merge config", mergeConfig))
	return mergeConfig
}
