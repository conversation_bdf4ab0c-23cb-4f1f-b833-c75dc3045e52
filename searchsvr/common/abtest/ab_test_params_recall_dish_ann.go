package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetDishAnnRecallConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.DishAnnRecallConfig {
	dishAnnConfig := &apollo.DishAnnRecallConfig{}

	dishAnnConfig.RedisPrefix = abParamClient.GetParamWithString("Search.Recall.DishAnnRecall.RedisPrefix", "")

	logger.MyDebug(context.Background(), isDebug, "dish ann from abtest params platform", zap.Any("dish ann config", dishAnnConfig))

	return dishAnnConfig
}
