package abtest

import (
	"context"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetCtrConfig(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.PredictCtrConfig {
	ctrConfig := &apollo.PredictCtrConfig{}
	ctrConfig.CtrModuleID = abParamClient.GetParamWithInt("Search.PredictCtr.CtrModuleID", 0)
	ctrConfig.CtrModuleName = abParamClient.GetParamWithString("Search.PredictCtr.CtrModuleName", "")
	ctrConfig.MaxTruncPctr = abParamClient.GetParamWithFloat("Search.PredictCtr.MaxTruncPctr", 0.0)
	ctrConfig.MinTruncPctr = abParamClient.GetParamWithFloat("Search.PredictCtr.MinTruncPctr", 0.0)

	logger.MyDebug(context.Background(), isDebug, "ctr from abtest params platform", zap.Any("ctr config", ctrConfig))
	return ctrConfig
}
