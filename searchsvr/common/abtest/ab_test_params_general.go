package abtest

import (
	"context"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetIsHideNonHalal(isDebug bool, abParamClient *SearchParamMultiClient) bool {
	isHit := getBool(abParamClient.GetParamWithInt("Search.General.HideNonHalal.OpenExp", 0))
	logger.MyDebug(context.Background(), isDebug, "Hide NonHalal from abtest params platform", zap.Bool("OpenExp", isHit))
	return isHit
}

func GetIsHitExactMatch(isDebug bool, abParamClient *SearchParamMultiClient) bool {
	isHit := 0
	if cid.IsVN() {
		isHit = abParamClient.GetParamWithInt("Search.General.VnExactMatch.OpenExp", 0)
		if isHit == 1 {
			return true
		}
	}
	isHit = abParamClient.GetParamWithInt("Search.General.ExactMatch.OpenExp", 0)
	if isHit == 1 {
		return true
	}
	return false
}

func GetIsNotRelevanceTabUseNewSort(isDebug bool, abParamClient *SearchParamMultiClient) bool {
	isHit := getBool(abParamClient.GetParamWithInt("Search.General.NotRelevanceTab.OpenExp", 0))
	logger.MyDebug(context.Background(), isDebug, "NotRelevanceTab from abtest params platform", zap.Bool("OpenExp", isHit))
	return isHit
}

type DeboostConfig struct {
	TopN             int
	TopK             int
	DeboostThreshold float64
	BoostThreshold   float64
}

func GetDeboostConfig(isDebug bool, abParamClient *SearchParamMultiClient) *DeboostConfig {
	config := &DeboostConfig{}
	config.TopN = abParamClient.GetParamWithInt("Search.Deboost.TopN", 1)
	config.TopK = abParamClient.GetParamWithInt("Search.Deboost.TopK", 0)
	config.DeboostThreshold = abParamClient.GetParamWithFloat("Search.Deboost.RelevanceThreshold", 0.0)

	logger.MyDebug(context.Background(), isDebug, "Deboost relevance get from abtest params platform", zap.Any("config", config))
	return config
}

func GetDeboostCategoryConfig(isDebug bool, abParamClient *SearchParamMultiClient) *DeboostConfig {
	config := &DeboostConfig{}
	config.TopN = abParamClient.GetParamWithInt("Search.DeboostCategory.TopN", 1)
	config.TopK = abParamClient.GetParamWithInt("Search.DeboostCategory.TopK", 0)
	config.DeboostThreshold = abParamClient.GetParamWithFloat("Search.DeboostCategory.DeboostThreshold", 0.0)
	config.BoostThreshold = abParamClient.GetParamWithFloat("Search.DeboostCategory.BoostThreshold", 0.0)

	logger.MyDebug(context.Background(), isDebug, "Deboost category get from abtest params platform", zap.Any("config", config))
	return config
}

func GetNewDumpLogSwitch(abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.General.NewDumpLogSwitch", 0))
}

func GetNewDumpLogPageSize(abParamClient *SearchParamMultiClient) int {
	return abParamClient.GetParamWithInt("Search.General.NewDumpLogPageSize", 100)
}

func GetPrintDishRecallCache(abParamClient *SearchParamMultiClient) string {
	storeIdsStr := abParamClient.GetParamWithString("Search.General.ForDebug.PrintDishRecallCacheDishIds", "")
	return storeIdsStr
}

func IsMainSiteFilterClosedStores(abParamClient *SearchParamMultiClient) bool {
	// 默认需要过滤 closed 门店
	return getBool(abParamClient.GetParamWithInt("Search.General.IsMainSiteFilterClosedStores", 1))
}
