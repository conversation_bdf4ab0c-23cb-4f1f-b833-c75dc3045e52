package abtest

import (
	"context"
	"strconv"
	"strings"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"
)

func GetMultiFactorParams(isDebug bool, abParamClient *SearchParamMultiClient) *apollo.PredictMultiFactorConfig {
	multiFactorConfig := &apollo.PredictMultiFactorConfig{}
	multiFactorConfig.ABTestVal = abParamClient.GetParamWithString("Search.MultiFactor.ABTestVal", "")

	multiFactorConfig.ExpParameters = abParamClient.GetParamWithString("Search.MultiFactor.ExpParameters", "")
	multiFactorConfig.ExpString = abParamClient.GetParamWithString("Search.MultiFactor.ExpString", "")
	multiFactorConfig.UsePcFactor = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.UsePcFactor", 0))

	// Ltr 相关
	multiFactorConfig.LtrModuleName = abParamClient.GetParamWithString("Search.MultiFactor.LtrModuleName", "")
	multiFactorConfig.UseLtr = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.UseLtr", 0))
	multiFactorConfig.CoarseSortExpString = abParamClient.GetParamWithString("Search.MultiFactor.CoarseSortExpString", "")
	multiFactorConfig.CoarseSortExpParameters = abParamClient.GetParamWithString("Search.MultiFactor.CoarseSortExpParameters", "")
	multiFactorConfig.CoarseSortExpParametersMap = parseDefaultExpParams(multiFactorConfig.CoarseSortExpParameters)

	multiFactorConfig.ExpParametersMap = parseDefaultExpParams(multiFactorConfig.ExpParameters)
	multiFactorConfig.ExpFunc = abParamClient.GetParamWithString("Search.MultiFactor.ExpFunc", "")
	multiFactorConfig.Relevance = abParamClient.GetParamWithInt("Search.MultiFactor.Relevance", 0)
	multiFactorConfig.RelevanceUseLevel = abParamClient.GetParamWithInt("Search.MultiFactor.RelevanceUseLevel", 0)
	multiFactorConfig.TruncDistance = abParamClient.GetParamWithFloat("Search.MultiFactor.TruncDistance", 0.0)
	// user exp
	multiFactorConfig.UserExpParameters = abParamClient.GetParamWithString("Search.MultiFactor.UserExpParameters", "")
	multiFactorConfig.UserExpParametersMap = parseOtherExpParams(multiFactorConfig.UserExpParameters)
	multiFactorConfig.UserExpType = abParamClient.GetParamWithInt("Search.MultiFactor.UserExpType", 0)
	multiFactorConfig.UserExp = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.UserExp", 0))
	// intent exp
	multiFactorConfig.IntentExpParameters = abParamClient.GetParamWithString("Search.MultiFactor.IntentExpParameters", "")
	multiFactorConfig.IntentExpParametersMap = parseOtherExpParams(multiFactorConfig.IntentExpParameters)
	multiFactorConfig.IntentExp = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.IntentExp", 0))

	multiFactorConfig.IsPredict = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.IsPredict", 0))
	multiFactorConfig.StoreIncubation = abParamClient.GetParamWithFloat("Search.MultiFactor.StoreIncubation", 0.0)

	// ctr 相关
	multiFactorConfig.MinTruncPctr = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncPctr", 0.0)
	multiFactorConfig.MaxTruncPctr = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncPctr", 0.0)
	multiFactorConfig.CtrModuleName = abParamClient.GetParamWithString("Search.MultiFactor.CtrModuleName", "")
	multiFactorConfig.IsCtrCvrUnified = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.IsCtrCvrUnified", 0))
	multiFactorConfig.UnifiedModuleCtrKey = abParamClient.GetParamWithString("Search.MultiFactor.UnifiedModuleCtrKey", "")
	multiFactorConfig.UnifiedModuleCvrKey = abParamClient.GetParamWithString("Search.MultiFactor.UnifiedModuleCvrKey", "")

	// cvr 相关
	multiFactorConfig.MinTruncPcvr = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncPcvr", 0.0)
	multiFactorConfig.MaxTruncPcvr = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncPcvr", 0.0)
	multiFactorConfig.CvrModuleName = abParamClient.GetParamWithString("Search.MultiFactor.CvrModuleName", "")
	// relevance 相关
	multiFactorConfig.MinTruncRelevance = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncRelevance", 0.0)
	multiFactorConfig.MaxTruncRelevance = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncRelevance", 0.0)
	multiFactorConfig.MaxTruncSemantic = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncSemantic", 0.0)
	multiFactorConfig.MinTruncSemantic = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncSemantic", 0.0)
	multiFactorConfig.ReleFusionFunc = abParamClient.GetParamWithInt("Search.MultiFactor.ReleFusionFunc", 0)
	multiFactorConfig.RelevanceModuleName = abParamClient.GetParamWithString("Search.MultiFactor.RelevanceModuleName", "")
	multiFactorConfig.SemanticModuleName = abParamClient.GetParamWithString("Search.MultiFactor.SemanticModuleName", "")
	multiFactorConfig.NewStrategy = abParamClient.GetParamWithInt("Search.MultiFactor.NewStrategy", 0)
	multiFactorConfig.Suppress = abParamClient.GetParamWithFloat("Search.MultiFactor.Suppress", 0.0)
	multiFactorConfig.Distance = abParamClient.GetParamWithFloat("Search.MultiFactor.Distance", 0.0)
	multiFactorConfig.RelResLayer = getBool(abParamClient.GetParamWithInt("Search.MultiFactor.RelResLayer", 0))
	// UE相关
	multiFactorConfig.PriceType = abParamClient.GetParamWithInt("Search.MultiFactor.PriceType", 0)
	multiFactorConfig.MinTruncPrice = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncPrice", 0.0)
	multiFactorConfig.MaxTruncPrice = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncPrice", 0.0)
	multiFactorConfig.UEDefaultVal = abParamClient.GetParamWithFloat("Search.MultiFactor.UEDefaultVal", 0.0)
	multiFactorConfig.UERadioDefaultVal = abParamClient.GetParamWithFloat("Search.MultiFactor.UERadioDefaultVal", 0.0)
	multiFactorConfig.UEModuleName = abParamClient.GetParamWithString("Search.MultiFactor.UEModuleName", "")
	multiFactorConfig.MaxTruncUE = abParamClient.GetParamWithFloat("Search.MultiFactor.MaxTruncUE", 0.0)
	multiFactorConfig.MinTruncUE = abParamClient.GetParamWithFloat("Search.MultiFactor.MinTruncUE", 0.0)

	// DF 相关
	multiFactorConfig.DFModelName = abParamClient.GetParamWithString("Search.MultiFactor.DFModelName", "")

	// listwise lrt_v1 表达式
	multiFactorConfig.LTRV1ExpString = abParamClient.GetParamWithString("Search.MultiFactor.LTRV1ExpString", "")
	multiFactorConfig.LTRV1ExpParameters = abParamClient.GetParamWithString("Search.MultiFactor.LTRV1ExpParameters", "")

	logger.MyDebug(context.Background(), isDebug, "multiFactor  from abtest params platform", zap.Any("multiFactor config", multiFactorConfig))

	return multiFactorConfig
}

func GetNotRelevanceTabExp(isDebug bool, abParamClient *SearchParamMultiClient) string {
	expString := abParamClient.GetParamWithString("Search.MultiFactor.NotRelevanceTabExpString", "")
	return expString
}

func parseDefaultExpParams(expParams string) map[string]interface{} {
	expList := strings.Split(expParams, ",")
	expParametersMap := make(map[string]interface{}, len(expList))
	for _, pair := range expList {
		keyValue := strings.Split(pair, "=")
		if len(keyValue) < 2 {
			continue
		}
		key := keyValue[0]
		value, err := strconv.ParseFloat(keyValue[1], 64)
		if err != nil {
			continue
		}
		expParametersMap[key] = value
	}
	return expParametersMap
}

func parseOtherExpParams(expParams string) map[string]map[string]interface{} {
	expList := strings.Split(expParams, ";")
	expParamsMap := make(map[string]map[string]interface{}, len(expList))
	for _, pair := range expList {
		keyValue := strings.Split(pair, ":")
		if len(keyValue) < 2 {
			continue
		}
		paramsList := strings.Split(keyValue[1], ",")
		parametersMap := make(map[string]interface{}, len(paramsList))
		for _, parameters := range paramsList {
			kv := strings.Split(parameters, "=")
			if len(kv) < 2 {
				continue
			}
			key := kv[0]
			value, err := strconv.ParseFloat(kv[1], 64)
			if err != nil {
				continue
			}
			parametersMap[key] = value
		}
		expParamsMap[keyValue[0]] = parametersMap
	}
	return expParamsMap
}

func getBool(val int) bool {
	if val == 0 {
		return false
	}
	return true
}
