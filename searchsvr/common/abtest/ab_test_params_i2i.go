package abtest

func GetI2iSwitch(abParamClient *SearchParamMultiClient) bool {
	return abParamClient.GetParamWithInt("Search.Recall.I2IRecall.I2iSwitch", 0) == 1
}

func GetStoreIndexEsScorePercent(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.StoreIndexEsScorePercent", 0.0)
}

func GetStoreWithDishFieldEsScorePercent(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.StoreWithDishFieldEsScorePercent", 0.0)
}

func GetStoreCategoryEsScorePercent(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.StoreCategoryEsScorePercent", 0.0)
}

func GetStoreIndexEsScoreThresholdFactor(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.GetStoreIndexEsScoreThresholdFactor", 0.0)
}

func GetStoreWithDishFieldEsScoreThresholdFactor(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.StoreWithDishFieldEsScoreThresholdFactor", 0.0)
}

func GetStoreCategoryEsScoreThresholdFactor(abParamClient *SearchParamMultiClient) float64 {
	return abParamClient.GetParamWithFloat("Search.Recall.I2IRecall.StoreCategoryEsScoreThresholdFactor", 0.0)
}

func GetI2iMergeSize(abParamClient *SearchParamMultiClient) int {
	return abParamClient.GetParamWithInt("Search.Recall.I2IRecall.I2iMergeSize", 1000)
}
