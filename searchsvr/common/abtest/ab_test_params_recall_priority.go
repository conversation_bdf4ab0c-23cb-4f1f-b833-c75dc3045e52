package abtest

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"go.uber.org/zap"
)

func GetRecallPriorityConfig(ctx context.Context, abParamClient *SearchParamMultiClient, isDebug bool, recallMaxSize int) *apollo.RecallPriorityConfig {
	recallPriorityConf := &apollo.RecallPriorityConfig{
		LimitSize:      recallMaxSize,
		RecallPriority: map[string]int{},
	}

	recallTypeMap := apollo.GetRecallTypeMap()
	for recallType := range recallTypeMap {
		priority := abParamClient.GetParamWithInt("Search.RecallPriority.Priority."+recallType, 0)
		priorityV2 := abParamClient.GetParamWithInt("Search.RecallPriorityV2.Priority."+recallType, 0)
		if priorityV2 > priority {
			priority = priorityV2
		}
		recallPriorityConf.RecallPriority[recallType] = priority
		if priority > recallPriorityConf.MinPriority {
			recallPriorityConf.MinPriority = priority
		}
	}
	recallPriorityConf.LimitSize = abParamClient.GetParamWithInt("Search.RecallPriority.LimitSize", recallMaxSize)
	recallPriorityConf.IsReRankSkipPriority = abParamClient.GetParamWithInt("Search.RecallPriority.IsReRankSkipPriority", 0)
	recallPriorityConf.IsReRankSkipPriorityRecallLayer = abParamClient.GetParamWithInt("Search.RecallPriority.IsReRankSkipPriorityRecallLayer", 0)

	limitSize := abParamClient.GetParamWithInt("Search.RecallPriorityV2.LimitSize", recallMaxSize)
	isReRankSkipPriority := abParamClient.GetParamWithInt("Search.RecallPriorityV2.IsReRankSkipPriority", 0)
	isReRankSkipPriorityRecallLayer := abParamClient.GetParamWithInt("Search.RecallPriorityV2.IsReRankSkipPriorityRecallLayer", 0)
	if limitSize > recallPriorityConf.LimitSize {
		recallPriorityConf.LimitSize = limitSize
	}
	// 兜底逻辑，避免参数配置错误为0
	if recallPriorityConf.LimitSize <= 0 {
		logkit.FromContext(ctx).Error("recall priority config limit size invalid, default recallMaxSize", zap.Int("old", recallPriorityConf.LimitSize), zap.Int("new", recallMaxSize))
		recallPriorityConf.LimitSize = recallMaxSize
	}
	if isReRankSkipPriority > recallPriorityConf.IsReRankSkipPriority {
		recallPriorityConf.IsReRankSkipPriority = isReRankSkipPriority
	}
	if isReRankSkipPriorityRecallLayer > recallPriorityConf.IsReRankSkipPriorityRecallLayer {
		recallPriorityConf.IsReRankSkipPriorityRecallLayer = isReRankSkipPriorityRecallLayer
	}
	if isDebug {
		logkit.FromContext(ctx).Info("recall priority config from abtest params platform", zap.Any("recall priority config", recallPriorityConf))
	}
	return recallPriorityConf
}
