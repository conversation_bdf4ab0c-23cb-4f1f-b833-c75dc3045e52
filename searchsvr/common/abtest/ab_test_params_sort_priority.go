package abtest

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"go.uber.org/zap"
)

func GetSortPriorityConfig(ctx context.Context, abParamClient *SearchParamMultiClient, isDebug bool) *apollo.SortPriorityConfig {
	sortPriorityConf := &apollo.SortPriorityConfig{
		SortPriority: map[string]int{},
	}

	recallTypeMap := apollo.GetRecallTypeMap()
	for recallType := range recallTypeMap {
		priority := abParamClient.GetParamWithInt("Search.SortPriority.Priority."+recallType, 0)
		sortPriorityConf.SortPriority[recallType] = priority
	}
	sortPriorityConf.IsUseSortPriority = getBool(abParamClient.GetParamWithInt("Search.SortPriority.IsUseSortPriority", 0))
	if isDebug {
		logkit.FromContext(ctx).Info("sort priority config from abtest params platform", zap.Any("sort priority config", sortPriorityConf))
	}
	return sortPriorityConf
}
