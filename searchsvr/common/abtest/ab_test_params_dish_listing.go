package abtest

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/constants"
)

func GetDishListingRankSwitch(abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.DishListing.DishListingRankSwitch", 1))
}

func GetDishListingRankExpressionName(abParamClient *SearchParamMultiClient) string {
	var defaultName = constants.DishFormulaStatic2
	return abParamClient.GetParamWithString("Search.DishListing.RankExpressionName", defaultName)
}

func GetDishListingRankExpression(abParamClient *SearchParamMultiClient) string {
	var defaultExpression = "w1 * sales_volume_7d_score + w2 * rating_score + w3*uv_repurchase_rate_30d_score + w4 *  dish_impr_to_atc_rate_30d + w5 * dish_atc_to_order_rate_30d + w6 * is_flash_sale"
	return abParamClient.GetParamWithString("Search.DishListing.RankExpression", defaultExpression)
}

func GetDishListingRankWeights(abParamClient *SearchParamMultiClient) string {
	var defaultWeights string
	switch env.GetCID() {
	case cid.TH:
		defaultWeights = "w1=2.4265,w2=0.9753,w3=1.5455,w4=10.3727,w5=1.1333,w6=3"
	case cid.MY:
		defaultWeights = "w1=2.8373,w2=1.3733,w3=2.1522,w4=9.2175,w5=1.3572,w6=3"
	case cid.VN:
		defaultWeights = "w1=2.8748,w2=0.3629,w3=2.2200,w4=7.3839,w5=1.0550,w6=3"
	default:
		defaultWeights = "w1=2.5884,w2=0.9916,w3=1.6303,w4=7.6568,w5=1.8187,w6=50"
	}
	return abParamClient.GetParamWithString("Search.DishListing.RankWeights", defaultWeights)
}

func GetDishListingRecallSize(abParamClient *SearchParamMultiClient) int {
	maxSize := abParamClient.GetParamWithInt("Search.DishListing.DishSize", 6)
	if maxSize <= 0 {
		return 0
	}
	return maxSize
}

func GetDishNotShowSize(abParamClient *SearchParamMultiClient) int {
	notShowSize := abParamClient.GetParamWithInt("Search.DishListing.GetDishNotShowSize", 3)
	if notShowSize <= 0 {
		return 0
	}
	return notShowSize
}

func GetFlashSaleDishShowSize(abParamClient *SearchParamMultiClient) int {
	size := abParamClient.GetParamWithInt("Search.DishListing.FlashSaleDishShowSize", 2)
	if size <= 0 {
		return 0
	}
	return size
}

func IsUserV2RelDishes(abParamClient *SearchParamMultiClient) bool {
	return getBool(abParamClient.GetParamWithInt("Search.DishListing.IsUserV2RelDishes", 1))
}
