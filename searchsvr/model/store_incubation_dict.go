package model

import (
	"bufio"
	"context"
	"io"
	"os"
	"strconv"
	"sync"
	"time"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"
)

type StoreIncubationDict struct {
	Dict map[uint64]interface{}
	sync.RWMutex
}

var StoreIncubationDictInstance *StoreIncubationDict

var storeIncubationDictOnce sync.Once

func NewStoreIncubationDict() *StoreIncubationDict {
	storeIncubationDictOnce.Do(func() {
		StoreIncubationDictInstance = &StoreIncubationDict{
			Dict:    nil,
			RWMutex: sync.RWMutex{},
		}
	})
	return StoreIncubationDictInstance
}

func (d *StoreIncubationDict) BuildStoreIncubationDict(ctx context.Context, filePwd string) {
	//准备读取文件
	startTime := time.Now()
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("StoreIncubationDict Failed to open file",
			logkit.String("file path", filePwd))
	}
	defer fs.Close()

	tempMap := make(map[uint64]interface{})
	br := bufio.NewReader(fs)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		str := string(a)
		storeId, _ := strconv.ParseInt(str, 10, 64)
		tempMap[uint64(storeId)] = 1
	}
	d.Lock()
	defer d.Unlock()
	d.Dict = tempMap
	logkit.Info("StoreIncubationDict finished", zap.String("cost", time.Since(startTime).String()), zap.Int("size", len(tempMap)))
}

func (d *StoreIncubationDict) IsStoreIncubation(storeID uint64) bool {
	if d == nil {
		return false
	}
	d.RLock()
	defer d.RUnlock()
	if _, ok := d.Dict[storeID]; ok {
		return true
	}
	return false
}
