package model

import (
	"strings"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"go.uber.org/zap"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/sqlbuilder"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
)

type PredefinedKeyword struct {
	UserSearchKeywordsStr *string
	*foodalgo_search.PredefinedKeyword
}

func NewPredefinedKeyword(kw *foodalgo_search.PredefinedKeyword) (*PredefinedKeyword, error) {
	userKwStr, err := json.MarshalToString(kw.GetUserSearchKeywords())
	if err != nil {
		return nil, errors.Wrap(errno.ErrDataTypeConvert, zap.Error(err), zap.Strings("user_keywords", kw.GetUserSearchKeywords()))
	}

	return &PredefinedKeyword{UserSearchKeywordsStr: &userKwStr, PredefinedKeyword: kw}, nil
}

func (p *PredefinedKeyword) GetUserSearchKeywordsStr() string {
	if p == nil || p.UserSearchKeywordsStr == nil {
		return ""
	}
	return *p.UserSearchKeywordsStr
}

func (p *PredefinedKeyword) ToPB() *foodalgo_search.PredefinedKeyword {
	if p == nil {
		return nil
	}
	return p.PredefinedKeyword
}

func (p *PredefinedKeyword) ParseUserSearchKeywords() error {
	return json.Unmarshal([]byte(*p.UserSearchKeywordsStr), &p.UserSearchKeywords)
}

func (p *PredefinedKeyword) GetDifferentKeywords(o *PredefinedKeyword) ([]string, []string) {
	mp := map[string]bool{}
	mo := map[string]bool{}
	for _, v := range p.UserSearchKeywords {
		mp[strings.ToLower(v)] = true
	}
	for _, v := range o.UserSearchKeywords {
		mo[strings.ToLower(v)] = true
	}
	var pres []string
	var ores []string

	for _, v := range o.UserSearchKeywords {
		if _, ok := mp[strings.ToLower(v)]; !ok {
			ores = append(ores, v)
		}
	}
	for _, v := range p.UserSearchKeywords {
		if _, ok := mo[strings.ToLower(v)]; !ok {
			pres = append(pres, v)
		}
	}
	return pres, ores
}

func (p *PredefinedKeyword) GetUpdateSql() (string, error) {
	if p == nil {
		return "", errors.Wrap(errno.ErrParamsInvalid, zap.String("predefined keyword is nil", ""))
	}

	builder := sqlbuilder.NewSetBuilder()
	if p.Creator != nil {
		builder.String("creator", p.GetCreator())
	}
	if p.Deleted != nil {
		builder.Uint64("deleted", p.GetDeleted())
	}
	if p.CreateTime != nil {
		builder.Uint64("create_time", p.GetCreateTime())
	}
	if p.ActualSearchKeyword != nil {
		builder.String("actual_search_keyword", p.GetActualSearchKeyword())
	}
	if p.UserSearchKeywordsStr != nil {
		builder.String("user_search_keywords", p.GetUserSearchKeywordsStr())
	}
	if p.DeleteTime != nil {
		builder.Uint64("delete_time", p.GetDeleteTime())
	}

	if builder.Nil() {
		return "", errors.Wrap(errno.ErrParamsInvalid, zap.String("builder is nil", ""),
			zap.String("predefined keyword", p.String()))
	}

	builder.Uint64("update_time", timeutil.Now())

	return builder.Sql(), nil
}
