package model

import (
	"math"
)

const (
	// EarthRadius is about 6,371km according to Wikipedia
	EarthRadius = 6371

	geoBoxTopBearing    = 0
	geoBoxBottomBearing = 180
	geoBoxLeftBearing   = 270
	geoBoxRightBearing  = 90
)

type GeoPoint struct {
	Lat          float64
	Lng          float64
	Address      string
	LocationType string
}

func NewGeoPoint(lat, lng float64) *GeoPoint {
	return &GeoPoint{Lat: lat, Lng: lng}
}

func (p *GeoPoint) PointAtDistanceAndBearing(dist float64, bearing float64) *GeoPoint {
	dr := dist / EarthRadius
	bearing = (bearing * (math.Pi / 180.0))

	lat1 := (p.Lat * (math.Pi / 180.0))
	lng1 := (p.Lng * (math.Pi / 180.0))

	lat2part1 := math.Sin(lat1) * math.Cos(dr)
	lat2part2 := math.Cos(lat1) * math.Sin(dr) * math.Cos(bearing)
	lat2 := math.Asin(lat2part1 + lat2part2)

	lng2part1 := math.Sin(bearing) * math.Sin(dr) * math.Cos(lat1)
	lng2part2 := math.Cos(dr) - (math.Sin(lat1) * math.Sin(lat2))
	lng2 := lng1 + math.Atan2(lng2part1, lng2part2)

	lng2 = math.Mod((lng2+3*math.Pi), (2*math.Pi)) - math.Pi

	lat2 = lat2 * (180.0 / math.Pi)
	lng2 = lng2 * (180.0 / math.Pi)

	return &GeoPoint{Lat: lat2, Lng: lng2}
}

func (p *GeoPoint) CalBox(dist float64) (*GeoPoint, *GeoPoint) {
	top := p.PointAtDistanceAndBearing(dist, geoBoxTopBearing)
	right := p.PointAtDistanceAndBearing(dist, geoBoxRightBearing)
	bottom := p.PointAtDistanceAndBearing(dist, geoBoxBottomBearing)
	left := p.PointAtDistanceAndBearing(dist, geoBoxLeftBearing)

	topLeft := NewGeoPoint(top.Lat, left.Lng)
	bottomRight := NewGeoPoint(bottom.Lat, right.Lng)
	return topLeft, bottomRight
}

func CalBox(lat, lng float64, distance float64) (*GeoPoint, *GeoPoint) {
	return NewGeoPoint(lat, lng).CalBox(distance)
}
