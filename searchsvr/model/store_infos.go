package model

import (
	"context"
	"math"
	"sort"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

type StoreInfos []*StoreInfo

// 相关性分数初始-10000，不调用也是-10000（为啥不用0：因为有一些case算出来的分数就是0，无法区分是default还是calc）
const DefaultNonConfiguredPRelevanceScore = -10000

func (s StoreInfos) StoreInfoMap() map[uint64]*StoreInfo {
	storeMap := make(map[uint64]*StoreInfo, len(s))
	for _, store := range s {
		if store == nil {
			continue
		}
		storeMap[store.StoreId] = store
	}
	return storeMap
}

func (s StoreInfos) StoreIDs() []uint64 {
	ids := make([]uint64, 0, len(s))
	for _, store := range s {
		if store == nil {
			continue
		}
		ids = append(ids, store.StoreId)
	}
	return ids
}

func (s StoreInfos) DistinctStoreIDs() []uint64 {
	idSet := make(map[uint64]struct{}, len(s))
	ids := make([]uint64, 0, len(s))
	for _, store := range s {
		if store == nil {
			continue
		}
		if _, ok := idSet[store.StoreId]; ok {
			continue
		}
		idSet[store.StoreId] = struct{}{}
		ids = append(ids, store.StoreId)
	}
	return ids
}

func (s StoreInfos) DishLength() int {
	var dl int
	for _, store := range s {
		dl += len(store.DishInfos)
	}
	return dl
}

func (s StoreInfos) SortByESScore() StoreInfos {
	// 顺序
	// 1. ES 召回分数
	// 2. store id 升序
	sort.Slice(s, func(i, j int) bool {
		if !acc.Equal(s[i].ESScore, s[j].ESScore) {
			return s[i].ESScore > s[j].ESScore
		}
		// 距离相同，则 ID 升序
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

func (s StoreInfos) SortByRelevance(ctx context.Context, traceInfo *traceinfo.TraceInfo) StoreInfos {
	switch env.GetCID() {
	case cid.ID, cid.TH, cid.MY, cid.XX:
		return sortByRelevanceV2(s, traceInfo.OptIntervention.InterventionType) // 新排序&置顶、干预排序
	case cid.VN:
		if decision.IsUseOriginalReRank(traceInfo) == false {
			return sortByRelevanceVNV3(ctx, s, traceInfo) // 融合计算排序
		} else {
			return sortByRelevanceVNV1(ctx, s, traceInfo) // 迁移逻辑排序
		}
	default:
		return sortByRelevanceV2(s, traceInfo.OptIntervention.InterventionType)
	}
}

func (s StoreInfos) SortByRelevanceAfterLtr(ctx context.Context, traceInfo *traceinfo.TraceInfo) StoreInfos {
	return sortByRelevanceV2(s, traceInfo.OptIntervention.InterventionType)
}

func sortByRelevanceVNV1(ctx context.Context, s StoreInfos, traceInfo *traceinfo.TraceInfo) StoreInfos {
	// 顺序
	// 1. 门店状态分数
	// 2. 距离分
	sort.Slice(s, func(i, j int) bool {
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}

		openingStatueI := s[i].GetOpeningStateWithPrecise()
		openingStatueJ := s[j].GetOpeningStateWithPrecise()
		if openingStatueI != openingStatueJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) < 无可售卖菜品(3)
			return openingStatueI < openingStatueJ
		}

		if !acc.Equal(s[i].ReRankScore, s[j].ReRankScore) {
			return s[i].ReRankScore > s[j].ReRankScore // 微调算法分数降序
		}
		if !acc.Equal(s[i].OpeningScore, s[j].OpeningScore) {
			return s[i].OpeningScore > s[j].OpeningScore // 状态分数降序
		}
		if !acc.Equal(s[i].DistanceScore, s[j].DistanceScore) {
			return s[i].DistanceScore > s[j].DistanceScore // 距离分数降序
		}
		return s[i].StoreId < s[j].StoreId // ID 升序
	})
	return s
}

func sortByRelevanceVNV2(ctx context.Context, stores StoreInfos, traceInfo *traceinfo.TraceInfo, isWithoutBoostFactor bool) StoreInfos {
	sort.Slice(stores, func(i, j int) bool {
		IsInterForSortI := stores[i].IsInterForSort
		IsInterForSortJ := stores[j].IsInterForSort
		if IsInterForSortI != IsInterForSortJ {
			return IsInterForSortI > IsInterForSortJ
		}
		if IsInterForSortJ == 1 && traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 {
			return stores.LessForStoreIntention(i, j)
		}

		if stores[i].ExactMatch != stores[j].ExactMatch {
			return stores[i].ExactMatch > stores[j].ExactMatch // extra match 的置顶
		}
		openingStatueI := stores[i].GetOpeningStateWithPrecise()
		openingStatueJ := stores[j].GetOpeningStateWithPrecise()
		if openingStatueI != openingStatueJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) < 无可售卖菜品(3)
			return openingStatueI < openingStatueJ
		}
		if stores[i].SortPriority != stores[j].SortPriority {
			// 召回优先级排序，0的优先级最高
			return stores[i].SortPriority < stores[j].SortPriority
		}
		scoreI := stores[i].ReRankScore
		scoreJ := stores[j].ReRankScore
		if isWithoutBoostFactor {
			scoreI = stores[i].ReRankScoreWithoutSegmentBoost
			scoreJ = stores[j].ReRankScoreWithoutSegmentBoost
		}
		if !acc.Equal(scoreI, scoreJ) {
			// 分数按照降序排序
			return scoreI > scoreJ
		}
		return stores[i].StoreId < stores[j].StoreId
	})
	logger.MyDebug(ctx, traceInfo.IsDebug, "search result after ctcvr rerank", zap.Any("store infos", stores))
	return stores
}

// vn 考虑 store segment boost top N 排序逻辑： https://confluence.shopee.io/pages/viewpage.action?pageId=2614796925
func sortByRelevanceVNV3(ctx context.Context, stores StoreInfos, traceInfo *traceinfo.TraceInfo) StoreInfos {
	if decision.IsStoreSegmentBoostTopN(traceInfo) == false {
		return sortByRelevanceVNV2(ctx, stores, traceInfo, false) // ReRankScore 排序
	}

	stores = sortByRelevanceVNV2(ctx, stores, traceInfo, true) // ReRankScoreWithoutSegmentBoost 排序
	segmentTopN := traceInfo.AbParamClient.GetParamWithInt("Search.MultiFactor.StoreSegmentBoostTopN", 0)
	if segmentTopN >= len(stores) {
		return stores
	}
	stores1 := stores[:segmentTopN]
	stores2 := stores[segmentTopN:]
	stores2 = sortByRelevanceVNV2(ctx, stores2, traceInfo, false) // ReRankScore 排序, 里面有segment boost factor 因子

	stores = append(stores1, stores2...)
	logger.MyDebug(ctx, traceInfo.IsDebug, "search result after ctcvr rerank", zap.Any("store infos", stores))
	return stores
}

// ID,TH,MY new
func sortByRelevanceV2(s StoreInfos, interventionType int32) StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		// 干预排序
		IsInterForSortI := s[i].IsInterForSort
		IsInterForSortJ := s[j].IsInterForSort
		if IsInterForSortI != IsInterForSortJ {
			return IsInterForSortI > IsInterForSortJ
		}
		if IsInterForSortJ == 1 && interventionType == traceinfo.InterventionTypeTop1 {
			return s.LessForStoreIntention(i, j)
		}
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		scoreI := s[i].ReRankScore
		scoreJ := s[j].ReRankScore
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		storeRatingScoreI := s[i].RatingScore
		storeRatingScoreJ := s[j].RatingScore
		StoreSellWeekI := s[i].StoreSellWeek
		StoreSellWeekJ := s[j].StoreSellWeek
		relevanceLevelI := s[i].RelevanceLevelForSort
		relevanceLevelJ := s[j].RelevanceLevelForSort
		if relevanceLevelI != relevanceLevelJ {
			// 一档的门店优先级更高
			return relevanceLevelI > relevanceLevelJ
		}
		if !acc.Equal(scoreI, scoreJ) {
			// 分数按照降序排序
			return scoreI > scoreJ
		}

		if !acc.Equal(distanceI, distanceJ) {
			// 分数相同，按距离由近到远排序（升序）
			return distanceI < distanceJ
		}
		if !acc.Equal(storeRatingScoreI, storeRatingScoreJ) {
			// 距离相同，按照店铺分降序
			return storeRatingScoreI > storeRatingScoreJ
		}
		if StoreSellWeekI != StoreSellWeekJ {
			// 店铺分相同，按照门店销量降序
			return StoreSellWeekI > StoreSellWeekJ
		}
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

func (s StoreInfos) SortByNearby() StoreInfos {
	// 顺序
	// 1. 门店状态
	// 2. 距离
	// 3. partner(仅VN, ID 无)
	// 4. 总和分(ID rerank score,  VN es score)
	// 5. store id 升序
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		if !acc.Equal(distanceI, distanceJ) {
			return distanceI < distanceJ // 距离升序
		}
		if env.GetCID() == cid.VN && s[i].IsPartnerMerchant != s[j].IsPartnerMerchant {
			return s[i].IsPartnerMerchant > s[j].IsPartnerMerchant // partner 降序
		}
		scoreI := s[i].ReRankScore
		scoreJ := s[j].ReRankScore
		if env.GetCID() == cid.VN {
			scoreI = s[i].ESScore
			scoreJ = s[j].ESScore
		}
		if !acc.Equal(scoreI, scoreJ) {
			return scoreI > scoreJ
		}
		return s[i].StoreId < s[j].StoreId // ID 升序
	})
	return s
}

func (s StoreInfos) SortByNearbyV2() StoreInfos {
	// 顺序
	// 1. 门店状态
	// 2. 总和分
	// 3. store id 升序
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		newScoreI := s[i].NotRelTabScore
		newScoreJ := s[j].NotRelTabScore
		if !acc.Equal(newScoreI, newScoreJ) {
			return newScoreI > newScoreJ
		}
		scoreI := s[i].ReRankScore
		scoreJ := s[j].ReRankScore
		if !acc.Equal(scoreI, scoreJ) {
			return scoreI > scoreJ
		}
		return s[i].StoreId < s[j].StoreId // ID 升序
	})
	return s
}

func (s StoreInfos) SortByRating() StoreInfos {
	switch env.GetCID() {
	case cid.VN:
		return sortByRatingForVN(s)
	default:
		return sortByRatingForIDTHMY(s)
	}
}

func (s StoreInfos) SortByRatingV2() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		// 评分条数都>=10, 划分: 0～10km 和 10km以外，分别按 店铺评分>匹配分>距离>storeID
		if s[i].RatingTotal >= 10 && s[j].RatingTotal >= 10 {
			if DistanceLimit(distanceI, distanceJ) {
				newScoreI := s[i].NotRelTabScore
				newScoreJ := s[j].NotRelTabScore
				if !acc.Equal(newScoreI, newScoreJ) {
					return newScoreI > newScoreJ
				}
				scoreI := s[i].ReRankScore
				scoreJ := s[j].ReRankScore

				if !acc.Equal(scoreI, scoreJ) {
					return scoreI > scoreJ
				}
				if !acc.Equal(distanceI, distanceJ) {
					return distanceI < distanceJ
				}
				return s[i].StoreId < s[j].StoreId
			}
			return distanceI < distanceJ
		}
		// 评分条数<10, 分数 > 距离 > storeID
		if s[i].RatingTotal < 10 && s[j].RatingTotal < 10 {
			scoreI := s[i].ReRankScore
			scoreJ := s[j].ReRankScore

			if !acc.Equal(scoreI, scoreJ) {
				return scoreI > scoreJ
			}

			if !acc.Equal(distanceI, distanceJ) {
				return distanceI < distanceJ
			}

			// ID 升序
			return s[i].StoreId < s[j].StoreId
		}
		// 评分条数多的门店排前面
		return s[i].RatingTotal > s[j].RatingTotal
	})
	return s
}

func sortByRatingForVN(s StoreInfos) StoreInfos {
	// 顺序
	// 1. 门店状态
	// 2. 评论条数分组(大于等于5的优先)
	// 	2.1. 当评论数大于5的时候，还需增加距离分组:0-5, 5-10, 10+
	//  2.2. 当评论数大于5的时候，还需增加rating score 降序
	// 3. partner
	// 4. 总和分
	// 5. 距离升序
	// 6. store id 升序
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		if s[i].RatingTotalGroup5 != s[j].RatingTotalGroup5 {
			return s[i].RatingTotalGroup5 > s[j].RatingTotalGroup5 //评论数分组降序
		}
		if s[i].RatingTotalGroup5 == 2 {
			if s[i].DistanceGroup5km != s[j].DistanceGroup5km {
				return s[i].DistanceGroup5km < s[j].DistanceGroup5km // 距离分组升序
			}
			if !acc.Equal(s[i].RatingScore, s[j].RatingScore) {
				return s[i].RatingScore > s[j].RatingScore // rating score降序
			}
		}
		if s[i].IsPartnerMerchant != s[j].IsPartnerMerchant {
			return s[i].IsPartnerMerchant > s[j].IsPartnerMerchant // partner 降序
		}
		if !acc.Equal(s[i].ESScore, s[j].ESScore) {
			return s[i].ESScore > s[j].ESScore // ES 分数降序
		}
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		if !acc.Equal(distanceI, distanceJ) {
			return distanceI < distanceJ // 距离升序
		}
		return s[i].StoreId < s[j].StoreId //ID 升序
	})
	return s
}

func sortByRatingForIDTHMY(s StoreInfos) StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		statusI := s[i].status()
		statusJ := s[j].status()
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		if statusI != statusJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		// 评分条数都>=10, 划分: 0～10km 和 10km以外，分别按 店铺评分>匹配分>距离>storeID
		if s[i].RatingTotal >= 10 && s[j].RatingTotal >= 10 {
			if DistanceLimit(distanceI, distanceJ) {
				if !acc.Equal(s[i].RatingScore, s[j].RatingScore) {
					return s[i].RatingScore > s[j].RatingScore
				}
				scoreI := s[i].ReRankScore
				scoreJ := s[j].ReRankScore

				if !acc.Equal(scoreI, scoreJ) {
					return scoreI > scoreJ
				}
				if !acc.Equal(distanceI, distanceJ) {
					return distanceI < distanceJ
				}
				return s[i].StoreId < s[j].StoreId
			}
			return distanceI < distanceJ
		}
		// 评分条数<10, 分数 > 距离 > storeID
		if s[i].RatingTotal < 10 && s[j].RatingTotal < 10 {
			scoreI := s[i].ReRankScore
			scoreJ := s[j].ReRankScore

			if !acc.Equal(scoreI, scoreJ) {
				return scoreI > scoreJ
			}

			if !acc.Equal(distanceI, distanceJ) {
				return distanceI < distanceJ
			}

			// ID 升序
			return s[i].StoreId < s[j].StoreId
		}
		// 评分条数多的门店排前面
		return s[i].RatingTotal > s[j].RatingTotal
	})
	return s
}

func (s StoreInfos) SortByTopSales() StoreInfos {
	switch env.GetCID() {
	case cid.VN:
		return sortByTopSalesForVN(s)
	default:
		return sortByTopSalesForIDTHMY(s)
	}
}

func (s StoreInfos) SortByTopSalesV2() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		if DistanceLimit(distanceI, distanceJ) {
			newScoreI := s[i].NotRelTabScore
			newScoreJ := s[j].NotRelTabScore
			if !acc.Equal(newScoreI, newScoreJ) {
				return newScoreI > newScoreJ
			}
			scoreI := s[i].ReRankScore
			scoreJ := s[j].ReRankScore

			if !acc.Equal(scoreI, scoreJ) {
				return scoreI > scoreJ
			}

			if !acc.Equal(distanceI, distanceJ) {
				return distanceI < distanceJ
			}

			// ID 升序
			return s[i].StoreId < s[j].StoreId
		}
		return distanceI < distanceJ
	})
	return s
}

func (s StoreInfos) GetAvgDistance() float64 {
	if len(s) == 0 {
		return 0.0
	}
	dis := 0.0
	for _, i := range s {
		dis += i.Distance
	}
	return dis / float64(len(s))
}

func (s StoreInfos) GetNearDistanceCnt(nearDistance float64) int {
	if len(s) == 0 {
		return 0
	}
	cnt := 0
	for _, s := range s {
		if s.Distance < nearDistance {
			cnt += 1
		}
	}
	return cnt
}

func (s StoreInfos) GetOpenStatusCnt() int {
	if len(s) == 0 {
		return 0
	}
	cnt := 0
	for _, s := range s {
		if s.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
			cnt += 1
		}
	}
	return cnt
}

func sortByTopSalesForVN(s StoreInfos) StoreInfos {
	// 顺序
	// 1. 门店状态
	// 2. 距离分组:0-5, 5-10, 10+
	// 3. 7天销量
	// 4. partner
	// 5. 总和分
	// 6. store id 升序
	sort.Slice(s, func(i, j int) bool {
		openingStatueI := s[i].GetOpeningState()
		openingStatueJ := s[j].GetOpeningState()
		if openingStatueI != openingStatueJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return openingStatueI < openingStatueJ
		}
		if s[i].DistanceGroup5km != s[j].DistanceGroup5km {
			return s[i].DistanceGroup5km < s[j].DistanceGroup5km // 距离分组升序
		}
		if s[i].StoreSellWeek != s[j].StoreSellWeek {
			return s[i].StoreSellWeek > s[j].StoreSellWeek // 销量降序
		}
		if s[i].IsPartnerMerchant != s[j].IsPartnerMerchant {
			return s[i].IsPartnerMerchant > s[j].IsPartnerMerchant //partner 降序
		}
		if !acc.Equal(s[i].ESScore, s[j].ESScore) {
			return s[i].ESScore > s[j].ESScore // ES 分数降序
		}
		return s[i].StoreId < s[j].StoreId // 则 ID 升序
	})
	return s
}

func sortByTopSalesForIDTHMY(s StoreInfos) StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		if DistanceLimit(distanceI, distanceJ) {
			if s[i].StoreSellWeek != s[j].StoreSellWeek {
				return s[i].StoreSellWeek > s[j].StoreSellWeek
			}
			scoreI := s[i].ReRankScore
			scoreJ := s[j].ReRankScore

			if !acc.Equal(scoreI, scoreJ) {
				return scoreI > scoreJ
			}

			if !acc.Equal(distanceI, distanceJ) {
				return distanceI < distanceJ
			}

			// ID 升序
			return s[i].StoreId < s[j].StoreId
		}
		return distanceI < distanceJ
	})
	return s
}

// ID search main site
func (s StoreInfos) SortMainSiteByRelevance(isDebug bool) StoreInfos {
	var maxStoreSellWeek uint64
	var minStoreSellWeek uint64
	minStoreSellWeek = math.MaxUint32
	scoreDenominator := MaxScoreDenominator

	for _, v := range s {
		if v.StoreSellWeek > maxStoreSellWeek {
			maxStoreSellWeek = v.StoreSellWeek
		}
		if v.StoreSellWeek < minStoreSellWeek {
			minStoreSellWeek = v.StoreSellWeek
		}
	}
	for k, v := range s {
		// 归一化门店销量
		storeSellDenominator := float64(maxStoreSellWeek - minStoreSellWeek)
		if storeSellDenominator == 0 {
			s[k].StoreSellScore = float64(1)
		} else {
			s[k].StoreSellScore = float64(v.StoreSellWeek-minStoreSellWeek) / storeSellDenominator
		}
		s[k].Score = v.Score / float64(scoreDenominator)
	}

	// debug模式拿到打分细节
	if isDebug {
		for _, v := range s {
			v.MainSiteScore = v.GetMainSiteScore()
			v.MainSiteScoreExpressionStr = v.GetMainSiteScoreStr()
		}
	}

	sort.Slice(s, func(i, j int) bool {
		storeIntentionI := s[i].StoreInterventionRecall
		storeIntentionJ := s[j].StoreInterventionRecall

		if storeIntentionI != storeIntentionJ {
			return storeIntentionI > storeIntentionJ
		}
		if storeIntentionI == 1 {
			return s.LessForStoreIntention(i, j)
		}
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		scoreI := s[i].GetMainSiteScore()
		scoreJ := s[j].GetMainSiteScore()
		distanceI := s[i].Distance
		distanceJ := s[j].Distance
		storeRatingScoreI := s[i].RatingScore
		storeRatingScoreJ := s[j].RatingScore
		StoreSellWeekI := s[i].StoreSellWeek
		StoreSellWeekJ := s[j].StoreSellWeek
		if !acc.Equal(scoreI, scoreJ) {
			// 分数按照降序排序
			return scoreI > scoreJ
		}

		if !acc.Equal(distanceI, distanceJ) {
			// 分数相同，按距离由近到远排序（升序）
			return distanceI < distanceJ
		}
		if !acc.Equal(storeRatingScoreI, storeRatingScoreJ) {
			// 距离相同，按照店铺分降序
			return storeRatingScoreI > storeRatingScoreJ
		}
		if StoreSellWeekI != StoreSellWeekJ {
			// 店铺分相同，按照门店销量降序
			return StoreSellWeekI > StoreSellWeekJ
		}

		return s[i].StoreId < s[j].StoreId
	})

	return s
}

func (s StoreInfos) SortByMergeScore() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		if !acc.Equal(s[i].MergeScore, s[j].MergeScore) {
			// 融合分数降序
			return s[i].MergeScore > s[j].MergeScore
		}
		if !acc.Equal(s[i].Distance, s[j].Distance) {
			// 距离升序
			return s[i].Distance < s[j].Distance
		}
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

func (s StoreInfos) SortByCoarseRankScore() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		if !acc.Equal(s[i].CoarseRankScore, s[j].CoarseRankScore) {
			// 融合分数降序
			return s[i].CoarseRankScore > s[j].CoarseRankScore
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		if !acc.Equal(s[i].Distance, s[j].Distance) {
			// 距离升序
			return s[i].Distance < s[j].Distance
		}
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

// ID search main site
func (s StoreInfos) SortMainSiteByLatest() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		return s[i].CreateTime > s[j].CreateTime
	})
	return s
}

// ID search main site
func (s StoreInfos) SortMainSiteByTopSales() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		return s[i].SalesVolume > s[j].SalesVolume
	})
	return s
}

// ID search main site
func (s StoreInfos) SortMainSiteByPriceDesc() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		return s[i].Price > s[j].Price
	})
	return s
}

// ID search main site
func (s StoreInfos) SortMainSiteByPriceAsc() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		return s[i].Price < s[j].Price
	})
	return s
}

func SortByRelevanceVNWithoutPriority(ctx context.Context, stores StoreInfos, traceInfo *traceinfo.TraceInfo) StoreInfos {
	sort.Slice(stores, func(i, j int) bool {
		IsInterForSortI := stores[i].IsInterForSort
		IsInterForSortJ := stores[j].IsInterForSort
		if IsInterForSortI != IsInterForSortJ {
			return IsInterForSortI > IsInterForSortJ
		}
		if IsInterForSortJ == 1 && traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 {
			return stores.LessForStoreIntention(i, j)
		}
		if stores[i].ExactMatch != stores[j].ExactMatch {
			return stores[i].ExactMatch > stores[j].ExactMatch // extra match 的置顶
		}
		openingStatueI := stores[i].GetOpeningStateWithPrecise()
		openingStatueJ := stores[j].GetOpeningStateWithPrecise()
		if openingStatueI != openingStatueJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) < 无可售卖菜品(3)
			return openingStatueI < openingStatueJ
		}
		//if stores[i].RecallPriority != stores[j].RecallPriority {
		//	// 召回优先级排序，0的优先级最高
		//	return stores[i].RecallPriority < stores[j].RecallPriority
		//}
		if stores[i].ReRankScore != stores[j].ReRankScore {
			return stores[i].ReRankScore > stores[j].ReRankScore
		}
		return stores[i].StoreId < stores[j].StoreId
	})
	logger.MyDebug(ctx, traceInfo.IsDebug, "search result after ctcvr rerank", zap.Any("store infos", stores))
	return stores
}

func SortByRelevanceVNWithEsScore(ctx context.Context, stores StoreInfos, traceInfo *traceinfo.TraceInfo) StoreInfos {
	sort.Slice(stores, func(i, j int) bool {
		IsInterForSortI := stores[i].IsInterForSort
		IsInterForSortJ := stores[j].IsInterForSort
		if IsInterForSortI != IsInterForSortJ {
			return IsInterForSortI > IsInterForSortJ
		}
		if IsInterForSortJ == 1 && traceInfo.OptIntervention.InterventionType == traceinfo.InterventionTypeTop1 {
			return stores.LessForStoreIntention(i, j)
		}
		if stores[i].ExactMatch != stores[j].ExactMatch {
			return stores[i].ExactMatch > stores[j].ExactMatch // extra match 的置顶
		}
		openingStatueI := stores[i].GetOpeningStateWithPrecise()
		openingStatueJ := stores[j].GetOpeningStateWithPrecise()
		if openingStatueI != openingStatueJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) < 无可售卖菜品(3)
			return openingStatueI < openingStatueJ
		}
		if stores[i].SortPriority != stores[j].SortPriority {
			// 召回优先级排序，0的优先级最高
			return stores[i].SortPriority < stores[j].SortPriority
		}
		if stores[i].ESScore != stores[j].ESScore {
			return stores[i].ESScore > stores[j].ESScore
		}
		return stores[i].StoreId < stores[j].StoreId
	})
	logger.MyDebug(ctx, traceInfo.IsDebug, "search result after ctcvr rerank", zap.Any("store infos", stores))
	return stores
}

func (s StoreInfos) SortByAffiliateRelevance() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		if s[i].ReRankScore != s[j].ReRankScore {
			return s[i].ReRankScore > s[j].ReRankScore
		}
		if s[i].RatingScore != s[j].RatingScore {
			return s[i].RatingScore > s[j].RatingScore
		}
		if s[i].StoreSellWeek != s[j].StoreSellWeek {
			return s[i].StoreSellWeek > s[j].StoreSellWeek // 销量降序
		}
		return s[i].StoreId > s[j].StoreId // ID降序, 兜底
	})
	return s
}

func (s StoreInfos) SortByAffiliateCommissionRate() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		if s[i].BaseCommissionRate != s[j].BaseCommissionRate {
			return s[i].BaseCommissionRate > s[j].BaseCommissionRate // BaseCommissionRate
		}
		if s[i].ReRankScore != s[j].ReRankScore {
			return s[i].ReRankScore > s[j].ReRankScore // 销量降序
		}
		return s[i].StoreId > s[j].StoreId // ID降序, 兜底
	})
	return s
}

func (s StoreInfos) SortByAffiliateTopSales() StoreInfos {
	sort.Slice(s, func(i, j int) bool {
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 分数相同，状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}
		if s[i].StoreSellWeek != s[j].StoreSellWeek {
			return s[i].StoreSellWeek > s[j].StoreSellWeek // 销量降序
		}
		if s[i].ReRankScore != s[j].ReRankScore {
			return s[i].ReRankScore > s[j].ReRankScore
		}
		return s[i].StoreId > s[j].StoreId // ID降序, 兜底
	})
	return s
}
