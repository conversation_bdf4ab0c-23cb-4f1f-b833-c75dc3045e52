package model

import (
	"encoding/json"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"math"
	"sort"

	"github.com/gogo/protobuf/proto"

	common "git.garena.com/shopee/o2o-intelligence/common/common-lib/math"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
)

var acc common.Accuracy = func() float64 { return 0.000001 }

// 菜品详细信息
type DishInfo struct {
	DishId                 uint64                            `json:"dish_id"`
	DishName               string                            `json:"dish_name"`
	DishNameNorm           string                            `json:"dish_name_norm"` //trim(lower(dish name))
	CatalogName            string                            `json:"catalog_name"`   // 菜单名
	StoreId                uint64                            `json:"store_id"`
	Picture                string                            `json:"picture"`
	MmsImage               string                            `json:"mms_image"`
	CrawlerPicture         string                            `json:"crawler_picture"`
	SalesVolume            uint32                            `json:"sales_volume"`
	ESScore                float64                           `json:"es_score"`
	Score                  float64                           `json:"score"`
	ScoreCalc              string                            `json:"score_calc"`                 //  debug模式下：怎么打得分
	DishTextMatchScore     float64                           `json:"dish_text_match_score"`      // 自算 tf 分数
	DishTextMatchScoreCalc string                            `json:"dish_text_match_score_calc"` // 自算 tf 分数，怎么打得分
	CreateTime             uint64                            `json:"create_time"`
	Price                  uint64                            `json:"-"` // 安全原因，不返回price敏感信息
	Available              foodalgo_search.Available         `json:"available"`
	ListingStatus          foodalgo_search.DishListingStatus `json:"listing_status"`
	Segments               []string                          `json:"-"`
	MaxWordSegments        []string                          `json:"-"`
	StoreIntents           []string                          `json:"store_intents"`
	HasPicture             bool                              `json:"has_picture"`
	SaleStatus             o2oalgo.DishSale                  `json:"sale_status"`
	OutOfStockFlag         o2oalgo.DishOutOfStock            `json:"out_of_stock_flag"`
	// 标识是否是补充召回的菜品, 区分 recall type的时候使用
	//IsDishSupplementRecall bool                          `json:"is_dish_supplement_recall"` // 补充挂菜remove
	DishRecallType              string                        `json:"dish_recall_type"`
	DishRecallTypes             []string                      `json:"dish_recall_types"`
	DishRecallPriorities        []int                         `json:"dish_recall_priorities"`
	DishRecallId                string                        `json:"dish_recall_id"`
	DishRecallIds               []string                      `json:"dish_recall_ids"`
	HideFlagForVn               bool                          `json:"hide_flag_for_vn"`
	TraceContext                o2oalgo.TraceContext          `json:"trace_context"`
	CommissionPlan              *o2oalgo.ItemToCommissionPlan `json:"commission_plan"`
	BaseCommissionRate          uint32                        `json:"base_commission_rate"`
	SearchNonHalalFlag          bool                          `json:"search_non_halal_flag"`
	ESMatchedQueries            []string                      `json:"es_matched_queries"`
	RecallPriority              int                           `json:"recall_priority"` // 可能被多路召回，其中最高的优先级 1 > 2 > 3
	DishParams                  map[string]interface{}        `json:"dish_params"`     // 菜品特征
	DishFeature                 *foodalgo_search.DishFeature  `json:"dish_feature"`
	IsFlashSale                 bool                          `json:"is_flash_sale"`
	IsFromES                    bool                          `json:"is_from_es"`
	IsFromFS                    bool                          `json:"is_from_fs"`
	FlashSaleDiscountPercentage int64                         `json:"flash_sale_discount_percentage"`
	FlashSaleIds                []uint64                      `json:"flash_sale_ids"`
	IsFlashSaleValid            bool                          `json:"is_flash_sale_valid"`
}

func (s DishInfo) GetIsFlashSale() int32 {
	if s.IsFlashSale && s.IsFlashSaleValid {
		return 1
	}
	return 0
}

func (s DishInfo) HasPictureToInt() int32 {
	switch s.HasPicture {
	case true:
		return 1
	case false:
		return 0
	}
	return 0
}

type DishInfos []*DishInfo

func (s DishInfos) StoreDishInfosMap() map[uint64]DishInfos {
	m := make(map[uint64]DishInfos, 0)
	for _, dish := range s {
		if dish == nil {
			continue
		}
		if dishes, exist := m[dish.StoreId]; !exist {
			dishes = make([]*DishInfo, 0)
			dishes = append(dishes, dish)
			m[dish.StoreId] = dishes
		} else {
			m[dish.StoreId] = append(m[dish.StoreId], dish)
		}
	}
	return m
}

func (s DishInfos) DishMap() map[uint64]*DishInfo {
	m := make(map[uint64]*DishInfo, 0)
	for _, dish := range s {
		if dish == nil {
			continue
		}
		m[dish.DishId] = dish

	}
	return m
}

func (s DishInfos) DishInfos2CachePB() []*foodalgo_search.RecallStore_DishInfos {
	dishInfosCachePB := make([]*foodalgo_search.RecallStore_DishInfos, 0, len(s))
	for _, infos := range s {
		dishInfosCachePB = append(dishInfosCachePB, infos.DishInfo2CachePB())
	}
	return dishInfosCachePB
}
func CachePB2DishInfos(s []*foodalgo_search.RecallStore_DishInfos) DishInfos {
	dishInfos := make(DishInfos, 0, len(s))
	for _, pb := range s {
		dishInfos = append(dishInfos, CachePB2DishInfo(pb))
	}
	return dishInfos
}

func (d *DishInfo) DishInfo2CachePB() *foodalgo_search.RecallStore_DishInfos {
	dishInfoPB := &foodalgo_search.RecallStore_DishInfos{
		DishId:          proto.Uint64(d.DishId),
		DishName:        proto.String(d.DishName),
		CatalogName:     proto.String(d.CatalogName),
		StoreId:         proto.Uint64(d.StoreId),
		Picture:         proto.String(d.Picture),
		SalesVolume:     proto.Uint32(d.SalesVolume),
		EsScore:         proto.Float64(d.ESScore),
		Score:           proto.Float64(d.Score),
		CreateTime:      proto.Uint64(d.CreateTime),
		Price:           proto.Uint64(d.Price),
		Available:       proto.Int32(int32(d.Available)),
		ListingStatus:   proto.Int32(int32(d.ListingStatus)),
		Segments:        d.Segments,
		MaxWordSegments: d.MaxWordSegments,
		StoreIntents:    d.StoreIntents,
		HasPicture:      proto.Bool(d.HasPicture),
		SaleStatus:      proto.Int32(int32(d.SaleStatus)),
		OutOfStockFlag:  proto.Int32(int32(d.OutOfStockFlag)),
	}
	return dishInfoPB
}
func CachePB2DishInfo(d *foodalgo_search.RecallStore_DishInfos) *DishInfo {
	dishInfo := &DishInfo{
		DishId:          d.GetDishId(),
		DishName:        d.GetDishName(),
		CatalogName:     d.GetCatalogName(),
		StoreId:         d.GetStoreId(),
		Picture:         d.GetPicture(),
		SalesVolume:     d.GetSalesVolume(),
		ESScore:         float64(d.GetEsScore()),
		Score:           float64(d.GetScore()),
		CreateTime:      d.GetCreateTime(),
		Price:           d.GetPrice(),
		Available:       foodalgo_search.Available(d.GetAvailable()),
		ListingStatus:   foodalgo_search.DishListingStatus(d.GetListingStatus()),
		Segments:        d.GetSegments(),
		MaxWordSegments: d.GetMaxWordSegments(),
		StoreIntents:    d.GetStoreIntents(),
		HasPicture:      d.GetHasPicture(),
		SaleStatus:      o2oalgo.DishSale(d.GetSaleStatus()),
		OutOfStockFlag:  o2oalgo.DishOutOfStock(d.GetOutOfStockFlag()),
	}
	return dishInfo
}
func SalesTimeStruct2CachePB(sales []*o2oalgo.TimeForSales) []*foodalgo_search.RecallStore_TimeForSales {
	if sales == nil {
		return nil
	}
	timeForSalesPB := make([]*foodalgo_search.RecallStore_TimeForSales, 0, len(sales))
	for _, sale := range sales {
		timeForSalesPB = append(timeForSalesPB, &foodalgo_search.RecallStore_TimeForSales{
			SaleStartTime: proto.Uint64(sale.GetSaleStartTime()),
			SaleEndTime:   proto.Uint64(sale.GetSaleEndTime()),
		})
	}
	return timeForSalesPB
}
func CachePB2SalesTimeStruct(sales []*foodalgo_search.RecallStore_TimeForSales) []*o2oalgo.TimeForSales {
	if sales == nil {
		return nil
	}
	timeForSales := make([]*o2oalgo.TimeForSales, 0, len(sales))
	for _, sale := range sales {
		timeForSales = append(timeForSales, &o2oalgo.TimeForSales{
			SaleStartTime: sale.GetSaleStartTime(),
			SaleEndTime:   sale.GetSaleEndTime(),
		})
	}
	return timeForSales
}

func (s DishInfos) SortByScore() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		scoreI := s[i].Score
		scoreJ := s[j].Score
		if !acc.Equal(scoreI, scoreJ) {
			return scoreI > scoreJ
		}
		return s[i].DishId < s[j].DishId
	})
	return s
}

func (s DishInfos) SortByPriorityAndId() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		if s[i].RecallPriority != s[j].RecallPriority {
			return s[i].RecallPriority < s[j].RecallPriority // RecallPriority 升序
		}
		dishIdI := s[i].DishId
		dishIdJ := s[j].DishId
		if dishIdI != dishIdJ {
			return dishIdI > dishIdJ
		} // Score 降序
		return s[i].DishId < s[j].DishId // ID 升序
	})
	return s
}

// RecallPriority 升序 > Score 降序 > ID 升序
func (s DishInfos) SortByPriorityAndScore() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		if s[i].RecallPriority != s[j].RecallPriority {
			return s[i].RecallPriority < s[j].RecallPriority // RecallPriority 升序
		}
		scoreI := s[i].Score
		scoreJ := s[j].Score
		if !acc.Equal(scoreI, scoreJ) {
			return scoreI > scoreJ
		} // Score 降序
		return s[i].DishId < s[j].DishId // ID 升序
	})
	return s
}

func (s DishInfos) Sort() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		scoreI := s[i].Score
		scoreJ := s[j].Score
		hasPictureI := s[i].HasPictureToInt()
		hasPictureJ := s[j].HasPictureToInt()
		salesVolumeI := s[i].SalesVolume
		salesVolumeJ := s[j].SalesVolume
		// 分数降序
		if !acc.Equal(scoreI, scoreJ) {
			return scoreI > scoreJ
		}
		// 分数相同，则是否有图片降序
		if hasPictureI != hasPictureJ {
			return hasPictureI > hasPictureJ
		}

		// 分数相同，都有图片，则根据销量降序
		if salesVolumeI != salesVolumeJ {
			return salesVolumeI > salesVolumeJ
		}
		return s[i].DishId < s[j].DishId
	})
	return s
}

func (s DishInfos) Len() int {
	return len(s)
}

func (s DishInfos) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

func (s DishInfos) Less(i, j int) bool {
	// 分数降序
	if s[i].Score > s[j].Score {
		return true
	}
	// 分数相同，则销量降序
	if s[i].Score == s[j].Score && s[i].SalesVolume > s[j].SalesVolume {
		return true
	}
	// 分数，销量都相同，则根据ID升序
	if s[i].Score == s[j].Score && s[i].SalesVolume == s[j].SalesVolume && s[i].DishId < s[j].DishId {
		return true
	}
	return false
}

// Truncate 截取前n个
func (s DishInfos) Truncate(n int) DishInfos {
	if len(s) == 0 {
		return nil
	}

	end := n
	if len(s) < end {
		end = len(s)
	}
	return s[:end]
}

func (s DishInfos) DishIds() []uint64 {
	if s == nil {
		return nil
	}
	ids := make([]uint64, 0, len(s))
	for _, dish := range s {
		if dish == nil {
			continue
		}

		ids = append(ids, dish.DishId)
	}
	return ids
}

func (s DishInfos) SortByStatusSales() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		if s[i].SaleStatus != s[j].SaleStatus {
			return s[i].SaleStatus > s[j].SaleStatus // SaleStatus 1-可售，0-不可售
		}
		if s[i].OutOfStockFlag != s[j].OutOfStockFlag {
			return s[i].OutOfStockFlag < s[j].OutOfStockFlag // OutOfStockFlag 0-有库存，1-无库存
		}
		if s[i].SalesVolume != s[j].SalesVolume {
			return s[i].SalesVolume > s[j].SalesVolume // 销量降序
		}
		return s[i].DishId > s[j].DishId // ID降序, 兜底
	})
	return s
}

func (d *DishInfo) GetSalesVolumeScore() float64 {
	truncSalesVolume := 1.0
	if apollo.SearchApolloCfg.MainSiteDishTruncSalesVolume > 0.0 {
		truncSalesVolume = apollo.SearchApolloCfg.MainSiteDishTruncSalesVolume
	}

	salesVolumeScore := math.Min(float64(d.SalesVolume), truncSalesVolume) / truncSalesVolume
	return salesVolumeScore
}

func (d *DishInfo) GetDishTextMatchScore(isDebug bool, query string, rewriteSet []string, dishName string, catalogName string) (float64, string) {
	fields := append(util.RemoveDuplicates(util.LowerAndSplitToWords(dishName)), util.RemoveDuplicates(util.LowerAndSplitToWords(catalogName))...)

	// 构造 Q+ 集合，包括原始查询 query 和改写集合 rewriteSet
	Q := append([]string{query}, rewriteSet...)

	// 初始化最大得分
	maxScore := 0.0

	// 遍历 Q+ 集合中的每个查询词，计算 R(t, F) 的最大值
	for _, q := range Q {
		score := util.TfScore(q, fields)
		if score > maxScore {
			maxScore = score
		}
	}

	// default = 5.0
	truncDishTextMatchScore := apollo.SearchApolloCfg.MainSiteDishTruncTextMatchScore
	if truncDishTextMatchScore == 0 {
		truncDishTextMatchScore = 5.0
	}

	// 对得分进行归一化，归一化公式为：min(dish_text_match_score, trunc_dish_text_match_score) / trunc_dish_text_match_score
	normalizedScore := util.MinFloat64(maxScore, truncDishTextMatchScore) / truncDishTextMatchScore

	if isDebug {
		// 构造 JSON 数据
		data := map[string]interface{}{
			"query":           query,
			"rewriteSet":      rewriteSet,
			"dishName":        dishName,
			"catalogName":     catalogName,
			"fields":          fields,
			"maxScore":        maxScore,
			"truncMatchScore": truncDishTextMatchScore,
			"normalizedScore": normalizedScore,
		}

		// 序列化 JSON
		jsonData, _ := json.Marshal(data)
		return normalizedScore, string(jsonData)
	}

	return normalizedScore, ""
}

func (s DishInfos) SortByDishCommissionRate() DishInfos {
	sort.Slice(s, func(i, j int) bool {
		if s[i].BaseCommissionRate != s[j].BaseCommissionRate {
			return s[i].BaseCommissionRate > s[j].BaseCommissionRate // BaseCommissionRate
		}
		if s[i].SalesVolume != s[j].SalesVolume {
			return s[i].SalesVolume > s[j].SalesVolume // 销量降序
		}
		return s[i].DishId > s[j].DishId // ID降序, 兜底
	})
	return s
}

// 用来相关性模型特征排序，避免影响原有顺序
type DishNameNormInfo struct {
	DishId       uint64 `json:"dish_id"`
	DishNameNorm string `json:"dish_name_norm"` //trim(lower(dish name))
}

func (s DishInfos) DishRecallFromESCount() int {
	if len(s) == 0 {
		return 0
	}
	cnt := 0
	for _, dish := range s {
		if dish == nil {
			continue
		}
		if dish.IsFromES {
			cnt++
		}
	}
	return cnt
}

func (s DishInfos) DishRecallFromFSCount() int {
	if len(s) == 0 {
		return 0
	}
	cnt := 0
	for _, dish := range s {
		if dish == nil {
			continue
		}
		if dish.IsFromFS {
			cnt++
		}
	}
	return cnt
}

func (s DishInfos) DeepClone() DishInfos {
	if len(s) == 0 {
		return nil
	}
	// 在赋值前创建深拷贝
	dishesClone := make([]*DishInfo, len(s))
	for i, dish := range s {
		dishCopy := *dish // 复制结构体
		dishesClone[i] = &dishCopy
	}
	return dishesClone
}
