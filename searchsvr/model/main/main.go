package main

import (
	"fmt"
	"strings"
	"unicode"
)

// tfScore 计算 R(t, F)，即查询词 term 中的所有子词在字段集合 fields 中出现的总次数
func tfScore(term string, fields []string) int {
	fmt.Println("fields:", fields) // 打印字段信息，便于调试
	count := 0

	// 将查询词按空格切分为子词
	terms := strings.Fields(strings.ToLower(term)) // 转为小写后切词
	fmt.Println("split terms:", terms)             // 打印切分后的子词

	for _, subTerm := range terms {
		for _, field := range fields {
			// 统计子词在字段中出现的次数
			if strings.EqualFold(field, subTerm) {
				count++
			}
		}
	}
	fmt.Println("term:", term, ", count:", count) // 打印查询词和统计结果
	return count
}

// textMatchScore 计算最终的文本匹配分数
func textMatchScore(query string, rewriteSet []string, fields []string, truncDishTextMatchScore int) float64 {
	// 构造 Q+ 集合，包括原始查询 query 和改写集合 rewriteSet
	Q := append([]string{query}, rewriteSet...)
	fmt.Println("all Q:", Q)

	// 初始化最大得分
	maxScore := 0
	// 遍历 Q+ 集合中的每个查询词，计算 R(t, F) 的最大值
	for _, q := range Q {
		fmt.Println("############")
		fmt.Println("every item in Q:", q)
		score := tfScore(q, fields)
		if score > maxScore {
			maxScore = score
		}
	}

	// 对得分进行归一化，归一化公式为：min(dish_text_match_score, trunc_dish_text_match_score) / trunc_dish_text_match_score
	normalizedScore := float64(min(maxScore, truncDishTextMatchScore)) / float64(truncDishTextMatchScore)
	return normalizedScore
}

// min 辅助函数，返回两个整数中的最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// 将字段切分为单词数组
func splitToWords(field string) []string {
	// 使用 FieldsFunc 按单词切分，支持保留特殊字符如连字符
	return strings.FieldsFunc(field, func(r rune) bool {
		return !unicode.IsLetter(r) && !unicode.IsDigit(r)
	})
}

func main() {
	// 示例输入数据
	query := "ice coffee"                         // 原始查询词
	rewriteSet := []string{"es kopi", "ice kopi"} // 改写词集合
	dishName := "Ice Kopi Susu Keluarga-OL"       // 菜品名称
	menuName := "Promo Coffee"                    // 菜单名称
	truncDishTextMatchScore := 5                  // 截断的最大匹配分数

	// 将 dishName 和 menuName 切分为单词数组，并拼接为 fields
	fields := append(splitToWords(dishName), splitToWords(menuName)...)

	// 计算最终的文本匹配分数
	finalTextMatch := textMatchScore(query, rewriteSet, fields, truncDishTextMatchScore)

	// 输出结果
	fmt.Printf("最终文本匹配分数: %.2f\n", finalTextMatch)
}
