// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: geo_routing.proto

/*
	Package geo_routing is a generated protocol buffer package.

	It is generated from these files:
		geo_routing.proto

	It has these top-level messages:
		Constant
		Location
		Step
		Leg
		Route
		Waypoint
		LocationError
		DirectionsRequest
		DirectionsResponse
		DirectionsErrorInfo
		MatrixRequest
		MatrixResponse
		MatrixRow
		MatrixElement
		MatrixErrorInfo
*/
package geo_routing

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	io "io"
	math "math"

	proto "github.com/golang/protobuf/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// ******** - ********
type Constant_ErrorCode int32

const (
	Constant_ERROR_REQUEST_ASSERTION              Constant_ErrorCode = ********
	Constant_ERROR_RESPONSE_ASSERTION             Constant_ErrorCode = ********
	Constant_ERROR_NOT_IMPLEMENTED                Constant_ErrorCode = ********
	Constant_ERROR_INVALID_LOCATION               Constant_ErrorCode = ********
	Constant_ERROR_NO_RESULT                      Constant_ErrorCode = 49700005
	Constant_ERROR_MAX_WAYPOINTS_EXCEEDED         Constant_ErrorCode = 49700006
	Constant_ERROR_ROUTE_TOO_LONG                 Constant_ErrorCode = 49700007
	Constant_ERROR_CROSS_COUNTRY_NOT_ALLOWED      Constant_ErrorCode = 49700008
	Constant_ERROR_OVER_QUERY_LIMIT               Constant_ErrorCode = 49700009
	Constant_ERROR_MISSING_GOOGLE_API_KEY         Constant_ErrorCode = 49700010
	Constant_ERROR_MAX_MATRIX_ELEMENTS_EXCEEDED   Constant_ErrorCode = 49700011
	Constant_ERROR_MULTIPLE_LOCATION_ERROR_STATUS Constant_ErrorCode = 49700012
)

var Constant_ErrorCode_name = map[int32]string{
	********: "ERROR_REQUEST_ASSERTION",
	********: "ERROR_RESPONSE_ASSERTION",
	********: "ERROR_NOT_IMPLEMENTED",
	********: "ERROR_INVALID_LOCATION",
	49700005: "ERROR_NO_RESULT",
	49700006: "ERROR_MAX_WAYPOINTS_EXCEEDED",
	49700007: "ERROR_ROUTE_TOO_LONG",
	49700008: "ERROR_CROSS_COUNTRY_NOT_ALLOWED",
	49700009: "ERROR_OVER_QUERY_LIMIT",
	49700010: "ERROR_MISSING_GOOGLE_API_KEY",
	49700011: "ERROR_MAX_MATRIX_ELEMENTS_EXCEEDED",
	49700012: "ERROR_MULTIPLE_LOCATION_ERROR_STATUS",
}
var Constant_ErrorCode_value = map[string]int32{
	"ERROR_REQUEST_ASSERTION":              ********,
	"ERROR_RESPONSE_ASSERTION":             ********,
	"ERROR_NOT_IMPLEMENTED":                ********,
	"ERROR_INVALID_LOCATION":               ********,
	"ERROR_NO_RESULT":                      49700005,
	"ERROR_MAX_WAYPOINTS_EXCEEDED":         49700006,
	"ERROR_ROUTE_TOO_LONG":                 49700007,
	"ERROR_CROSS_COUNTRY_NOT_ALLOWED":      49700008,
	"ERROR_OVER_QUERY_LIMIT":               49700009,
	"ERROR_MISSING_GOOGLE_API_KEY":         49700010,
	"ERROR_MAX_MATRIX_ELEMENTS_EXCEEDED":   49700011,
	"ERROR_MULTIPLE_LOCATION_ERROR_STATUS": 49700012,
}

func (x Constant_ErrorCode) Enum() *Constant_ErrorCode {
	p := new(Constant_ErrorCode)
	*p = x
	return p
}
func (x Constant_ErrorCode) String() string {
	return proto.EnumName(Constant_ErrorCode_name, int32(x))
}
func (x *Constant_ErrorCode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_ErrorCode_value, data, "Constant_ErrorCode")
	if err != nil {
		return err
	}
	*x = Constant_ErrorCode(value)
	return nil
}
func (Constant_ErrorCode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeoRouting, []int{0, 0}
}

// Other success status not defined in SPEX
type Constant_SuccessStatus int32

const (
	Constant_PARTIAL_SUCCESS Constant_SuccessStatus = 1
)

var Constant_SuccessStatus_name = map[int32]string{
	1: "PARTIAL_SUCCESS",
}
var Constant_SuccessStatus_value = map[string]int32{
	"PARTIAL_SUCCESS": 1,
}

func (x Constant_SuccessStatus) Enum() *Constant_SuccessStatus {
	p := new(Constant_SuccessStatus)
	*p = x
	return p
}
func (x Constant_SuccessStatus) String() string {
	return proto.EnumName(Constant_SuccessStatus_name, int32(x))
}
func (x *Constant_SuccessStatus) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_SuccessStatus_value, data, "Constant_SuccessStatus")
	if err != nil {
		return err
	}
	*x = Constant_SuccessStatus(value)
	return nil
}
func (Constant_SuccessStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeoRouting, []int{0, 1}
}

type Constant_ClientType int32

const (
	Constant_CLIENT_TYPE_INTERNAL Constant_ClientType = 1
	Constant_CLIENT_TYPE_USER     Constant_ClientType = 2
)

var Constant_ClientType_name = map[int32]string{
	1: "CLIENT_TYPE_INTERNAL",
	2: "CLIENT_TYPE_USER",
}
var Constant_ClientType_value = map[string]int32{
	"CLIENT_TYPE_INTERNAL": 1,
	"CLIENT_TYPE_USER":     2,
}

func (x Constant_ClientType) Enum() *Constant_ClientType {
	p := new(Constant_ClientType)
	*p = x
	return p
}
func (x Constant_ClientType) String() string {
	return proto.EnumName(Constant_ClientType_name, int32(x))
}
func (x *Constant_ClientType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_ClientType_value, data, "Constant_ClientType")
	if err != nil {
		return err
	}
	*x = Constant_ClientType(value)
	return nil
}
func (Constant_ClientType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeoRouting, []int{0, 2}
}

type Constant_Overview int32

const (
	Constant_SIMPLIFIED Constant_Overview = 1
	Constant_FULL       Constant_Overview = 2
)

var Constant_Overview_name = map[int32]string{
	1: "SIMPLIFIED",
	2: "FULL",
}
var Constant_Overview_value = map[string]int32{
	"SIMPLIFIED": 1,
	"FULL":       2,
}

func (x Constant_Overview) Enum() *Constant_Overview {
	p := new(Constant_Overview)
	*p = x
	return p
}
func (x Constant_Overview) String() string {
	return proto.EnumName(Constant_Overview_name, int32(x))
}
func (x *Constant_Overview) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_Overview_value, data, "Constant_Overview")
	if err != nil {
		return err
	}
	*x = Constant_Overview(value)
	return nil
}
func (Constant_Overview) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGeoRouting, []int{0, 3}
}

type Constant_Mode int32

const (
	Constant_CAR  Constant_Mode = 1
	Constant_BIKE Constant_Mode = 2
	Constant_FOOT Constant_Mode = 3
)

var Constant_Mode_name = map[int32]string{
	1: "CAR",
	2: "BIKE",
	3: "FOOT",
}
var Constant_Mode_value = map[string]int32{
	"CAR":  1,
	"BIKE": 2,
	"FOOT": 3,
}

func (x Constant_Mode) Enum() *Constant_Mode {
	p := new(Constant_Mode)
	*p = x
	return p
}
func (x Constant_Mode) String() string {
	return proto.EnumName(Constant_Mode_name, int32(x))
}
func (x *Constant_Mode) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_Mode_value, data, "Constant_Mode")
	if err != nil {
		return err
	}
	*x = Constant_Mode(value)
	return nil
}
func (Constant_Mode) EnumDescriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{0, 4} }

type Constant_Avoid int32

const (
	Constant_TOLL    Constant_Avoid = 1
	Constant_HIGHWAY Constant_Avoid = 2
	Constant_FERRY   Constant_Avoid = 3
)

var Constant_Avoid_name = map[int32]string{
	1: "TOLL",
	2: "HIGHWAY",
	3: "FERRY",
}
var Constant_Avoid_value = map[string]int32{
	"TOLL":    1,
	"HIGHWAY": 2,
	"FERRY":   3,
}

func (x Constant_Avoid) Enum() *Constant_Avoid {
	p := new(Constant_Avoid)
	*p = x
	return p
}
func (x Constant_Avoid) String() string {
	return proto.EnumName(Constant_Avoid_name, int32(x))
}
func (x *Constant_Avoid) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_Avoid_value, data, "Constant_Avoid")
	if err != nil {
		return err
	}
	*x = Constant_Avoid(value)
	return nil
}
func (Constant_Avoid) EnumDescriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{0, 5} }

type Constant_Source int32

const (
	Constant_GEO    Constant_Source = 1
	Constant_GOOGLE Constant_Source = 2
)

var Constant_Source_name = map[int32]string{
	1: "GEO",
	2: "GOOGLE",
}
var Constant_Source_value = map[string]int32{
	"GEO":    1,
	"GOOGLE": 2,
}

func (x Constant_Source) Enum() *Constant_Source {
	p := new(Constant_Source)
	*p = x
	return p
}
func (x Constant_Source) String() string {
	return proto.EnumName(Constant_Source_name, int32(x))
}
func (x *Constant_Source) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Constant_Source_value, data, "Constant_Source")
	if err != nil {
		return err
	}
	*x = Constant_Source(value)
	return nil
}
func (Constant_Source) EnumDescriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{0, 6} }

type Constant struct {
	XXX_unrecognized []byte `json:"-"`
}

func (m *Constant) Reset()                    { *m = Constant{} }
func (m *Constant) String() string            { return proto.CompactTextString(m) }
func (*Constant) ProtoMessage()               {}
func (*Constant) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{0} }

type Location struct {
	Lon              *float64 `protobuf:"fixed64,1,opt,name=lon" json:"lon,omitempty"`
	Lat              *float64 `protobuf:"fixed64,2,opt,name=lat" json:"lat,omitempty"`
	XXX_unrecognized []byte   `json:"-"`
}

func (m *Location) Reset()                    { *m = Location{} }
func (m *Location) String() string            { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()               {}
func (*Location) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{1} }

func (m *Location) GetLon() float64 {
	if m != nil && m.Lon != nil {
		return *m.Lon
	}
	return 0
}

func (m *Location) GetLat() float64 {
	if m != nil && m.Lat != nil {
		return *m.Lat
	}
	return 0
}

type Step struct {
	Distance         *uint64   `protobuf:"varint,1,opt,name=distance" json:"distance,omitempty"`
	Duration         *uint64   `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	Geometry         *string   `protobuf:"bytes,3,opt,name=geometry" json:"geometry,omitempty"`
	StartLocation    *Location `protobuf:"bytes,4,opt,name=start_location" json:"start_location,omitempty"`
	EndLocation      *Location `protobuf:"bytes,5,opt,name=end_location" json:"end_location,omitempty"`
	XXX_unrecognized []byte    `json:"-"`
}

func (m *Step) Reset()                    { *m = Step{} }
func (m *Step) String() string            { return proto.CompactTextString(m) }
func (*Step) ProtoMessage()               {}
func (*Step) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{2} }

func (m *Step) GetDistance() uint64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *Step) GetDuration() uint64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *Step) GetGeometry() string {
	if m != nil && m.Geometry != nil {
		return *m.Geometry
	}
	return ""
}

func (m *Step) GetStartLocation() *Location {
	if m != nil {
		return m.StartLocation
	}
	return nil
}

func (m *Step) GetEndLocation() *Location {
	if m != nil {
		return m.EndLocation
	}
	return nil
}

type Leg struct {
	Distance         *uint64   `protobuf:"varint,1,opt,name=distance" json:"distance,omitempty"`
	Duration         *uint64   `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	StartLocation    *Location `protobuf:"bytes,4,opt,name=start_location" json:"start_location,omitempty"`
	EndLocation      *Location `protobuf:"bytes,5,opt,name=end_location" json:"end_location,omitempty"`
	Steps            []*Step   `protobuf:"bytes,6,rep,name=steps" json:"steps,omitempty"`
	XXX_unrecognized []byte    `json:"-"`
}

func (m *Leg) Reset()                    { *m = Leg{} }
func (m *Leg) String() string            { return proto.CompactTextString(m) }
func (*Leg) ProtoMessage()               {}
func (*Leg) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{3} }

func (m *Leg) GetDistance() uint64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *Leg) GetDuration() uint64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *Leg) GetStartLocation() *Location {
	if m != nil {
		return m.StartLocation
	}
	return nil
}

func (m *Leg) GetEndLocation() *Location {
	if m != nil {
		return m.EndLocation
	}
	return nil
}

func (m *Leg) GetSteps() []*Step {
	if m != nil {
		return m.Steps
	}
	return nil
}

type Route struct {
	Distance         *uint64 `protobuf:"varint,1,opt,name=distance" json:"distance,omitempty"`
	Duration         *uint64 `protobuf:"varint,2,opt,name=duration" json:"duration,omitempty"`
	Geometry         *string `protobuf:"bytes,3,opt,name=geometry" json:"geometry,omitempty"`
	Legs             []*Leg  `protobuf:"bytes,4,rep,name=legs" json:"legs,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *Route) Reset()                    { *m = Route{} }
func (m *Route) String() string            { return proto.CompactTextString(m) }
func (*Route) ProtoMessage()               {}
func (*Route) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{4} }

func (m *Route) GetDistance() uint64 {
	if m != nil && m.Distance != nil {
		return *m.Distance
	}
	return 0
}

func (m *Route) GetDuration() uint64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *Route) GetGeometry() string {
	if m != nil && m.Geometry != nil {
		return *m.Geometry
	}
	return ""
}

func (m *Route) GetLegs() []*Leg {
	if m != nil {
		return m.Legs
	}
	return nil
}

type Waypoint struct {
	Location         *Location `protobuf:"bytes,1,opt,name=location" json:"location,omitempty"`
	XXX_unrecognized []byte    `json:"-"`
}

func (m *Waypoint) Reset()                    { *m = Waypoint{} }
func (m *Waypoint) String() string            { return proto.CompactTextString(m) }
func (*Waypoint) ProtoMessage()               {}
func (*Waypoint) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{5} }

func (m *Waypoint) GetLocation() *Location {
	if m != nil {
		return m.Location
	}
	return nil
}

type LocationError struct {
	Status           *string `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *LocationError) Reset()                    { *m = LocationError{} }
func (m *LocationError) String() string            { return proto.CompactTextString(m) }
func (*LocationError) ProtoMessage()               {}
func (*LocationError) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{6} }

func (m *LocationError) GetStatus() string {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return ""
}

type DirectionsRequest struct {
	Origin           *Location   `protobuf:"bytes,1,opt,name=origin" json:"origin,omitempty"`
	Destination      *Location   `protobuf:"bytes,2,opt,name=destination" json:"destination,omitempty"`
	Waypoints        []*Location `protobuf:"bytes,3,rep,name=waypoints" json:"waypoints,omitempty"`
	Source           *string     `protobuf:"bytes,4,opt,name=source" json:"source,omitempty"`
	Mode             *string     `protobuf:"bytes,5,opt,name=mode" json:"mode,omitempty"`
	Region           *string     `protobuf:"bytes,6,opt,name=region" json:"region,omitempty"`
	Steps            *bool       `protobuf:"varint,7,opt,name=steps" json:"steps,omitempty"`
	DepartureTime    *uint64     `protobuf:"varint,8,opt,name=departure_time" json:"departure_time,omitempty"`
	Alternatives     *bool       `protobuf:"varint,9,opt,name=alternatives" json:"alternatives,omitempty"`
	Avoid            []string    `protobuf:"bytes,10,rep,name=avoid" json:"avoid,omitempty"`
	Overview         *string     `protobuf:"bytes,11,opt,name=overview" json:"overview,omitempty"`
	User             *string     `protobuf:"bytes,12,opt,name=user" json:"user,omitempty"`
	Project          *string     `protobuf:"bytes,13,opt,name=project" json:"project,omitempty"`
	ClientKey        *string     `protobuf:"bytes,14,opt,name=client_key" json:"client_key,omitempty"`
	XXX_unrecognized []byte      `json:"-"`
}

func (m *DirectionsRequest) Reset()                    { *m = DirectionsRequest{} }
func (m *DirectionsRequest) String() string            { return proto.CompactTextString(m) }
func (*DirectionsRequest) ProtoMessage()               {}
func (*DirectionsRequest) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{7} }

func (m *DirectionsRequest) GetOrigin() *Location {
	if m != nil {
		return m.Origin
	}
	return nil
}

func (m *DirectionsRequest) GetDestination() *Location {
	if m != nil {
		return m.Destination
	}
	return nil
}

func (m *DirectionsRequest) GetWaypoints() []*Location {
	if m != nil {
		return m.Waypoints
	}
	return nil
}

func (m *DirectionsRequest) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

func (m *DirectionsRequest) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *DirectionsRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *DirectionsRequest) GetSteps() bool {
	if m != nil && m.Steps != nil {
		return *m.Steps
	}
	return false
}

func (m *DirectionsRequest) GetDepartureTime() uint64 {
	if m != nil && m.DepartureTime != nil {
		return *m.DepartureTime
	}
	return 0
}

func (m *DirectionsRequest) GetAlternatives() bool {
	if m != nil && m.Alternatives != nil {
		return *m.Alternatives
	}
	return false
}

func (m *DirectionsRequest) GetAvoid() []string {
	if m != nil {
		return m.Avoid
	}
	return nil
}

func (m *DirectionsRequest) GetOverview() string {
	if m != nil && m.Overview != nil {
		return *m.Overview
	}
	return ""
}

func (m *DirectionsRequest) GetUser() string {
	if m != nil && m.User != nil {
		return *m.User
	}
	return ""
}

func (m *DirectionsRequest) GetProject() string {
	if m != nil && m.Project != nil {
		return *m.Project
	}
	return ""
}

func (m *DirectionsRequest) GetClientKey() string {
	if m != nil && m.ClientKey != nil {
		return *m.ClientKey
	}
	return ""
}

type DirectionsResponse struct {
	Status           *string              `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	Source           *string              `protobuf:"bytes,2,opt,name=source" json:"source,omitempty"`
	Waypoints        []*Waypoint          `protobuf:"bytes,3,rep,name=waypoints" json:"waypoints,omitempty"`
	Routes           []*Route             `protobuf:"bytes,4,rep,name=routes" json:"routes,omitempty"`
	ErrorMessage     *string              `protobuf:"bytes,100,opt,name=error_message" json:"error_message,omitempty"`
	ErrorInfo        *DirectionsErrorInfo `protobuf:"bytes,101,opt,name=error_info" json:"error_info,omitempty"`
	XXX_unrecognized []byte               `json:"-"`
}

func (m *DirectionsResponse) Reset()                    { *m = DirectionsResponse{} }
func (m *DirectionsResponse) String() string            { return proto.CompactTextString(m) }
func (*DirectionsResponse) ProtoMessage()               {}
func (*DirectionsResponse) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{8} }

func (m *DirectionsResponse) GetStatus() string {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return ""
}

func (m *DirectionsResponse) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

func (m *DirectionsResponse) GetWaypoints() []*Waypoint {
	if m != nil {
		return m.Waypoints
	}
	return nil
}

func (m *DirectionsResponse) GetRoutes() []*Route {
	if m != nil {
		return m.Routes
	}
	return nil
}

func (m *DirectionsResponse) GetErrorMessage() string {
	if m != nil && m.ErrorMessage != nil {
		return *m.ErrorMessage
	}
	return ""
}

func (m *DirectionsResponse) GetErrorInfo() *DirectionsErrorInfo {
	if m != nil {
		return m.ErrorInfo
	}
	return nil
}

type DirectionsErrorInfo struct {
	OriginError      *LocationError   `protobuf:"bytes,1,opt,name=origin_error" json:"origin_error,omitempty"`
	DestinationError *LocationError   `protobuf:"bytes,2,opt,name=destination_error" json:"destination_error,omitempty"`
	WaypointsErrors  []*LocationError `protobuf:"bytes,3,rep,name=waypoints_errors" json:"waypoints_errors,omitempty"`
	XXX_unrecognized []byte           `json:"-"`
}

func (m *DirectionsErrorInfo) Reset()                    { *m = DirectionsErrorInfo{} }
func (m *DirectionsErrorInfo) String() string            { return proto.CompactTextString(m) }
func (*DirectionsErrorInfo) ProtoMessage()               {}
func (*DirectionsErrorInfo) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{9} }

func (m *DirectionsErrorInfo) GetOriginError() *LocationError {
	if m != nil {
		return m.OriginError
	}
	return nil
}

func (m *DirectionsErrorInfo) GetDestinationError() *LocationError {
	if m != nil {
		return m.DestinationError
	}
	return nil
}

func (m *DirectionsErrorInfo) GetWaypointsErrors() []*LocationError {
	if m != nil {
		return m.WaypointsErrors
	}
	return nil
}

type MatrixRequest struct {
	Origins          []*Location `protobuf:"bytes,1,rep,name=origins" json:"origins,omitempty"`
	Destinations     []*Location `protobuf:"bytes,2,rep,name=destinations" json:"destinations,omitempty"`
	Source           *string     `protobuf:"bytes,4,opt,name=source" json:"source,omitempty"`
	Mode             *string     `protobuf:"bytes,5,opt,name=mode" json:"mode,omitempty"`
	Region           *string     `protobuf:"bytes,6,opt,name=region" json:"region,omitempty"`
	DepartureTime    *uint64     `protobuf:"varint,7,opt,name=departure_time" json:"departure_time,omitempty"`
	Avoid            []string    `protobuf:"bytes,8,rep,name=avoid" json:"avoid,omitempty"`
	User             *string     `protobuf:"bytes,9,opt,name=user" json:"user,omitempty"`
	Project          *string     `protobuf:"bytes,10,opt,name=project" json:"project,omitempty"`
	ClientKey        *string     `protobuf:"bytes,11,opt,name=client_key" json:"client_key,omitempty"`
	XXX_unrecognized []byte      `json:"-"`
}

func (m *MatrixRequest) Reset()                    { *m = MatrixRequest{} }
func (m *MatrixRequest) String() string            { return proto.CompactTextString(m) }
func (*MatrixRequest) ProtoMessage()               {}
func (*MatrixRequest) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{10} }

func (m *MatrixRequest) GetOrigins() []*Location {
	if m != nil {
		return m.Origins
	}
	return nil
}

func (m *MatrixRequest) GetDestinations() []*Location {
	if m != nil {
		return m.Destinations
	}
	return nil
}

func (m *MatrixRequest) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

func (m *MatrixRequest) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *MatrixRequest) GetRegion() string {
	if m != nil && m.Region != nil {
		return *m.Region
	}
	return ""
}

func (m *MatrixRequest) GetDepartureTime() uint64 {
	if m != nil && m.DepartureTime != nil {
		return *m.DepartureTime
	}
	return 0
}

func (m *MatrixRequest) GetAvoid() []string {
	if m != nil {
		return m.Avoid
	}
	return nil
}

func (m *MatrixRequest) GetUser() string {
	if m != nil && m.User != nil {
		return *m.User
	}
	return ""
}

func (m *MatrixRequest) GetProject() string {
	if m != nil && m.Project != nil {
		return *m.Project
	}
	return ""
}

func (m *MatrixRequest) GetClientKey() string {
	if m != nil && m.ClientKey != nil {
		return *m.ClientKey
	}
	return ""
}

type MatrixResponse struct {
	Status           *string          `protobuf:"bytes,1,opt,name=status" json:"status,omitempty"`
	Source           *string          `protobuf:"bytes,2,opt,name=source" json:"source,omitempty"`
	Origins          []*Waypoint      `protobuf:"bytes,3,rep,name=origins" json:"origins,omitempty"`
	Destinations     []*Waypoint      `protobuf:"bytes,4,rep,name=destinations" json:"destinations,omitempty"`
	Distances        []*MatrixRow     `protobuf:"bytes,5,rep,name=distances" json:"distances,omitempty"`
	Durations        []*MatrixRow     `protobuf:"bytes,6,rep,name=durations" json:"durations,omitempty"`
	ErrorMessage     *string          `protobuf:"bytes,100,opt,name=error_message" json:"error_message,omitempty"`
	ErrorInfo        *MatrixErrorInfo `protobuf:"bytes,101,opt,name=error_info" json:"error_info,omitempty"`
	XXX_unrecognized []byte           `json:"-"`
}

func (m *MatrixResponse) Reset()                    { *m = MatrixResponse{} }
func (m *MatrixResponse) String() string            { return proto.CompactTextString(m) }
func (*MatrixResponse) ProtoMessage()               {}
func (*MatrixResponse) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{11} }

func (m *MatrixResponse) GetStatus() string {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return ""
}

func (m *MatrixResponse) GetSource() string {
	if m != nil && m.Source != nil {
		return *m.Source
	}
	return ""
}

func (m *MatrixResponse) GetOrigins() []*Waypoint {
	if m != nil {
		return m.Origins
	}
	return nil
}

func (m *MatrixResponse) GetDestinations() []*Waypoint {
	if m != nil {
		return m.Destinations
	}
	return nil
}

func (m *MatrixResponse) GetDistances() []*MatrixRow {
	if m != nil {
		return m.Distances
	}
	return nil
}

func (m *MatrixResponse) GetDurations() []*MatrixRow {
	if m != nil {
		return m.Durations
	}
	return nil
}

func (m *MatrixResponse) GetErrorMessage() string {
	if m != nil && m.ErrorMessage != nil {
		return *m.ErrorMessage
	}
	return ""
}

func (m *MatrixResponse) GetErrorInfo() *MatrixErrorInfo {
	if m != nil {
		return m.ErrorInfo
	}
	return nil
}

type MatrixRow struct {
	Elements         []*MatrixElement `protobuf:"bytes,1,rep,name=elements" json:"elements,omitempty"`
	XXX_unrecognized []byte           `json:"-"`
}

func (m *MatrixRow) Reset()                    { *m = MatrixRow{} }
func (m *MatrixRow) String() string            { return proto.CompactTextString(m) }
func (*MatrixRow) ProtoMessage()               {}
func (*MatrixRow) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{12} }

func (m *MatrixRow) GetElements() []*MatrixElement {
	if m != nil {
		return m.Elements
	}
	return nil
}

type MatrixElement struct {
	Value            *uint64 `protobuf:"varint,1,opt,name=value" json:"value,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *MatrixElement) Reset()                    { *m = MatrixElement{} }
func (m *MatrixElement) String() string            { return proto.CompactTextString(m) }
func (*MatrixElement) ProtoMessage()               {}
func (*MatrixElement) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{13} }

func (m *MatrixElement) GetValue() uint64 {
	if m != nil && m.Value != nil {
		return *m.Value
	}
	return 0
}

type MatrixErrorInfo struct {
	OriginsErrors      []*LocationError `protobuf:"bytes,1,rep,name=origins_errors" json:"origins_errors,omitempty"`
	DestinationsErrors []*LocationError `protobuf:"bytes,2,rep,name=destinations_errors" json:"destinations_errors,omitempty"`
	XXX_unrecognized   []byte           `json:"-"`
}

func (m *MatrixErrorInfo) Reset()                    { *m = MatrixErrorInfo{} }
func (m *MatrixErrorInfo) String() string            { return proto.CompactTextString(m) }
func (*MatrixErrorInfo) ProtoMessage()               {}
func (*MatrixErrorInfo) Descriptor() ([]byte, []int) { return fileDescriptorGeoRouting, []int{14} }

func (m *MatrixErrorInfo) GetOriginsErrors() []*LocationError {
	if m != nil {
		return m.OriginsErrors
	}
	return nil
}

func (m *MatrixErrorInfo) GetDestinationsErrors() []*LocationError {
	if m != nil {
		return m.DestinationsErrors
	}
	return nil
}

func init() {
	proto.RegisterType((*Constant)(nil), "geo.routing.Constant")
	proto.RegisterType((*Location)(nil), "geo.routing.Location")
	proto.RegisterType((*Step)(nil), "geo.routing.Step")
	proto.RegisterType((*Leg)(nil), "geo.routing.Leg")
	proto.RegisterType((*Route)(nil), "geo.routing.Route")
	proto.RegisterType((*Waypoint)(nil), "geo.routing.Waypoint")
	proto.RegisterType((*LocationError)(nil), "geo.routing.LocationError")
	proto.RegisterType((*DirectionsRequest)(nil), "geo.routing.DirectionsRequest")
	proto.RegisterType((*DirectionsResponse)(nil), "geo.routing.DirectionsResponse")
	proto.RegisterType((*DirectionsErrorInfo)(nil), "geo.routing.DirectionsErrorInfo")
	proto.RegisterType((*MatrixRequest)(nil), "geo.routing.MatrixRequest")
	proto.RegisterType((*MatrixResponse)(nil), "geo.routing.MatrixResponse")
	proto.RegisterType((*MatrixRow)(nil), "geo.routing.MatrixRow")
	proto.RegisterType((*MatrixElement)(nil), "geo.routing.MatrixElement")
	proto.RegisterType((*MatrixErrorInfo)(nil), "geo.routing.MatrixErrorInfo")
	proto.RegisterEnum("geo.routing.Constant_ErrorCode", Constant_ErrorCode_name, Constant_ErrorCode_value)
	proto.RegisterEnum("geo.routing.Constant_SuccessStatus", Constant_SuccessStatus_name, Constant_SuccessStatus_value)
	proto.RegisterEnum("geo.routing.Constant_ClientType", Constant_ClientType_name, Constant_ClientType_value)
	proto.RegisterEnum("geo.routing.Constant_Overview", Constant_Overview_name, Constant_Overview_value)
	proto.RegisterEnum("geo.routing.Constant_Mode", Constant_Mode_name, Constant_Mode_value)
	proto.RegisterEnum("geo.routing.Constant_Avoid", Constant_Avoid_name, Constant_Avoid_value)
	proto.RegisterEnum("geo.routing.Constant_Source", Constant_Source_name, Constant_Source_value)
}
func (m *Constant) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Constant) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Location) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Location) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Lon != nil {
		dAtA[i] = 0x9
		i++
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(*m.Lon))))
		i += 8
	}
	if m.Lat != nil {
		dAtA[i] = 0x11
		i++
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(*m.Lat))))
		i += 8
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Step) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Step) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Distance != nil {
		dAtA[i] = 0x8
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Distance))
	}
	if m.Duration != nil {
		dAtA[i] = 0x10
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Duration))
	}
	if m.Geometry != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Geometry)))
		i += copy(dAtA[i:], *m.Geometry)
	}
	if m.StartLocation != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.StartLocation.Size()))
		n1, err := m.StartLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if m.EndLocation != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.EndLocation.Size()))
		n2, err := m.EndLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Leg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Leg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Distance != nil {
		dAtA[i] = 0x8
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Distance))
	}
	if m.Duration != nil {
		dAtA[i] = 0x10
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Duration))
	}
	if m.StartLocation != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.StartLocation.Size()))
		n3, err := m.StartLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	if m.EndLocation != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.EndLocation.Size()))
		n4, err := m.EndLocation.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	if len(m.Steps) > 0 {
		for _, msg := range m.Steps {
			dAtA[i] = 0x32
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Route) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Route) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Distance != nil {
		dAtA[i] = 0x8
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Distance))
	}
	if m.Duration != nil {
		dAtA[i] = 0x10
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Duration))
	}
	if m.Geometry != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Geometry)))
		i += copy(dAtA[i:], *m.Geometry)
	}
	if len(m.Legs) > 0 {
		for _, msg := range m.Legs {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *Waypoint) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Waypoint) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Location != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.Location.Size()))
		n5, err := m.Location.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *LocationError) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocationError) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Status != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Status)))
		i += copy(dAtA[i:], *m.Status)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *DirectionsRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DirectionsRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Origin != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.Origin.Size()))
		n6, err := m.Origin.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.Destination != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.Destination.Size()))
		n7, err := m.Destination.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	if len(m.Waypoints) > 0 {
		for _, msg := range m.Waypoints {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Source != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Source)))
		i += copy(dAtA[i:], *m.Source)
	}
	if m.Mode != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Mode)))
		i += copy(dAtA[i:], *m.Mode)
	}
	if m.Region != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Region)))
		i += copy(dAtA[i:], *m.Region)
	}
	if m.Steps != nil {
		dAtA[i] = 0x38
		i++
		if *m.Steps {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if m.DepartureTime != nil {
		dAtA[i] = 0x40
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.DepartureTime))
	}
	if m.Alternatives != nil {
		dAtA[i] = 0x48
		i++
		if *m.Alternatives {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	if len(m.Avoid) > 0 {
		for _, s := range m.Avoid {
			dAtA[i] = 0x52
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.Overview != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Overview)))
		i += copy(dAtA[i:], *m.Overview)
	}
	if m.User != nil {
		dAtA[i] = 0x62
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.User)))
		i += copy(dAtA[i:], *m.User)
	}
	if m.Project != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Project)))
		i += copy(dAtA[i:], *m.Project)
	}
	if m.ClientKey != nil {
		dAtA[i] = 0x72
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.ClientKey)))
		i += copy(dAtA[i:], *m.ClientKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *DirectionsResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DirectionsResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Status != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Status)))
		i += copy(dAtA[i:], *m.Status)
	}
	if m.Source != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Source)))
		i += copy(dAtA[i:], *m.Source)
	}
	if len(m.Waypoints) > 0 {
		for _, msg := range m.Waypoints {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Routes) > 0 {
		for _, msg := range m.Routes {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ErrorMessage != nil {
		dAtA[i] = 0xa2
		i++
		dAtA[i] = 0x6
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.ErrorMessage)))
		i += copy(dAtA[i:], *m.ErrorMessage)
	}
	if m.ErrorInfo != nil {
		dAtA[i] = 0xaa
		i++
		dAtA[i] = 0x6
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.ErrorInfo.Size()))
		n8, err := m.ErrorInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *DirectionsErrorInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DirectionsErrorInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OriginError != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.OriginError.Size()))
		n9, err := m.OriginError.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	if m.DestinationError != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.DestinationError.Size()))
		n10, err := m.DestinationError.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if len(m.WaypointsErrors) > 0 {
		for _, msg := range m.WaypointsErrors {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *MatrixRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatrixRequest) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Origins) > 0 {
		for _, msg := range m.Origins {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Destinations) > 0 {
		for _, msg := range m.Destinations {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.Source != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Source)))
		i += copy(dAtA[i:], *m.Source)
	}
	if m.Mode != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Mode)))
		i += copy(dAtA[i:], *m.Mode)
	}
	if m.Region != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Region)))
		i += copy(dAtA[i:], *m.Region)
	}
	if m.DepartureTime != nil {
		dAtA[i] = 0x38
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.DepartureTime))
	}
	if len(m.Avoid) > 0 {
		for _, s := range m.Avoid {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if m.User != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.User)))
		i += copy(dAtA[i:], *m.User)
	}
	if m.Project != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Project)))
		i += copy(dAtA[i:], *m.Project)
	}
	if m.ClientKey != nil {
		dAtA[i] = 0x5a
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.ClientKey)))
		i += copy(dAtA[i:], *m.ClientKey)
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *MatrixResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatrixResponse) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Status != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Status)))
		i += copy(dAtA[i:], *m.Status)
	}
	if m.Source != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.Source)))
		i += copy(dAtA[i:], *m.Source)
	}
	if len(m.Origins) > 0 {
		for _, msg := range m.Origins {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Destinations) > 0 {
		for _, msg := range m.Destinations {
			dAtA[i] = 0x22
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Distances) > 0 {
		for _, msg := range m.Distances {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Durations) > 0 {
		for _, msg := range m.Durations {
			dAtA[i] = 0x32
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.ErrorMessage != nil {
		dAtA[i] = 0xa2
		i++
		dAtA[i] = 0x6
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(len(*m.ErrorMessage)))
		i += copy(dAtA[i:], *m.ErrorMessage)
	}
	if m.ErrorInfo != nil {
		dAtA[i] = 0xaa
		i++
		dAtA[i] = 0x6
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(m.ErrorInfo.Size()))
		n11, err := m.ErrorInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *MatrixRow) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatrixRow) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Elements) > 0 {
		for _, msg := range m.Elements {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *MatrixElement) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatrixElement) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Value != nil {
		dAtA[i] = 0x8
		i++
		i = encodeVarintGeoRouting(dAtA, i, uint64(*m.Value))
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func (m *MatrixErrorInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatrixErrorInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OriginsErrors) > 0 {
		for _, msg := range m.OriginsErrors {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.DestinationsErrors) > 0 {
		for _, msg := range m.DestinationsErrors {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGeoRouting(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.XXX_unrecognized != nil {
		i += copy(dAtA[i:], m.XXX_unrecognized)
	}
	return i, nil
}

func encodeVarintGeoRouting(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *Constant) Size() (n int) {
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Location) Size() (n int) {
	var l int
	_ = l
	if m.Lon != nil {
		n += 9
	}
	if m.Lat != nil {
		n += 9
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Step) Size() (n int) {
	var l int
	_ = l
	if m.Distance != nil {
		n += 1 + sovGeoRouting(uint64(*m.Distance))
	}
	if m.Duration != nil {
		n += 1 + sovGeoRouting(uint64(*m.Duration))
	}
	if m.Geometry != nil {
		l = len(*m.Geometry)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.StartLocation != nil {
		l = m.StartLocation.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.EndLocation != nil {
		l = m.EndLocation.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Leg) Size() (n int) {
	var l int
	_ = l
	if m.Distance != nil {
		n += 1 + sovGeoRouting(uint64(*m.Distance))
	}
	if m.Duration != nil {
		n += 1 + sovGeoRouting(uint64(*m.Duration))
	}
	if m.StartLocation != nil {
		l = m.StartLocation.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.EndLocation != nil {
		l = m.EndLocation.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.Steps) > 0 {
		for _, e := range m.Steps {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Route) Size() (n int) {
	var l int
	_ = l
	if m.Distance != nil {
		n += 1 + sovGeoRouting(uint64(*m.Distance))
	}
	if m.Duration != nil {
		n += 1 + sovGeoRouting(uint64(*m.Duration))
	}
	if m.Geometry != nil {
		l = len(*m.Geometry)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.Legs) > 0 {
		for _, e := range m.Legs {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Waypoint) Size() (n int) {
	var l int
	_ = l
	if m.Location != nil {
		l = m.Location.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *LocationError) Size() (n int) {
	var l int
	_ = l
	if m.Status != nil {
		l = len(*m.Status)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DirectionsRequest) Size() (n int) {
	var l int
	_ = l
	if m.Origin != nil {
		l = m.Origin.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Destination != nil {
		l = m.Destination.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.Waypoints) > 0 {
		for _, e := range m.Waypoints {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Mode != nil {
		l = len(*m.Mode)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Region != nil {
		l = len(*m.Region)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Steps != nil {
		n += 2
	}
	if m.DepartureTime != nil {
		n += 1 + sovGeoRouting(uint64(*m.DepartureTime))
	}
	if m.Alternatives != nil {
		n += 2
	}
	if len(m.Avoid) > 0 {
		for _, s := range m.Avoid {
			l = len(s)
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.Overview != nil {
		l = len(*m.Overview)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.User != nil {
		l = len(*m.User)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Project != nil {
		l = len(*m.Project)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.ClientKey != nil {
		l = len(*m.ClientKey)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DirectionsResponse) Size() (n int) {
	var l int
	_ = l
	if m.Status != nil {
		l = len(*m.Status)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.Waypoints) > 0 {
		for _, e := range m.Waypoints {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.Routes) > 0 {
		for _, e := range m.Routes {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.ErrorMessage != nil {
		l = len(*m.ErrorMessage)
		n += 2 + l + sovGeoRouting(uint64(l))
	}
	if m.ErrorInfo != nil {
		l = m.ErrorInfo.Size()
		n += 2 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *DirectionsErrorInfo) Size() (n int) {
	var l int
	_ = l
	if m.OriginError != nil {
		l = m.OriginError.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.DestinationError != nil {
		l = m.DestinationError.Size()
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.WaypointsErrors) > 0 {
		for _, e := range m.WaypointsErrors {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MatrixRequest) Size() (n int) {
	var l int
	_ = l
	if len(m.Origins) > 0 {
		for _, e := range m.Origins {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.Destinations) > 0 {
		for _, e := range m.Destinations {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Mode != nil {
		l = len(*m.Mode)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Region != nil {
		l = len(*m.Region)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.DepartureTime != nil {
		n += 1 + sovGeoRouting(uint64(*m.DepartureTime))
	}
	if len(m.Avoid) > 0 {
		for _, s := range m.Avoid {
			l = len(s)
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.User != nil {
		l = len(*m.User)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Project != nil {
		l = len(*m.Project)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.ClientKey != nil {
		l = len(*m.ClientKey)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MatrixResponse) Size() (n int) {
	var l int
	_ = l
	if m.Status != nil {
		l = len(*m.Status)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if m.Source != nil {
		l = len(*m.Source)
		n += 1 + l + sovGeoRouting(uint64(l))
	}
	if len(m.Origins) > 0 {
		for _, e := range m.Origins {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.Destinations) > 0 {
		for _, e := range m.Destinations {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.Distances) > 0 {
		for _, e := range m.Distances {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.Durations) > 0 {
		for _, e := range m.Durations {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.ErrorMessage != nil {
		l = len(*m.ErrorMessage)
		n += 2 + l + sovGeoRouting(uint64(l))
	}
	if m.ErrorInfo != nil {
		l = m.ErrorInfo.Size()
		n += 2 + l + sovGeoRouting(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MatrixRow) Size() (n int) {
	var l int
	_ = l
	if len(m.Elements) > 0 {
		for _, e := range m.Elements {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MatrixElement) Size() (n int) {
	var l int
	_ = l
	if m.Value != nil {
		n += 1 + sovGeoRouting(uint64(*m.Value))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *MatrixErrorInfo) Size() (n int) {
	var l int
	_ = l
	if len(m.OriginsErrors) > 0 {
		for _, e := range m.OriginsErrors {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if len(m.DestinationsErrors) > 0 {
		for _, e := range m.DestinationsErrors {
			l = e.Size()
			n += 1 + l + sovGeoRouting(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovGeoRouting(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGeoRouting(x uint64) (n int) {
	return sovGeoRouting(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Constant) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Constant: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Constant: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Location) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Location: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Location: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lon", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			v2 := float64(math.Float64frombits(v))
			m.Lon = &v2
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Lat", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			v2 := float64(math.Float64frombits(v))
			m.Lat = &v2
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Step) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Step: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Step: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Distance", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Distance = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Duration = &v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Geometry", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Geometry = &s
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StartLocation == nil {
				m.StartLocation = &Location{}
			}
			if err := m.StartLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EndLocation == nil {
				m.EndLocation = &Location{}
			}
			if err := m.EndLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Leg) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Leg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Leg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Distance", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Distance = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Duration = &v
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StartLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.StartLocation == nil {
				m.StartLocation = &Location{}
			}
			if err := m.StartLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field EndLocation", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.EndLocation == nil {
				m.EndLocation = &Location{}
			}
			if err := m.EndLocation.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Steps", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Steps = append(m.Steps, &Step{})
			if err := m.Steps[len(m.Steps)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Route) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Route: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Route: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Distance", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Distance = &v
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Duration", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Duration = &v
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Geometry", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Geometry = &s
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Legs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Legs = append(m.Legs, &Leg{})
			if err := m.Legs[len(m.Legs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Waypoint) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Waypoint: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Waypoint: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Location", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Location == nil {
				m.Location = &Location{}
			}
			if err := m.Location.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocationError) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: LocationError: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: LocationError: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Status = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DirectionsRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DirectionsRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DirectionsRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Origin", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Origin == nil {
				m.Origin = &Location{}
			}
			if err := m.Origin.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Destination", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Destination == nil {
				m.Destination = &Location{}
			}
			if err := m.Destination.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Waypoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Waypoints = append(m.Waypoints, &Location{})
			if err := m.Waypoints[len(m.Waypoints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Mode = &s
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Region = &s
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Steps", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Steps = &b
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DepartureTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DepartureTime = &v
		case 9:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Alternatives", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			b := bool(v != 0)
			m.Alternatives = &b
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Avoid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Avoid = append(m.Avoid, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Overview", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Overview = &s
			iNdEx = postIndex
		case 12:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.User = &s
			iNdEx = postIndex
		case 13:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Project", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Project = &s
			iNdEx = postIndex
		case 14:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ClientKey = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DirectionsResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DirectionsResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DirectionsResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Status = &s
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Waypoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Waypoints = append(m.Waypoints, &Waypoint{})
			if err := m.Waypoints[len(m.Waypoints)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Routes", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Routes = append(m.Routes, &Route{})
			if err := m.Routes[len(m.Routes)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 100:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ErrorMessage = &s
			iNdEx = postIndex
		case 101:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ErrorInfo == nil {
				m.ErrorInfo = &DirectionsErrorInfo{}
			}
			if err := m.ErrorInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *DirectionsErrorInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: DirectionsErrorInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: DirectionsErrorInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OriginError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.OriginError == nil {
				m.OriginError = &LocationError{}
			}
			if err := m.OriginError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DestinationError", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.DestinationError == nil {
				m.DestinationError = &LocationError{}
			}
			if err := m.DestinationError.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field WaypointsErrors", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.WaypointsErrors = append(m.WaypointsErrors, &LocationError{})
			if err := m.WaypointsErrors[len(m.WaypointsErrors)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatrixRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MatrixRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MatrixRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Origins", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Origins = append(m.Origins, &Location{})
			if err := m.Origins[len(m.Origins)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Destinations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Destinations = append(m.Destinations, &Location{})
			if err := m.Destinations[len(m.Destinations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Mode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Mode = &s
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Region", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Region = &s
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DepartureTime", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.DepartureTime = &v
		case 8:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Avoid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Avoid = append(m.Avoid, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field User", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.User = &s
			iNdEx = postIndex
		case 10:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Project", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Project = &s
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ClientKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ClientKey = &s
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatrixResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MatrixResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MatrixResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Status = &s
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Source", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.Source = &s
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Origins", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Origins = append(m.Origins, &Waypoint{})
			if err := m.Origins[len(m.Origins)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Destinations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Destinations = append(m.Destinations, &Waypoint{})
			if err := m.Destinations[len(m.Destinations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Distances", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Distances = append(m.Distances, &MatrixRow{})
			if err := m.Distances[len(m.Distances)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Durations", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Durations = append(m.Durations, &MatrixRow{})
			if err := m.Durations[len(m.Durations)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 100:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorMessage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			s := string(dAtA[iNdEx:postIndex])
			m.ErrorMessage = &s
			iNdEx = postIndex
		case 101:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ErrorInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ErrorInfo == nil {
				m.ErrorInfo = &MatrixErrorInfo{}
			}
			if err := m.ErrorInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatrixRow) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MatrixRow: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MatrixRow: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Elements", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Elements = append(m.Elements, &MatrixElement{})
			if err := m.Elements[len(m.Elements)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatrixElement) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MatrixElement: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MatrixElement: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var v uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Value = &v
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatrixErrorInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: MatrixErrorInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: MatrixErrorInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OriginsErrors", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OriginsErrors = append(m.OriginsErrors, &LocationError{})
			if err := m.OriginsErrors[len(m.OriginsErrors)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field DestinationsErrors", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGeoRouting
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.DestinationsErrors = append(m.DestinationsErrors, &LocationError{})
			if err := m.DestinationsErrors[len(m.DestinationsErrors)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGeoRouting(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGeoRouting
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipGeoRouting(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGeoRouting
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGeoRouting
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGeoRouting
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGeoRouting
					}
					if iNdEx >= l {
						return 0, io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGeoRouting(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGeoRouting = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGeoRouting   = fmt.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("geo_routing.proto", fileDescriptorGeoRouting) }

var fileDescriptorGeoRouting = []byte{
	// 1195 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0xcf, 0x73, 0xdb, 0x44,
	0x14, 0x46, 0xfe, 0x15, 0xfb, 0x39, 0x76, 0x36, 0xdb, 0x34, 0xd5, 0x40, 0xeb, 0x7a, 0xd4, 0x52,
	0x5c, 0x0a, 0x99, 0x4e, 0x29, 0xc3, 0x30, 0xc3, 0x45, 0xb5, 0xb7, 0xa9, 0xa6, 0xb2, 0xe5, 0x4a,
	0x72, 0x53, 0x1f, 0x98, 0x1d, 0x8f, 0xbd, 0xf5, 0x08, 0x1c, 0xad, 0x91, 0xe4, 0x94, 0x5c, 0xf8,
	0x3b, 0x7a, 0x05, 0x0a, 0xe5, 0xe7, 0x8d, 0x4b, 0xff, 0x03, 0x6e, 0x70, 0xed, 0x8d, 0x09, 0x57,
	0xfe, 0x08, 0x66, 0x57, 0x52, 0x62, 0x87, 0xc4, 0xe9, 0x30, 0x70, 0xd3, 0xbe, 0xf7, 0xbd, 0x7d,
	0x9f, 0xbe, 0xf7, 0xed, 0x83, 0xf5, 0x31, 0xe3, 0x34, 0xe0, 0xb3, 0xc8, 0xf3, 0xc7, 0x5b, 0xd3,
	0x80, 0x47, 0x1c, 0x97, 0xc7, 0x8c, 0x6f, 0x25, 0x21, 0xed, 0xe7, 0x3c, 0x14, 0x9b, 0xdc, 0x0f,
	0xa3, 0x81, 0x1f, 0x69, 0xcf, 0xb3, 0x50, 0x22, 0x41, 0xc0, 0x83, 0x26, 0x1f, 0x31, 0x5c, 0x83,
	0x0b, 0xc4, 0xb6, 0x2d, 0x9b, 0xda, 0xe4, 0x41, 0x8f, 0x38, 0x2e, 0xd5, 0x1d, 0x87, 0xd8, 0xae,
	0x61, 0x75, 0xd0, 0x97, 0x2f, 0x5e, 0x5e, 0xc0, 0x97, 0x41, 0x4d, 0xf3, 0x4e, 0xd7, 0xea, 0x38,
	0x64, 0x0e, 0xf0, 0x95, 0x00, 0x5c, 0x84, 0xf3, 0x31, 0xa0, 0x63, 0xb9, 0xd4, 0x68, 0x77, 0x4d,
	0xd2, 0x26, 0x1d, 0x97, 0xb4, 0xd0, 0xd7, 0x22, 0x7b, 0x09, 0x36, 0xe3, 0xac, 0xd1, 0x79, 0xa8,
	0x9b, 0x46, 0x8b, 0x9a, 0x56, 0x53, 0x97, 0xc5, 0xcf, 0x44, 0x7a, 0x13, 0xd6, 0xd2, 0x62, 0xd1,
	0xa0, 0x67, 0xba, 0xe8, 0x1b, 0x11, 0xbf, 0x02, 0x17, 0xe3, 0x78, 0x5b, 0x7f, 0x44, 0x77, 0xf4,
	0x7e, 0xd7, 0x32, 0x3a, 0xae, 0x43, 0xc9, 0xa3, 0x26, 0x21, 0x2d, 0xd2, 0x42, 0xdf, 0x0a, 0xd0,
	0x1b, 0xb0, 0x91, 0x50, 0xb3, 0x7a, 0x2e, 0xa1, 0xae, 0x65, 0x51, 0xd3, 0xea, 0x6c, 0xa3, 0xe7,
	0x22, 0x79, 0x0d, 0x2e, 0xc7, 0xc9, 0xa6, 0x6d, 0x39, 0x0e, 0x6d, 0x5a, 0xbd, 0x8e, 0x6b, 0xf7,
	0x25, 0x49, 0xdd, 0x34, 0xad, 0x1d, 0xd2, 0x42, 0xdf, 0x2d, 0x10, 0xb4, 0x1e, 0x12, 0x9b, 0x3e,
	0xe8, 0x11, 0xbb, 0x4f, 0x4d, 0xa3, 0x6d, 0xb8, 0xe8, 0xfb, 0x45, 0x22, 0x86, 0xe3, 0x18, 0x9d,
	0x6d, 0xba, 0x6d, 0x59, 0xdb, 0x26, 0xa1, 0x7a, 0xd7, 0xa0, 0xf7, 0x49, 0x1f, 0xfd, 0x20, 0x40,
	0xd7, 0x41, 0x3b, 0x62, 0xdb, 0xd6, 0x5d, 0xdb, 0x78, 0x44, 0x49, 0xac, 0xc3, 0x1c, 0xe7, 0x1f,
	0x05, 0xf4, 0x06, 0x5c, 0x4d, 0xa0, 0x3d, 0xd3, 0x35, 0xba, 0x26, 0x39, 0x14, 0x84, 0xc6, 0x71,
	0xc7, 0xd5, 0xdd, 0x9e, 0x83, 0x7e, 0x7a, 0xf1, 0xf2, 0x82, 0x76, 0x15, 0x2a, 0xce, 0x6c, 0x38,
	0x64, 0x61, 0xe8, 0x44, 0x83, 0x68, 0x16, 0xe2, 0x73, 0xb0, 0xd6, 0xd5, 0x6d, 0xd7, 0xd0, 0x4d,
	0xea, 0xf4, 0x9a, 0x4d, 0xe2, 0x38, 0x48, 0xd1, 0x3e, 0x02, 0x68, 0x4e, 0x3c, 0xe6, 0x47, 0xee,
	0xfe, 0x94, 0x61, 0x15, 0x36, 0x9a, 0xa6, 0x41, 0x3a, 0x2e, 0x75, 0xfb, 0x5d, 0x42, 0x8d, 0x8e,
	0x4b, 0xec, 0x8e, 0x6e, 0x22, 0x05, 0x6f, 0x00, 0x9a, 0xcf, 0xf4, 0x1c, 0x62, 0xa3, 0x8c, 0x76,
	0x15, 0x8a, 0xd6, 0x1e, 0x0b, 0xf6, 0x3c, 0xf6, 0x04, 0x57, 0x01, 0x1c, 0x31, 0x41, 0xe3, 0xae,
	0x41, 0x5a, 0x48, 0xc1, 0x45, 0xc8, 0xdd, 0xed, 0x99, 0x26, 0xca, 0x68, 0x57, 0x20, 0xd7, 0x16,
	0x6e, 0x59, 0x81, 0x6c, 0x53, 0xb7, 0xe3, 0xd4, 0x1d, 0xe3, 0x3e, 0x41, 0x19, 0x09, 0xb2, 0x2c,
	0x17, 0x65, 0xb5, 0xeb, 0x90, 0xd7, 0xf7, 0xb8, 0x37, 0x12, 0x21, 0xd7, 0x32, 0x45, 0xcf, 0x32,
	0xac, 0xdc, 0x33, 0xb6, 0xef, 0xed, 0xe8, 0x7d, 0x94, 0xc1, 0x25, 0xc8, 0xdf, 0x25, 0xb6, 0xdd,
	0x47, 0x59, 0xed, 0x12, 0x14, 0x1c, 0x3e, 0x0b, 0x86, 0xf2, 0xc6, 0x6d, 0x62, 0x21, 0x05, 0x03,
	0x14, 0x62, 0x6d, 0x63, 0x52, 0x26, 0x1f, 0x0e, 0x22, 0x8f, 0xfb, 0xb8, 0x0c, 0xd9, 0x09, 0xf7,
	0x55, 0xa5, 0xae, 0x34, 0x14, 0x79, 0x18, 0x44, 0x6a, 0x46, 0x1c, 0xb4, 0xa7, 0x0a, 0xe4, 0x9c,
	0x88, 0x4d, 0x31, 0x82, 0xe2, 0xc8, 0x13, 0xe6, 0x1e, 0x32, 0x89, 0xcb, 0xc9, 0xc8, 0x2c, 0x90,
	0x17, 0x48, 0xb0, 0x8c, 0x8c, 0x19, 0xdf, 0x65, 0x51, 0xb0, 0xaf, 0x66, 0xeb, 0x4a, 0xa3, 0x84,
	0xdf, 0x85, 0x6a, 0x18, 0x0d, 0x82, 0x88, 0x4e, 0x92, 0x56, 0x6a, 0xae, 0xae, 0x34, 0xca, 0xb7,
	0xce, 0x6f, 0xcd, 0x3d, 0x9d, 0xad, 0x43, 0x1e, 0x37, 0x60, 0x95, 0xf9, 0xa3, 0x23, 0x70, 0x7e,
	0x09, 0x58, 0x7b, 0xae, 0x40, 0xd6, 0x64, 0xe3, 0x57, 0x62, 0xf6, 0x3f, 0xf2, 0xc0, 0x75, 0xc8,
	0x87, 0x11, 0x9b, 0x86, 0x6a, 0xa1, 0x9e, 0x6d, 0x94, 0x6f, 0xad, 0x2f, 0xa0, 0x84, 0x76, 0xda,
	0xc7, 0x90, 0xb7, 0xf9, 0x2c, 0x62, 0xff, 0x52, 0xc4, 0x1a, 0xe4, 0x26, 0x6c, 0x1c, 0xaa, 0x39,
	0x79, 0x3f, 0x5a, 0x64, 0xc1, 0xc6, 0xda, 0x7b, 0x50, 0xdc, 0x19, 0xec, 0x4f, 0xb9, 0xe7, 0x47,
	0xf8, 0x2d, 0x28, 0x1e, 0xb2, 0x56, 0x96, 0xa9, 0x77, 0x19, 0x2a, 0xe9, 0xb7, 0x5c, 0x54, 0xb8,
	0x0a, 0x85, 0x50, 0xbe, 0x00, 0x59, 0x57, 0xd2, 0x7e, 0xcb, 0xc0, 0x7a, 0xcb, 0x0b, 0xd8, 0x50,
	0x40, 0x42, 0x9b, 0x7d, 0x36, 0x63, 0x61, 0x84, 0xdf, 0x84, 0x02, 0x0f, 0xbc, 0xb1, 0xb7, 0xfc,
	0x76, 0xfc, 0x36, 0x94, 0x47, 0x2c, 0x8c, 0x3c, 0xff, 0xe8, 0xcf, 0x4e, 0xc5, 0x36, 0xa0, 0xf4,
	0x24, 0xa1, 0x1f, 0xaa, 0x59, 0xf9, 0x8f, 0xa7, 0x20, 0x05, 0x45, 0xe9, 0x68, 0x39, 0xbd, 0x12,
	0x5e, 0x85, 0xdc, 0x2e, 0x1f, 0x31, 0x39, 0x9e, 0x92, 0xc8, 0x06, 0x6c, 0x2c, 0xda, 0x15, 0xe4,
	0xb9, 0x92, 0xce, 0x65, 0xa5, 0xae, 0x34, 0x8a, 0x78, 0x13, 0xaa, 0x23, 0x36, 0x1d, 0x04, 0xd1,
	0x2c, 0x60, 0x34, 0xf2, 0x76, 0x99, 0x5a, 0x94, 0x7a, 0x6f, 0xc0, 0xea, 0x60, 0x12, 0xb1, 0x40,
	0x50, 0xdd, 0x63, 0xa1, 0x5a, 0x92, 0xe8, 0x0a, 0xe4, 0x07, 0xe2, 0x9d, 0xa9, 0x50, 0xcf, 0x36,
	0x4a, 0x62, 0x28, 0x3c, 0x79, 0xc1, 0x6a, 0x39, 0xed, 0x3d, 0x0b, 0x59, 0xa0, 0xae, 0xca, 0xd3,
	0x1a, 0xac, 0x4c, 0x03, 0xfe, 0x09, 0x1b, 0x46, 0x6a, 0x45, 0x06, 0x30, 0xc0, 0x50, 0x2e, 0x0c,
	0xfa, 0x29, 0xdb, 0x57, 0xab, 0xb1, 0xa2, 0x0a, 0xe0, 0x79, 0x45, 0xc3, 0x29, 0xf7, 0x43, 0x76,
	0x5c, 0xf8, 0xb9, 0xbf, 0xcc, 0xc8, 0xf3, 0x99, 0xfa, 0x1c, 0x0e, 0x5f, 0x83, 0x82, 0x88, 0xb1,
	0xd4, 0x2a, 0x78, 0x01, 0x16, 0x5b, 0xf0, 0x3c, 0x54, 0x98, 0x98, 0x37, 0xdd, 0x65, 0x61, 0x38,
	0x18, 0x33, 0x75, 0x24, 0x9b, 0xdc, 0x06, 0x88, 0xc3, 0x9e, 0xff, 0x98, 0xab, 0x4c, 0xce, 0xab,
	0xbe, 0x50, 0x7e, 0xc4, 0x5c, 0xfa, 0xc5, 0xf0, 0x1f, 0x73, 0xed, 0x17, 0x05, 0xce, 0x9d, 0x10,
	0xc7, 0x37, 0x61, 0x35, 0x76, 0x09, 0x95, 0x97, 0x26, 0x5e, 0x79, 0xfd, 0xc4, 0xa9, 0xc6, 0xee,
	0x7b, 0x1f, 0xd6, 0xe7, 0x0c, 0x93, 0x94, 0x65, 0xce, 0x2c, 0xbb, 0x0d, 0xe8, 0x50, 0x9b, 0xb8,
	0x28, 0x95, 0x68, 0x49, 0x95, 0xf6, 0x97, 0x02, 0x95, 0xf6, 0x20, 0x0a, 0xbc, 0xcf, 0x53, 0x5b,
	0x5f, 0x83, 0x95, 0x98, 0xb0, 0x18, 0x42, 0x76, 0xe9, 0x62, 0x98, 0xa3, 0x19, 0xaa, 0x99, 0xff,
	0xce, 0xae, 0xff, 0xf4, 0xe7, 0x8a, 0xf4, 0xe7, 0xa1, 0x13, 0x8b, 0xd2, 0x89, 0xa9, 0xef, 0x4a,
	0xc7, 0x7d, 0x07, 0x27, 0xf8, 0x4e, 0x5a, 0x55, 0x7b, 0x96, 0x81, 0x6a, 0xfa, 0xbb, 0xaf, 0xe8,
	0xb9, 0x39, 0x3d, 0x96, 0x3a, 0xee, 0xb8, 0x1e, 0xb9, 0x65, 0xe0, 0xeb, 0x50, 0x4a, 0xb7, 0x5f,
	0xa8, 0xe6, 0x25, 0x72, 0x73, 0x01, 0x99, 0x90, 0xe4, 0x4f, 0x24, 0x34, 0x59, 0x8b, 0xe9, 0x5e,
	0x3d, 0x0d, 0x7a, 0x8a, 0xa1, 0x6f, 0x9e, 0x60, 0xe8, 0x8b, 0x27, 0x5c, 0x71, 0x64, 0xe6, 0x0f,
	0xa1, 0x74, 0x74, 0xeb, 0x3b, 0x50, 0x64, 0x13, 0xb6, 0xcb, 0xc4, 0x9b, 0x53, 0x4e, 0x30, 0x54,
	0x52, 0x1c, 0x43, 0xb4, 0x5a, 0xea, 0xa7, 0x24, 0x20, 0x86, 0xb6, 0x37, 0x98, 0xcc, 0x92, 0x2d,
	0xaf, 0x7d, 0x01, 0x6b, 0xc7, 0xba, 0xe1, 0x5b, 0x50, 0x4d, 0x14, 0x4e, 0x7d, 0xab, 0x9c, 0xe5,
	0x5b, 0xfc, 0x01, 0x9c, 0x9b, 0x57, 0x3b, 0x2d, 0xcc, 0x9c, 0x55, 0x78, 0x07, 0xfd, 0x7a, 0x50,
	0x53, 0x7e, 0x3f, 0xa8, 0x29, 0x7f, 0x1c, 0xd4, 0x94, 0xa7, 0x7f, 0xd6, 0x5e, 0xfb, 0x3b, 0x00,
	0x00, 0xff, 0xff, 0xe1, 0x08, 0x2d, 0x1b, 0xce, 0x0a, 0x00, 0x00,
}
