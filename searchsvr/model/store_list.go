package model

import (
	"context"
	"fmt"
	"math"
	"sort"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
)

// listwise 候选多个序列
type StoreLists []*StoreList

// 获取候选序列中所有门店集合, 跳过广告门店
func (s StoreLists) AllDistinctStores() []*StoreInfo {
	storeMap := make(map[uint64]*StoreInfo)
	for _, storeList := range s {
		if storeList == nil || len(storeList.StoresOriginal) == 0 {
			continue
		}
		for _, store := range storeList.StoresOriginal {
			if store == nil {
				continue
			}
			if store.ItemFeature.GetCIStoreType() == StoreTypeAds {
				continue
			}
			if _, exist := storeMap[store.StoreId]; !exist {
				storeMap[store.StoreId] = store
			}
		}
	}
	stores := make([]*StoreInfo, 0, len(storeMap))
	for _, store := range storeMap {
		stores = append(stores, store)
	}
	return stores
}

func (sls StoreLists) SortByListEvaluateScore() StoreLists {
	sort.Slice(sls, func(i, j int) bool {
		return sls[i].ListEvaluateScore > sls[j].ListEvaluateScore
	})
	return sls
}

type ListWiseItems []*ListWiseItem

type ListWiseItem struct {
	StoreId               uint64                       `json:"StoreId"`
	StoreType             int64                        `json:"StoreType"`
	DisplayOpeningStatus  o2oalgo.DisplayOpeningStatus `json:"display_opening_status"` // 用于item排序
	PRelevanceScore       float64                      `json:"PRelevanceScore"`
	DeboostCategoryScore  float64                      `json:"DeboostCategoryScore"`
	ExactMatch            uint32                       `json:"ExactMatch"`
	ItemParams            map[string]interface{}       `json:"item_params"`
	GeneratorScore        float64                      `json:"GeneratorScore"` // 用于item排序
	EvaluatorScore        float64                      `json:"EvaluatorScore"`
	MMRRelevance          float64                      `json:"MMRRelevance"`
	MMRRelevanceLambda    float64                      `json:"MMRRelevanceLambda"` // mmrConf.Lambda*item.MMRRelevance
	MMROpeningStatusScore float64                      `json:"MMROpeningStatusScore"`
	MMRScore              float64                      `json:"MMRScore"`

	DeboostType int `json:"DeboostType"`
}

func (s *ListWiseItem) status() int32 {
	switch s.DisplayOpeningStatus {
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN:
		return 0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE:
		return 1
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE:
		return 2
	default:
		return math.MaxInt32
	}
}

func (s ListWiseItems) ListWiseItemMap() map[uint64]*ListWiseItem {
	m := make(map[uint64]*ListWiseItem, len(s))
	for _, item := range s {
		m[item.StoreId] = item
	}
	return m
}

func (s ListWiseItems) SortByGeneratorScore() ListWiseItems {
	// 顺序
	// 1. 门店营业状态
	// 2. GeneratorScore 分数
	// 3. store id 升序
	sort.Slice(s, func(i, j int) bool {
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}

		if !acc.Equal(s[i].GeneratorScore, s[j].GeneratorScore) {
			return s[i].GeneratorScore > s[j].GeneratorScore
		}
		// 距离相同，则 ID 升序
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

func (s ListWiseItems) SortByEvaluatorScore() ListWiseItems {
	// 顺序
	// 1. 门店营业状态
	// 2. EvaluatorScore 分数
	// 3. store id 升序
	sort.Slice(s, func(i, j int) bool {
		if s[i].ExactMatch != s[j].ExactMatch {
			return s[i].ExactMatch > s[j].ExactMatch // extra match 的置顶
		}
		statusI := s[i].status()
		statusJ := s[j].status()
		if statusI != statusJ {
			// 状态按照 OPEN(0) < PAUSE(1) < CLOSED(2) 升序
			return statusI < statusJ
		}

		if !acc.Equal(s[i].EvaluatorScore, s[j].EvaluatorScore) {
			return s[i].EvaluatorScore > s[j].EvaluatorScore
		}
		// 距离相同，则 ID 升序
		return s[i].StoreId < s[j].StoreId
	})
	return s
}

// listwise 候选序列
type StoreList struct {
	ListOriginalIndex uint64                         `json:"ListOriginalIndex"`
	ListSortedIndex   uint64                         `json:"ListSortedIndex"`
	GeneratorConf     apollo.ListWiseGeneratorConfig `json:"GeneratorConf"`

	StoresOriginal StoreInfos `json:"-"` // 原始门店列表，包含广告、干预门店且顺序不变。
	StoresPredict  StoreInfos `json:"-"` // 用于预估的门店列表,不包含广告门店， 是Stores子集，且顺序和stores 保持一致
	StoresStatic   StoreInfos `json:"-"` // 用于lise wise item 特征排序,不包含广告门店， 是Stores子集，顺序会根据计算随时变化
	StoresFinal    StoreInfos `json:"-"` // 最后门店列表，包含广告、干预门店且有序

	ListWiseItems ListWiseItems `json:"ListWiseItems"`

	// listwise模型预估的context及item特征
	ListWiseItemIds          []uint64                    `json:"ListWiseItemIds"` // 数量和顺序与Stores一致
	ListWiseItemFeatures     []*food.ListWiseItemFeature `json:"-"`               // 数量和顺序与Stores一致
	ListWiseItemFeatureBytes [][]byte                    `json:"-"`               // 数量和顺序与Stores一致

	//ListWiseContextFeature      *food.ListWiseContextFeature `json:"ListWiseContextFeature"`
	ListWiseContextFeatureBytes []byte `json:"-"`

	// 预估计算得分
	ListEvaluateExpStr string  `json:"ListEvaluateExpStr"`
	ListEvaluateScore  float64 `json:"ListEvaluateScore"` // direct_out 计算每个序列的分数
	IsSelected         bool    `json:"IsSelected"`

	ListEvaluateParams map[string]interface{} `json:"ListEvaluateParams"`
	SpexInstanceId     string                 `json:"SpexInstanceId"`
	ReqId              string                 `json:"ReqId"`
}

// 构建list wise item 特征
func (sl *StoreList) BuildLiseWiseItemFeatures(ctx context.Context, traceInfo *traceinfo.TraceInfo) *StoreList {
	if sl == nil || len(sl.StoresPredict) == 0 {
		return sl
	}
	nStores := len(sl.StoresPredict)
	sl.ListWiseItemFeatures = make([]*food.ListWiseItemFeature, nStores)
	// 这里要用StoresPredict，用于预估
	for i := range sl.StoresPredict {
		sl.ListWiseItemFeatures[i] = &food.ListWiseItemFeature{}
	}
	// 计算StoresSlice中各属性的排名，并赋值到ListWiseItemFeature
	sl.CalSliceAttrPos(ctx, "CIPctrSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCPctr() })
	sl.CalSliceAttrPos(ctx, "CIPcvrSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCPcvr() })
	sl.CalSliceAttrPos(ctx, "CCtrSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCCtr() })
	sl.CalSliceAttrPos(ctx, "CCvrSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCCvr() })
	sl.CalSliceAttrPos(ctx, "CIPueSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCPredictUeFactor() })
	sl.CalSliceAttrPos(ctx, "CIRelSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCPredictRelevanceScore() })
	sl.CalSliceAttrPos(ctx, "CIDistSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCStoreDistanceScore() })
	sl.CalSliceAttrPos(ctx, "CISaleSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCStoreSalesScore() })
	sl.CalSliceAttrPos(ctx, "CISalesCate1SIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCSalesScoreCate1() })
	sl.CalSliceAttrPos(ctx, "CIAccSalesCate1SIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCAccSalesScoreCate1() })
	sl.CalSliceAttrPos(ctx, "CIGmvCate1SIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCGmvScoreCate1() })
	sl.CalSliceAttrPos(ctx, "CIAccGmvCate1SIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCAccGmvScoreCate1() })
	sl.CalSliceAttrPos(ctx, "CIPriceSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCPriceIndex() })
	sl.CalSliceAttrPos(ctx, "CICommissionSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCCommissionRate() })
	sl.CalSliceAttrPos(ctx, "CIRatingSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCRatingScore() })
	sl.CalSliceAttrPos(ctx, "CICateRelSIndex", func(store *StoreInfo) float32 { return store.ItemFeature.GetCICateRel() })

	sl.ListWiseItemIds = make([]uint64, 0, nStores)
	sl.ListWiseItemFeatureBytes = make([][]byte, 0, nStores)
	for i, store := range sl.StoresPredict {
		sl.ListWiseItemIds = append(sl.ListWiseItemIds, store.StoreId)

		sl.ListWiseItemFeatures[i].CIPos = int64(i + 1) // 顺序从1开始
		data, _ := proto.Marshal(sl.ListWiseItemFeatures[i])
		sl.ListWiseItemFeatureBytes = append(sl.ListWiseItemFeatureBytes, data)
	}
	return sl
}

// 构建list wise context 特征
func (sl *StoreList) BuildLiseWiseContextFeatures() *StoreList {
	// 直接使用 sl.StoresStatic 计算平均值， 已经去除了广告门店, 不关心顺序
	lwContextFea := &food.ListWiseContextFeature{
		CSPctrAvg:   CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCPctr() }),
		CSPcvrAvg:   CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCPcvr() }),
		CSPueAvg:    CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCPredictUeFactor() }),
		CSRelAvg:    CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCPredictRelevanceScore() }),
		CSDistAvg:   CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCStoreDistanceScore() }),
		CSSaleAvg:   CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCStoreSalesScore() }),
		CSPriceAvg:  CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCPriceIndex() }),
		CSRatingAvg: CalculateAttrAvg(sl.StoresStatic, func(store *StoreInfo) float32 { return store.ItemFeature.GetCRatingScore() }),
	}
	lwContextFeaBytes, _ := proto.Marshal(lwContextFea)
	//sl.ListWiseContextFeature = lwContextFea
	sl.ListWiseContextFeatureBytes = lwContextFeaBytes
	return sl
}

// 计算指定属性的平均值
func CalculateAttrAvg(stores []*StoreInfo, field func(*StoreInfo) float32) float32 {
	// 用于累加字段的总和
	var fieldSum float32
	// 计算字段的总和
	for _, store := range stores {
		fieldSum += field(store)
	}
	// 计算并返回字段的平均值
	if len(stores) > 0 {
		return fieldSum / float32(len(stores))
	}
	return 0.0
}

// 计算指定列表各属性的排名
func CalAttrPos(stores []*StoreInfo, field func(*StoreInfo) float32) map[uint64]int {
	sort.SliceStable(stores, func(i, j int) bool {
		return field(stores[i]) > field(stores[j])
	})
	// 创建一个map来存储门店ID对应的排序位置
	positionMap := make(map[uint64]int)
	for i, store := range stores {
		positionMap[store.StoreId] = i + 1 // 从 1 开始计数
	}
	return positionMap
}

// 根据某个属性字段排序，并直接返回每个商店的排序位置
func (sl *StoreList) CalSliceAttrPos(ctx context.Context, fieldName string, field func(*StoreInfo) float32) {
	// 注意排序用StoresSlice，不要改变原有sl.StoresOriginal 的顺序
	positionMap := CalAttrPos(sl.StoresStatic, field)
	// 注意赋值用sl.StoresPredict, ListWiseItemFeature的顺序和Stores顺序一致，没有position的默认0
	for i, store := range sl.StoresPredict {
		if index, ok := positionMap[store.StoreId]; ok {
			AssignOrderToFeatures(ctx, sl.ListWiseItemFeatures[i], fieldName, int64(index))
		}
	}
}

func AssignOrderToFeatures(ctx context.Context, features *food.ListWiseItemFeature, fieldName string, index int64) {
	switch fieldName {
	case "CIPctrSIndex":
		features.CIPctrSIndex = index
	case "CIPcvrSIndex":
		features.CIPcvrSIndex = index
	case "CCtrSIndex":
		features.CICtrSIndex = index
	case "CCvrSIndex":
		features.CICvrSIndex = index
	case "CIPueSIndex":
		features.CIPueSIndex = index
	case "CIRelSIndex":
		features.CIRelSIndex = index
	case "CIDistSIndex":
		features.CIDistSIndex = index
	case "CISaleSIndex":
		features.CISaleSIndex = index
	case "CISalesCate1SIndex":
		features.CISalesCate1SIndex = index
	case "CIAccSalesCate1SIndex":
		features.CIAccSalesCate1SIndex = index
	case "CIGmvCate1SIndex":
		features.CIGmvCate1SIndex = index
	case "CIAccGmvCate1SIndex":
		features.CIAccGmvCate1SIndex = index
	case "CIPriceSIndex":
		features.CIPriceSIndex = index
	case "CICommissionSIndex":
		features.CICommissionSIndex = index
	case "CIRatingSIndex":
		features.CIRatingSIndex = index
	case "CICateRelSIndex":
		features.CICateRelSIndex = index
	default:
		logkit.FromContext(ctx).Error("invalid filed name for order index", zap.String("fieldName", fieldName))
	}
}

type IdPairKey struct {
	MaxId uint64
	MinId uint64
}

// 实现 String 方法，Pair 结构体可以作为 sync.Map 的键
func (p IdPairKey) String() string {
	return fmt.Sprintf("%d,%d", p.MaxId, p.MinId)
}

func GetIdPairKey(Id1, Id2 uint64) IdPairKey {
	if Id1 > Id2 {
		return IdPairKey{MaxId: Id1, MinId: Id2}
	} else {
		return IdPairKey{MaxId: Id2, MinId: Id1}
	}
}
