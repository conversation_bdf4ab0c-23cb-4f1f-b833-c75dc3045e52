package polygon

import (
	"fmt"
	"math"
)

type Polygon struct {
	Latitudes  []float64 `json:"Latitudes"`
	Longitudes []float64 `json:"Longitudes"`
}

func (p *Polygon) String() string {
	str := "{"
	for i := 0; i < len(p.Latitudes); i++ {
		str = str + fmt.Sprintf("%v,%v;", p.Latitudes[i], p.Longitudes[i])
	}
	str = str + "}"
	return str
}

func (p *Polygon) Add(latitude float64, longitude float64) {
	p.Latitudes = append(p.Latitudes, latitude)
	p.Longitudes = append(p.Longitudes, longitude)
}

func (p *Polygon) IsClosed() bool {
	if len(p.Latitudes) < 3 {
		return false
	}

	return true
}

func (p *Polygon) Contains(pointLatitude, pointLongitude float64) bool {
	if !p.IsClosed() {
		return false
	}

	start := len(p.Latitudes) - 1
	end := 0

	contains := p.intersectsWithRaycast(pointLatitude, pointLongitude, p.Latitudes[start], p.Longitudes[start], p.Latitudes[end], p.Longitudes[end])

	for i := 1; i < len(p.Latitudes); i++ {
		if p.intersectsWithRaycast(pointLatitude, pointLongitude, p.Latitudes[i-1], p.Longitudes[i-1], p.Latitudes[i], p.Longitudes[i]) {
			contains = !contains
		}
	}

	return contains
}

func (p *Polygon) intersectsWithRaycast(pointLatitude, pointLongitude float64, startLatitude, startLongitude float64, endLatitude, endLongitude float64) bool {
	if startLongitude > endLongitude {
		startLatitude, endLatitude = endLatitude, startLatitude
		startLongitude, endLongitude = endLongitude, startLongitude
	}
	for pointLongitude == startLongitude || pointLongitude == endLongitude {
		newLng := math.Nextafter(pointLongitude, math.Inf(1))
		pointLongitude = newLng
	}
	if pointLongitude < startLongitude || pointLongitude > endLongitude {
		return false
	}

	if startLatitude > endLatitude {
		if pointLatitude > startLatitude {
			return false
		}
		if pointLatitude < endLatitude {
			return true
		}

	} else {
		if pointLatitude > endLatitude {
			return false
		}
		if pointLatitude < startLatitude {
			return true
		}
	}

	raySlope := (pointLongitude - startLongitude) / (pointLatitude - startLatitude)
	diagSlope := (endLongitude - startLongitude) / (endLatitude - startLatitude)

	return raySlope >= diagSlope
}
