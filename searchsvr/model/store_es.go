package model

import (
	"strconv"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
)

type StoreSearchES struct {
	ID                 *string                      `json:"id,omitempty"`
	Name               *string                      `json:"store_name,omitempty"`
	Tags               *[]string                    `json:"tags,omitempty"`
	Status             *foodalgo_search.StoreStatus `json:"store_status,omitempty"`
	State              *string                      `json:"state,omitempty"`
	City               *string                      `json:"city,omitempty"`
	Location           *Location                    `json:"location,omitempty"`
	BestSellingDishId  *string                      `json:"best_selling_dish_id,omitempty"`
	RealName           *string                      `json:"real_name,omitempty"`
	Segments           *[]string                    `json:"segments,omitempty"`
	MaxWordSegments    *[]string                    `json:"max_word_segments,omitempty"`
	GrabFoodTags       *[]string                    `json:"grabfood_tags,omitempty"`
	StoreSellWeek      *string                      `json:"store_sell_week,omitempty"`
	StoreDishAvailable *int32                       `json:"store_dish_available,omitempty"`
	RealStoreName      *string                      `json:"real_store_name,omitempty"`
	BranchName         *string                      `json:"branch_name,omitempty"`
	// for MY, MY站点为brand_name
	BrandName         *string                    `json:"brand_name,omitempty"` // for vn 复用
	HalalType         *foodalgo_search.HalalType `json:"halal_type,omitempty"`
	TagsSegments      *string                    `json:"tags_segments,omitempty"`
	StoreNameSegments *string                    `json:"store_name_segments,omitempty"`
	BrandID           *string                    `json:"brand_id,omitempty"`
	MerchantID        *string                    `json:"merchant_id,omitempty"`
	//新增
	DishName                    *[]string `json:"dish_name,omitempty"`
	DishNameSegments            *[]string `json:"dish_name_segments,omitempty"`
	DishNameSegmentsKeyword     *[]string `json:"dish_name_segments_keyword,omitempty"`
	DishNamesMaxSegmentsKeyword *[]string `json:"dish_name_max_word_segments_keyword,omitempty"`
	//主营分类、辅营分类
	MainCategory        *Category   `json:"main_category,omitempty"`
	SubCategory         *[]Category `json:"sub_category,omitempty"`
	CategoryType        *string     `json:"category_type,omitempty"`         // for vn
	IsPreferredMerchant *string     `json:"is_preferred_merchant,omitempty"` // for vn
	IsPartnerMerchant   *string     `json:"is_partner_merchant,omitempty"`   // for vn
	RankingScore        *float64    `json:"ranking_score,omitempty"`         // for vn
	TimeFactorScore     *float64    `json:"time_factor_score,omitempty"`     // for vn
	SeoKeywords         *[]string   `json:"seo_keywords,omitempty"`          // for vn
	//距离分数
	EsDistance float64 `json:"es_distance,omitempty"`
}

type Location struct {
	Longitude *float64 `json:"lon,omitempty"`
	Latitude  *float64 `json:"lat,omitempty"`
}

func (s *StoreSearchES) GetStoreSellWeek() uint32 {
	if s != nil && s.StoreSellWeek != nil {
		val, _ := strconv.Atoi(*s.StoreSellWeek)
		return uint32(val)
	}
	return 0
}

func (s *StoreSearchES) GetName() string {
	if s != nil && s.Name != nil {
		return *s.Name
	}
	return ""
}

func (s *StoreSearchES) GetRealName() string {
	if s != nil && s.RealName != nil {
		return *s.RealName
	}
	return ""
}

func (s *StoreSearchES) GetTags() []string {
	if s != nil && s.Tags != nil {
		return *s.Tags
	}
	return nil
}

func (s *StoreSearchES) GetBestSellingDishId() string {
	if s != nil && s.BestSellingDishId != nil {
		return *s.BestSellingDishId
	}
	return "0"
}

func (s *StoreSearchES) GetSegments() []string {
	if s != nil && s.Segments != nil {
		return *s.Segments
	}
	return nil
}

func (s *StoreSearchES) GetMaxWordSegments() []string {
	if s != nil && s.MaxWordSegments != nil {
		return *s.MaxWordSegments
	}
	return nil
}

func (s *StoreSearchES) GetGrabFoodTags() []string {
	if s != nil && s.GrabFoodTags != nil {
		return *s.GrabFoodTags
	}
	return nil
}

func (s *StoreSearchES) GetHalalType() *foodalgo_search.HalalType {
	if s != nil && s.HalalType != nil {
		return s.HalalType
	}
	return nil
}

func (s *StoreSearchES) GetStatus() *foodalgo_search.StoreStatus {
	if s != nil && s.Status != nil {
		return s.Status
	}
	return nil
}

func (s *StoreSearchES) GetLongitude() float64 {
	if s != nil && s.Location != nil && s.Location.Longitude != nil {
		return *s.Location.Longitude
	}
	return 0
}

func (s *StoreSearchES) GetLatitude() float64 {
	if s != nil && s.Location != nil && s.Location.Latitude != nil {
		return *s.Location.Latitude
	}
	return 0
}

type HistoryOrderSearchES struct {
	OrderId   *int64    `json:"order_id,omitempty"`
	StoreName *string   `json:"store_name,omitempty"`
	StoreId   *int64    `json:"store_id,omitempty"`
	DishNames *[]string `json:"dish_names,omitempty"`
	DishIds   *[]int64  `json:"dish_ids,omitempty"`
}
