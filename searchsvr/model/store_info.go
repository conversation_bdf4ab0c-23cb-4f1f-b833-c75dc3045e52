package model

import (
	"context"
	"math"
	"strings"

	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/geohash"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model/polygon"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

const (
	TextMatchWeight        = 0.7
	StoreRatingScoreWeight = 0.07
	StoreSaleScoreWeight   = 0.03
	StoreDistanceWeight    = 0.2
	MaxScoreDenominator    = 2047

	DistanceLimit4km  = 4000.0
	DistanceLimit10km = 10000.0
	bit7              = 1 << 6
	bit8              = 1 << 7
	bit9              = 1 << 8
	bit10             = 1 << 9

	scoreWeight    = 0.3
	distanceWeight = 0.7
)

const ClosedMerchantFactor = 0.4

const MaxDistance = 99999.0     //m
const DistanceLimit5km = 5000.0 //m
const RatingTotalLimit5 = 5     // 评论条数5

const DistancePivot = 2
const DistanceBoostPercentage = 0.2

const StoreTypeInter = 1  // 干预门店
const StoreTypeAds = 2    // 广告门店
const StoreTypeNormal = 3 // 自然搜索门店

// Mart （service id = 4，新L1对应id=1）
// Fresh （service id = 5，新L1对应id=2）
// Medicine （service id = 7，新L1对应id=5）
// Pets （service id = 12，，新L1对应id=4）
// Flowers （service id = 6，新L1对应id=3）
// Beer（service id = 13，对应新L1 id=6）
var NonFoodCategoryMap = map[uint32]uint32{
	4:  1,
	5:  2,
	7:  5,
	12: 4,
	6:  3,
	13: 6,
}

// 每一路召回的结构体
type RecallStore struct {
	Stores             StoreInfos
	TotalHits          uint64
	RecallTypeStr      string
	RecallId           string
	RelLevelFromRecall int
	IsNeedDishRecall   bool
}

// 每一路dish召回的结构体
type RecallDish struct {
	Dishes         DishInfos
	TotalHits      uint64
	RecallTypeStr  string
	RecallId       string
	RecallPriority int
}

func GetStoresByRecallType(recallStores []*RecallStore, recallType string) StoreInfos {
	for _, recallStore := range recallStores {
		if recallStore != nil && recallStore.RecallTypeStr == recallType {
			return recallStore.Stores
		}
	}
	return nil
}

type PromotionData struct {
	HasShippingFeePromotion bool `json:"has_shipping_fee_promotion"`
	HasShopPromotion        bool `json:"has_shop_promotion"`
	HasDishPromotion        bool `json:"has_dish_promotion"`
}

type StorePromotionData struct {
	HasShippingFeeVoucher        bool `json:"has_shipping_fee_voucher"`
	HasCoinCashBackVoucher       bool `json:"has_coin_cash_back_voucher"`
	HasFoodDiscountVoucher       bool `json:"has_food_discount_voucher"`
	HasShippingFeeDirectDiscount bool `json:"has_shipping_fee_direct_discount"`
	HasOrderDirectDiscount       bool `json:"has_order_direct_discount"`
	HasFoodDirectDiscount        bool `json:"has_food_direct_discount"`
}

// 门店详细信息
type StoreInfo struct {
	StoreId uint64 `json:"store_id"`
	// 召回信息
	Score                  float64   `json:"score"`    // 门店相关性分数，会有多处计算赋值。https://confluence.shopee.io/pages/viewpage.action?pageId=1728874128
	ESScore                float64   `json:"es_score"` // 门店ES得分，最终用来传递给模型CEsRelevanceScore字段，4种赋值： 1.门店召回ES分 2.门店合并取最大值 3.MY TH3类召回策略,召回分数减掉1 4.未入职扩召回归一化
	RecallTypes            []string  `json:"recall_types"`
	RecallTypeAndIds       []string  `json:"recall_type_and_ids"` // recallType_recallId like: storeIndex_2
	RecallScores           []float64 `json:"recall_scores"`
	RecallPosList          []string  `json:"recall_pos_list"`
	RecallQueries          []string  `json:"recall_query"`
	RelLevelFromRecall     int       `json:"rel_level_from_recall,omitempty"` //相关性档位透传，在召回配置中设置
	IsNeedDishRecall       bool      `json:"is_need_dish_recall"`             // 门店召回后设置
	IsSupplementDishRecall bool      `json:"is_supplement_dish_recall"`       //标记该门店是否有进行菜品补充召回
	RecallSuppressType     uint32    `json:"recall_suppress_type"`            // 召回门店是否打压标记,从召回配置获取
	IsRecallSuppress       uint32    `json:"is_recall_suppress"`              // 是否打压. 0-不打压，1-打压。通过RecallSuppressType和其他信息计算, 最后作为is_recall_suppress融合因子
	RecallPriority         int       `json:"recall_priority"`                 // 各路召回的优先级，默认0，优先级最高
	SortPriority           int       `json:"sort_priority"`                   // 排序优先级，默认0，优先级最高

	// 正排信息
	StoreName                string                        `json:"store_name"`
	BrandId                  uint64                        `json:"brand_id"`
	MerchantId               uint64                        `json:"merchant_id"`
	Location                 *o2oalgo.Location             `json:"location"`
	RatingTotal              uint32                        `json:"rating_total"`
	RatingTotalGroup5        uint32                        `json:"rating_total_group_5"` // 评论数分组  1: 小于5条评论数,  2: 大于等于5条评论数
	RatingScore              float64                       `json:"rating_score"`         // 门店正排获取的门店评分，用来1. 参与排序 2.计算下面的归一化评分
	RatingScoreNorm          float64                       `json:"rating_score_norm"`    // 根据 RatingScore 归一化之后的评分，用来作为模型特征上报模型以及计算融合公式。 StoreRatingScoreParam 重命名为 RatingScoreNorm
	StoreSellWeek            uint64                        `json:"store_sell_week"`
	StoreSellScore           float64                       `json:"store_sell_score"` // 根据 StoreSellWeek 归一化计算
	DisplayOpeningStatus     o2oalgo.DisplayOpeningStatus  `json:"display_opening_status"`
	DisplayDistrictStatus    o2oalgo.DisplayDistrictStatus `json:"display_district_status"`
	DeliveryDistance         uint64                        `json:"delivery_distance"`
	OriginalDeliveryDistance uint64                        `json:"original_delivery_distance"`
	SelfPickupDistance       uint64                        `json:"self_pickup_distance"` // 自提商家距离
	Logo                     string                        `json:"logo"`
	Timezone                 string                        `json:"timezone"`
	StoreDishCnt             int64                         `json:"store_dish_cnt"`           // 门店下菜品数量 GetDishNum()
	StoreAvailableDishCnt    int64                         `json:"store_available_dish_cnt"` // 门店下可售卖菜品数量 GetDishSalesNum(),包含available、listing_status、sales_time、out_of_stock_time、dish_special_sale_date_tab，vn 还包括price>0
	Promotion                *PromotionData                `json:"promotion"`
	StorePromotion           *StorePromotionData           `json:"store_promotion"`
	StorePolygon             *polygon.Polygon              `json:"-"`
	StorePickupPolygon       *polygon.Polygon              `json:"-"`
	StoreTags                map[uint64]struct{}           `json:"store_tags"`
	ShippingFee              uint64                        `json:"shipping_fee"`
	MaxShippingFeeDiscount   uint64                        `json:"max_shipping_fee_discount"`
	StorePrice               uint64                        `json:"-"`                     // 安全问题，不返回敏感数据
	SearchNonHalalFlag       bool                          `json:"search_non_halal_flag"` // search nonHalal flag
	City                     string                        `json:"city"`                  // 正排中location.city, ID 是city name, VN 是city id
	District                 string                        `json:"district"`              // 正排中location.district, ID district name, VN district id
	CategoryType             o2oalgo.CategoryType          `json:"category_type"`
	StoreStatus              o2oalgo.StoreStatus           `json:"store_status"`
	IsPreferredMerchant      int32                         `json:"is_preferred_merchant"`
	// 补充信息
	DishInfos                             DishInfos   `json:"dish_infos"`
	DishInfosBeforeFilter                 DishInfos   `json:"dish_infos_before_filter"`
	DishInfosAfterRank                    DishInfos   `json:"dish_infos_after_rank"`
	DishInfosForFeature                   DishInfos   `json:"dish_infos_for_feature"`     // 专用来做特征打分因子的dishInfos
	MainCategory                          *Category   `json:"main_category"`              // 结构体 {"level_1":2448,"level_2":2601}
	SubCategory                           []*Category `json:"sub_category"`               // 结构体 [{"level_1":2448,"level_2":2601},{"level_1":2389,"level_2":3002}]
	MainCategoryIdStr                     string      `json:"main_category_id_str"`       // 字符串ID '{"level_1":2448,"level_2":2601}'
	SubCategoryIdStr                      string      `json:"sub_category_id_str"`        // 字符串ID '[{"level_1":2448,"level_2":2601},{"level_1":2389,"level_2":3002}]'
	MainCategoryNameStr                   string      `json:"main_category_name_str"`     // 字符串Name 'Bakery & Cake|Middle-East'
	SubCategoryNameStr                    []string    `json:"sub_category_name_str"`      // 字符串数组Name ['Bakery & Cake|Middle-East'，'Dessert|Fine Dining'
	MainCategoryL2NameStr                 string      `json:"main_category_l_2_name_str"` // 门店main category l2的name, "Middle-East", 用来计算list wise l2_cate_sim 特征
	Distance                              float64     `json:"distance"`                   // 实际使用距离，单位m，优先导航距离，没有就用飞行距离兜底
	FlyingDistance                        float64     `json:"flying_distance"`            // 直线/飞行距离，单位m，用经纬度计算
	RoutingDistance                       float64     `json:"routing_distance"`           // 导航距离，单位m，调用map 团队geo in_house 获取
	DistanceGroup5km                      uint32      `json:"distance_group_5km"`         // 距离分组  1: 0~5km,  2:5~10km, 3:10~
	DistanceScore                         float64     `json:"distance_score"`             // 实际有3个计算版本，4各地区统一使用 CountDistanceScore() 方法获取
	OpeningScore                          float64     `json:"opening_score"`
	OpeningRatio                          float64     `json:"opening_ratio"` // 门店状态因子，opening:1.0,  non-opening:0.4-0.9。 vn rerank 分数根据开关决定是否要除以此因子。
	PCtrScore                             float64     `json:"p_ctr_score"`
	PCvrScore                             float64     `json:"p_cvr_score"`
	PRelevanceScore                       float64     `json:"p_relevance_score"`
	CtrScoreOffline                       float64     `json:"ctr_score_offline"` // id 离线ctr，StoreCtrCvrDaoDict 获取   StoreCtr 重命名为 CtrScoreOffline
	CvrScoreOffline                       float64     `json:"cvr_score_offline"` // id 离线cvr，StoreCtrCvrDaoDict 获取   StoreCvr 重命名为 CtrScoreOffline
	PriceScore1                           float64     `json:"price_score_1"`     // id 离线价格分数 StoreCtrCvrDaoDict 获取
	PriceScore2                           float64     `json:"price_score_2"`     // id 离线价格分数 StoreCtrCvrDaoDict 获取
	PriceScore3                           float64     `json:"price_score_3"`     // id 离线价格分数 StoreCtrCvrDaoDict 获取
	PUEScore                              float64     `json:"p_ue_score"`
	PLtrScore                             float64     `json:"p_ltr_score"`
	PLtrV1Score                           float64     `json:"p_ltr_v1_score"`     // 调用df模型获取w1-w7，通过ltr v1 融合公式计算后得到的分数, 对应因子是pltr_v1
	NotRelTabScore                        float64     `json:"not_rel_tab_score"`  // 非相关性 tab 页使用的新分数, sort type = 2,3,4才会使用
	UE                                    float64     `json:"ue,omitempty"`       // id 离线UE StoreCtrCvrDaoDict 获取
	UERadio                               float64     `json:"ue_radio,omitempty"` // id 离线UE因子 StoreCtrCvrDaoDict 获取
	UeRation                              float64     `json:"ue_ration"`
	Revenue                               float64     `json:"revenue"`
	Commission                            float64     `json:"commission"`
	SemanticRelevanceScore                float64     `json:"semantic_relevance_score"`                   // 语义相关分数
	RelevanceScore                        float64     `json:"relevance_score"`                            // 相关性模型返回的分数
	RelevanceLevel                        float64     `json:"relevance_level"`                            // 相关性模型返回的门店分档
	RelevanceLevelForSort                 float64     `json:"relevance_level_for_sort"`                   // 相关性模型返回的门店分档排序标记
	ReRankScore                           float64     `json:"re_rank_score"`                              // 最终重排分数
	ReRankScoreWithoutSegmentBoost        float64     `json:"re_rank_score_without_segment_boost"`        // vn segment_boost_factor因子统一赋值1时精排分数，用于boost topN
	FusionScore                           float64     `json:"fusion_score"`                               // ltr分组之前的预估融合计算分数
	StoreInterventionRecall               int32       `json:"store_intervention_recall"`                  // 召回标记  1. store_ids, brand_id, query_store 3种维度召回的门店 2. Top1类型此类干预门店的同品牌门店。
	StoreInterventionWithMerchantIDRecall int32       `json:"store_intervention_with_merchant_id_recall"` // 召回标记   merchant_id 维度召回，赋值1. 在门店召回后根据recall type设置为1 2. merchant 去重后可能需要设置为0
	IsInterForSort                        int32       `json:"is_inter_for_sort"`                          // 排序标记，真正有效干预排序的门店
	InterBeforePos                        int64       `json:"inter_before_pos"`
	InterAfterPos                         int64       `json:"inter_after_pos"`
	IsTargetA30                           bool        `json:"is_target_a30"`                  // 店铺A30数据，用于重排标记：是否目标NA30商户
	StoreImpCnt180d                       int64       `json:"store_imp_cnt_180d"`             // 店铺A30数据，用于重排标记：门店过去180d 曝光量
	StoreOrderCnt180d                     int64       `json:"store_order_cnt_180d"`           // 店铺A30数据，用于重排标记：门店过去180d 订单量
	StoreCtCvr180d                        float64     `json:"store_ct_cvr_180d"`              // 店铺A30数据，用于重排标记：门店过去180d ctcvr
	IsMeetA30QualityConstraint            bool        `json:"is_meet_a30_quality_constraint"` // 店铺A30数据，用于重排标记：门店基础质量是否达标
	ReRankScoreBeforeA30                  float64     `json:"re_rank_score_before_a30"`       // 店铺A30数据，用于重排标记：重排分数之前的记录
	StoreA30ReRankBeforePos               int         `json:"store_a30_re_rank_before_pos"`   // 店铺A30数据，用于重排标记：排序前位置
	StoreA30ReRankAfterPos                int         `json:"store_a30_re_rank_after_pos"`    // 店铺A30数据，用于重排标记：排序后位置
	TermMatchScore                        uint32      `json:"term_match_score"`               // query 与 store name 两个字符串中 term 匹配个数，考虑query term 重复。
	CategoryMatchScore                    uint32      `json:"category_match_score"`
	MergeType                             uint32      `json:"merge_type"` //0-未触发截断，1-保量填充、2-候补填充、3-被截断过滤
	MergeScore                            float64     `json:"merge_score"`
	CoarseRankScore                       float64     `json:"coarse_rank_score"`              // 粗排模型分数
	MainSiteScore                         float64     `json:"main_site_score"`                // 主站分数
	MainSiteScoreExpressionStr            string      `json:"main_site_score_expression_str"` // 主站分数计算来源
	StoreTextMatchScore                   float64     `json:"store_text_match_score"`         // store text match 分数
	StoreTextMatchScoreCalc               string      `json:"store_text_match_score_calc"`    // store text match 分数 怎么来的，debug 模式
	StoreCategoryScore                    float64     `json:"store_category_score"`           // store category 分数
	NormalizedScore                       float64     `json:"normalized_score"`               // 非 relevance tab 页对应因素的归一化分数

	TraceContext  o2oalgo.TraceContext          `json:"trace_context"`
	ItemSceneType foodalgo_search.ItemSceneType `json:"item_scene_type"` // 0-normal, 1-few result item

	RealName   string `json:"real_name"`   // real name
	BranchName string `json:"branch_name"` // branch name, 分店名

	// vn 特殊
	NonFoodSortRate   float64 `xml:"non_food_sort_rate"` // VN non-food 类别排序增强因子
	StoreNameAscii    string  `json:"store_name_ascii"`
	IsPartnerMerchant uint32  `json:"is_partner_merchant"`
	//FineTuneRatio     float64 `json:"fine_tune_ratio"`  // 算法微调因子, 删除20230628
	IsPreciseStore          uint32  `json:"is_precise_store"` // 0-非精准店铺，1-精准店铺 query == store_name or query == real_store_name (归一化处理：去越南语调，大写转小写，去特殊符号且多个空格保留一个)
	StoreSegment            string  `json:"store_segment"`
	StoreSegmentBoostFactor float64 `json:"store_segment_boost_factor"`

	// MY 特殊
	HalalType foodalgo_search.HalalType `json:"halal_type"`

	// 广告相关
	BusinessType          foodalgo_search.BusinessType `json:"business_type"`
	AdsExtraInfo          string                       `json:"ads_extra_info"`          // 广告需要字段,给到前端埋点的
	PredictRelevance      float32                      `json:"predict_relevance"`       // 广告需要字段
	PredictRelevanceLevel int32                        `json:"predict_relevance_level"` // 广告需要字段
	AdsExtraInfoInternal  string                       `json:"ads_extra_info_internal"` // 广告需要字段,给到 mixer 使用的

	// ID 从ES 返回字段
	Tags              []string `json:"tags"`
	StoreIntents      []string `json:"store_intents"` // StoreIntents 就是 grab food tags
	TagSegments       []string `json:"tag_segments"`
	BestSellingDishId uint64   `json:"best_selling_dish_id"`
	Segments          []string `json:"segments"`
	MaxWordSegments   []string `json:"max_word_segments"`
	EsExplains        []string `json:"es_explain"` // es的 explain，调试模式下使用，多路召回就有多个 explain

	// 主站排序相关
	CreateTime      uint64 `json:"create_time"`
	Price           uint64 `json:"-"` // 安全问题，不返回敏感数据
	SalesVolume     uint32 `json:"sales_volume"`
	L2CategoryId    map[uint32]struct{}
	L1CategoryId    map[uint32]struct{}
	PartnerType     o2oalgo.PartnerType
	IsSelfPickUp    uint32 `json:"is_self_pick_up"`
	IsFoodyDelivery uint32 `json:"is_foody_delivery"`

	// VN Extra Match 相关，5种情况, 档位与赋值相反主要是方便排序
	// 0: 默认值;
	// 1: 第四档,去除音调去除-后完全一样
	// 2: 第三档,去除音调后完全一样
	// 3: 第二档,去除-后完全一样
	// 4: 第一档,完全一样
	ExactMatch  uint32            `json:"exact_match"`
	ItemFeature *food.ItemFeature `json:"ItemFeature"` // 模型预估item特征

	// listwise 相关
	//ItemEvaluatorScore  float64                           `json:"ItemEvaluatorScore"`   // 用于单词排序
	//ItemEvaluatorScores map[string]float64                `json:"ItemEvaluatorScores"`  // 用于记录序列中的分数，debug 日志
	CateRelevance     float64             `json:"cate_relevance"`      // "StoreCateExpand" 路召回原始分数,用于计算CateRelevanceNorm
	CateRelevanceNorm float64             `json:"cate_relevance_norm"` // "StoreCateExpand" 路召回归一化分数,用于计算cate_relevance因子，以及赋值给c_i_cate_rel 特征
	StoreNameTerms    map[string]struct{} `json:"store_name_terms"`    // store name去掉标点符号、统一小写后，按照空格切分，用于计算jaccard_sim
	//ItemEvaluateParams  map[string]map[string]interface{} `json:"item_evaluate_params"` // generator_name: param_key: value

	// commission
	CommissionPlan     *o2oalgo.ItemToCommissionPlan `json:"commission_plan"`
	BaseCommissionRate uint32                        `json:"base_commission_rate"`
	SameBrandCount     uint32

	// deboost 相关
	DeboostType          int     `json:"deboost_type"`
	DeboostCategoryType  int     `json:"deboost_category_type"`
	DeboostCategoryScore float64 `json:"deboost_category_score"`

	// diff
	IsMockPCtrScore int `json:"is_mock_p_ctr_score"`
	IsMockPCvrScore int `json:"is_mock_p_cvr_score"`
	IsMockPLtrScore int `json:"is_mock_p_ltr_score"`
	IsMockPUeScore  int `json:"is_mock_p_ue_score"`
	IsMockPRelScore int `json:"is_mock_p_rel_score"`
	IsMockRelScore  int `json:"is_mock_rel_score"`

	// 召回配置化平台配置过滤
	FilterRecallConfigDistance        float64 `json:"filter_recall_config_distance"`
	FilterRecallConfigPRelevanceScore float64 `json:"filter_recall_config_p_relevance_score"`
	IsFilterByPRelevanceScore         int     `json:"is_filter_by_p_relevance_score"`

	// for dump log
	NormalRecallFilterFlag int `json:"normal_recall_filter_flag"` // 0表示不是normal召回，1表示被normal召回但被过滤了，2表示被normal召回且没被过滤
	IsNormalEnterPredict   int `json:"is_normal_enter_predict"`   // 0表示没有进入精排，1表示进入了精排
}

// TH, MY all
// ID old  ==> 未使用
func (s *StoreInfo) distanceScoreV1() float64 {
	dis := float64(1)
	if s.Distance < 1000 {
		dis = 10
	} else if s.Distance < 3000 {
		dis = 9
	} else if s.Distance < 5000 {
		dis = 8
	} else if s.Distance < 6000 {
		dis = 6
	} else if s.Distance < 8000 {
		dis = 5
	} else if s.Distance < 10000 {
		dis = 3
	} else if s.Distance < 15000 {
		dis = 2
	}
	return dis * 0.1
}

// ID new
func (s *StoreInfo) DistanceScoreV2() float64 {
	score := 1 - math.Min(s.Distance/1000, apollo.AlgoApolloCfg.TruncDistance)/apollo.AlgoApolloCfg.TruncDistance
	return score
}

// vn. python 逻辑，可以废弃
func (s *StoreInfo) distanceScoreV3() float64 {
	if s.ESScore == 0.0 {
		return 0.0
	}
	distance := s.Distance
	distanceRate := DistanceBoostPercentage * DistancePivot / (DistancePivot + distance/1000) //km
	score := s.ESScore * (1 + distanceRate)
	return score
}

// 统一赋值
func (s *StoreInfo) CountDistanceScore(handlerType traceinfo.HandlerType) float64 {
	if handlerType == traceinfo.HandlerTypeSearchMainSite {
		return s.distanceScoreV1()
	}
	if env.GetCID() == cid.ID || env.GetCID() == cid.VN {
		return s.DistanceScoreV2()
	}
	return s.distanceScoreV1()
}

// 单位m
func (s *StoreInfo) CountDistance(handlerType traceinfo.HandlerType, lon, lat float64) float64 {
	if handlerType == traceinfo.HandlerTypeSearchStoresForAffiliate {
		return 1000.0 //联盟接口默认都是1km
	}

	distance := MaxDistance // m
	if util.IsZeroFloat64(lon) && util.IsZeroFloat64(lat) {
		return distance
	}
	if util.IsZeroFloat32(s.Location.GetLongitude()) && util.IsZeroFloat32(s.Location.GetLatitude()) {
		return distance
	}
	distance = geohash.DistanceSimplify(
		/* lonX= */ lon,
		/* latX= */ lat,
		/* lonY= */ float64(s.Location.GetLongitude()),
		/* latY= */ float64(s.Location.GetLatitude()))
	return distance
}

// 1: 0~5km,  2:5~10km, 3:10~
func (s *StoreInfo) CountDistanceGroup5km() uint32 {
	distance := s.Distance
	if distance <= DistanceLimit5km {
		return 1
	}
	if distance > DistanceLimit5km && distance <= DistanceLimit10km {
		return 2
	}
	return 3
}

// 1: 小于5条评论数,  2: 大于等于5条评论数
func (s *StoreInfo) CountRatingTotalGroup5() uint32 {
	if s.RatingTotal < RatingTotalLimit5 {
		return 1
	}
	return 2
}

func (s *StoreInfo) GetBrandId() uint64 {
	if s == nil || s.BrandId == 0 {
		return 0
	}
	return s.BrandId
}

func (s *StoreInfo) GetMerchantId() uint64 {
	if s == nil || s.MerchantId == 0 {
		return 0
	}
	return s.MerchantId
}

func (s *StoreInfo) GetOpeningScore(query string, queryAscii string) float64 {
	if s.ESScore == 0.0 {
		return 0.0
	}
	return s.ESScore * s.GetOpeningRatio(query, queryAscii)
}

func (s *StoreInfo) GetOpeningRatio(query string, queryAscii string) float64 {
	if s.DisplayOpeningStatus == o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN {
		return 1.0
	}
	factor := ClosedMerchantFactor
	if s.StoreNameAscii == queryAscii {
		factor = 0.6
		if s.StoreNameAscii == query {
			factor = 0.7
			if s.StoreName == query {
				factor = 0.9
			}
		}
	} else {
		nameParts := strings.Split(s.StoreName, "-")
		if len(nameParts) > 1 {
			name := strings.ToLower(strings.TrimSpace(nameParts[0]))
			asciiName := util2.ToAscii(name)
			if asciiName == queryAscii {
				factor = 0.4
				if asciiName == query {
					factor = 0.5
					if name == query {
						factor = 0.6
					}
				}
			}
		}
	}
	return factor
}

func (s *StoreInfo) GetOpeningState() int32 {
	switch s.DisplayOpeningStatus {
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN:
		return 0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE:
		return 1
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE:
		return 2
	default:
		return math.MaxInt32
	}
}

func (s *StoreInfo) GetOpeningStateWithPrecise() int32 {
	// todo: 特殊逻辑，精准店铺需要跳过门店下沉的逻辑，那么就当成开店状态处理即可
	// extra match 的门店, 需要按照门店状态来排.
	if s.ExactMatch == 0 && s.IsPreciseStore == 1 {
		return 0
	}
	if cid.IsVNRegion() && s.StoreAvailableDishCnt <= 0 {
		return 3 // 无可售卖菜品的门店，优先级比closed还低
	}

	switch s.DisplayOpeningStatus {
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_OPEN:
		return 0
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_PAUSE:
		return 1
	case o2oalgo.DisplayOpeningStatus_DISPLAY_OPENING_STATUS_CLOSE:
		return 2
	default:
		return math.MaxInt32
	}
}

func IsSortEnhancement(traceInfo *traceinfo.TraceInfo) (bool, uint32) {
	if len(traceInfo.TraceRequest.GetFilterType().GetCategoryType()) != 1 {
		return false, 0
	}
	if traceInfo.TraceRequest.GetFilterType().GetCategoryType()[0] != 1002 {
		return false, 0
	}
	targetL1Id, exist := NonFoodCategoryMap[traceInfo.TraceRequest.FocusServiceId]
	if exist {
		return true, targetL1Id
	}
	return false, 0
}

func (s *StoreInfo) IsStoreNeedEnhancement(ctx context.Context, targetL1 uint32) bool {
	if s == nil || targetL1 == 0 {
		return false
	}
	if s.MainCategory != nil && s.MainCategory.Level1Id == targetL1 {
		return true
	}
	if s.SubCategory != nil {
		for _, category := range s.SubCategory {
			if category.Level1Id == targetL1 {
				return true
			}
		}
	}
	return false
}

func (s *StoreInfo) GetMainSiteScore() float64 {
	return s.MainSiteScore
}

func (s *StoreInfo) GetMainSiteScoreStr() string {
	return s.MainSiteScoreExpressionStr
}

func (s *StoreInfo) CountCategoryMatchScore(queryCategories []*qp.CategoryIntention) uint32 {
	if len(queryCategories) == 0 || (len(s.MainCategoryNameStr) == 0 && len(s.SubCategoryNameStr) == 0) {
		return 0
	}
	var isL1Hit, isL2Hit bool
	var score uint32
	for _, cate := range queryCategories {
		if s.MainCategory != nil && s.MainCategory.Level1Name == cate.GetLevel1Name() {
			isL1Hit = true
		}
		if s.MainCategory != nil && s.MainCategory.Level2Name == cate.GetLevel2Name() {
			isL2Hit = true
		}
		for _, subStoreCate := range s.SubCategory {
			if subStoreCate != nil && subStoreCate.Level1Name == cate.GetLevel1Name() {
				isL1Hit = true
			}
			if subStoreCate != nil && subStoreCate.Level2Name == cate.GetLevel2Name() {
				isL2Hit = true
			}
		}
	}
	if isL1Hit {
		score += 1
	}
	if isL2Hit {
		score += 2
	}
	return score
}

func (s *StoreInfo) IsSelfPickupRecall() bool {
	if s == nil || len(s.RecallTypes) == 0 {
		return false
	}
	for _, recallType := range s.RecallTypes {
		if recallType == foodalgo_search.RecallType_SelfPickup.String() {
			return true
		}
	}
	return false
}

func (s *StoreInfo) IsOnlySelfPickupRecall() bool {
	if s == nil || len(s.RecallTypes) == 0 {
		return false
	}
	if len(s.RecallTypes) == 1 && s.RecallTypes[0] == foodalgo_search.RecallType_SelfPickup.String() {
		return true
	}
	return false
}

func (s *StoreInfo) GetSalesVolumeScore() float64 {
	truncSalesVolume := 1.0
	if apollo.SearchApolloCfg.MainSiteDishTruncSalesVolume > 0.0 {
		truncSalesVolume = apollo.SearchApolloCfg.MainSiteDishTruncSalesVolume
	}

	salesVolumeScore := math.Min(float64(s.SalesVolume), truncSalesVolume) / truncSalesVolume
	return salesVolumeScore
}

func (s *StoreInfo) IsStoreCateExpandRecallWithScore() (bool, float64) {
	score := 0.0
	if s == nil || len(s.RecallTypes) == 0 {
		return false, score
	}
	for i, recallType := range s.RecallTypes {
		if recallType == foodalgo_search.RecallType_StoreCateExpand.String() {
			if i < len(s.RecallScores) {
				score = s.RecallScores[i]
			}
			return true, score
		}
	}
	return false, score
}
