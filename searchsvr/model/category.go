package model

import (
	"context"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"
	"go.uber.org/zap"
)

var categoryLevel1Mapping = map[uint32]string{
	1:  "Bakery & Cake",
	2:  "Beverages",
	3:  "Dessert",
	4:  "Fast Food",
	5:  "Japanese",
	6:  "Chinese",
	7:  "Korean",
	8:  "Italian",
	9:  "Middle-East",
	10: "Thai",
	11: "Western",
	12: "Indian",
	13: "Vietnamese",
	14: "Meat",
	15: "Seafood",
	16: "Noodles",
	18: "Healthy Food",
	19: "Rice",
	20: "Indonesian",
	21: "Convenient Store",
	22: "Malaysian",
	25: "Mexican",
	27: "Fusion",
	28: "Fine Dining",
	40: "Snack",
	60: "Vegetarian",
}

var categoryLevel2Mapping = map[uint32]string{
	1:    "Bread",
	2:    "Cake",
	3:    "Cookies",
	4:    "Juice & Smoothies",
	5:    "Milk",
	6:    "Tea",
	7:    "Coffee",
	8:    "Bubble Tea",
	9:    "Ice Cream",
	10:   "Sweets",
	11:   "Martabak",
	12:   "Snack",
	13:   "Fruits",
	15:   "Pizza & Pasta",
	16:   "Burger",
	17:   "Fried Chicken",
	18:   "Kebab",
	19:   "Sandwich & Wraps",
	20:   "Hotdog",
	21:   "Japanese",
	22:   "Chinese",
	23:   "Korean",
	24:   "Italian",
	25:   "Middle-East",
	26:   "Thai",
	27:   "Western",
	28:   "Indian",
	29:   "Vietnamese",
	30:   "Beef",
	31:   "Chicken",
	32:   "Pork",
	33:   "Duck",
	34:   "Satay",
	35:   "Meatball & Soto",
	36:   "Seafood",
	37:   "Fried Noodles",
	38:   "Noodle Soup",
	39:   "Chicken Noodles",
	40:   "Bakmi",
	43:   "Salad",
	44:   "Konjac",
	45:   "Jamu & Herbal Drink",
	46:   "Rice",
	47:   "Porridge",
	48:   "Chicken Rice",
	49:   "Grilled Rice",
	50:   "Fried Rice",
	51:   "Rice Bowl",
	52:   "Padang",
	53:   "Manado",
	54:   "Jawa",
	55:   "Sunda",
	56:   "Bali",
	57:   "Aceh",
	58:   "Medan",
	59:   "Convenient Store",
	60:   "Alcohol",
	1001: "Bakery",
	1002: "Pastries",
	1101: "Noodles",
	1302: "BBQ/Yakiniku",
	1303: "Ramen",
	1304: "Sukiyaki/Shabu",
	1305: "Sushi",
	1306: "Izakaya",
	1307: "Donburi",
	1308: "Curry Rice",
	1309: "Gyoza",
	1402: "Mala",
	1403: "Dimsum",
	1404: "Chinese Hotpot",
	1405: "Chinese Congee",
	1801: "International",
	1902: "Steak",
	1903: "Cold Cuts & Platter",
	1904: "Tapas",
	1905: "Pizza",
	1906: "Pasta",
	2006: "Soft Drink",
	2008: "Local Beverage",
	2009: "Beverages",
	2102: "Somtum",
	2103: "Street Food",
	2104: "Cooked to Order",
	2105: "Yum",
	2106: "Esan",
	2107: "Southern",
	2108: "Northern",
	2109: "Thai Congee",
	2110: "Thai Hotpot",
	2111: "Thai Curry",
	2201: "Malaysian",
	2202: "Satay",
	2501: "Mexican",
	2701: "Fusion",
	2801: "Fine Dining",
	3001: "Yogurt",
	3004: "Pancake",
	3005: "Shaved Ice",
	3006: "Waffle",
	3007: "Crepe",
	3008: "Bingsu",
	3011: "Dessert",
	4001: "Meatball/Fishball",
	4002: "Soup",
	4004: "Fresh Fruits",
	4005: "Appetizer",
	5001: "Poke Bowl",
	5002: "Acai Bowl",
	5003: "Organic",
	5007: "Healthy",
	6001: "Vegetarian",
	6002: "Vegan",
	7007: "Fast Food",
	7008: "Fried Food",
	8007: "Lamb",
}

var categoryLevel1MappingStandard = map[uint32]string{
	10: "Bakery & Cake",
	11: "Noodles",
	12: "Rice",
	13: "Japanese",
	14: "Chinese",
	15: "Korean",
	16: "Italian",
	17: "Middle-East",
	18: "International",
	19: "Western",
	20: "Beverages",
	21: "Thai",
	22: "Malaysian",
	23: "Indian",
	24: "Vietnamese",
	25: "Mexican",
	26: "Convenient Store",
	27: "Fusion",
	28: "Fine Dining",
	29: "Indonesian",
	30: "Dessert",
	31: "Halal",
	40: "Snack",
	50: "Healthy Food",
	60: "Vegetarian",
	70: "Fast Food",
	71: "Pizza & Pasta",
	72: "Burger",
	73: "Fried Chicken",
	75: "Chicken",
	76: "Chinese Muslim",
	77: "Pharmacy",
	80: "Meat",
	90: "Seafood",
}

var categoryLevel2MappingStandard = map[uint32]string{
	1001: "Bakery",
	1002: "Pastries",
	1003: "Cake",
	1004: "Cookies",
	1005: "Bread & Pastries",
	1101: "Noodles",
	1102: "Fried Noodles",
	1103: "Noodle Soup",
	1104: "Chicken Noodles",
	1105: "Bakmi",
	1201: "Rice",
	1202: "Chicken Rice",
	1203: "Porridge",
	1204: "Fried Rice",
	1205: "Grilled Rice",
	1206: "Rice Bowl",
	1207: "Mixed Rice",
	1208: "Nasi Lemak",
	1301: "Japanese",
	1302: "BBQ/Yakiniku",
	1303: "Ramen",
	1304: "Sukiyaki/Shabu",
	1305: "Sushi",
	1306: "Izakaya",
	1307: "Donburi",
	1308: "Curry Rice",
	1309: "Gyoza",
	1310: "Teppanyaki",
	1311: "Takoyaki",
	1401: "Chinese",
	1402: "Mala",
	1403: "Dimsum",
	1404: "Chinese Hotpot",
	1405: "Chinese Congee",
	1501: "Korean",
	1601: "Italian",
	1701: "Middle-East",
	1801: "International",
	1901: "Western",
	1902: "Steak",
	1903: "Cold Cuts & Platter",
	1904: "Tapas",
	1905: "Pizza",
	1906: "Pasta",
	2001: "Juice & Smoothies",
	2002: "Milk",
	2003: "Tea",
	2004: "Coffee",
	2005: "Bubble Tea",
	2006: "Soft Drink",
	2007: "Alcohol",
	2008: "Local Beverage",
	2009: "Beverages",
	2101: "Thai",
	2102: "Somtum",
	2103: "Street Food",
	2104: "Cooked to Order",
	2105: "Yum",
	2106: "Esan",
	2107: "Southern",
	2108: "Northern",
	2109: "Thai Congee",
	2110: "Thai Hotpot",
	2111: "Thai Curry",
	2201: "Malaysian",
	2202: "Satay",
	2203: "Mamak",
	2204: "Yong Tau Foo",
	2301: "Indian",
	2401: "Vietnamese",
	2501: "Mexican",
	2601: "Convenient Store",
	2701: "Fusion",
	2801: "Fine Dining",
	2901: "Padang",
	2902: "Manado",
	2903: "Jawa",
	2904: "Sunda",
	2905: "Bali",
	2906: "Aceh",
	2907: "Medan",
	2908: "Indonesian",
	3001: "Yogurt",
	3002: "Ice Cream & Yogurt",
	3003: "Local Dessert",
	3004: "Pancake",
	3005: "Shaved Ice",
	3006: "Waffle",
	3007: "Crepe",
	3008: "Bingsu",
	3009: "Martabak",
	3010: "Fruits",
	3011: "Dessert",
	3101: "Halal",
	4001: "Meatball/Fishball",
	4002: "Soup",
	4003: "Snack",
	4004: "Fresh Fruits",
	4005: "Appetizer",
	4006: "Kuih",
	4007: "Waffle",
	4008: "Apam Balik",
	4009: "Pisang Goreng",
	5001: "Poke Bowl",
	5002: "Acai Bowl",
	5003: "Organic",
	5004: "Salad & Poke Bowl",
	5005: "Konjac",
	5006: "Jamu & Herbal Drink",
	5007: "Healthy",
	5008: "Sandwich & Wraps",
	6001: "Vegetarian",
	6002: "Vegan",
	7001: "Pizza & Pasta",
	7002: "Burger",
	7003: "Fried Chicken",
	7004: "Sandwich & Wraps",
	7005: "Hotdog",
	7006: "Kebab",
	7007: "Fast Food",
	7008: "Fried Food",
	7101: "Pizza & Pasta",
	7201: "Burger",
	7301: "Fried Chicken",
	7501: "Ayam",
	7502: "Korean Fried Chicken",
	7503: "Chicken",
	7601: "Chinese Muslim",
	7701: "Pharmacy",
	8001: "Beef",
	8002: "Chicken",
	8003: "Pork",
	8004: "Duck",
	8005: "Satay",
	8006: "Meatball & Soto",
	8007: "Lamb",
	9001: "Seafood",
}

type Category struct {
	Level1Id   uint32 `json:"level1_id,omitempty"`
	Level1Name string `json:"level1_name,omitempty"`
	Level2Id   uint32 `json:"level2_id,omitempty"`
	Level2Name string `json:"level2_name,omitempty"`
}

var l1Mapping, l2Mapping map[uint32]string // 实际使用的映射表, vn只有id, 没有用到name

func init() {
	if env.GetCID() == cid.ID || env.GetCID() == cid.XX {
		l1Mapping = categoryLevel1Mapping
		l2Mapping = categoryLevel2Mapping
	}
	if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
		l1Mapping = categoryLevel1MappingStandard
		l2Mapping = categoryLevel2MappingStandard
	}
	// TH词表特殊处理, 待接入store_category DB 后删除
	if env.GetCID() == cid.TH {
		l2Mapping[3002] = "Ice Cream"
		l2Mapping[5004] = "Salad"
	}
}

func transMap2Category(categoryMap map[string]uint32) *Category {
	// {"level_1":1,"level_2":1}
	level1Id, ok1 := categoryMap["level_1"]
	level2Id, ok2 := categoryMap["level_2"]
	if ok1 && ok2 {
		category := &Category{}
		category.Level1Id = level1Id
		category.Level2Id = level2Id
		if len(l1Mapping[level1Id]) > 0 && len(l2Mapping[level2Id]) > 0 {
			category.Level1Name = strings.ToLower(l1Mapping[level1Id])
			category.Level2Name = strings.ToLower(l2Mapping[level2Id])
		}
		return category
	}
	return nil
}

func GetMainCategory(ctx context.Context, storeId uint64, mainCategoryStr string) *Category {
	if len(mainCategoryStr) == 0 {
		return nil
	}
	mainCategoryMap := make(map[string]uint32, 2)
	err := json.Unmarshal([]byte(mainCategoryStr), &mainCategoryMap)
	if err != nil {
		logkit.FromContext(ctx).Error("json Unmarshal fail: main_category", zap.Uint64("storeId", storeId), zap.String("mainCategory", mainCategoryStr))
		return nil
	}
	return transMap2Category(mainCategoryMap)
}

func GetSubCategory(ctx context.Context, storeId uint64, subCategoryStr string) []*Category {
	if len(subCategoryStr) == 0 {
		return nil
	}
	var subCategoryList []*Category
	var subCategoryMap []map[string]uint32
	err := json.Unmarshal([]byte(subCategoryStr), &subCategoryMap)
	if err != nil {
		logkit.FromContext(ctx).Error("json Unmarshal fail: sub_category", zap.Uint64("storeId", storeId), zap.String("subCategory", subCategoryStr))
		return subCategoryList
	}
	// [{"level_1":1,"level_2":2},{"level_1":2,"level_2":4},{"level_1":4,"level_2":15},{"level_1":4,"level_2":16}]
	for i := range subCategoryMap {
		subCategory := transMap2Category(subCategoryMap[i])
		if subCategory != nil {
			subCategoryList = append(subCategoryList, subCategory)
		}
	}
	return subCategoryList
}

// 获取category name 总字符串，用"|"连接
func GetMainCategoryNameStr(mainCategory *Category) string {
	if mainCategory != nil && len(mainCategory.Level1Name) > 0 && len(mainCategory.Level2Name) > 0 {
		return mainCategory.Level1Name + "|" + mainCategory.Level2Name
	}
	return ""
}

// 获取category name 总字符串，用"|"连接
func GetSubCategoryNameStr(subCategories []*Category) []string {
	var t = make([]string, 0)
	for _, subCategory := range subCategories {
		if subCategory != nil && len(subCategory.Level1Name) > 0 && len(subCategory.Level2Name) > 0 {
			t = append(t, subCategory.Level1Name+"|"+subCategory.Level2Name)
		}
	}
	return t
}

func GetL2CategoryId(mainCategory *Category, subCategories []*Category) map[uint32]struct{} {
	l2CategoryIdMap := make(map[uint32]struct{})
	if mainCategory != nil && mainCategory.Level2Id > 0 {
		l2CategoryIdMap[mainCategory.Level2Id] = struct{}{}
	}
	if len(subCategories) > 0 {
		for _, subCategory := range subCategories {
			if subCategory != nil && subCategory.Level2Id > 0 {
				l2CategoryIdMap[subCategory.Level2Id] = struct{}{}
			}
		}
	}
	return l2CategoryIdMap
}

func GetL1CategoryId(mainCategory *Category, subCategories []*Category) map[uint32]struct{} {
	l1CategoryIdMap := make(map[uint32]struct{})
	if mainCategory != nil && mainCategory.Level1Id > 0 {
		l1CategoryIdMap[mainCategory.Level1Id] = struct{}{}
	}
	if len(subCategories) > 0 {
		for _, subCategory := range subCategories {
			if subCategory != nil && subCategory.Level1Id > 0 {
				l1CategoryIdMap[subCategory.Level1Id] = struct{}{}
			}
		}
	}
	return l1CategoryIdMap
}
