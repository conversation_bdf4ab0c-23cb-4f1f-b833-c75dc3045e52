package model

import (
	"fmt"
	"strconv"
	"strings"

	"git.garena.com/shopee/feed/comm_lib/env"
	qp "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_queryprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

const (
	distance              = 20 //km
	downgradeDistance     = 6
	size                  = 500
	mainSiteDishStoreSize = 100
	NonHalal              = 1
	Halal                 = 2
	MustAndMatchType      = 1
	MustOrMatchType       = 2
	ShouldMatchType       = 3
)

var NonHalalWord = []string{"non halal", "no halal", "not halal", "nonhalal", "nohalal", "nothalal", "non-halal", "no-halal", "not-halal"}
var HalalWord = "halal"

// TODO 考虑是否直接使用pb
type SearchRequest struct {
	BuyerID                    uint64
	Keyword                    string
	Location                   *foodalgo_search.Geo
	HalalType                  int
	FilterByHalalType          bool
	RecallByHalalType          bool
	FilterByPreferredMerchant  bool
	FilterTypeShippingDistance uint32
}

func NewSearchRequest(traceInfo *traceinfo.TraceInfo, queryKeyword string) *SearchRequest {
	lowerKeyword := strings.ToLower(queryKeyword)
	filterByHalalType := false
	recallByHalalType := false
	var halalType int
	if env.GetCID() == cid.MY {
		// 判断是否为non-halal
		for _, str := range NonHalalWord {
			if strings.Contains(lowerKeyword, str) {
				filterByHalalType = true
				halalType = NonHalal
				newKeyword := strings.Trim(lowerKeyword, " ")
				if newKeyword == str {
					recallByHalalType = true
				}
				break
			}
		}
		if !filterByHalalType {
			// 判断是否为Halal
			if strings.Contains(lowerKeyword, HalalWord) {
				filterByHalalType = true
				newKeyword := strings.Trim(lowerKeyword, " ")
				if newKeyword == HalalWord {
					recallByHalalType = true
				}
				halalType = Halal
			}
		}
	}
	filterByPreferredMerchant := false
	if traceInfo.TraceRequest.FilterType != nil {
		filterByPreferredMerchant = traceInfo.TraceRequest.FilterType.GetIsPreferredMerchant()
	}
	return &SearchRequest{
		Keyword: queryKeyword,
		Location: &foodalgo_search.Geo{
			Latitude:  proto.Float32(float32(traceInfo.TraceRequest.Latitude)),
			Longitude: proto.Float32(float32(traceInfo.TraceRequest.Longitude)),
		},
		FilterByHalalType:          filterByHalalType,
		HalalType:                  halalType,
		RecallByHalalType:          recallByHalalType,
		FilterByPreferredMerchant:  filterByPreferredMerchant,
		FilterTypeShippingDistance: traceInfo.TraceRequest.GetFilterType().GetShippingDistance(),
	}
}

func (r *SearchRequest) ToStoreESFunctionScoreSearchForMainSite(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	grabfoodTags := traceInfo.QPResult.StoreIntents
	var should []elastic.Query
	var must []elastic.Query
	// 如果命中门店意图，需要加上门店一级分类筛选条件
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		should = append(should, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "branch_name", "tags"))
		for _, grabfoodTag := range grabfoodTags {
			should = append(should, elastic.NewMatchQuery("grabfood_tags", grabfoodTag))
		}
	} else {
		must = append(must, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "branch_name", "tags"))
	}
	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}
	store := es.NewESSearch(es.WithMustQueries(must), es.WithShouldQueries(should), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(mainSiteDishStoreSize),
		es.WithSourceInclude("best_selling_dish_id", "grabfood_tags"),
		es.WithRecallType("ToStoreESFunctionScoreSearchForMainSite"),
		es.WithRecallId("2001"),
	)
	return store
}

// MY TH新召回策略
func (r *SearchRequest) ToStoreESFunctionScoreSearchNew(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	grabfoodTags := traceInfo.QPResult.StoreIntents
	fsq := elastic.NewFunctionScoreQuery()
	storeIntentHit := false
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		storeIntentHit = true
	}
	dishIntentHit := false
	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		dishIntentHit = true
	}
	var should []elastic.Query
	var must []elastic.Query
	var storeIntentStoreNameBm25, storeIntentTagMatch, storeIntentMatch float64
	var dishIntentStoreNameBm25, dishIntentTagMatch, grabFoodTagBm25 float64
	storeIntentStoreNameBm25 = 0.45
	storeIntentTagMatch = 1.35
	storeIntentMatch = 1.1
	dishIntentStoreNameBm25 = 0.3
	dishIntentTagMatch = 1.2
	grabFoodTagBm25 = 0.2
	otherStoreNameBm25 := 0.4
	otherTagMatch := 1.2

	Bm25Weight := 1.0
	TagMatchWeight := 1.0

	// 都不命中意图
	if !storeIntentHit && !dishIntentHit {
		Bm25Weight = otherStoreNameBm25
		TagMatchWeight = otherTagMatch
	} else if (storeIntentHit && dishIntentHit) || storeIntentHit { // 都命中意图或命中门店意图
		Bm25Weight = storeIntentStoreNameBm25
		TagMatchWeight = storeIntentTagMatch
		if len(traceInfo.OptIntervention.QueryStoreTop) > 0 {
			// 命中门店意图
			fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(storeIntentMatch))
		}
	} else { // 命中菜品意图
		Bm25Weight = dishIntentStoreNameBm25
		TagMatchWeight = dishIntentTagMatch
	}
	if env.GetCID() == cid.TH {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags_segments", buildStrFromArr(traceInfo.QPResult.Segments))), elastic.NewWeightFactorFunction(TagMatchWeight))
	} else {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags", r.Keyword)), elastic.NewWeightFactorFunction(TagMatchWeight))
	}
	fsq = fsq.ScoreMode("sum").BoostMode("sum")

	// 召回不同域加boost
	if env.GetCID() == cid.ID || env.GetCID() == cid.XX {
		must = append(must, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "branch_name", "tags"))
	} else if env.GetCID() == cid.MY {
		// MY站点,为brand_name
		mulQuery := elastic.NewMultiMatchQuery(r.Keyword)
		if Bm25Weight == 0 {
			Bm25Weight = 1
		}
		mulQuery = mulQuery.FieldWithBoost("tags", 0.0) // tag bm25不需要
		mulQuery = mulQuery.FieldWithBoost("real_store_name", Bm25Weight)
		mulQuery = mulQuery.FieldWithBoost("brand_name", Bm25Weight)
		mulQuery = mulQuery.TieBreaker(1.0)
		must = append(must, mulQuery)
	} else { // TH
		if Bm25Weight == 0 {
			Bm25Weight = 1
		}
		if env.GetCID() == cid.TH {
			mulQuery := elastic.NewMultiMatchQuery(buildStrFromArr(traceInfo.QPResult.Segments))
			mulQuery = mulQuery.FieldWithBoost("tags_segments", 0.0) // tag bm25不需要
			mulQuery = mulQuery.FieldWithBoost("store_name_segments", Bm25Weight)
			mulQuery = mulQuery.TieBreaker(1.0)
			must = append(must, mulQuery)
		} else {
			mulQuery := elastic.NewMultiMatchQuery(r.Keyword)
			mulQuery = mulQuery.FieldWithBoost("tags", 0.0) // tag bm25不需要
			mulQuery = mulQuery.FieldWithBoost("store_name", Bm25Weight)
			mulQuery = mulQuery.TieBreaker(1.0)
			must = append(must, mulQuery)
		}
	}
	// 如果命中门店意图，需要加上门店一级分类筛选条件
	if storeIntentHit {
		for _, grabfoodTag := range grabfoodTags {
			grabFoodTagQuery := elastic.NewMatchQuery("grabfood_tags", grabfoodTag)
			grabFoodTagQuery = grabFoodTagQuery.Boost(grabFoodTagBm25)
			should = append(should, grabFoodTagQuery)
		}
		// 需grabfood tag扩召回, 改为should
		if len(grabfoodTags) > 0 {
			should = append(should, must...)
			must = make([]elastic.Query, 0)
		}
	}

	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}
	var sort []elastic.Sorter
	if env.GetCID() == cid.TH || env.GetCID() == cid.MY {
		if traceInfo.PredictConfig.IsOpenLocationSort {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("_score").Desc(),
				elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
				elastic.NewFieldSort("_id").Asc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("_score").Desc(),
				elastic.NewFieldSort("_id").Asc(),
			}
		}
	} else if env.GetCID() == cid.ID || env.GetCID() == cid.XX {
		sort = []elastic.Sorter{
			elastic.NewFieldSort("_score").Desc(),
			elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
			elastic.NewFieldSort("_id").Asc(),
		}
	} else {
		sort = []elastic.Sorter{
			elastic.NewFieldSort("_score").Desc(),
			elastic.NewFieldSort("_id").Asc(),
		}
	}
	var store *es.ESSearch
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
		// 在MY站点,搜索词只要halal type 类型,直接召回对应halal type的门店.
		if r.RecallByHalalType {
			must = make([]elastic.Query, 0)
			should = make([]elastic.Query, 0)
		}
	}
	store = es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithMustQueries(must), es.WithSorters(sort), es.WithShouldQueries(should), es.WithFilters(filters), es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithSourceInclude("store_name", "tags", "best_selling_dish_id", "segments", "max_word_segments", "grabfood_tags", "store_sell_week", "store_name_segments"),
		es.WithRecallType("ToStoreESFunctionScoreSearchNew"),
		es.WithRecallId("1001"),
	)
	logkit.Debug("SearchRequest.ToStoreESFunctionScoreSearchNew", zap.Any("store", store))
	return store
}

// th,my菜品召回第一阶段,先召回门店ID
func (r *SearchRequest) ToStoreEsWithDishInfoSearchNew(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	fsq := elastic.NewFunctionScoreQuery()
	// 调权参数
	var storeIntentStoreNameBm25, storeIntentDishNameBm25, storeIntentTagMatch, storeIntentMatch float64
	var dishIntentStoreNameBm25, dishIntentDishNameBm25, dishIntentTagMatch, dishIntentMatch float64
	storeIntentStoreNameBm25 = 0.45
	storeIntentDishNameBm25 = 0.1
	storeIntentTagMatch = 1.35
	storeIntentMatch = 1.1
	dishIntentStoreNameBm25 = 0.3
	dishIntentDishNameBm25 = 0.4
	dishIntentTagMatch = 1.2
	dishIntentMatch = 1.1
	otherStoreNameBm25 := 0.4
	otherDishNameBm25 := 0.4
	otherTagMatch := 1.2

	storeIntentHit := false
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		storeIntentHit = true
	}
	dishIntentHit := false
	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		dishIntentHit = true
	}
	dishBm25Weight := 1.0
	storeBm25Weight := 1.0
	tagMatchWeight := 1.0
	// 只命中门店意图 或 都不命中 或 都命中
	if !storeIntentHit && !dishIntentHit {
		dishBm25Weight = otherDishNameBm25
		storeBm25Weight = otherStoreNameBm25
		tagMatchWeight = otherTagMatch
	} else if (storeIntentHit && dishIntentHit) || storeIntentHit { // 只命中门店意图 或 都命中
		dishBm25Weight = storeIntentDishNameBm25
		storeBm25Weight = storeIntentStoreNameBm25
		tagMatchWeight = storeIntentTagMatch
		// 门店意图匹配，店门部分命中，加权
		if env.GetCID() == cid.TH {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("store_name_segments", buildStrFromArr(traceInfo.QPResult.Segments))),
				elastic.NewWeightFactorFunction(storeIntentMatch))
		} else {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("store_name", r.Keyword)),
				elastic.NewWeightFactorFunction(storeIntentMatch))
		}
	} else { // 只命中菜品意图
		dishBm25Weight = dishIntentDishNameBm25
		storeBm25Weight = dishIntentStoreNameBm25
		tagMatchWeight = dishIntentTagMatch
		// 门店意图匹配，菜名部分命中，加权
		if env.GetCID() == cid.TH {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("dish_name", buildStrFromArr(traceInfo.QPResult.Segments))),
				elastic.NewWeightFactorFunction(dishIntentMatch))
		} else {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("dish_name", r.Keyword)),
				elastic.NewWeightFactorFunction(dishIntentMatch))
		}
	}
	if env.GetCID() == cid.TH {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags_segments", buildStrFromArr(traceInfo.QPResult.Segments))), elastic.NewWeightFactorFunction(tagMatchWeight))
	} else {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags", r.Keyword)), elastic.NewWeightFactorFunction(tagMatchWeight))
	}
	fsq = fsq.ScoreMode("sum").BoostMode("sum")

	var must []elastic.Query
	//must = []elastic.Query{
	//	elastic.NewMatchQuery("dish_name", r.Keyword),
	//}
	if dishBm25Weight == 0 {
		dishBm25Weight = 1
	}
	if storeBm25Weight == 0 {
		storeBm25Weight = 1
	}
	if env.GetCID() == cid.TH {
		mulQuery := elastic.NewMultiMatchQuery(buildStrFromArr(traceInfo.QPResult.Segments))
		mulQuery = mulQuery.FieldWithBoost("store_name_segments", storeBm25Weight)
		mulQuery = mulQuery.FieldWithBoost("dish_name", dishBm25Weight)
		mulQuery = mulQuery.TieBreaker(1.0)
		must = append(must, mulQuery)
	} else {
		mulQuery := elastic.NewMultiMatchQuery(r.Keyword)
		mulQuery = mulQuery.FieldWithBoost("store_name", storeBm25Weight)
		mulQuery = mulQuery.FieldWithBoost("dish_name", dishBm25Weight)
		mulQuery = mulQuery.TieBreaker(1.0)
		must = append(must, mulQuery)
	}

	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	var dishNameQuery elastic.Query
	dishNameQuery = elastic.NewMatchQuery("dish_name", r.Keyword)
	if env.GetCID() == cid.TH {
		dishNameQuery = elastic.NewMatchQuery("dish_name", buildStrFromArr(traceInfo.QPResult.Segments))
	}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}
	filters = append(filters, dishNameQuery)

	sort := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
	}
	store := es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithSorters(sort), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size), es.WithSourceInclude("_id"),
		es.WithRecallType("ToStoreEsWithDishInfoSearchNew"),
		es.WithRecallId("1002"),
	)
	logkit.Debug("SearchRequest.ToDishESSearchStoreId", zap.Any("dish_search_storeId", store))
	return store
}

// th,my菜品召回第二阶段,召回指定门店的菜品
func (r *SearchRequest) ToDishSecondaryRecallNew(traceInfo *traceinfo.TraceInfo, storeID []uint64) *es.ESSearch {
	fsq := elastic.NewFunctionScoreQuery()
	// 调权参数
	var storeIntentStoreNameBm25, storeIntentDishNameBm25, storeIntentTagMatch, storeIntentMatch float64
	var dishIntentStoreNameBm25, dishIntentDishNameBm25, dishIntentTagMatch, dishIntentMatch float64
	storeIntentStoreNameBm25 = 0.45
	storeIntentDishNameBm25 = 0.1
	storeIntentTagMatch = 1.35
	storeIntentMatch = 1.1
	dishIntentStoreNameBm25 = 0.3
	dishIntentDishNameBm25 = 0.4
	dishIntentTagMatch = 1.2
	dishIntentMatch = 1.1
	otherStoreNameBm25 := 0.4
	otherDishNameBm25 := 0.4
	otherTagMatch := 1.2

	storeIntentHit := false
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		storeIntentHit = true
	}
	dishIntentHit := false
	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		dishIntentHit = true
	}
	dishBm25Weight := 1.0
	storeBm25Weight := 1.0
	tagMatchWeight := 1.0
	// 只命中门店意图 或 都不命中 或 都命中
	if !storeIntentHit && !dishIntentHit {
		dishBm25Weight = otherDishNameBm25
		storeBm25Weight = otherStoreNameBm25
		tagMatchWeight = otherTagMatch
	} else if (storeIntentHit && dishIntentHit) || storeIntentHit { // 只命中门店意图 或 都命中
		dishBm25Weight = storeIntentDishNameBm25
		storeBm25Weight = storeIntentStoreNameBm25
		tagMatchWeight = storeIntentTagMatch
		// 门店意图匹配，店门部分命中，加权
		if env.GetCID() == cid.TH {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("store_name_segments", buildStrFromArr(traceInfo.QPResult.Segments))),
				elastic.NewWeightFactorFunction(storeIntentMatch))
		} else {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("store_name", r.Keyword)),
				elastic.NewWeightFactorFunction(storeIntentMatch))
		}
	} else { // 只命中菜品意图
		dishBm25Weight = dishIntentDishNameBm25
		storeBm25Weight = dishIntentStoreNameBm25
		tagMatchWeight = dishIntentTagMatch
		// 门店意图匹配，菜名部分命中，加权
		if env.GetCID() == cid.TH {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("dish_name_segments", buildStrFromArr(traceInfo.QPResult.Segments))),
				elastic.NewWeightFactorFunction(dishIntentMatch))
		} else {
			fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("dish_name", r.Keyword)),
				elastic.NewWeightFactorFunction(dishIntentMatch))
		}
	}
	if env.GetCID() == cid.TH {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags_segments", buildStrFromArr(traceInfo.QPResult.Segments))), elastic.NewWeightFactorFunction(tagMatchWeight))
	} else {
		fsq = fsq.Add(elastic.NewBoolQuery().Should(elastic.NewMatchQuery("tags", r.Keyword)), elastic.NewWeightFactorFunction(tagMatchWeight))
	}
	fsq = fsq.ScoreMode("sum").BoostMode("sum")

	var must []elastic.Query
	//must = []elastic.Query{
	//	elastic.NewMatchQuery("dish_name", r.Keyword),
	//}
	if dishBm25Weight == 0 {
		dishBm25Weight = 1
	}
	if storeBm25Weight == 0 {
		storeBm25Weight = 1
	}
	if env.GetCID() == cid.TH {
		mulQuery := elastic.NewMultiMatchQuery(buildStrFromArr(traceInfo.QPResult.Segments))
		mulQuery = mulQuery.FieldWithBoost("store_name_segments", storeBm25Weight)
		mulQuery = mulQuery.FieldWithBoost("dish_name_segments", dishBm25Weight)
		mulQuery = mulQuery.TieBreaker(1.0)
		must = append(must, mulQuery)
	} else {
		mulQuery := elastic.NewMultiMatchQuery(r.Keyword)
		mulQuery = mulQuery.FieldWithBoost("store_name", storeBm25Weight)
		mulQuery = mulQuery.FieldWithBoost("dish_name", dishBm25Weight)
		mulQuery = mulQuery.TieBreaker(1.0)
		must = append(must, mulQuery)
	}

	var dishNameQuery elastic.Query
	dishNameQuery = elastic.NewMatchQuery("dish_name", r.Keyword)
	if env.GetCID() == cid.TH {
		dishNameQuery = elastic.NewMatchQuery("dish_name_segments", buildStrFromArr(traceInfo.QPResult.Segments))
	}

	v := make([]interface{}, 0, len(storeID))
	for _, c := range storeID {
		v = append(v, c)
	}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_id", v...),
	}
	filters = append(filters, dishNameQuery)

	aggTopHist := elastic.NewTopHitsAggregation().
		Sort("_score", false).Sort("has_picture", false).
		Sort("sales_volume", false).Sort("_id", false).
		Size(2).FetchSource(false)
	aggMaxScore := elastic.NewMaxAggregation().Script(elastic.NewScript("_score"))
	// 创建Terms桶聚合, top_dishes 这个聚合名称需要与结果解析时的json 保持一致，见 searchsvr/dao/es.go
	aggs := elastic.NewTermsAggregation().Field("store_id").Size(size).SubAggregation("top_dishes", aggTopHist).
		SubAggregation("max_score", aggMaxScore).OrderByAggregation("max_score", false)
	sort := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewFieldSort("store_id").Asc(),
	}
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
	}
	dish := es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithSorters(sort), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithTermsAggregation(aggs),
		es.WithRecallType("ToDishSecondaryRecallNew"),
		es.WithRecallId("4001"),
	)
	logkit.Debug("SearchRequest.ToDishESSearch", zap.Any("dish", dish))
	return dish
}

func buildStrFromArr(arr []string) string {
	var str string
	for _, s := range arr {
		str += s + " "
	}
	return strings.Trim(str, " ")
}

func (r *SearchRequest) ToStoreEsWithDishInfoSearchMainSite(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	must := []elastic.Query{
		elastic.NewMatchQuery("dish_name", r.Keyword),
	}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}
	var sort = []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	dish := es.NewESSearch(es.WithSorters(sort), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(mainSiteDishStoreSize),
		es.WithSourceInclude("best_selling_dish_id", "grabfood_tags"),
		es.WithRecallType("ToStoreEsWithDishInfoSearchMainSite"),
		es.WithRecallId("2002"),
	)
	return dish
}

func (r *SearchRequest) ToDishSecondaryRecall(traceInfo *traceinfo.TraceInfo, storeID []uint64) *es.ESSearch {
	segments := traceInfo.QPResult.Segments
	maxWordSegments := traceInfo.QPResult.MaxWordSegments
	grabfoodTags := traceInfo.QPResult.StoreIntents
	fsq := elastic.NewFunctionScoreQuery()
	grabfoodTagsQ := make([]elastic.Query, 0, len(grabfoodTags))
	segmentsQ := make([]elastic.Query, 0, len(segments))
	maxWordsQ := make([]elastic.Query, 0, len(maxWordSegments))
	var isUsedFsq bool
	for _, tag := range grabfoodTags {
		grabfoodTagsQ = append(grabfoodTagsQ, elastic.NewTermQuery("grabfood_tags", tag))
	}
	for _, maxWord := range maxWordSegments {
		maxWordsQ = append(maxWordsQ, elastic.NewTermQuery("max_word_segments", maxWord))
	}
	for _, realName := range segments {
		segmentsQ = append(segmentsQ, elastic.NewTermQuery("segments", realName))
	}

	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		// 只要命中菜品意图，分数直接+1024
		isUsedFsq = true
		fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(1024))
	}
	// 未命中门店意图且query grabfoodTags 不为空
	if len(traceInfo.QPResult.QueryStoreIntention) == 0 && len(grabfoodTags) > 0 {
		// 意图命中一级分类的情况下，分数+512
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(grabfoodTagsQ...), elastic.NewWeightFactorFunction(512))
	}
	// 细力度分词匹配模式为and匹配的情况下，分数+256
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Must(segmentsQ...), elastic.NewWeightFactorFunction(256))
	}
	// 匹配域命中粗粒度分词时，分数+128
	if len(maxWordSegments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(maxWordsQ...), elastic.NewWeightFactorFunction(128))
	}
	// 匹配域命中细粒度分词时，分数+64
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(segmentsQ...), elastic.NewWeightFactorFunction(64))
	}
	if isUsedFsq {
		fsq = fsq.ScoreMode("sum").BoostMode("replace")
	} else {
		fsq = nil
	}
	must := []elastic.Query{
		elastic.NewMatchQuery("dish_name", r.Keyword),
	}
	v := make([]interface{}, 0, len(storeID))
	for _, c := range storeID {
		v = append(v, c)
	}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_id", v...),
	}
	aggTopHist := elastic.NewTopHitsAggregation().
		Sort("_score", false).Sort("has_picture", false).
		Sort("sales_volume", false).Sort("_id", false).
		Size(2)
	aggMaxScore := elastic.NewMaxAggregation().Script(elastic.NewScript("_score"))
	// 创建Terms桶聚合, top_dishes 这个聚合名称需要与结果解析时的json 保持一致，见 searchsvr/dao/es.go
	aggs := elastic.NewTermsAggregation().Field("store_id").Size(size).SubAggregation("top_dishes", aggTopHist).
		SubAggregation("max_score", aggMaxScore).OrderByAggregation("max_score", false)
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
	}
	dish := es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithTermsAggregation(aggs), es.WithRecallType("ToDishSecondaryRecall"),
		es.WithRecallId("4001"),
		es.WithSourceInclude("store_id"),
	)
	return dish
}

func (r *SearchRequest) ToDishSecondaryRecallForMainSite(traceInfo *traceinfo.TraceInfo, storeID []uint64) *es.ESSearch {
	segments := traceInfo.QPResult.Segments
	maxWordSegments := traceInfo.QPResult.MaxWordSegments
	grabfoodTags := traceInfo.QPResult.StoreIntents
	fsq := elastic.NewFunctionScoreQuery()
	grabfoodTagsQ := make([]elastic.Query, 0, len(grabfoodTags))
	segmentsQ := make([]elastic.Query, 0, len(segments))
	maxWordsQ := make([]elastic.Query, 0, len(maxWordSegments))
	var isUsedFsq bool
	for _, tag := range grabfoodTags {
		grabfoodTagsQ = append(grabfoodTagsQ, elastic.NewTermQuery("grabfood_tags", tag))
	}
	for _, maxWord := range maxWordSegments {
		maxWordsQ = append(maxWordsQ, elastic.NewMatchQuery("max_word_segments", maxWord))
	}
	for _, realName := range segments {
		segmentsQ = append(segmentsQ, elastic.NewMatchQuery("segments", realName))
	}

	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		// 只要命中菜品意图，分数直接+1024
		isUsedFsq = true
		fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(1024))
	}
	// 未命中门店意图且query grabfoodTags 不为空
	if len(traceInfo.QPResult.QueryStoreIntention) == 0 && len(grabfoodTags) > 0 {
		// 意图命中一级分类的情况下，分数+512
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(grabfoodTagsQ...), elastic.NewWeightFactorFunction(512))
	}
	// 细力度分词匹配模式为and匹配的情况下，分数+256
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Must(segmentsQ...), elastic.NewWeightFactorFunction(256))
	}
	// 匹配域命中粗粒度分词时，分数+128
	if len(maxWordSegments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(maxWordsQ...), elastic.NewWeightFactorFunction(128))
	}
	// 匹配域命中细粒度分词时，分数+64
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(segmentsQ...), elastic.NewWeightFactorFunction(64))
	}
	if isUsedFsq {
		fsq = fsq.ScoreMode("sum").BoostMode("sum")
	} else {
		fsq = nil
	}
	must := []elastic.Query{
		elastic.NewMatchQuery("dish_name", r.Keyword),
	}
	v := make([]interface{}, 0, len(storeID))
	for _, c := range storeID {
		v = append(v, c)
	}
	filters := []elastic.Query{
		elastic.NewTermsQuery("store_id", v...),
	}
	dish := es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithRecallType("ToDishSecondaryRecallForMainSite"),
		es.WithRecallId("2003"),
	)
	return dish
}

func (r *SearchRequest) ToStoreEsWithNer(traceInfo *traceinfo.TraceInfo, opt string, matchType int) *es.ESSearch {
	ner := traceInfo.QPResult.NerResult
	if opt == "AND" {
		ner = traceInfo.QPResult.NerAndRecallResult
	}
	if opt == "OR" {
		ner = traceInfo.QPResult.NerOrRecallResult
	}
	if len(ner) == 0 {
		return nil
	}
	query := r.BuildMultiMatchWithNerType(ner, opt, matchType, traceInfo)
	grabfoodTags := traceInfo.QPResult.StoreIntents
	grabfoodTagsQ := make([]elastic.Query, 0, len(grabfoodTags))
	if len(grabfoodTags) > 0 {
		for _, tag := range grabfoodTags {
			grabfoodTagsQ = append(grabfoodTagsQ, elastic.NewTermQuery("grabfood_tags", tag))
		}
	}
	MustAndMatchTypeSize := size
	ShouldMatchTypeSize := size
	MustOrMatchTypeSize := size
	if traceInfo.PredictConfig != nil {
		if traceInfo.PredictConfig.MustAndMatchTypeSize > 0 {
			MustAndMatchTypeSize = traceInfo.PredictConfig.MustAndMatchTypeSize
		}
		if traceInfo.PredictConfig.ShouldMatchTypeSize > 0 {
			ShouldMatchTypeSize = traceInfo.PredictConfig.ShouldMatchTypeSize
		}
		if traceInfo.PredictConfig.MustOrMatchTypeSize > 0 {
			MustOrMatchTypeSize = traceInfo.PredictConfig.MustOrMatchTypeSize
		}
		if MustAndMatchTypeSize > 999 {
			MustAndMatchTypeSize = 999
		}
		if ShouldMatchTypeSize > 999 {
			ShouldMatchTypeSize = 999
		}
		if MustOrMatchTypeSize > 999 {
			MustOrMatchTypeSize = 999
		}
	}
	filters := r.BuildStoreFilterDSL()
	sort := make([]elastic.Sorter, 0)
	if traceInfo.PredictConfig != nil && traceInfo.PredictConfig.NerRecallUseScriptSort {
		sort = append(sort, elastic.NewFieldSort("_score").Desc())
		sort = append(sort, elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc())
		//distanceNormalValue := 1000.0
		//if traceInfo.PredictConfig.NerDistanceNormal > 0.0 {
		//	distanceNormalValue = traceInfo.PredictConfig.NerDistanceNormal
		//}
		//paramMap := make(map[string]interface{})
		//paramMap["lat"] = r.Location.GetLatitude()
		//paramMap["lon"] = r.Location.GetLongitude()
		//paramMap["distance_normal"] = distanceNormalValue
		//distanceScript := elastic.NewScript("Math.floor(doc['location'].arcDistance(params.lat, params.lon)/params.distance_normal)").Params(paramMap)
		//scriptSort := elastic.NewScriptSort(distanceScript, "number").Asc()
		//sort = append(sort, scriptSort)
		sort = append(sort, elastic.NewFieldSort("store_sell_week").Desc())
		sort = append(sort, elastic.NewFieldSort("_id").Asc())
	}
	var dsl *es.ESSearch
	switch matchType {
	case MustAndMatchType:
		dsl = es.NewESSearch(es.WithMustQueries(query), es.WithShouldQueries(grabfoodTagsQ), es.WithFilters(filters),
			es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(uint64(MustAndMatchTypeSize)),
			es.WithSorters(sort),
			es.WithSourceInclude("_id", "grabfood_tags"),
			es.WithRecallType("ToStoreEsWithNer-MustAndMatchType"),
			es.WithRecallId("1004"),
		)
	case ShouldMatchType:
		if len(grabfoodTagsQ) > 0 {
			shouldQuery := elastic.NewBoolQuery().Should(grabfoodTagsQ...)
			query = append(query, shouldQuery)
		}
		dsl = es.NewESSearch(es.WithShouldQueries(query), es.WithFilters(filters),
			es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(uint64(ShouldMatchTypeSize)),
			es.WithSorters(sort),
			es.WithSourceInclude("_id", "grabfood_tags"),
			es.WithRecallType("ToStoreEsWithNer-ShouldMatchType"),
			es.WithRecallId("1004"),
		)
	case MustOrMatchType:
		dsl = es.NewESSearch(es.WithMustQueries(query), es.WithShouldQueries(grabfoodTagsQ), es.WithFilters(filters),
			es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(uint64(MustOrMatchTypeSize)),
			es.WithSorters(sort),
			es.WithSourceInclude("_id", "grabfood_tags"),
			es.WithRecallType("ToStoreEsWithNer-MustOrMatchType"),
			es.WithRecallId("1004"),
		)
	}
	dsl.Query = traceInfo.QueryKeyword
	return dsl
}

func (r *SearchRequest) BuildMultiMatchWithNerType(nerList []*qp.Ner, opt string, matchType int, traceInfo *traceinfo.TraceInfo) []elastic.Query {
	queryList := make([]elastic.Query, 0)
	for _, ner := range nerList {
		keyword := ner.GetToken()
		for _, nerType := range ner.NerType {
			var query *elastic.MultiMatchQuery
			switch nerType {
			case qp.NERType_LOCATION:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueLocation>store_name^0.2;branch_name^0.8</MustAndMatchTypeFieldBoostQueueLocation>
					fieldBoostQueue = "store_name^0.2;branch_name^0.8"
				} else {
					// <OtherFieldBoostQueueLocation>store_name^0.3;branch_name^0.7</OtherFieldBoostQueueLocation>
					fieldBoostQueue = "store_name^0.3;branch_name^0.7"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.LocationNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueLocation) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueLocation) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueLocation, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueLocation, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)

			case qp.NERType_STORE:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueStore>store_name^0.8;dish_name^0.2</MustAndMatchTypeFieldBoostQueueStore>
					fieldBoostQueue = "store_name^0.8;dish_name^0.2"
				} else {
					// <OtherFieldBoostQueueStore>store_name^0.6;dish_name^0.2;grabfood_tags^0.1;tags^0.1</OtherFieldBoostQueueStore>
					fieldBoostQueue = "store_name^0.6;dish_name^0.2;grabfood_tags^0.1;tags^0.1"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.StoreNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueStore) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueStore) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueStore, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueStore, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)

			case qp.NERType_DISH:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueDish>store_name^0.2;dish_name^0.8</MustAndMatchTypeFieldBoostQueueDish>
					fieldBoostQueue = "store_name^0.2;dish_name^0.8"
				} else {
					// <OtherFieldBoostQueueDish>store_name^0.2;dish_name^0.6;grabfood_tags^0.1;tags^0.1</OtherFieldBoostQueueDish>
					fieldBoostQueue = "store_name^0.2;dish_name^0.6;grabfood_tags^0.1;tags^0.1"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.DishNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueDish) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueDish) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueDish, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueDish, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)

			case qp.NERType_CATEGORY:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueCategory>store_name^0.3;dish_name^0.2;grabfood_tags^0.3;tags^0.2</MustAndMatchTypeFieldBoostQueueCategory>
					fieldBoostQueue = "store_name^0.3;dish_name^0.2;grabfood_tags^0.3;tags^0.2"
				} else {
					// <OtherFieldBoostQueueCategory>store_name^0.4;dish_name^0.4;grabfood_tags^0.1;tags^0.1</OtherFieldBoostQueueCategory>
					fieldBoostQueue = "store_name^0.4;dish_name^0.4;grabfood_tags^0.1;tags^0.1"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.CategoryNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueCategory) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueCategory) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueCategory, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueCategory, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)

			case qp.NERType_INGREDIENT:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueIngredient>store_name^0.2;dish_name^0.8</MustAndMatchTypeFieldBoostQueueIngredient>
					fieldBoostQueue = "store_name^0.2;dish_name^0.8"
				} else {
					// <OtherFieldBoostQueueIngredient>store_name^0.2;dish_name^0.6;tags^0.1;grabfood_tags^0.1</OtherFieldBoostQueueIngredient>
					fieldBoostQueue = "store_name^0.2;dish_name^0.6;tags^0.1;grabfood_tags^0.1"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.IngredientNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueIngredient) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueIngredient) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueIngredient, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueIngredient, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)

			default:
				fieldBoostQueue := ""
				if matchType == MustAndMatchType {
					// <MustAndMatchTypeFieldBoostQueueDefault>store_name^0.4;dish_name^0.4;grabfood_tags^0.1;tags^0.1</MustAndMatchTypeFieldBoostQueueDefault>
					fieldBoostQueue = "store_name^0.4;dish_name^0.4;grabfood_tags^0.1;tags^0.1"
				} else {
					// <OtherFieldBoostQueueDefault>store_name^0.45;dish_name^0.35;grabfood_tags^0.1;tags^0.1</OtherFieldBoostQueueDefault>
					fieldBoostQueue = "store_name^0.45;dish_name^0.35;grabfood_tags^0.1;tags^0.1"
				}
				nerBoost := 0.0
				if traceInfo.PredictConfig != nil {
					nerBoost = traceInfo.PredictConfig.DefaultNerBoost
					if len(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueDefault) > 0 ||
						len(traceInfo.PredictConfig.OtherFieldBoostQueueDefault) > 0 {
						MustNerIntentionList := strings.Split(traceInfo.PredictConfig.MustAndMatchTypeFieldBoostQueueDefault, ",")
						OtherNerIntentionList := strings.Split(traceInfo.PredictConfig.OtherFieldBoostQueueDefault, ",")
						if len(MustNerIntentionList) == 3 && len(OtherNerIntentionList) == 3 {
							intentionBoostQueue := r.ChooseIntentionFieldBoostQueue(matchType, MustNerIntentionList, OtherNerIntentionList, traceInfo)
							if len(intentionBoostQueue) > 0 {
								fieldBoostQueue = intentionBoostQueue
							}
						}
					}
				}
				query = r.BuildNerQuery(keyword, opt, fieldBoostQueue, nerBoost, traceInfo)
			}
			queryList = append(queryList, query)
		}
	}

	return queryList
}

func (r *SearchRequest) BuildNerQuery(keyword, opt, fieldBoostQueue string, nerBoost float64, traceInfo *traceinfo.TraceInfo) *elastic.MultiMatchQuery {
	query := elastic.NewMultiMatchQuery(keyword)
	fieldBoosts := strings.Split(fieldBoostQueue, ";")
	for _, fieldBoost := range fieldBoosts {
		kv := strings.Split(fieldBoost, "^")
		if len(kv) == 2 {
			field := kv[0]
			boost, err := strconv.ParseFloat(kv[1], 64)
			if err != nil {
				logkit.Error("failed to build boost", logkit.Any("pub_id", traceInfo.TraceRequest.PublishId))
				continue
			}
			query.FieldWithBoost(field, boost)
		}
	}
	query.TieBreaker(1)
	query.Operator(opt)
	if nerBoost > 0.0 {
		query.Boost(nerBoost)
	}
	return query
}

func (r SearchRequest) ChooseIntentionFieldBoostQueue(matchType int, nerQueueList, otherNerQueueList []string, traceInfo *traceinfo.TraceInfo) string {
	boostQueue := ""
	var queueList []string
	if matchType == MustAndMatchType {
		queueList = nerQueueList
	} else {
		queueList = otherNerQueueList
	}
	storeIntentionBoostQueue := ""
	dishIntentionBoostQueue := ""
	unknownIntentionBoostQueue := ""
	for _, str := range queueList {
		tmpList := strings.Split(str, ":")
		if len(tmpList) == 2 {
			switch tmpList[0] {
			case "store":
				storeIntentionBoostQueue = tmpList[1]
				break
			case "dish":
				dishIntentionBoostQueue = tmpList[1]
				break
			case "unknown":
				unknownIntentionBoostQueue = tmpList[1]
				break
			default:
				break
			}
		}
	}
	if traceInfo.QPResult != nil {
		if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
			boostQueue = storeIntentionBoostQueue
		} else if len(traceInfo.QPResult.QueryDishIntention) > 0 {
			boostQueue = dishIntentionBoostQueue
		} else {
			boostQueue = unknownIntentionBoostQueue
		}
	}

	return boostQueue
}

func (r *SearchRequest) BuildStoreFilterDSL() []elastic.Query {
	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), downgradeDistance)
	filters := []elastic.Query{
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewTermQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
	}
	if r.FilterTypeShippingDistance == 0 {
		filters = append(filters, elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"))
	} else {
		t := elastic.NewGeoDistanceQuery("location").Distance(fmt.Sprintf("%dm", r.FilterTypeShippingDistance)).
			Lon(float64(r.Location.GetLongitude())).Lat(float64(r.Location.GetLatitude())).DistanceType("plane")
		filters = append(filters, t)
	}

	if r.FilterByPreferredMerchant {
		filters = append(filters, elastic.NewTermQuery("is_preferred_merchant", 1))
	}
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
	}
	return filters
}
