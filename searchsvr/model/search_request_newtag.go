package model

import (
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/golang/protobuf/proto"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

func (r *SearchRequest) ToStoreESFunctionScoreSearchV2NewCategory(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	segments := traceInfo.QPResult.Segments
	maxWordSegments := traceInfo.QPResult.MaxWordSegments
	categoryTags := traceInfo.QPResult.ProbFilterCategoryIntentions
	fsq := elastic.NewFunctionScoreQuery()
	categoryTagsQ1 := make([]elastic.Query, 0, len(categoryTags))
	categoryTagsQ2 := make([]elastic.Query, 0, len(categoryTags))
	segmentsQ := make([]elastic.Query, 0, len(segments))
	maxWordsQ := make([]elastic.Query, 0, len(maxWordSegments))
	var isUsedFsq bool
	for _, tag := range categoryTags {
		if tag != nil && tag.Level1Name != nil && len(*tag.Level1Name) > 0 {
			categoryTagsQ1 = append(categoryTagsQ1, elastic.NewMultiMatchQuery(*tag.Level1Name, "main_category.level1_name.keyword", "sub_category.level1_name.keyword").Type("phrase"))
		}
		if tag != nil && tag.Level2Name != nil && len(*tag.Level2Name) > 0 {
			categoryTagsQ2 = append(categoryTagsQ2, elastic.NewMultiMatchQuery(*tag.Level2Name, "main_category.level2_name.keyword", "sub_category.level2_name.keyword").Type("phrase"))
		}
	}
	for _, maxWord := range maxWordSegments {
		maxWordsQ = append(maxWordsQ, elastic.NewTermQuery("max_word_segments", maxWord))
	}
	for _, segment := range segments {
		segmentsQ = append(segmentsQ, elastic.NewTermQuery("segments", segment))
	}
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		// 只要命中门店意图，分数直接+1024
		isUsedFsq = true
		fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(1024))
	}
	if len(categoryTagsQ1) > 0 {
		// 意图命中主营分类的情况下，分数+512 , 意图命中辅营分类的情况下，分数+256+128
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(categoryTagsQ1...), elastic.NewWeightFactorFunction(512))
	}
	if len(categoryTagsQ2) > 0 {
		// 意图命中主营分类的情况下，分数+512 , 意图命中辅营分类的情况下，分数+256+128
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(categoryTagsQ2...), elastic.NewWeightFactorFunction(384))
	}

	// 细力度分词匹配模式为and匹配的情况下，分数+256
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Must(segmentsQ...), elastic.NewWeightFactorFunction(256))
	}
	// 匹配域命中粗粒度分词时，分数+128
	if len(maxWordSegments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(maxWordsQ...), elastic.NewWeightFactorFunction(128))
	}
	// 匹配域命中细粒度分词时，分数+64
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(segmentsQ...), elastic.NewWeightFactorFunction(64))
	}

	if isUsedFsq {
		fsq = fsq.ScoreMode("sum").BoostMode("replace")
	} else {
		fsq = nil
	}
	var should []elastic.Query
	var must []elastic.Query

	// 如果命中门店意图，需要加上门店一级分类筛选条件
	if len(traceInfo.QPResult.QueryStoreIntention) > 0 {
		if env.GetCID() == cid.ID || env.GetCID() == cid.XX {
			should = append(should, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "branch_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		} else if env.GetCID() == cid.MY {
			// MY站点,为brand_name
			should = append(should, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "brand_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		} else {
			should = append(should, elastic.NewMultiMatchQuery(r.Keyword, "store_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		}
	} else {
		// 把tags -> main_category和sub_category
		if env.GetCID() == cid.ID || env.GetCID() == cid.XX {
			must = append(must, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "branch_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		} else if env.GetCID() == cid.MY {
			// MY站点,为brand_name
			must = append(must, elastic.NewMultiMatchQuery(r.Keyword, "real_store_name", "brand_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		} else {
			must = append(must, elastic.NewMultiMatchQuery(r.Keyword, "store_name",
				"main_category.level1_name.keyword", "main_category.level2_name.keyword", "sub_category.level1_name.keyword", "sub_category.level2_name.keyword"))
		}
	}

	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	filters := []elastic.Query{
		elastic.NewTermQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}
	sort := []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
		elastic.NewFieldSort("_id").Asc(),
	}
	var store *es.ESSearch
	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
		// 在MY站点,搜索词只要halal type 类型,直接召回对应halal type的门店.
		if r.RecallByHalalType {
			must = make([]elastic.Query, 0)
			should = make([]elastic.Query, 0)
		}
	}
	store = es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithMustQueries(must), es.WithSorters(sort), es.WithShouldQueries(should), es.WithFilters(filters), es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithSourceInclude("store_name", "tags", "best_selling_dish_id", "segments", "max_word_segments", "grabfood_tags", "store_sell_week", "main_category", "sub_category"),
		es.WithRecallType("ToStoreESFunctionScoreSearchV2NewCategory"),
		es.WithRecallId("10000"))
	logkit.Debug("SearchRequest.ToStoreESSearch", zap.Any("store", store))
	return store
}

func (r *SearchRequest) ToStoreEsWithDishInfoSearchV2NewCategory(traceInfo *traceinfo.TraceInfo) *es.ESSearch {
	segments := traceInfo.QPResult.Segments
	maxWordSegments := traceInfo.QPResult.MaxWordSegments
	categoryTags := traceInfo.QPResult.ProbFilterCategoryIntentions
	fsq := elastic.NewFunctionScoreQuery()
	categoryTagsQ1 := make([]elastic.Query, 0, len(categoryTags))
	categoryTagsQ2 := make([]elastic.Query, 0, len(categoryTags))
	segmentsQ := make([]elastic.Query, 0, len(segments))
	maxWordsQ := make([]elastic.Query, 0, len(maxWordSegments))
	var isUsedFsq bool
	for _, tag := range categoryTags {
		if tag != nil && tag.Level1Name != nil && len(*tag.Level1Name) > 0 {
			categoryTagsQ1 = append(categoryTagsQ1, elastic.NewTermsQuery(*tag.Level1Name, "main_category.level1_name.keyword", "sub_category.level1_name.keyword"))
		}
		if tag != nil && tag.Level2Name != nil && len(*tag.Level2Name) > 0 {
			categoryTagsQ2 = append(categoryTagsQ2, elastic.NewTermsQuery(*tag.Level2Name, "main_category.level2_name.keyword", "sub_category.level2_name.keyword"))
		}
	}
	for _, maxWord := range maxWordSegments {
		maxWordsQ = append(maxWordsQ, elastic.NewTermQuery("dish_name_max_word_segments_keyword", maxWord))
	}
	segmentsStr := ""
	for _, segments := range segments {
		if len(segmentsStr) > 0 {
			segmentsStr += " "
		}
		segmentsStr += segments
		segmentsQ = append(segmentsQ, elastic.NewTermQuery("dish_name_segments_keyword", segments))
	}

	if len(traceInfo.QPResult.QueryDishIntention) > 0 {
		// 只要命中菜品意图，分数直接+1024
		isUsedFsq = true
		fsq = fsq.AddScoreFunc(elastic.NewWeightFactorFunction(1024))
	}
	// 未命中门店意图且query categoryTagsQ1 不为空
	if len(traceInfo.QPResult.QueryStoreIntention) == 0 && len(categoryTagsQ1) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(categoryTagsQ1...), elastic.NewWeightFactorFunction(512))
	}
	// 未命中门店意图且query categoryTagsQ2 不为空
	if len(traceInfo.QPResult.QueryStoreIntention) == 0 && len(categoryTagsQ2) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(categoryTagsQ2...), elastic.NewWeightFactorFunction(384))
	}
	// 细力度分词匹配模式为and匹配的情况下，分数+256
	if len(segments) > 0 {
		isUsedFsq = true
		//fsq = fsq.Add(elastic.NewBoolQuery().Must(segmentsQ...), elastic.NewWeightFactorFunction(256))
		fsq = fsq.Add(elastic.NewBoolQuery().Must(elastic.NewMatchPhraseQuery("dish_name_segments", segmentsStr).Slop(10)), elastic.NewWeightFactorFunction(256))
	}
	// 匹配域命中粗粒度分词时，分数+128
	if len(maxWordSegments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(maxWordsQ...), elastic.NewWeightFactorFunction(128))
	}
	// 匹配域命中细粒度分词时，分数+64
	if len(segments) > 0 {
		isUsedFsq = true
		fsq = fsq.Add(elastic.NewBoolQuery().Should(segmentsQ...), elastic.NewWeightFactorFunction(64))
	}
	if isUsedFsq {
		fsq = fsq.ScoreMode("sum").BoostMode("replace")
	} else {
		fsq = nil
	}
	topLeft, bottomRight := CalBox(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude()), distance)
	must := []elastic.Query{
		elastic.NewMatchQuery("dish_name", r.Keyword),
	}
	filters := []elastic.Query{
		elastic.NewTermQuery("store_status", foodalgo_search.StoreStatus_STORE_ACTIVE),
		elastic.NewTermQuery("store_dish_available", foodalgo_search.Available_AVAILABLE),
		elastic.NewGeoBoundingBoxQuery("location").TopLeft(topLeft.Lat, topLeft.Lng).BottomRight(bottomRight.Lat, bottomRight.Lng).Type("indexed"),
	}

	var sort = []elastic.Sorter{
		elastic.NewFieldSort("_score").Desc(),
		elastic.NewGeoDistanceSort("location").Point(float64(r.Location.GetLatitude()), float64(r.Location.GetLongitude())).Asc(),
		elastic.NewFieldSort("_id").Asc(),
	}

	if env.GetCID() == cid.MY {
		if r.FilterByHalalType {
			if r.HalalType == NonHalal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_NON_HALAL, foodalgo_search.HalalType_HALAL_FRIENDLY))
			} else if r.HalalType == Halal {
				filters = append(filters, elastic.NewTermsQuery("halal_type", foodalgo_search.HalalType_CERTIFIED_HALAL))
			}
		}
	}
	dish := es.NewESSearch(es.WithFunctionScoreQueries(fsq), es.WithSorters(sort), es.WithMustQueries(must), es.WithFilters(filters),
		es.WithExactTotal(proto.Bool(false)), es.WithFrom(0), es.WithSize(size),
		es.WithSourceInclude("_id"),
		es.WithRecallType("ToStoreEsWithDishInfoSearchV2NewCategory"),
		es.WithRecallId("10000"),
	)
	return dish
}
