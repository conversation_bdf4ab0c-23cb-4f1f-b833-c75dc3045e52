package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"
	"github.com/gogo/protobuf/proto"
)

// 新门店collection带搜菜
func SearchCollectionWishDishListing(ctx context.Context, rsp *foodalgo_search.SearchStoresWithListingDishResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.Stores = make([]*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo, 0)
	var recallStoreInfos model.StoreInfos
	var storeInfos model.StoreInfos
	var err error
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchCollectionWishDishListing", len(rsp.GetStores()))

		// 后置处理：打印dump log,  上报&打印各阶段门店长度
		postprocess.PostProcessor(ctx, traceInfo, debugInfo, recallStoreInfos, storeInfos)
		postprocess.PostProcessorAckDump(ctx, traceInfo, storeInfos, storeInfos)
	}()

	traceInfo.PipelineType = traceinfo.PipelineTypeSearchCollection
	var stores []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo
	var categories []*foodalgo_search.CategoryInfo
	var hasMore bool
	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)
	recallStoreInfos, storeInfos, err = processor.SearchInternalPipeline(ctx, traceInfo, debugInfo)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("SearchInternalPipeline error")
		return err
	}
	storeInfos = rerank.LtrReRank(ctx, traceInfo, storeInfos)

	stores = buildRespStores(traceInfo, storeInfos)
	categories = buildStoresCategories(storeInfos)
	stores, hasMore = cutOffPageStoresForListingDish(stores, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(stores) > 0 {
		rsp.Stores = stores
	}
	if len(categories) > 0 {
		rsp.CategoryInfos = categories
	}
	rsp.CorrectedInfo = &foodalgo_search.CorrectedInfo{
		IsCorrected: proto.Bool(traceInfo.IsCorrected),
	}
	if traceInfo.IsCorrected {
		rsp.CorrectedInfo.CorrectedWord = proto.String(traceInfo.CorrectKeyword)
		rsp.CorrectedInfo.CorrectedType = buildCorrectedType(traceInfo.RspCorrectedType)
	}
	return nil
}
