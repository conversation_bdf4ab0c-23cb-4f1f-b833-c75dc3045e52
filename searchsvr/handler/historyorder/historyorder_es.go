package historyorder

import (
	"context"
	"fmt"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/es"
	"github.com/gogo/protobuf/proto"
	"github.com/olivere/elastic/v7"
	"strings"
	"time"
)

func CreateEsSearch(ctx context.Context, traceInfo *traceinfo.TraceInfo, pageNum int, pageSize int, keyword string, userId uint64) *es.ESSearch {
	now := time.Now()

	// 合法订单开始时间，默认为 365 天前
	validCreatedStartDays := 365
	if apollo.SearchApolloCfg.HistoryOrderSearchBeforeDays > 0 {
		validCreatedStartDays = apollo.SearchApolloCfg.HistoryOrderSearchBeforeDays
	}
	createdBeforeDate := now.AddDate(0, 0, -validCreatedStartDays) // 当前时间减去配置的天数

	// 基础过滤条件：用户 ID 和时间范围
	filters := []elastic.Query{
		elastic.NewTermQuery("user_id", userId),                                 // 精确匹配用户 ID
		elastic.NewRangeQuery("create_time").Gte(createdBeforeDate.UnixMilli()), // 时间范围过滤: 规定有效时间
	}

	// 如果配置了订单状态范围，则添加状态过滤条件
	if len(apollo.SearchApolloCfg.HistoryOrderValidStatus) > 0 {
		validOrderStatusStr := apollo.SearchApolloCfg.HistoryOrderValidStatus
		validOrderStatus := util2.StringToIntInterfaceList(validOrderStatusStr)
		filters = append(filters, elastic.NewTermsQuery("order_status", validOrderStatus...))
	}

	if strings.Contains(keyword, " ") {
		// 多词查询：使用 match_phrase
		keywordBoolQuery := elastic.NewBoolQuery().
			Should(
				elastic.NewMatchPhraseQuery("store_name", keyword),
				elastic.NewMatchPhraseQuery("dish_name_list", keyword),
			).
			MinimumShouldMatch("1")
		filters = append(filters, keywordBoolQuery)
	} else {
		// 单词查询：使用更高效的 match 查询
		keywordBoolQuery := elastic.NewBoolQuery().
			Should(
				elastic.NewMatchQuery("store_name", keyword),
				elastic.NewMatchQuery("dish_name_list", keyword),
			).
			MinimumShouldMatch("1")
		filters = append(filters, keywordBoolQuery)
	}

	// 时间降序
	sorters := []elastic.Sorter{
		elastic.NewFieldSort("create_time").Desc(),
	}

	// 分页
	from := int32((pageNum - 1) * pageSize)
	logkit.FromContext(ctx).Info("SearchHistoryOrders",
		logkit.Int32("from", from),
		logkit.Int("pageNum", pageNum),
		logkit.Int("pageSize", pageSize),
	)

	// 构建 ES 查询对象
	esSearch := es.NewESSearch(
		es.WithFilters(filters), // 将所有条件放在 filter 上下文中以提高性能
		es.WithSourceInclude("id", "store_name", "store_id", "dish_name_list", "dish_id_list"), // 返回的字段
		es.WithExactTotal(proto.Bool(true)),                                                    // 需要总数
		es.WithFrom(from),                                                                      // 分页开始
		es.WithSize(uint64(pageSize+1)),                                                        // 分页大小，这里+1用来判断分页是否有下一页
		es.WithSorters(sorters),                                                                // 排序条件
		es.WithRecallType("SearchHistoryOrders"),
	)
	if apollo.SearchApolloCfg.HistoryOrderUseRouting == 1 {
		esSearch.Routing = fmt.Sprintf("%d", userId)
	}

	// 如果是调试模式，打印 DSL
	if traceInfo.IsDebug {
		logkit.FromContext(ctx).Info("SearchHistoryOrders dsl",
			logkit.String("dsl", esSearch.DslString(ctx)),
		)
	}

	return esSearch
}
