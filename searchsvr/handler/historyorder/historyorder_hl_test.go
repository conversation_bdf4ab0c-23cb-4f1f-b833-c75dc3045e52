package historyorder

import (
	"context"
	"encoding/json"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"strings"
	"testing"
)

func TestHighLightWords_SingleWordKeyword(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "1",
			StoreId:   100,
			StoreName: "Shopee Food",
			DishNames: []string{"Shopee Rice", "Shopee Noodle"},
			DishIds:   []uint64{1001, 1002},
		},
	}
	keyword := "Shopee"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)

	if len(highlightedOrders) == 0 {
		t.<PERSON><PERSON><PERSON>("Expected at least one highlighted order, got 0")
	}

	for _, order := range highlightedOrders {
		if order.GetStoreItem().GetHighlightPosList() == nil {
			t.Errorf("Expected store name to be highlighted, got nil")
		}
		for i, dishItem := range order.DishItems {
			highlightPos := dishItem.GetHighlightPosList()
			if i == 0 && (highlightPos == nil || len(highlightPos) == 0) {
				t.<PERSON><PERSON><PERSON>("Expected first dish name to be highlighted, got nil")
			}
		}
	}
}

func TestHighLightWords_SpecialWordKeywordV1(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "1",
			StoreId:   100,
			StoreName: "Shopee - Food",
			DishNames: []string{"Shopee - Rice", "Shopee -- Noodle"},
			DishIds:   []uint64{1001, 1002},
		},
	}
	keyword := "-"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 7 {
		t.Errorf("Expected store name to be highlighted, start should be 7")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 7 {
		t.Errorf("Expected store name to be highlighted, end should be 7")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected dish name -1 to be highlighted, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 7 {
		t.Errorf("Expected dish name -1 to be highlighted, start should be 7")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 7 {
		t.Errorf("Expected dish name -1 to be highlighted, end should be 7")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name -2 to be highlighted, shoud be nil")
	}
}

func TestHighLightWords_SpecialWordKeywordV2(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "1",
			StoreId:   100,
			StoreName: "Shopee 2. Food",
			DishNames: []string{"Shopee 2. Rice", "Shopee 2.. Noodle", "Shopee .2. Noodle"},
			DishIds:   []uint64{1001, 1002},
		},
	}
	keyword := "2."

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 7 {
		t.Errorf("Expected store name to be highlighted, start should be 7")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 8 {
		t.Errorf("Expected store name to be highlighted, end should be 8")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 7 {
		t.Errorf("Expected dish name - 1 to be highlighted, start should be 7")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 8 {
		t.Errorf("Expected dish name - 1 to be highlighted, end should be 7")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name -2 to be highlighted, shoud be nil")
	}
	if highlightedOrders[0].GetDishItems()[2].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name -3 to be highlighted, shoud be nil")
	}
}

func TestHighLightWords_PhraseKeyword(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "2",
			StoreId:   200,
			StoreName: "Hoc Milk Store",
			DishNames: []string{"Fresh Hoc Milk", "Sweet Milk"},
			DishIds:   []uint64{2001, 2002},
		},
	}
	keyword := "Hoc Milk"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order, got 0")
	}

	for _, order := range highlightedOrders {
		if order.GetStoreItem().GetHighlightPosList() == nil {
			t.Errorf("Expected store name to be highlighted for phrase keyword, got nil")
		}
		for i, dishItem := range order.DishItems {
			highlightPos := dishItem.GetHighlightPosList()
			if i == 0 && (highlightPos == nil || len(highlightPos) == 0) {
				t.Errorf("Expected first dish name to be highlighted for phrase keyword, got nil")
			} else if i == 1 && highlightPos != nil {
				t.Errorf("Expected second dish name not to be highlighted, but got highlight")
			}
		}
	}
}

func TestHighLightWords_ID(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "3",
			StoreId:   300,
			StoreName: "Toko Susu",
			DishNames: []string{"Susu Segar", "Manis"},
			DishIds:   []uint64{3001, 3002},
		},
	}
	keyword := "Susu"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order for locale ID, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted for locale ID, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 5 {
		t.Errorf("Expected store name to be highlighted for locale ID, start should be 5")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 8 {
		t.Errorf("Expected store name to be highlighted for locale ID, end should be 8")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected dish name - 1 to be highlighted for locale ID, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 0 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale ID, start should be 0")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 3 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale ID, end should be 3")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name - 2 to not be highlighted for locale ID, should be nil")
	}
}

func TestHighLightWords_TH(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "4",
			StoreId:   400,
			StoreName: "ร้านนม นมสด",
			DishNames: []string{"นมสด", "หวาน"},
			DishIds:   []uint64{4001, 4002},
		},
	}
	keyword := "นมสด"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order for locale TH, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted for locale TH, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 7 {
		t.Errorf("Expected store name to be highlighted for locale TH, start should be 6")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 10 {
		t.Errorf("Expected store name to be highlighted for locale TH, end should be 9")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted for locale TH, should be nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected dish name - 1 to be highlighted for locale TH, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 0 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale TH, start should be 0")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 3 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale TH, end should be 1")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name - 2 to not be highlighted for locale TH, should be nil")
	}
}

func TestHighLightWords_MY(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "5",
			StoreId:   500,
			StoreName: "Kedai Susu",
			DishNames: []string{"Susu Segar", "Manis"},
			DishIds:   []uint64{5001, 5002},
		},
	}
	keyword := "Susu"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order for locale MY, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted for locale MY, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 6 {
		t.Errorf("Expected store name to be highlighted for locale MY, start should be 6")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 9 {
		t.Errorf("Expected store name to be highlighted for locale MY, end should be 9")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected dish name - 1 to be highlighted for locale MY, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 0 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale MY, start should be 0")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 3 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale MY, end should be 3")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name - 2 to not be highlighted for locale MY, should be nil")
	}
}

func TestHighLightWords_VN(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "6",
			StoreId:   600,
			StoreName: "Cửa Hàng Sữa",
			DishNames: []string{"Sữa Tươi", "Ngọt"},
			DishIds:   []uint64{6001, 6002},
		},
	}
	keyword := "Sữa"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)
	highlightedOrdersStr, _ := json.Marshal(highlightedOrders)
	fmt.Println(string(highlightedOrdersStr))

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order for locale VN, got 0")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList() == nil {
		t.Errorf("Expected store name to be highlighted for locale VN, got nil")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetStart() != 9 {
		t.Errorf("Expected store name to be highlighted for locale VN, start should be 9")
	}
	if highlightedOrders[0].GetStoreItem().GetHighlightPosList()[0].GetEnd() != 11 {
		t.Errorf("Expected store name to be highlighted for locale VN, end should be 11")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList() == nil {
		t.Errorf("Expected dish name - 1 to be highlighted for locale VN, got nil")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetStart() != 0 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale VN, start should be 0")
	}
	if highlightedOrders[0].GetDishItems()[0].GetHighlightPosList()[0].GetEnd() != 2 {
		t.Errorf("Expected dish name - 1 to be highlighted for locale VN, end should be 2")
	}
	if highlightedOrders[0].GetDishItems()[1].GetHighlightPosList() != nil {
		t.Errorf("Expected dish name - 2 to not be highlighted for locale VN, should be nil")
	}
}

func TestHighLightWords_Positions(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "7",
			StoreId:   700,
			StoreName: "Pos Test Store",
			DishNames: []string{"Exact Match", "Contains Match Here", "Partial Contains Match", "No Match At All"},
			DishIds:   []uint64{7001, 7002, 7003, 7004},
		},
	}
	keyword := "Match"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order, got 0")
	}

	for _, order := range highlightedOrders {
		if order.GetStoreItem().GetHighlightPosList() != nil {
			t.Errorf("Expected store name should be nil")
		}
		for i, dishItem := range order.DishItems {
			highlightPos := dishItem.GetHighlightPosList()
			switch i {
			case 0:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 6 || highlightPos[0].GetEnd() != 10 {
					t.Errorf("Expected exact match highlight at [6, 10], got %+v", highlightPos)
				}
			case 1:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 9 || highlightPos[0].GetEnd() != 13 {
					t.Errorf("Expected contains match highlight at [9, 13], got %+v", highlightPos)
				}
			case 2:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 17 || highlightPos[0].GetEnd() != 21 {
					t.Errorf("Expected partial contains match highlight at [17, 21], got %+v", highlightPos)
				}
			case 3:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 3 || highlightPos[0].GetEnd() != 7 {
					t.Errorf("Expected no highlight, but got %+v", highlightPos)
				}
			}
		}
	}
}

func TestHighLightWords_Positions_2(t *testing.T) {
	historyOrders := []*model.HistoryOrderInfo{
		{
			OrderId:   "7",
			StoreId:   700,
			StoreName: "Pos Test Store",
			DishNames: []string{"Tmp Exact Match", "Match Here", "Partial Match Here Haha", "No At All"},
			DishIds:   []uint64{7001, 7002, 7003, 7004},
		},
	}
	keyword := "Match Here"

	highlightedOrders := HighLightWords(context.Background(), historyOrders, keyword)

	if len(highlightedOrders) == 0 {
		t.Errorf("Expected at least one highlighted order, got 0")
	}

	for _, order := range highlightedOrders {
		if order.GetStoreItem().GetHighlightPosList() != nil {
			t.Errorf("Expected store name should be nil")
		}
		for i, dishItem := range order.DishItems {
			highlightPos := dishItem.GetHighlightPosList()
			switch i {
			case 0:
				if highlightPos != nil {
					t.Errorf("Expected no highlight, but got %+v", highlightPos)
				}
			case 1:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 0 || highlightPos[0].GetEnd() != 9 {
					t.Errorf("Expected contains match highlight at [0, 9], got %+v", highlightPos)
				}
			case 2:
				if highlightPos == nil || len(highlightPos) != 1 || highlightPos[0].GetStart() != 8 || highlightPos[0].GetEnd() != 17 {
					t.Errorf("Expected partial contains match highlight at [8, 17], got %+v", highlightPos)
				}
			case 3:
				if highlightPos != nil {
					t.Errorf("Expected no highlight, but got %+v", highlightPos)
				}
			}
		}
	}
}

func TestHighLightWords_EmptyInput(t *testing.T) {
	// 测试空输入
	cases := []struct {
		name          string
		historyOrders []*model.HistoryOrderInfo
		keyword       string
	}{
		{
			name:          "empty orders",
			historyOrders: []*model.HistoryOrderInfo{},
			keyword:       "test",
		},
		{
			name: "empty keyword",
			historyOrders: []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreName: "Test Store",
					DishNames: []string{"Test Dish"},
				},
			},
			keyword: "",
		},
		{
			name: "empty store name",
			historyOrders: []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreName: "",
					DishNames: []string{"Test Dish"},
				},
			},
			keyword: "test",
		},
		{
			name: "empty dish names",
			historyOrders: []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreName: "Test Store",
					DishNames: []string{},
				},
			},
			keyword: "test",
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			result := HighLightWords(context.Background(), tc.historyOrders, tc.keyword)
			if tc.historyOrders == nil && len(result) != 0 {
				t.Errorf("Expected empty result for nil input")
			}
		})
	}
}

func TestHighLightWords_SpecialCharacters(t *testing.T) {
	cases := []struct {
		name          string
		storeName     string
		dishNames     []string
		keyword       string
		expectedStore *struct {
			match bool
			pos   []struct{ start, end uint32 }
		}
		expectedDishes []struct {
			match bool
			pos   []struct{ start, end uint32 }
		}
	}{
		{
			name:      "single special char",
			storeName: "A - B",
			dishNames: []string{"X - Y", "No Match"},
			keyword:   "-",
			expectedStore: &struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				match: true,
				pos:   []struct{ start, end uint32 }{{2, 2}},
			},
			expectedDishes: []struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				{match: true, pos: []struct{ start, end uint32 }{{2, 2}}},
				{match: false, pos: nil},
			},
		},
		{
			name:      "special char with number",
			storeName: "Store 2. Food",
			dishNames: []string{"Dish 2. Special", "No 2.. Match", "Not Match"},
			keyword:   "2.",
			expectedStore: &struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				match: true,
				pos:   []struct{ start, end uint32 }{{6, 7}},
			},
			expectedDishes: []struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				{match: true, pos: []struct{ start, end uint32 }{{5, 6}}},
				{match: false, pos: nil},
				{match: false, pos: nil},
			},
		},
		{
			name:      "multiple special chars (should match)",
			storeName: "A -- B",
			dishNames: []string{"X -- Y"},
			keyword:   "--",
			expectedStore: &struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				match: true,
				pos:   []struct{ start, end uint32 }{{2, 3}},
			},
			expectedDishes: []struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				{match: true,
					pos: []struct{ start, end uint32 }{{2, 3}}},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			historyOrders := []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreId:   100,
					StoreName: tc.storeName,
					DishNames: tc.dishNames,
				},
			}
			result := HighLightWords(context.Background(), historyOrders, tc.keyword)

			if len(result) == 0 {
				t.Fatalf("Expected non-empty result")
			}

			// 验证店铺名高亮
			storeItem := result[0].GetStoreItem()
			if tc.expectedStore.match {
				if storeItem.GetHighlightPosList() == nil {
					t.Errorf("Expected store name to be highlighted for %s", tc.keyword)
				} else {
					highlightPos := storeItem.GetHighlightPosList()
					if len(highlightPos) != len(tc.expectedStore.pos) {
						t.Errorf("Expected %d highlight positions for store name, got %d",
							len(tc.expectedStore.pos), len(highlightPos))
					}
					for i, pos := range tc.expectedStore.pos {
						if highlightPos[i].GetStart() != pos.start || highlightPos[i].GetEnd() != pos.end {
							t.Errorf("Store highlight position mismatch at index %d: expected [%d, %d], got [%d, %d]",
								i, pos.start, pos.end, highlightPos[i].GetStart(), highlightPos[i].GetEnd())
						}
					}
				}
			} else if storeItem.GetHighlightPosList() != nil {
				t.Errorf("Expected no highlight in store name for %s", tc.keyword)
			}

			// 验证菜品名高亮
			for i, expectedDish := range tc.expectedDishes {
				if i >= len(result[0].GetDishItems()) {
					t.Errorf("Missing dish item at index %d", i)
					continue
				}
				dishItem := result[0].GetDishItems()[i]
				if expectedDish.match {
					if dishItem.GetHighlightPosList() == nil {
						t.Errorf("Expected dish name %d to be highlighted for %s", i, tc.keyword)
					} else {
						highlightPos := dishItem.GetHighlightPosList()
						if len(highlightPos) != len(expectedDish.pos) {
							t.Errorf("Expected %d highlight positions for dish %d, got %d",
								len(expectedDish.pos), i, len(highlightPos))
						}
						for j, pos := range expectedDish.pos {
							if highlightPos[j].GetStart() != pos.start || highlightPos[j].GetEnd() != pos.end {
								t.Errorf("Dish %d highlight position mismatch at index %d: expected [%d, %d], got [%d, %d]",
									i, j, pos.start, pos.end, highlightPos[j].GetStart(), highlightPos[j].GetEnd())
							}
						}
					}
				} else if dishItem.GetHighlightPosList() != nil {
					t.Errorf("Expected no highlight in dish name %d for %s", i, tc.keyword)
				}
			}
		})
	}
}

func TestHighLightWords_MultiLanguage(t *testing.T) {
	cases := []struct {
		name          string
		storeName     string
		dishNames     []string
		keyword       string
		expectedStore *struct {
			match bool
			pos   []struct{ start, end uint32 }
		}
		expectedDishes []struct {
			match bool
			pos   []struct{ start, end uint32 }
		}
	}{
		{
			name:      "thai characters",
			storeName: "ร้านนม นมสด",
			dishNames: []string{"นมสด", "หวาน"},
			keyword:   "นมสด",
			expectedStore: &struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				match: true,
				pos:   []struct{ start, end uint32 }{{7, 10}},
			},
			expectedDishes: []struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				{match: true, pos: []struct{ start, end uint32 }{{0, 3}}},
				{match: false, pos: nil},
			},
		},
		{
			name:      "vietnamese characters",
			storeName: "Cửa Hàng Sữa",
			dishNames: []string{"Sữa Tươi", "Ngọt"},
			keyword:   "Sữa",
			expectedStore: &struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				match: true,
				pos:   []struct{ start, end uint32 }{{9, 11}},
			},
			expectedDishes: []struct {
				match bool
				pos   []struct{ start, end uint32 }
			}{
				{match: true, pos: []struct{ start, end uint32 }{{0, 2}}},
				{match: false, pos: nil},
			},
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			historyOrders := []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreName: tc.storeName,
					DishNames: tc.dishNames,
				},
			}
			result := HighLightWords(context.Background(), historyOrders, tc.keyword)

			if len(result) == 0 {
				t.Fatalf("Expected non-empty result")
			}

			// 验证店铺名高亮
			storeItem := result[0].GetStoreItem()
			if tc.expectedStore.match {
				if storeItem.GetHighlightPosList() == nil {
					t.Errorf("Expected store name to be highlighted for %s", tc.keyword)
				} else {
					highlightPos := storeItem.GetHighlightPosList()
					if len(highlightPos) != len(tc.expectedStore.pos) {
						t.Errorf("Expected %d highlight positions for store name, got %d",
							len(tc.expectedStore.pos), len(highlightPos))
					}
					for i, pos := range tc.expectedStore.pos {
						if highlightPos[i].GetStart() != pos.start || highlightPos[i].GetEnd() != pos.end {
							t.Errorf("Store highlight position mismatch at index %d: expected [%d, %d], got [%d, %d]",
								i, pos.start, pos.end, highlightPos[i].GetStart(), highlightPos[i].GetEnd())
						}
					}
				}
			} else if storeItem.GetHighlightPosList() != nil {
				t.Errorf("Expected no highlight in store name for %s", tc.keyword)
			}

			// 验证菜品名高亮
			for i, expectedDish := range tc.expectedDishes {
				if i >= len(result[0].GetDishItems()) {
					t.Errorf("Missing dish item at index %d", i)
					continue
				}
				dishItem := result[0].GetDishItems()[i]
				if expectedDish.match {
					if dishItem.GetHighlightPosList() == nil {
						t.Errorf("Expected dish name %d to be highlighted for %s", i, tc.keyword)
					} else {
						highlightPos := dishItem.GetHighlightPosList()
						if len(highlightPos) != len(expectedDish.pos) {
							t.Errorf("Expected %d highlight positions for dish %d, got %d",
								len(expectedDish.pos), i, len(highlightPos))
						}
						for j, pos := range expectedDish.pos {
							if highlightPos[j].GetStart() != pos.start || highlightPos[j].GetEnd() != pos.end {
								t.Errorf("Dish %d highlight position mismatch at index %d: expected [%d, %d], got [%d, %d]",
									i, j, pos.start, pos.end, highlightPos[j].GetStart(), highlightPos[j].GetEnd())
							}
						}
					}
				} else if dishItem.GetHighlightPosList() != nil {
					t.Errorf("Expected no highlight in dish name %d for %s", i, tc.keyword)
				}
			}
		})
	}
}

func TestHighLightWords_EdgeCases(t *testing.T) {
	// 测试边界情况
	cases := []struct {
		name      string
		storeName string
		dishNames []string
		keyword   string
	}{
		{
			name:      "very long store name",
			storeName: strings.Repeat("A", 1000) + " keyword " + strings.Repeat("B", 1000),
			dishNames: []string{"Test Dish"},
			keyword:   "keyword",
		},
		{
			name:      "keyword at start",
			storeName: "keyword rest of text",
			dishNames: []string{"keyword dish"},
			keyword:   "keyword",
		},
		{
			name:      "keyword at end",
			storeName: "text keyword",
			dishNames: []string{"dish keyword"},
			keyword:   "keyword",
		},
		{
			name:      "multiple matches in one string",
			storeName: "keyword text keyword text keyword",
			dishNames: []string{"keyword dish keyword"},
			keyword:   "keyword",
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			historyOrders := []*model.HistoryOrderInfo{
				{
					OrderId:   "1",
					StoreName: tc.storeName,
					DishNames: tc.dishNames,
				},
			}
			result := HighLightWords(context.Background(), historyOrders, tc.keyword)

			if result[0].GetStoreItem().GetHighlightPosList() == nil {
				t.Errorf("Expected highlight in store name for %s", tc.keyword)
			}
		})
	}
}
