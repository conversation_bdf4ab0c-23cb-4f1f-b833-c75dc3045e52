package historyorder

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"
)

func GenerateNextID(pageNum, pageSize int, lastFetchedTime time.Time) (string, error) {
	if pageNum < 1 || pageSize < 1 {
		return "", errors.New("pageNum and pageSize must be greater than 0")
	}

	timestamp := lastFetchedTime.UnixMilli()

	nextID := fmt.Sprintf("%d|%d|%d", pageNum, pageSize, timestamp)
	return base64.StdEncoding.EncodeToString([]byte(nextID)), nil
}

func ParseNextID(nextID string, defaultPageNum, defaultPageSize int) (pageNum, pageSize int, lastFetchedTime time.Time, err error) {
	if nextID == "" {
		return defaultPageNum, defaultPageSize, time.Time{}, nil
	}

	decoded, err := base64.StdEncoding.DecodeString(nextID)
	if err != nil {
		return 0, 0, time.Time{}, fmt.Errorf("failed to decode nextId: %w", err)
	}

	parts := strings.Split(string(decoded), "|")
	if len(parts) != 3 {
		return 0, 0, time.Time{}, errors.New("invalid nextId format: expected 'pageNum|pageSize|timestamp'")
	}

	pageNum, err = strconv.Atoi(parts[0])
	if err != nil || pageNum < 1 {
		return 0, 0, time.Time{}, fmt.Errorf("invalid pageNum in nextId: %w", err)
	}

	pageSize, err = strconv.Atoi(parts[1])
	if err != nil || pageSize < 1 {
		return 0, 0, time.Time{}, fmt.Errorf("invalid pageSize in nextId: %w", err)
	}

	timestamp, err := strconv.ParseInt(parts[2], 10, 64)
	if err != nil {
		return 0, 0, time.Time{}, fmt.Errorf("invalid timestamp in nextId: %w", err)
	}

	return pageNum, pageSize, time.UnixMilli(timestamp), nil
}
