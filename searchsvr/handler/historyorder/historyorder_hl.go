package historyorder

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"github.com/gogo/protobuf/proto"
	"sort"
	"strconv"
	"strings"
	"unicode"
	"unicode/utf8"
)

func HighLightWords(ctx context.Context, historyOrders []*model.HistoryOrderInfo, keyword string) []*foodalgo_search.SearchHistoryOrderItem {
	// 将关键词转换为 ASCII（保留完整的 query）
	asciiKeyword := util2.ToAsciiWithMapping(keyword)

	// 构造高亮结果
	orders := make([]*foodalgo_search.SearchHistoryOrderItem, 0)
	for _, order := range historyOrders {
		orderId, _ := strconv.ParseUint(order.OrderId, 10, 64)

		// StoreName 高亮
		storeHighlightItem := createHighlightItem(ctx, order.StoreId, order.StoreName, asciiKeyword)

		// DishNames 高亮
		dishHighlightItems := make([]*foodalgo_search.HighlightItem, 0)
		for idx, dishName := range order.DishNames {
			var dishId uint64
			if len(order.DishIds) > idx {
				dishId = order.DishIds[idx]
			}
			dishItem := createHighlightItem(ctx, dishId, dishName, asciiKeyword)
			if dishItem != nil {
				dishHighlightItems = append(dishHighlightItems, dishItem)
			}
		}

		// 构造结果
		orderItem := &foodalgo_search.SearchHistoryOrderItem{
			OrderId:   proto.Uint64(orderId),
			StoreItem: storeHighlightItem,
			DishItems: dishHighlightItems,
		}
		orders = append(orders, orderItem)
	}
	return orders
}

func isThaiLetter(r rune) bool {
	return r >= 0x0E00 && r <= 0x0E7F // Unicode 泰语字符范围
}

func isFullWordMatch(text string, keyword string, startIdx int) bool {
	endIdx := startIdx + len(keyword)

	// 检查前后字符是否是泰语字母，避免部分匹配
	if startIdx > 0 {
		prevRune, _ := utf8.DecodeLastRuneInString(text[:startIdx])
		if isThaiLetter(prevRune) {
			return false
		}
	}
	if endIdx < len(text) {
		nextRune, _ := utf8.DecodeRuneInString(text[endIdx:])
		if isThaiLetter(nextRune) {
			return false
		}
	}
	return true
}

func byteToRuneIndex(text string, byteIndex int) int {
	runeIndex := 0
	byteCount := 0
	for _, r := range text {
		if byteCount >= byteIndex {
			break
		}
		byteCount += utf8.RuneLen(r)
		runeIndex++
	}
	return runeIndex
}

func findWordPositions(text, word string) []*foodalgo_search.HighlightRange {
	var result []*foodalgo_search.HighlightRange
	// 使用 utf8.RuneCountInString 计算字符数
	// TH地区有些 字符数  = 3倍字符数，要特殊处理
	textRuneCount := utf8.RuneCountInString(text)
	wordRuneCount := utf8.RuneCountInString(word)

	// 将字符串转换为 rune 切片，方便处理 Unicode 字符
	textRunes := []rune(text)
	wordRunes := []rune(word)

	for i := 0; i <= textRuneCount-wordRuneCount; i++ {
		// 检查当前位置开始的子串是否与目标单词匹配
		if isSubRuneSliceEqual(textRunes[i:i+wordRuneCount], wordRunes) {
			// 检查是否为完整的单词，即前后为空格或文本边界
			isWordBoundaryBefore := i == 0 || isSpace(textRunes[i-1])
			isWordBoundaryAfter := i+wordRuneCount == textRuneCount || isSpace(textRunes[i+wordRuneCount])

			if isWordBoundaryBefore && isWordBoundaryAfter {
				start := uint32(i)
				end := uint32(i + wordRuneCount - 1)
				result = append(result, &foodalgo_search.HighlightRange{
					Start: &start,
					End:   &end,
				})
			}
		}
	}
	return result
}

func isSubRuneSliceEqual(a, b []rune) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

func isSpace(r rune) bool {
	return unicode.IsSpace(r)
}

// createHighlightItem 只高亮完整匹配的关键词
func createHighlightItem(ctx context.Context, itemID uint64, originalText string, keyword string) *foodalgo_search.HighlightItem {
	if len(originalText) == 0 {
		logkit.FromContext(ctx).Error("createHighlightItem invalid, originalText is nil")
		return nil
	}
	if len(keyword) == 0 {
		logkit.FromContext(ctx).Error("createHighlightItem invalid, keyword is nil")
		return nil
	}

	if env.GetCID() == cid.TH {
		return createHighlightItemForTh(ctx, itemID, originalText, keyword)
	}

	asciiText := util2.ToAsciiWithMapping(originalText)
	ranges := findWordPositions(asciiText, keyword)

	// 如果没有找到高亮范围，返回原始内容
	if len(ranges) == 0 {
		return &foodalgo_search.HighlightItem{
			ItemId:   proto.String(strconv.FormatUint(itemID, 10)),
			ItemName: proto.String(originalText),
		}
	}

	// 合并范围并返回
	return &foodalgo_search.HighlightItem{
		ItemId:           proto.String(strconv.FormatUint(itemID, 10)),
		ItemName:         proto.String(originalText),
		HighlightPosList: mergeHighlightRanges(ranges),
	}
}

// createHighlightItemForTh 只高亮完整匹配的关键词，字符数  = 3倍字符数，要特殊处理
func createHighlightItemForTh(ctx context.Context, itemID uint64, originalText string, keyword string) *foodalgo_search.HighlightItem {
	if len(originalText) == 0 {
		logkit.FromContext(ctx).Error("createHighlightItem invalid, originalText is nil")
		return nil
	}
	if len(keyword) == 0 {
		logkit.FromContext(ctx).Error("createHighlightItem invalid, keyword is nil")
		return nil
	}

	asciiText := util2.ToAsciiWithMapping(originalText)
	asciiKeyword := util2.ToAsciiWithMapping(keyword)

	var ranges []*foodalgo_search.HighlightRange
	searchStart := 0
	loopCnt := 0

	for {
		loopCnt += 1
		if loopCnt >= 100 {
			break
		}

		// 只查找完整单词
		startIndex := strings.Index(asciiText[searchStart:], asciiKeyword)
		if startIndex == -1 {
			break
		}

		// 计算全局 **字节索引**
		startIndex += searchStart
		endIndex := startIndex + len(asciiKeyword)

		// **转换为字符索引**
		startRuneIndex := byteToRuneIndex(originalText, startIndex)
		endRuneIndex := byteToRuneIndex(originalText, endIndex) - 1 // end 为闭区间

		// 确保是完整单词
		if isFullWordMatch(originalText, keyword, startIndex) {
			ranges = append(ranges, &foodalgo_search.HighlightRange{
				Start: proto.Uint32(uint32(startRuneIndex)),
				End:   proto.Uint32(uint32(endRuneIndex)),
			})
		}

		// 更新起始位置，继续搜索
		searchStart = endIndex
	}

	// 如果没有找到高亮范围，返回原始内容
	if len(ranges) == 0 {
		return &foodalgo_search.HighlightItem{
			ItemId:   proto.String(strconv.FormatUint(itemID, 10)),
			ItemName: proto.String(originalText),
		}
	}

	// 合并范围并返回
	return &foodalgo_search.HighlightItem{
		ItemId:           proto.String(strconv.FormatUint(itemID, 10)),
		ItemName:         proto.String(originalText),
		HighlightPosList: mergeHighlightRanges(ranges),
	}
}

func mergeHighlightRanges(ranges []*foodalgo_search.HighlightRange) []*foodalgo_search.HighlightRange {
	if len(ranges) == 0 {
		return ranges
	}

	// 按起点排序
	sort.Slice(ranges, func(i, j int) bool {
		return ranges[i].GetStart() < ranges[j].GetStart()
	})

	// 合并重叠或相邻的范围
	merged := []*foodalgo_search.HighlightRange{ranges[0]}
	for _, r := range ranges[1:] {
		last := merged[len(merged)-1]
		if r.GetStart() <= last.GetEnd()+1 { // 重叠或相邻
			last.End = proto.Uint32(max(last.GetEnd(), r.GetEnd()))
		} else {
			merged = append(merged, r)
		}
	}
	return merged
}

func max(a, b uint32) uint32 {
	if a > b {
		return a
	}
	return b
}
