package handler

import (
	"context"
	"errors"
	"sort"
	"sync"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/filter"

	o2oalgo2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/o2o-intelligence/ml-common/public-message/biz/food"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/merge"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rank"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/golang/protobuf/proto"
	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
)

func SPFSearch(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.HasMore = proto.Bool(false)
	rsp.Ids = make([]*foodalgo_search.SearchResponse_IDPair, 0)
	var idPairs []*foodalgo_search.SearchResponse_IDPair
	var err error
	defer func() {
		metric_reporter2.ReportNoResultWithHandler(traceInfo.HandlerType.String(), len(rsp.GetIds()))
		if traceInfo.HandlerType == traceinfo.HandlerTypeSearch || traceInfo.HandlerType == traceinfo.HandlerTypeSearchFood {
			metric_reporter2.ReportNoResultWithScene("SearchFoodStores", len(rsp.GetIds()))
		}
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(idPairs))
		traceInfo.AddPhraseStoreLength(ctx, "ResultCount", len(idPairs))
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	if decision.IsNeedListWise(ctx, traceInfo) {
		idPairs, err = SearchWithListWise(ctx, req, traceInfo, debugInfo)
	} else {
		idPairs, err = SearchAndMixResult(ctx, req, traceInfo, debugInfo)
	}
	if err != nil {
		return err
	}

	// handle page offset
	begin := int((req.GetPageNum() - 1) * req.GetPageSize())
	end := begin + int(req.GetPageSize())
	hasMore := true
	resultSize := len(idPairs)
	if begin >= resultSize {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SPFSearch result not enough", logkit.Int("begin", begin), logkit.Int("end", end), logkit.Int("size", resultSize))
		return nil
	}
	if end >= resultSize {
		end = resultSize
		hasMore = false
	}
	resultIDs := idPairs[begin:end]
	rsp.HasMore = proto.Bool(hasMore)
	rsp.Ids = resultIDs
	rsp.IsRewrite = proto.Bool(traceInfo.IsCorrected)
	rsp.IsCorrected = proto.Bool(traceInfo.IsCorrected)
	if traceInfo.IsCorrected {
		rsp.Rewrite = proto.String(traceInfo.CorrectKeyword)
		rsp.CorrectedWord = proto.String(traceInfo.CorrectKeyword)
		rsp.CorrectedType = traceInfo.RspCorrectedType.Enum()
	}
	if len(traceInfo.StoreCntOfL2Category) > 0 {
		storeCountInfo := make([]*foodalgo_search.SearchResponse_StoreCountOfL2Category, 0, len(traceInfo.StoreCntOfL2Category))
		for k, v := range traceInfo.StoreCntOfL2Category {
			storeCountInfo = append(storeCountInfo, &foodalgo_search.SearchResponse_StoreCountOfL2Category{
				L2CategoryId: proto.Uint32(k),
				StoreCount:   proto.Uint32(v),
			})
		}
		sort.SliceStable(storeCountInfo, func(i, j int) bool {
			if storeCountInfo[i].GetStoreCount() == storeCountInfo[j].GetStoreCount() {
				return storeCountInfo[i].GetL2CategoryId() > storeCountInfo[j].GetL2CategoryId()
			}
			return storeCountInfo[i].GetStoreCount() > storeCountInfo[j].GetStoreCount()
		})
		rsp.L2CategoryStoreCount = storeCountInfo
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "SPFSearch result", logkit.Int("begin", begin), logkit.Int("end", end), logkit.Int("size", len(resultIDs)), logkit.Any("req", req))
	return nil
}

func SearchForTotalNum(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchTotalNumResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	defer func() {
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, int(rsp.GetTotalNum()))
		metric_reporter2.ReportNoResultWithHandler(traceInfo.HandlerType.String(), int(rsp.GetTotalNum()))
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	idPairs, err := SearchAndMixResult(ctx, req, traceInfo, debugInfo)
	if err != nil {
		rsp.TotalNum = proto.Uint64(0)
		logkit.FromContext(ctx).WithError(err).Error("SearchForTotalNum SearchAndMixResult failed, return default 0")
		return err
	}
	rsp.TotalNum = proto.Uint64(uint64(len(idPairs)))
	rsp.IsRewrite = proto.Bool(traceInfo.IsCorrected)
	rsp.Rewrite = proto.String(traceInfo.CorrectKeyword)
	logger.MyDebug(ctx, traceInfo.IsDebug, "SearchForTotalNum result", logkit.Uint64("size", rsp.GetTotalNum()), logkit.Any("req", req))
	return nil
}

// todo 优化：调整为normal pipeline
func doSearchAndMixer(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) (model.StoreInfos, error) {

	var normalErr, adsErr, err error
	var recallStores, normalStores, adsStores, mixerStores model.StoreInfos
	defer func() {
		// 后置处理：打印dump log 上报&打印各阶段门店长度
		postprocess.PostProcessor(ctx, traceInfo, debugInfo, recallStores, mixerStores)
		postprocess.PostProcessorAckDump(ctx, traceInfo, normalStores, mixerStores)
	}()

	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)

	wg := sync.WaitGroup{}
	wg.Add(2)
	goroutine.WithGo(ctx, "SearchNormal", func(params ...interface{}) {
		defer wg.Done()
		recallStores, normalStores, normalErr = processor.SearchPipeline(ctx, traceInfo, debugInfo)
		if normalErr != nil {
			traceInfo.AddErrorToTraceInfo(normalErr)
			logkit.FromContext(ctx).Error("SearchNormal error", logkit.Err(normalErr))
			return
		}
	})
	goroutine.WithGo(ctx, "SearchAds", func(params ...interface{}) {
		defer wg.Done()
		if !decision.IsNeedAds(traceInfo) {
			logkit.Debug("skip ads")
			return
		}
		adsStores, adsErr = processor.SearchAdsPipeline(ctx, traceInfo, debugInfo)
		if adsErr != nil {
			traceInfo.AddErrorToTraceInfo(adsErr)
			logkit.FromContext(ctx).Error("SearchAds error", logkit.Err(adsErr))
			return
		}
		// 广告菜品截断
		adsStores = filter.AdsDishSizeLimitFilter(ctx, traceInfo, adsStores, 2)
	})
	wg.Wait()
	// 不返回单纯广告的结果
	if normalErr != nil {
		return nil, errors.New("search pipeline failed")
	}
	// vn门店精排(模型排序)， ID/TH/MY在pipeline里面
	normalStores = rank.FusionRankVN(ctx, traceInfo, normalStores, adsStores)

	if len(normalStores) == 0 {
		mixerStores = normalStores
		return mixerStores, nil // 不返回单纯广告的结果
	}
	// mixer
	mixerStores, err = merge.NormalAndAdsStoresMerge(ctx, traceInfo, normalStores, adsStores)

	// rerank
	mixerStores = rerank.StoresReRank(ctx, traceInfo, mixerStores)

	// filter2.0需求: 给 traceInfo 填充 StoreCntOfL2Category
	statisticStoreCountOfL2Category(mixerStores, traceInfo)

	if len(mixerStores) == 0 {
		mixerStores = normalStores // mixer异常兜底
	}
	return mixerStores, err
}

func SearchAndMixResult(ctx context.Context, req *foodalgo_search.SearchRequest, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]*foodalgo_search.SearchResponse_IDPair, error) {
	var idPairs []*foodalgo_search.SearchResponse_IDPair
	var err error
	if decision.IsUseMixProcessCache(ctx, req, traceInfo) {
		idPairs, err = integrate.MixSearchResultCache.GetMixerResult(ctx, traceInfo)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).Error("MixSearchResultCache.GetMixerResults error", logkit.Err(err))
		}
	}
	// hit cache
	if len(idPairs) > 0 {
		traceInfo.IsHitMixResultCache = true
		logger.MyDebug(ctx, traceInfo.IsDebug, "MixSearchResultCache.GetMixerResult ids get from cache", zap.Any("result", idPairs))
		return idPairs, nil
	}
	logkit.FromContext(ctx).Info("MixSearchResultCache idPairs is empty, need doSearchAndMixer")

	// search with normal and ads, then mixer
	mixStores, err := doSearchAndMixer(ctx, traceInfo, debugInfo)
	if err != nil {
		logkit.FromContext(ctx).Error("SearchAndMixResult doSearchAndMixer error", logkit.Err(err))
		return nil, err
	} else {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAndMixResult doSearchAndMixer success", logkit.Int("size", len(idPairs)))
	}

	idPairs = buildIdPairs(traceInfo, mixStores)

	if decision.IsUseMixProcessCache(ctx, req, traceInfo) {
		err = integrate.MixSearchResultCache.SetMixerResult(ctx, traceInfo, idPairs)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).WithError(err).Error("SearchAndMixResult SetMixerResult cache failed")
		} else {
			logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAndMixResult SetMixerResult cache success", logkit.String("keyword", traceInfo.QueryKeyword), logkit.Int("size", len(idPairs)))
		}
	}
	logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAndMixResult final result", logkit.Any("idPairs", idPairs))
	return idPairs, nil
}

func SearchWithListWise(ctx context.Context, req *foodalgo_search.SearchRequest, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) ([]*foodalgo_search.SearchResponse_IDPair, error) {
	var mixStores []*model.StoreInfo
	var err error
	if decision.IsUseListWiseCache(ctx, req, traceInfo) {
		cacheInfo, err1 := integrate.MixSearchResultCache.GetLiseWiseCacheInfoCache(ctx, traceInfo)
		if err1 != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).Error("GetLiseWiseCacheInfoCache error", logkit.Err(err1))
		}
		// 将pb 转化为 []*model.StoreInfo
		mixStores, _ = buildStoreInfos(ctx, traceInfo, cacheInfo)
	}

	if len(mixStores) == 0 {
		logkit.FromContext(ctx).Info("GetLiseWiseCacheInfoCache mixStores is empty, need doSearchAndMixer")
		mixStores, err = doSearchAndMixer(ctx, traceInfo, debugInfo)
		if err != nil {
			logkit.FromContext(ctx).Error("SearchWithListWise doSearchAndMixer error", logkit.Err(err))
			return nil, err
		}
	} else {
		logger.MyDebug(ctx, traceInfo.IsDebug, "mixStores from cache is not empty, skip doSearchAndMixer")
	}
	// list wise 重排，因为缓存之后需要重新listwise重排，所以只能放这里，不能放到doSearchAndMixer里面，且需要重新填充下debug展示信息
	mixStores = rerank.ListWiseReRank(ctx, traceInfo, debugInfo, mixStores)
	debugInfo.FillMixStores(traceInfo, mixStores)
	debugInfo.FillNormalStores(traceInfo, mixStores)

	if decision.IsUseListWiseCache(ctx, req, traceInfo) {
		cacheInfo := buildCacheInfo(traceInfo, mixStores, 0)
		err = integrate.MixSearchResultCache.SetLiseWiseCacheInfoCache(ctx, traceInfo, cacheInfo)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).WithError(err).Error("SearchWithListWise SetSearchStoreInfos cache failed")
		}
	}
	idPairs := buildIdPairs(traceInfo, mixStores)
	return idPairs, nil
}

func buildStoreInfos(ctx context.Context, traceInfo *traceinfo.TraceInfo, cacheInfo *foodalgo_search.SearchResultCacheInfo) ([]*model.StoreInfo, uint32) {
	storeInfos := make([]*model.StoreInfo, 0, len(cacheInfo.GetStores()))
	for _, store := range cacheInfo.GetStores() {
		// item 特征相关
		itemFea := food.ItemFeature{}
		err := proto.Unmarshal([]byte(store.GetItemFeatureBytes()), &itemFea)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("buildStoreInfos Unmarshal item feature failed")
		}
		s := &model.StoreInfo{
			StoreId:     store.GetStoreId(),
			ItemFeature: &itemFea,
		}
		// buildIdPair 相关
		s.DisplayOpeningStatus = o2oalgo2.DisplayOpeningStatus(store.GetStoreInfo().GetAlgoStoreStatus())
		s.BusinessType = store.GetStoreInfo().GetBusinessType()
		dishIds := make([]uint64, 0, len(store.GetStoreInfo().GetDishes()))
		dishes := make([]*model.DishInfo, 0, len(store.GetStoreInfo().GetDishes()))
		for _, dish := range store.GetStoreInfo().GetDishes() {
			dishes = append(dishes, &model.DishInfo{
				DishId: dish.GetDishId(),
			})
			dishIds = append(dishIds, dish.GetDishId())
		}
		s.DishInfos = dishes
		s.AdsExtraInfo = store.GetStoreInfo().GetAdsExtraInfo()
		s.StoreSegment = store.GetStoreInfo().GetStoreSegment()
		s.ItemSceneType = store.GetStoreInfo().GetItemSceneType()

		// 剩余需要用到的store info 字段, 除了在item feature 没有的， 后续cache新增字段，需同步函数 buildCacheInfo
		s.PLtrV1Score = store.GetPlrtV1()
		s.StoreName = store.GetStoreName()
		s.MainCategoryL2NameStr = store.GetMainCategoryL2Name()
		s.ExactMatch = store.GetExactMatch()
		storeInfos = append(storeInfos, s)
	}
	traceInfo.ContextFeatureBytes = []byte(cacheInfo.GetContextFeatureBytes())
	ctxFea := &food.ContextFeature{}
	err := proto.Unmarshal(traceInfo.ContextFeatureBytes, ctxFea)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("buildStoreInfos parse ContextFeature error", zap.String("cacheInfo.GetContextFeatureBytes", string(cacheInfo.GetContextFeatureBytes())))
	} else {
		traceInfo.ContextFeature = ctxFea
	}
	return storeInfos, cacheInfo.GetFewResultIndex()
}

func buildCacheInfo(traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo, fewResultIndex uint32) *foodalgo_search.SearchResultCacheInfo {
	cacheInfo := &foodalgo_search.SearchResultCacheInfo{
		ContextFeatureBytes: proto.String(string(traceInfo.ContextFeatureBytes)),
		FewResultIndex:      proto.Uint32(fewResultIndex),
	}
	storeInfos := make([]*foodalgo_search.SearchResultStoreInfo, 0, len(stores))
	// 后续cache新增字段，需同步函数 buildStoreInfos
	for _, store := range stores {
		if store == nil {
			continue
		}
		var itemFeaBytes []byte
		if store.ItemFeature != nil {
			itemFeaBytes, _ = proto.Marshal(store.ItemFeature)
		}
		storeInfo := &foodalgo_search.SearchResultStoreInfo{
			StoreId:            proto.Uint64(store.StoreId),
			StoreInfo:          buildRspStore(traceInfo, store),
			ItemFeatureBytes:   proto.String(string(itemFeaBytes)),
			PlrtV1:             proto.Float64(store.PLtrV1Score),
			StoreName:          proto.String(store.StoreName),
			MainCategoryL2Name: proto.String(store.MainCategoryL2NameStr),
			ExactMatch:         proto.Uint32(store.ExactMatch),
		}
		storeInfos = append(storeInfos, storeInfo)
	}
	cacheInfo.Stores = storeInfos
	return cacheInfo
}

func buildIdPairs(traceInfo *traceinfo.TraceInfo, stores []*model.StoreInfo) []*foodalgo_search.SearchResponse_IDPair {
	idPairs := make([]*foodalgo_search.SearchResponse_IDPair, 0, len(stores))
	for _, store := range stores {
		if traceInfo.HandlerType == traceinfo.HandlerTypeSearchIdsDishes && len(store.DishInfos) == 0 {
			continue // 召菜
		}
		idPair := buildIdPair(traceInfo, store)
		idPairs = append(idPairs, idPair)
	}
	return idPairs
}

func buildIdPair(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo) *foodalgo_search.SearchResponse_IDPair {
	idPair := &foodalgo_search.SearchResponse_IDPair{}
	algoStoreStatus := foodalgo_search.AlgoStoreStatus(store.DisplayOpeningStatus)
	// 广告都默认是open门店
	if store.BusinessType == foodalgo_search.BusinessType_Ads {
		algoStoreStatus = foodalgo_search.AlgoStoreStatus_ALGO_STORE_STATUS_OPEN
	}
	algoDishStatus := make([]foodalgo_search.AlgoDishStatus, 0)
	for range store.DishInfos {
		algoDishStatus = append(algoDishStatus, foodalgo_search.AlgoDishStatus_ALGO_DISH_STATUS_ACTIVE)
	}

	store.TraceContext = o2oalgo2.TraceContext{
		Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "store")),
		StoreId: proto.Uint64(store.StoreId),
		DishIds: store.DishInfos.DishIds(),
		Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
	}
	storeTraceContextStr, _ := store.TraceContext.Marshal()
	idPair.TraceContextBytes = proto.String(string(storeTraceContextStr))

	dishes := make([]*foodalgo_search.SearchResponse_Dish, 0, len(store.DishInfos))
	for _, dish := range store.DishInfos {
		dish.TraceContext = o2oalgo2.TraceContext{
			Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "dish")),
			StoreId: proto.Uint64(store.StoreId),
			DishIds: []uint64{dish.DishId},
			Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
		}
		dishTraceContextStr, _ := dish.TraceContext.Marshal()
		dishes = append(dishes, &foodalgo_search.SearchResponse_Dish{
			DishId:            proto.Uint64(dish.DishId),
			TraceContextBytes: proto.String(string(dishTraceContextStr)),
		})
	}
	idPair.Dishes = dishes

	idPair.StoreId = proto.Uint64(store.StoreId)
	idPair.DishIds = store.DishInfos.DishIds()
	idPair.BusinessType = store.BusinessType.Enum()
	idPair.AdsExtraInfo = proto.String(store.AdsExtraInfo)
	idPair.AlgoStoreStatus = algoStoreStatus.Enum()
	idPair.AlgoDishStatus = algoDishStatus
	idPair.StoreSegment = proto.String(store.StoreSegment)
	return idPair
}

func buildRspStore(traceInfo *traceinfo.TraceInfo, store *model.StoreInfo) *foodalgo_search.SearchStoresWithListingDishResp_StoreInfo {
	rspStore := &foodalgo_search.SearchStoresWithListingDishResp_StoreInfo{}
	algoStoreStatus := foodalgo_search.AlgoStoreStatus(store.DisplayOpeningStatus)
	// 广告都默认是open门店
	if store.BusinessType == foodalgo_search.BusinessType_Ads {
		algoStoreStatus = foodalgo_search.AlgoStoreStatus_ALGO_STORE_STATUS_OPEN
	}
	algoDishStatus := make([]foodalgo_search.AlgoDishStatus, 0)
	for range store.DishInfos {
		algoDishStatus = append(algoDishStatus, foodalgo_search.AlgoDishStatus_ALGO_DISH_STATUS_ACTIVE)
	}

	store.TraceContext = o2oalgo2.TraceContext{
		Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "store")),
		StoreId: proto.Uint64(store.StoreId),
		DishIds: store.DishInfos.DishIds(),
		Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
	}
	storeTraceContextStr, _ := store.TraceContext.Marshal()
	rspStore.TraceContextBytes = proto.String(string(storeTraceContextStr))

	dishes := make([]*foodalgo_search.SearchStoresWithListingDishResp_DishInfo, 0, len(store.DishInfos))
	for _, dish := range store.DishInfos {
		dish.TraceContext = o2oalgo2.TraceContext{
			Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "dish")),
			StoreId: proto.Uint64(store.StoreId),
			DishIds: []uint64{dish.DishId},
			Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
		}
		dishTraceContextStr, _ := dish.TraceContext.Marshal()
		dishes = append(dishes, &foodalgo_search.SearchStoresWithListingDishResp_DishInfo{
			DishId:            proto.Uint64(dish.DishId),
			TraceContextBytes: proto.String(string(dishTraceContextStr)),
		})
	}
	rspStore.Dishes = dishes

	rspStore.StoreId = proto.Uint64(store.StoreId)
	rspStore.BusinessType = store.BusinessType.Enum()
	rspStore.AdsExtraInfo = proto.String(store.AdsExtraInfo)
	rspStore.AlgoStoreStatus = algoStoreStatus.Enum()
	rspStore.StoreSegment = proto.String(store.StoreSegment)
	rspStore.ItemSceneType = store.ItemSceneType.Enum()
	return rspStore
}

func statisticStoreCountOfL2Category(stores []*model.StoreInfo, traceInfo *traceinfo.TraceInfo) {
	bStatistic := false
	if traceInfo.TraceRequest.GetFilterType() == nil || traceInfo.TraceRequest.GetFilterType().GetL2Categories() == nil || len(traceInfo.TraceRequest.GetFilterType().GetL2Categories()) == 0 {
		bStatistic = true
	}
	storeCntOfL2Category := make(map[uint32]uint32)
	for _, store := range stores {
		if env.GetCID() != cid.VN && bStatistic {
			for k, _ := range store.L2CategoryId {
				storeCntOfL2Category[k] += 1
			}
		}
	}
	traceInfo.StoreCntOfL2Category = storeCntOfL2Category
}

func getItemNums(ids []*foodalgo_search.SearchResponse_IDPair) (int, int, int, int) {
	storeNum, dishNum, adsStoreNum, adsDishNum := 0, 0, 0, 0
	if len(ids) == 0 {
		return storeNum, dishNum, adsStoreNum, adsDishNum
	}
	for _, idPair := range ids {
		dn := len(idPair.GetDishes())
		storeNum++
		dishNum += dn
		if idPair.GetBusinessType() == foodalgo_search.BusinessType_Ads {
			adsStoreNum++
			adsDishNum += dn
		}
	}
	return storeNum, dishNum, adsStoreNum, adsDishNum
}

func getItemNumsForDishRecall(recallItems []*foodalgo_search.DishRecallItem) (int, int, int, int) {
	storeNum, dishNum, adsStoreNum, adsDishNum := 0, 0, 0, 0
	if len(recallItems) == 0 {
		return storeNum, dishNum, adsStoreNum, adsDishNum
	}
	for _, recallItem := range recallItems {
		dn := len(recallItem.GetDishItems())
		storeNum += 1
		dishNum += dn
		adsStoreNum += 1
		adsDishNum += dn
	}
	return storeNum, dishNum, adsStoreNum, adsDishNum
}
func getDishNumForDishRecall(recallItems []*foodalgo_search.DishRecallItem) int {
	dishNum := 0
	if len(recallItems) == 0 {
		return dishNum
	}
	for _, recallItem := range recallItems {
		dn := len(recallItem.GetDishItems())
		dishNum += dn
	}
	return dishNum
}

func getItemNumsDishListing(stores []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo) (int, int, int, int) {
	storeNum, dishNum, adsStoreNum, adsDishNum := 0, 0, 0, 0
	if len(stores) == 0 {
		return storeNum, dishNum, adsStoreNum, adsDishNum
	}
	for _, store := range stores {
		dn := len(store.GetDishes())
		storeNum++
		dishNum += dn
		if store.GetBusinessType() == foodalgo_search.BusinessType_Ads {
			adsStoreNum++
			adsDishNum += dn
		}
	}
	return storeNum, dishNum, adsStoreNum, adsDishNum
}
