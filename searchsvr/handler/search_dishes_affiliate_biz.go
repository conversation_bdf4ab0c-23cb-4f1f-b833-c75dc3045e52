package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
)

// 佣金门店搜菜
func SearchDishesForAffiliate(ctx context.Context, req *foodalgo_search.SearchDishesAffiliateReq, rsp *foodalgo_search.SearchDishesAffiliateResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.Dishes = make([]*foodalgo_search.SearchDishesAffiliateResp_DishInfo, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchDishesForAffiliate", len(rsp.GetDishes()))
	}()
	traceInfo.PipelineType = traceinfo.PipelineTypeSearchDishesForAffiliate
	var dishes []*foodalgo_search.SearchDishesAffiliateResp_DishInfo
	var hasMore bool

	// 第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseSearchDishAffiliateCache(ctx, traceInfo) {
		dishes, _ = integrate.SearchAffiliateResultCache.GetSearchDishesAffiliateResult(ctx, traceInfo)
	}
	// hit cache
	if len(dishes) > 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAffiliateResultCache.GetSearchDishesAffiliateResult result get from cache", zap.Any("dishes", dishes))
	} else {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAffiliateResultCache.GetSearchDishesAffiliateResult result get from cache", zap.Any("dishes", dishes))
		dishInfos, dishErr := processor.SearchDishesAffiliatePipeline(ctx, traceInfo, debugInfo)
		if dishErr != nil {
			logkit.FromContext(ctx).WithError(dishErr).Error("SearchDishesAffiliatePipeline error", logkit.Err(dishErr))
		}
		dishes = buildDishesAffiliate(traceInfo.QueryKeyword, dishInfos)
		if decision.IsUseSearchDishAffiliateCache(ctx, traceInfo) {
			_ = integrate.SearchAffiliateResultCache.SetSearchDishesAffiliateResult(ctx, traceInfo, dishes)
		}
	}
	dishes, hasMore = cutOffPage(dishes, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(dishes) > 0 {
		rsp.Dishes = dishes
	}
	return nil
}

func buildDishesAffiliate(query string, dishInfos model.DishInfos) []*foodalgo_search.SearchDishesAffiliateResp_DishInfo {
	query = util.ToAscii(vn_char_mapping_job.CharMappingInstance.ReplaceChar(query))
	dishes := make([]*foodalgo_search.SearchDishesAffiliateResp_DishInfo, 0, len(dishInfos))
	for _, d := range dishInfos {
		dish := &foodalgo_search.SearchDishesAffiliateResp_DishInfo{
			DishId:         proto.Uint64(d.DishId),
			CommissionRate: proto.Uint32(d.BaseCommissionRate),
			HighlightPos:   processor.BuildHighlight(query, d.DishName),
		}
		dishes = append(dishes, dish)
	}
	return dishes
}

func cutOffPage(dishes []*foodalgo_search.SearchDishesAffiliateResp_DishInfo, pageNum uint32, pageSize uint32) ([]*foodalgo_search.SearchDishesAffiliateResp_DishInfo, bool) {
	start := int(pageSize * (pageNum - 1))
	end := int(pageSize) + start
	flag := false
	if end < len(dishes) {
		flag = true
	}
	if len(dishes) <= start {
		return []*foodalgo_search.SearchDishesAffiliateResp_DishInfo{}, flag
	}
	if end <= len(dishes) {
		return dishes[start:end], flag
	}

	return dishes[start:], flag
}
