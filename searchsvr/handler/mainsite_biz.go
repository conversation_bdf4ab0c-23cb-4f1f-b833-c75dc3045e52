package handler

import (
	"context"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"github.com/golang/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
)

const maxPageSizeForMainSite = 1000

// ID 主站接口
func SearchMainSite(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchForMainSiteResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo, sortBy foodalgo_search.SearchForMainSiteRequest_SortBy) error {
	rsp.Ids = make([]*foodalgo_search.SearchForMainSiteResponse_IDPair, 0)
	var stores model.StoreInfos
	var err error
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchMainSite", len(rsp.GetIds()))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(rsp.Ids))
		if traceInfo.IsDebug {
			debugInfo.FillNormalStores(traceInfo, stores)
		}
		goroutine.WithGo(ctx, "PrintDumpLogForMainSite", func(params ...interface{}) {
			postprocess.PrintDumpLogForMainSite(ctx, traceInfo, stores)
		})
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)
	// 正排字段 storeId, dishId, createTime, Price,SalesVolume
	_, stores, err = processor.MainSitePipeline(ctx, traceInfo)
	if err != nil {
		return err
	}

	switch sortBy {
	case foodalgo_search.SearchForMainSiteRequest_Latest:
		stores.SortMainSiteByLatest()
	case foodalgo_search.SearchForMainSiteRequest_TopSales:
		stores.SortMainSiteByTopSales()
	case foodalgo_search.SearchForMainSiteRequest_Price_DESC:
		stores.SortMainSiteByPriceDesc()
	case foodalgo_search.SearchForMainSiteRequest_Price_ASC:
		stores.SortMainSiteByPriceAsc()
	}
	sliceMaxCount := len(stores)
	if len(stores) > maxPageSizeForMainSite {
		sliceMaxCount = maxPageSizeForMainSite
	}
	resStores := make([]*foodalgo_search.SearchForMainSiteResponse_IDPair, 0, sliceMaxCount)
	for _, store := range stores[:sliceMaxCount] {
		resStore := &foodalgo_search.SearchForMainSiteResponse_IDPair{
			StoreId: proto.Uint64(store.StoreId),
		}
		if len(store.DishInfos) > 0 {
			resStore.DishId = proto.Uint64(store.DishInfos[0].DishId) // filter 阶段保证有且只有1个菜品id
		}
		resStores = append(resStores, resStore)
	}
	rsp.Ids = resStores
	rsp.TotalCnt = proto.Int32(int32(len(stores)))
	return nil
}
