package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	o2oalgo2 "git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"
	"github.com/gogo/protobuf/proto"
)

// 新门店搜菜
func SearchStoresWishDishListing(ctx context.Context, rsp *foodalgo_search.SearchStoresWithListingDishResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.Stores = make([]*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo, 0)
	var storeInfos []*model.StoreInfo
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchStoresWishDishListing", len(rsp.GetStores()))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(storeInfos))
		traceInfo.AddPhraseStoreLength(ctx, "ResultCount", len(storeInfos))
	}()
	var stores []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo
	var categories []*foodalgo_search.CategoryInfo

	var hasMore bool
	var fewResultIndex uint32
	// 第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseSearchStoresWithListingDishCache(ctx, traceInfo) {
		cacheInfo, err1 := integrate.MixSearchResultCache.GetLiseWiseCacheInfoCache(ctx, traceInfo)
		if err1 != nil {
			traceInfo.AddErrorToTraceInfo(err1)
			logkit.FromContext(ctx).Error("GetLiseWiseCacheInfoCache error", logkit.Err(err1))
		} else {
			storeInfos, fewResultIndex = buildStoreInfos(ctx, traceInfo, cacheInfo) // 将pb 转化为 []*model.StoreInfo
		}
	}
	// not hit cache
	if len(storeInfos) == 0 {
		storeInfos, fewResultIndex = processor.SearchNormalAndFewResult(ctx, traceInfo, debugInfo)
	}

	storeInfos = rerank.ListWiseReRank(ctx, traceInfo, debugInfo, storeInfos)
	if traceInfo.IsDebug {
		// list wire 会影响debug下的最终的门店列表，需要及时更新
		debugInfo.FillMixStores(traceInfo, storeInfos)
	}
	if decision.IsUseSearchStoresWithListingDishCache(ctx, traceInfo) {
		cacheInfo := buildCacheInfo(traceInfo, storeInfos, fewResultIndex)
		err2 := integrate.MixSearchResultCache.SetLiseWiseCacheInfoCache(ctx, traceInfo, cacheInfo)
		if err2 != nil {
			traceInfo.AddErrorToTraceInfo(err2)
			logkit.FromContext(ctx).WithError(err2).Error("SetLiseWiseCacheInfoCache failed")
		}
	}
	stores = buildRespStores(traceInfo, storeInfos)
	categories = buildStoresCategories(storeInfos)
	stores, hasMore = cutOffPageStoresForListingDish(stores, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(stores) > 0 {
		rsp.Stores = stores
	}
	if len(categories) > 0 {
		rsp.CategoryInfos = categories
	}
	// 减少diff的包体积
	if decision.IsDiffCheck(traceInfo) {
		rsp.CategoryInfos = nil
	}
	rsp.FewResultIndex = proto.Uint32(fewResultIndex)
	rsp.CorrectedInfo = &foodalgo_search.CorrectedInfo{
		IsCorrected: proto.Bool(traceInfo.IsCorrected),
	}
	if traceInfo.IsCorrected {
		rsp.CorrectedInfo.CorrectedWord = proto.String(traceInfo.CorrectKeyword)
		rsp.CorrectedInfo.CorrectedType = buildCorrectedType(traceInfo.RspCorrectedType)
	}
	return nil
}

func buildRespStores(traceInfo *traceinfo.TraceInfo, storeInfos model.StoreInfos) []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo {
	stores := make([]*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo, 0, len(storeInfos))
	for _, s := range storeInfos {
		store := buildRespStore(traceInfo, s)
		stores = append(stores, store)
	}
	return stores
}

func buildRespStore(traceInfo *traceinfo.TraceInfo, s *model.StoreInfo) *foodalgo_search.SearchStoresWithListingDishResp_StoreInfo {
	store := &foodalgo_search.SearchStoresWithListingDishResp_StoreInfo{}
	store.StoreId = proto.Uint64(s.StoreId)
	algoStoreStatus := foodalgo_search.AlgoStoreStatus(s.DisplayOpeningStatus)
	store.BusinessType = s.BusinessType.Enum()
	if store.GetBusinessType() == foodalgo_search.BusinessType_Ads {
		algoStoreStatus = foodalgo_search.AlgoStoreStatus_ALGO_STORE_STATUS_OPEN
	}
	store.AdsExtraInfo = proto.String(s.AdsExtraInfo)
	store.AlgoStoreStatus = algoStoreStatus.Enum()

	// dishes
	dishes := make([]*foodalgo_search.SearchStoresWithListingDishResp_DishInfo, 0, len(s.DishInfos))
	for _, dish := range s.DishInfos {
		dish.TraceContext = o2oalgo2.TraceContext{
			Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "dish")),
			StoreId: proto.Uint64(s.StoreId),
			DishIds: []uint64{dish.DishId},
			Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
		}
		dishTraceContextStr, _ := dish.TraceContext.Marshal()
		dishItem := &foodalgo_search.SearchStoresWithListingDishResp_DishInfo{
			DishId:            proto.Uint64(dish.DishId),
			TraceContextBytes: proto.String(string(dishTraceContextStr)),
			AlgoDishStatus:    foodalgo_search.AlgoDishStatus_ALGO_DISH_STATUS_ACTIVE.Enum(),
		}

		// 减少diff的包体积
		if decision.IsDiffCheck(traceInfo) {
			dishItem.TraceContextBytes = nil
		}

		dishes = append(dishes, dishItem)
	}
	store.Dishes = dishes
	s.TraceContext = o2oalgo2.TraceContext{
		Source:  proto.String(traceinfo.GetTraceContextSource(traceInfo.HandlerType, "store")),
		StoreId: proto.Uint64(s.StoreId),
		DishIds: s.DishInfos.DishIds(),
		Query:   proto.String(traceInfo.TraceRequest.QueryRaw),
	}
	storeTraceContextStr, _ := s.TraceContext.Marshal()
	store.TraceContextBytes = proto.String(string(storeTraceContextStr))
	store.StoreSegment = proto.String(s.StoreSegment)
	store.ItemSceneType = s.ItemSceneType.Enum()

	// 减少diff的包体积
	if decision.IsDiffCheck(traceInfo) {
		store.TraceContextBytes = nil
	}
	return store
}

func cutOffPageStoresForListingDish(stores []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo, pageNum uint32, pageSize uint32) ([]*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo, bool) {
	start := int(pageSize * (pageNum - 1))
	end := int(pageSize) + start
	flag := false
	if end < len(stores) {
		flag = true
	}
	if len(stores) <= start {
		return []*foodalgo_search.SearchStoresWithListingDishResp_StoreInfo{}, flag
	}
	if end <= len(stores) {
		return stores[start:end], flag
	}

	return stores[start:], flag
}

func buildCorrectedType(correctedType foodalgo_search.SearchResponse_CorrectedType) *foodalgo_search.CorrectedType {
	switch correctedType {
	case foodalgo_search.SearchResponse_UseCorrectedKeyword:
		return foodalgo_search.CorrectedType_UseCorrectedKeyword.Enum()
	case foodalgo_search.SearchResponse_UseOriginalKeyword:
		return foodalgo_search.CorrectedType_UseOriginalKeyword.Enum()
	case foodalgo_search.SearchResponse_OriginalNoResultUseCorrectedKeyword:
		return foodalgo_search.CorrectedType_OriginalNoResultUseCorrectedKeyword.Enum()
	default:
		return foodalgo_search.CorrectedType_UseCorrectedKeyword.Enum()
	}
}
