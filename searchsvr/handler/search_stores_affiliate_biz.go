package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	affiliate_client "git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/affiliate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"github.com/gogo/protobuf/proto"
	"go.uber.org/zap"
)

// 佣金门店搜菜
func SearchStoresForAffiliate(ctx context.Context, req *foodalgo_search.SearchStoresAffiliateReq, rsp *foodalgo_search.SearchStoresAffiliateResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.Stores = make([]*foodalgo_search.SearchStoresAffiliateResp_StoreInfo, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchStoresForAffiliate", len(rsp.GetStores()))
	}()
	traceInfo.PipelineType = traceinfo.PipelineTypeSearchStoresForAffiliate
	var stores []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo
	var categories []*foodalgo_search.CategoryInfo
	var hasMore bool

	// 查询KOL生效的佣金计划
	storeCommPlans, dishCommPlans, planCommRate := affiliate_client.GetCommissionPlansByAffiliateId(ctx, traceInfo, traceInfo.TraceRequest.AffiliateId)
	if len(storeCommPlans) == 0 && len(dishCommPlans) == 0 {
		logkit.FromContext(ctx).Info("affiliate without any commission plans， return", logkit.Uint64("affiliateId", traceInfo.TraceRequest.AffiliateId))
		return nil
	}
	traceInfo.StoreCommPlans = storeCommPlans
	traceInfo.DishCommPlans = dishCommPlans
	traceInfo.PlanCommRate = planCommRate

	// 第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseSearchStoreAffiliateCache(ctx, traceInfo) {
		stores, _ = integrate.SearchAffiliateResultCache.GetSearchStoresAffiliateResult(ctx, traceInfo)
	}
	// hit cache
	if len(stores) > 0 {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAffiliateResultCache.GetSearchStoresAffiliateResult result get from cache", zap.Any("stores", stores))
	} else {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchAffiliateResultCache.GetSearchStoresAffiliateResult result get from cache", zap.Any("stores", stores))
		storeInfos, err := processor.SearchStoresAffiliatePipeline(ctx, traceInfo, debugInfo)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("SearchStoresAffiliatePipeline error", logkit.Err(err))
		}
		stores = buildStoresAffiliate(traceInfo.QueryKeyword, storeInfos)
		categories = buildStoresCategories(storeInfos)
		if decision.IsUseSearchStoreAffiliateCache(ctx, traceInfo) {
			_ = integrate.SearchAffiliateResultCache.SetSearchStoresAffiliateResult(ctx, traceInfo, stores)
		}
	}
	stores, hasMore = cutOffPageStores(stores, traceInfo.TraceRequest.PageNum, traceInfo.TraceRequest.PageSize)
	if hasMore {
		rsp.NextPageToken = proto.String(util.GetToken(traceInfo.TraceRequest.PublishId, traceInfo.TraceRequest.PageNum+1))
	}
	if len(stores) > 0 {
		stores = processor.SameBrandCountPipeline(ctx, traceInfo, stores) // 填充same brand count
		rsp.Stores = stores
	}
	if len(categories) > 0 {
		rsp.CategoryInfos = categories
	}
	return nil
}

func buildStoresCategories(stores model.StoreInfos) []*foodalgo_search.CategoryInfo {
	l2CategoryIdMap := make(map[uint32]uint32)
	for _, store := range stores {
		if store.MainCategory != nil && store.MainCategory.Level2Id > 0 {
			l2CategoryIdMap[store.MainCategory.Level2Id]++
		}
		if len(store.SubCategory) > 0 {
			for _, subCategory := range store.SubCategory {
				if subCategory != nil && subCategory.Level2Id > 0 {
					l2CategoryIdMap[subCategory.Level2Id]++
				}
			}
		}
	}
	categories := make([]*foodalgo_search.CategoryInfo, 0)
	for l2Id, count := range l2CategoryIdMap {
		categories = append(categories, &foodalgo_search.CategoryInfo{
			Category: proto.Uint32(l2Id),
			Count:    proto.Uint32(count),
			Level:    foodalgo_search.CategoryInfoLevel_CATEGORY_INFO_LEVEL_2.Enum(),
		})
	}
	return categories
}

func buildStoresAffiliate(query string, storeInfos model.StoreInfos) []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo {
	query = util.ToAscii(vn_char_mapping_job.CharMappingInstance.ReplaceChar(query))
	stores := make([]*foodalgo_search.SearchStoresAffiliateResp_StoreInfo, 0, len(storeInfos))
	for _, s := range storeInfos {
		store := &foodalgo_search.SearchStoresAffiliateResp_StoreInfo{
			StoreId:        proto.Uint64(s.StoreId),
			DishIds:        s.DishInfos.DishIds(),
			CommissionRate: proto.Uint32(s.BaseCommissionRate),
			SameBrandCount: proto.Uint32(s.SameBrandCount),
			BrandId:        proto.Uint64(s.GetBrandId()),
		}
		stores = append(stores, store)
	}
	return stores
}

func cutOffPageStores(stores []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo, pageNum uint32, pageSize uint32) ([]*foodalgo_search.SearchStoresAffiliateResp_StoreInfo, bool) {
	start := int(pageSize * (pageNum - 1))
	end := int(pageSize) + start
	flag := false
	if end < len(stores) {
		flag = true
	}
	if len(stores) <= start {
		return []*foodalgo_search.SearchStoresAffiliateResp_StoreInfo{}, flag
	}
	if end <= len(stores) {
		return stores[start:end], flag
	}

	return stores[start:], flag
}
