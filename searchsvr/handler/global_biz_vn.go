package handler

import (
	"context"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"github.com/gogo/protobuf/proto"
	"github.com/jinzhu/copier"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// vn search global 未分页接口。 将food-1001 & mart-1002 拆开请求MixerProcessMerge，然后再合并返回
func SearchGlobalForVN(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	resultLen := 0
	rsp.GlobalData = make([]*foodalgo_search.SearchResponse_GlobalData, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithHandler(traceInfo.HandlerType.String(), resultLen)
	}()
	wg := sync.WaitGroup{}
	parallel := len(req.GetFilterType().GetCategoryType())
	wg.Add(parallel)
	for _, ct := range req.GetFilterType().GetCategoryType() {
		goroutine.WithGo(ctx, "SearchGlobalForVN", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			category := param[0].(uint32)
			ctxCate := logkit.NewContext(context.Background(), logkit.FromContext(ctx))
			reqCate := &foodalgo_search.SearchRequest{}
			copier.Copy(&reqCate, &req)
			reqCate.PageNum = proto.Uint32(1)
			reqCate.FilterType.CategoryType = []uint32{category}
			traceInfoCate := traceinfo.BuildTraceInfo(ctxCate, reqCate, traceInfo.HandlerType)
			defer traceInfoCate.CloseTraceInfo()
			traceInfoCate.PipelineType = traceinfo.GetPipelineType(traceInfoCate.HandlerType, reqCate) // 重新获取类型。 HandlerTypeSearchGlobal => PipelineSearchFood/PipelineSearchMart
			idPairs, err2 := SearchAndMixResult(ctxCate, reqCate, traceInfoCate, debugInfo)
			traceInfoCate.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(idPairs))
			debugInfo.FillProcessInfo(traceInfoCate) // vn global 接口比较特殊，如果出最外层再来赋值 debugInfo 就来不及了，因为 traceInfo 是一个临时变量
			if err2 != nil {
				return
			}
			if traceInfoCate.IsCorrected && len(traceInfoCate.CorrectKeyword) > 0 {
				rsp.IsRewrite = proto.Bool(traceInfoCate.IsCorrected)
				rsp.Rewrite = proto.String(traceInfoCate.CorrectKeyword)
			}
			rsp.GlobalData = append(rsp.GlobalData, &foodalgo_search.SearchResponse_GlobalData{
				CategoryType: proto.Uint32(category),
				TotalNum:     proto.Uint64(uint64(len(idPairs))),
				IdPairs:      idPairs,
			})
			resultLen += len(idPairs)
		}, ct)
	}
	wg.Wait()
	return nil
}

// vn search global v1未分页旧接口。 foody_service_id = Food(1) + Mart(4) + Fresh(5) + Flower(6) +Medicines(7) + Pets(12) + Beer(13)
func SearchGlobalV1(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	resultLen := 0
	rsp.GlobalData = make([]*foodalgo_search.SearchResponse_GlobalData, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithHandler(traceInfo.HandlerType.String(), resultLen)
	}()

	foodyServiceIds := req.GetVnPromotionParams().GetFoodyServiceIds()
	if len(foodyServiceIds) == 0 {
		foodyServiceIds = []uint32{1, 4, 5, 6, 7, 12, 13}
	}
	wg := sync.WaitGroup{}
	parallel := len(foodyServiceIds)
	wg.Add(parallel)
	for _, id := range foodyServiceIds {
		goroutine.WithGo(ctx, "SearchGlobalV1", func(params ...interface{}) {
			defer wg.Done()
			param := params[0].([]interface{})
			fid := param[0].(uint32)
			ctxCate := logkit.NewContext(context.Background(), logkit.FromContext(ctx))
			reqCate := &foodalgo_search.SearchRequest{}
			copier.Copy(&reqCate, &req)
			reqCate.PageNum = proto.Uint32(1)
			if reqCate.VnPromotionParams == nil {
				reqCate.VnPromotionParams = &foodalgo_search.SearchRequest_VNPromotionParams{}
			}
			if fid == 1 {
				reqCate.FilterType.CategoryType = []uint32{1001}
				reqCate.VnPromotionParams.FoodyServiceIds = []uint32{1}
			} else {
				reqCate.FilterType.CategoryType = []uint32{1002}
				reqCate.VnPromotionParams.FoodyServiceIds = []uint32{fid}
			}
			traceInfoCate := traceinfo.BuildTraceInfo(ctxCate, reqCate, traceInfo.HandlerType)
			defer traceInfoCate.CloseTraceInfo()
			traceInfoCate.PipelineType = traceinfo.GetPipelineType(traceInfoCate.HandlerType, reqCate)
			idPairs, err2 := SearchAndMixResult(ctxCate, reqCate, traceInfoCate, debugInfo)
			traceInfoCate.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(idPairs))
			if err2 != nil {
				return
			}
			if len(idPairs) == 0 {
				return
			}
			rsp.GlobalData = append(rsp.GlobalData, &foodalgo_search.SearchResponse_GlobalData{
				CategoryType: proto.Uint32(fid),
				TotalNum:     proto.Uint64(uint64(len(idPairs))),
				IdPairs:      idPairs,
			})
			resultLen += len(idPairs)
		}, id)
	}
	wg.Wait()
	return nil
}
