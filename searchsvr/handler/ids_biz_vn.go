package handler

import (
	"context"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"github.com/gogo/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
)

// vn search global 未分页接口。 将food & mart 拆开请求MixerProcessMerge，然后再合并返回
func SearchIdsForVN(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	rsp.IdsData = &foodalgo_search.SearchResponse_IdsData{}
	var stores model.StoreInfos
	var err error
	defer func() {
		metric_reporter2.ReportNoResultWithHandler(traceInfo.HandlerType.String(), len(rsp.GetIdsData().GetStoreIds()))
		traceInfo.AddPhraseStoreLength(ctx, traceinfo.StoreLenFinal, len(stores))
		if traceInfo.IsDebug {
			debugInfo.FillNormalStores(traceInfo, stores)
		}
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)
	_, stores, err = processor.SearchPipeline(ctx, traceInfo, debugInfo)
	if err != nil {
		return err
	}
	storeIds := make([]uint64, 0, len(stores))
	for _, store := range stores {
		storeIds = append(storeIds, store.StoreId)
	}
	rsp.IdsData.TotalNum = proto.Uint64(uint64(len(storeIds)))
	rsp.IdsData.StoreIds = storeIds
	return nil
}
