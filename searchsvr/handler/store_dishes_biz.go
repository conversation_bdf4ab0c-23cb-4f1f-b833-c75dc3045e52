package handler

import (
	"context"
	"math"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/decision"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/recall"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

// vn mart 门店搜菜
func SearchStoreDishes(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchDishesResp, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	dishLen := 0
	rsp.Stores = make([]*foodalgo_search.SearchDishesResp_Store, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchStoreDishes", dishLen)
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	var stores []*foodalgo_search.SearchDishesResp_Store
	var categories []uint32
	from := int32(math.Ceil(float64(req.GetPageNum()*req.GetPageSize())/recall.StoreDishRecallSize)) - 1 // 接口每页20，缓存和ES每页400
	if from < 0 {
		from = 0
	}
	traceInfo.From = from
	wg := sync.WaitGroup{}
	wg.Add(2)
	goroutine.WithGo(ctx, "doSearchDishes", func(params ...interface{}) {
		defer wg.Done()
		stores = doSearchDishes(ctx, traceInfo, debugInfo)
	})

	goroutine.WithGo(ctx, "doSearchCategories", func(params ...interface{}) {
		defer wg.Done()
		categories = doSearchCategories(ctx, traceInfo, debugInfo)
	})
	wg.Wait()
	if len(stores) == 1 {
		hasMore := true
		store := stores[0]
		dishes := store.GetDishes()
		resultSize := len(dishes)
		// 分页是针对门店下菜品的
		begin := int((req.GetPageNum()-1)*req.GetPageSize()) - int(traceInfo.From*recall.StoreDishRecallSize) // 接口每页20，缓存和ES每页400
		if begin < 0 {
			begin = 0
		}
		if begin >= resultSize {
			logger.MyDebug(ctx, traceInfo.IsDebug, "SearchStoreDishes result not enough", logkit.Int("begin", begin), logkit.Int("size", resultSize))
			return nil
		}
		end := begin + int(req.GetPageSize())
		if end > resultSize {
			end = resultSize
			hasMore = false
		}
		store.Dishes = dishes[begin:end]
		if len(store.Dishes) > 0 {
			dishLen += len(store.Dishes)
			rsp.HasMore = proto.Bool(hasMore)
		}
		store.L3SkuCategoryIds = categories
		rsp.Stores = append(rsp.Stores, store)
	}
	return nil
}

func doSearchDishes(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) []*foodalgo_search.SearchDishesResp_Store {
	var stores []*foodalgo_search.SearchDishesResp_Store
	var err error
	// 第一页请求，不读缓存，但是下面需要写缓存
	if traceInfo.TraceRequest.PageNum > 1 && decision.IsUseSearchDishCache(ctx, traceInfo) {
		stores, err = integrate.SearchDishesResultCache.GetSearchDishesResult(ctx, traceInfo)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).Error("SearchDishesResultCache.GetSearchDishesResult error", logkit.Err(err))
		}
	}
	// hit cache
	if len(stores) > 0 && len(stores[0].GetDishes()) > 0 {
		traceInfo.IsHitMixResultCache = true
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchDishesResultCache.GetSearchDishesResult ids get from cache", zap.Any("stores", stores))
		return stores
	}
	stores, err = processor.SearchDishesPipeline(ctx, traceInfo, debugInfo)
	if decision.IsUseSearchDishCache(ctx, traceInfo) {
		err = integrate.SearchDishesResultCache.SetSearchDishesResult(ctx, traceInfo, stores)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).WithError(err).Error("SearchDishesResultCache GetSearchDishesResult cache failed")
		} else {
			logger.MyDebug(ctx, traceInfo.IsDebug, "SearchDishesResultCache GetSearchDishesResult cache success", logkit.String("keyword", traceInfo.QueryKeyword), logkit.Int("size", len(stores)))
		}
	}
	return stores
}

func doSearchCategories(ctx context.Context, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) []uint32 {
	var categories []uint32
	var err error
	if decision.IsUseSearchDishL3CategoryCache(ctx, traceInfo) {
		categories, err = integrate.SearchStoreCategoriesResultCache.GetSearchStoreCategoriesResult(ctx, traceInfo)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).Error("SearchStoreCategoriesResultCache.GetSearchStoreCategoriesResult error", logkit.Err(err))
		}
	}
	// hit cache
	if len(categories) > 0 {
		traceInfo.IsHitMixResultCache = true
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchStoreCategoriesResultCache.GetSearchStoreCategoriesResult ids get from cache", zap.Any("categories", categories))
		return categories
	}
	categories, err = processor.SearchL3CategoryPipeline(ctx, traceInfo, debugInfo)
	if decision.IsUseSearchDishL3CategoryCache(ctx, traceInfo) {
		err = integrate.SearchStoreCategoriesResultCache.SetSearchStoreCategoriesResult(ctx, traceInfo, categories)
		if err != nil {
			traceInfo.AddErrorToTraceInfo(err)
			logkit.FromContext(ctx).WithError(err).Error("SearchStoreCategoriesResultCache SetSearchStoreCategoriesResult cache failed")
		} else {
			logger.MyDebug(ctx, traceInfo.IsDebug, "SearchStoreCategoriesResultCache SetSearchStoreCategoriesResult cache success", logkit.String("keyword", traceInfo.QueryKeyword), logkit.Int("size", len(categories)))
		}
	}
	return categories
}
