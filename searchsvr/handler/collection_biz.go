package handler

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/debuginfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	metric_reporter2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/postprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/preprocess"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor/rerank"
	"github.com/golang/protobuf/proto"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/traceinfo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/processor"
)

func SearchCollection(ctx context.Context, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, traceInfo *traceinfo.TraceInfo, debugInfo *debuginfo.CombineDebugInfo) error {
	var err error
	var recallStores, stores model.StoreInfos
	rsp.Ids = make([]*foodalgo_search.SearchResponse_IDPair, 0)
	defer func() {
		metric_reporter2.ReportNoResultWithScene("SearchCollection", len(rsp.GetIds()))
		postprocess.PostProcessor(ctx, traceInfo, debugInfo, recallStores, stores)
		postprocess.PostProcessorAckDump(ctx, traceInfo, stores, stores)
	}()
	traceInfo.PipelineType = traceinfo.GetPipelineType(traceInfo.HandlerType, req)
	// 前置处理
	preprocess.SearchPipelinePreProcess(ctx, traceInfo)
	recallStores, stores, err = processor.SearchPipeline(ctx, traceInfo, debugInfo)
	if err != nil {
		return err
	}
	// Ltr
	stores = rerank.LtrReRank(ctx, traceInfo, stores)
	doPage(ctx, traceInfo, req, rsp, stores)
	return nil
}

func doPage(ctx context.Context, traceInfo *traceinfo.TraceInfo, req *foodalgo_search.SearchRequest, rsp *foodalgo_search.SearchResponse, stores model.StoreInfos) {

	begin := int((req.GetPageNum() - 1) * req.GetPageSize())
	end := begin + int(req.GetPageSize())
	hasMore := true
	resultSize := len(stores)
	if begin >= resultSize {
		logger.MyDebug(ctx, traceInfo.IsDebug, "SearchCollection result not enough", logkit.Int("begin", begin), logkit.Int("end", end), logkit.Int("size", resultSize))
		return
	}
	if end >= resultSize {
		end = resultSize
		hasMore = false
	}
	pageStores := stores[begin:end]
	idPairs := buildIdPairs(traceInfo, pageStores)

	rsp.HasMore = proto.Bool(hasMore)
	rsp.Ids = idPairs
	rsp.IsRewrite = proto.Bool(traceInfo.IsCorrected)
	rsp.Rewrite = proto.String(traceInfo.CorrectKeyword)

	logger.MyDebug(ctx, traceInfo.IsDebug, "SearchCollection result", logkit.Int("begin", begin), logkit.Int("end", end), logkit.Int("size", len(pageStores)), logkit.Any("req", req))
	return
}
