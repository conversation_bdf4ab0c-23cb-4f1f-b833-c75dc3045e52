package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"
)

var (
	configFile = flag.String("config", "conf/config.yml", "config file")
)

func main() {
	flag.Parse()

	svr := &MerchantSvr{}
	err := svr.Init(*configFile)
	if err != nil {
		fmt.Println(" svr.Init failed", err)
		return
	}
	goroutine.WithGo(context.TODO(), "pprofListenAndServe", func(params ...interface{}) {
		fmt.Println(http.ListenAndServe(os.Getenv("POD_IP")+":"+os.Getenv("PORT_pprof"), nil))
	})
	svr.Run()
}
