package async

import (
	"context"
	"sync"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/goroutine"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron"
)

type TaskPool struct {
	tasks []Task
}

func NewTaskPool(
	c *cron.Cron,
) *TaskPool {
	return &TaskPool{
		tasks: []Task{c},
	}
}

func (p *TaskPool) Start() {
	for _, t := range p.tasks {
		goroutine.WithGo(context.TODO(), "TaskPoolStart", func(params ...interface{}) {
			t.Start()
		})
	}
}

func (p *TaskPool) Stop() {
	wg := sync.WaitGroup{}
	wg.Add(len(p.tasks))
	for _, t := range p.tasks {
		t := t
		goroutine.WithGo(context.TODO(), "TaskPoolStop", func(params ...interface{}) {
			t.Stop()
			wg.Done()
		})
	}
	wg.Wait()
}
