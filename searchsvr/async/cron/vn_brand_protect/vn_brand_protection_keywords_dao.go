package vn_brand_protection_keywords

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/query"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"github.com/gogo/protobuf/proto"
)

type VnBrandProtectionKeywordsDao struct {
	vnBrandProtectDB *VnBrandProtectionKeywordsDB
	storeDB          *StoresDB
}

func NewVnBrandProtectionKeywordsDao(vnBrandProtectDB *VnBrandProtectionKeywordsDB, storeDB *StoresDB) *VnBrandProtectionKeywordsDao {
	return &VnBrandProtectionKeywordsDao{
		vnBrandProtectDB: vnBrandProtectDB,
		storeDB:          storeDB,
	}
}

func (p *VnBrandProtectionKeywordsDao) QueryAll(ctx context.Context) ([]*VNBrandProtect, error) {
	offset := uint64(0)
	limit := uint64(1000)
	var res []*VNBrandProtect
	for {
		param := &query.QueryParam{
			Offset: proto.Uint64(offset),
			Limit:  limit,
		}

		batchKeywords, err := p.vnBrandProtectDB.Query(ctx, param)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("VnBrandProtectionKeywordsDao db.Query parser")
			return nil, err
		}
		if len(batchKeywords) == 0 {
			break
		}
		for _, k := range batchKeywords {
			res = append(res, k)
		}
		// 更新 offset
		offset += limit
	}
	res, _ = p.MergeWithBrandIds(ctx, res)
	return res, nil
}

func (p *VnBrandProtectionKeywordsDao) MergeWithBrandIds(ctx context.Context, bps []*VNBrandProtect) ([]*VNBrandProtect, error) {
	var brandIds []uint64
	brandIdMap := make(map[uint64][]*StoreDB)
	for _, bp := range bps {
		for _, brandId := range bp.BrandIds {
			if brandId <= 0 {
				continue
			}
			if _, exist := brandIdMap[brandId]; !exist {
				brandIdMap[brandId] = make([]*StoreDB, 0)
				brandIds = append(brandIds, brandId)
			}
		}
	}
	if len(brandIds) == 0 {
		return bps, nil
	}

	lastId := uint64(0)
	for {
		param := &query.QueryParam{
			Offset: proto.Uint64(0),
			Limit:  200,
		}
		param.AddCondition("id", query.GT, lastId)
		param.AddCondition("brand_id", query.IN, brandIds)
		param.AddCondition("store_status", query.EQ, 2)
		param.AddSort("id", query.ASC)

		stores, err := p.storeDB.Query(ctx, param)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("VnBrandProtectionKeywordsDao db.Query parser")
			return nil, err
		}
		if len(stores) == 0 {
			break
		}
		for _, store := range stores {
			brandIdMap[store.BrandId] = append(brandIdMap[store.BrandId], store)
		}
		lastId = stores[len(stores)-1].StoreId
	}

	// 合并
	for _, bp := range bps {
		bp.StoreIds = make([]uint32, 0)
		// restaurant ids 直接填充
		for _, resId := range bp.RestaurantIds {
			bp.StoreIds = append(bp.StoreIds, uint32(resId))
		}
		// 通过brand id+ city id + store status = 2 将brand id 转化为res ids
		for _, brandId := range bp.BrandIds {
			if _, exist := brandIdMap[brandId]; exist {
				for _, store := range brandIdMap[brandId] {
					if bp.CityId == store.CityId {
						bp.StoreIds = append(bp.StoreIds, uint32(store.StoreId))
					}
				}
			}
		}
		bp.StoreIds = util.UniqueSlice(bp.StoreIds)
	}
	return bps, nil
}
