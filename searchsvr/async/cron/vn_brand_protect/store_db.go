package vn_brand_protection_keywords

import (
	"context"
	"database/sql"
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/query"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
)

type StoreDB struct {
	StoreId uint64 `json:"store_id"`
	BrandId uint64 `json:"brand_id"`
	CityId  uint32 `json:"city_id"`
}

type StoresDB struct {
	client *sql.DB
}

func NewStoresDB(dataPlatformMysql *config2.DataPlatformMysqlDB) *StoresDB {
	db := (*sql.DB)(dataPlatformMysql)
	return &StoresDB{client: db}
}

func (p *StoresDB) Query(ctx context.Context, param *query.QueryParam) ([]*StoreDB, error) {
	condStr, args, err := param.Sql()
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err), zap.Any("param", param))
	}

	query := fmt.Sprintf(`SELECT id, brand_id, city  FROM shopee_foodalgo_search_dataplatform_vn_db.store_tab %s`, condStr)

	logger.MyDebug(ctx, false, "StoresDB Query", logkit.String("query", query), logkit.Any("args", args))

	rows, err := p.client.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err),
			zap.String("StoresDB sql", query), zap.Any("param", param), zap.Any("args", args))
	}
	defer rows.Close()

	stores := make([]*StoreDB, 0, param.Limit)
	for rows.Next() {
		store := &StoreDB{}
		err = rows.Scan(&store.StoreId, &store.BrandId, &store.CityId)
		if err != nil && err != sql.ErrNoRows {
			logkit.FromContext(ctx).Error("StoresDB Rows.Scan", logkit.Err(err))
			continue
		}
		stores = append(stores, store)
	}
	return stores, nil
}
