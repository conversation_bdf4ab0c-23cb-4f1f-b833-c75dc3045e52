package vn_brand_protection_keywords

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/query"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
)

type VNBrandProtect struct {
	Keyword       string   `json:"keyword"`
	CityId        uint32   `json:"city_id"`
	RestaurantIds []uint64 `json:"restaurant_ids"` // 原始数据
	BrandIds      []uint64 `json:"brand_ids"`      // 原始数据
	StoreIds      []uint32 `json:"store_ids"`      // 合并数据
}

type VnBrandProtectionKeywordsDB struct {
	client *sql.DB
}

func NewVnBrandProtectionKeywordsDB(dataPlatformMysql *config2.DataPlatformMysqlDB) *VnBrandProtectionKeywordsDB {
	db := (*sql.DB)(dataPlatformMysql)
	return &VnBrandProtectionKeywordsDB{client: db}
}

func (p *VnBrandProtectionKeywordsDB) Query(ctx context.Context, param *query.QueryParam) ([]*VNBrandProtect, error) {
	condStr, args, err := param.Sql()
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err), zap.Any("param", param))
	}

	query := fmt.Sprintf(`select keyword,city_id, extra_data 
								 from shopee_foodalgo_search_dataplatform_vn_db.search_predefined_result_keyword_mapping_tab m
								 left join shopee_foodalgo_search_dataplatform_vn_db.search_predefined_result_tab r on m.group_id = r.id 
								 where r.status=1 %s`, condStr)

	logger.MyDebug(ctx, false, "VnBrandProtectionKeywordsDB Query", logkit.String("query", query), logkit.Any("args", args))

	rows, err := p.client.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err),
			zap.String("VnBrandProtectionKeywordsDB sql", query), zap.Any("param", param), zap.Any("args", args))
	}
	defer rows.Close()

	bps := make([]*VNBrandProtect, 0, param.Limit)
	for rows.Next() {
		bp := &VNBrandProtect{}
		extraDateStr := ""
		err = rows.Scan(&bp.Keyword, &bp.CityId, &extraDateStr)
		if err != nil && err != sql.ErrNoRows {
			logkit.FromContext(ctx).Error("VnBrandProtectionKeywordsDB Rows.Scan", logkit.Err(err))
			continue
		}
		if len(extraDateStr) > 0 {
			err = json.Unmarshal([]byte(extraDateStr), &bp)
			if err != nil {
				logkit.FromContext(ctx).Error("VnBrandProtectionKeywordsDB json.Unmarshal", logkit.Err(err), logkit.Any("bp", bp), logkit.String("extraDateStr", extraDateStr))
				continue
			}
		}
		bps = append(bps, bp)
	}
	return bps, nil
}
