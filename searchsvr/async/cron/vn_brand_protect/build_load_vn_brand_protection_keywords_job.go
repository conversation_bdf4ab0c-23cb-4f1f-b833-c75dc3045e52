package vn_brand_protection_keywords

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

var (
	VnBrandProtectKeywords map[string]bool     // key:keyword, 其中keyword 不去语调,仅小写+去首尾空格，用来判断query是否配置品牌保护的词表
	VnBrandProtectStoreIds map[string][]uint32 // key:keyword+city,其中keyword 去语调，且小写+去首尾空格，用来获取品牌保护门店列表
	rwMutex                sync.RWMutex
)

type LoadVnBrandProtectionKeywordJob struct {
	dao *VnBrandProtectionKeywordsDao
}

func NewLoadVnBrandProtectionKeywordJob(dao *VnBrandProtectionKeywordsDao) *LoadVnBrandProtectionKeywordJob {
	return &LoadVnBrandProtectionKeywordJob{
		dao: dao,
	}
}

func (c *LoadVnBrandProtectionKeywordJob) Run() {
	logkit.Info("LoadVnBrandProtectionKeywordJob.Run start")
	ctx := context.Background()
	vnBrandProtects, err := c.dao.QueryAll(ctx)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("LoadVnBrandProtectionKeywordJob dao.QueryAll error")
		_ = metric_reporter.ReportCronJobError(1, "LoadVnBrandProtectionKeywordJob")
		return
	}
	vnBrandProtectKeywords := make(map[string]bool, len(vnBrandProtects))
	vnBrandProtectStoreIds := make(map[string][]uint32, len(vnBrandProtects))
	for _, vnBrandProtect := range vnBrandProtects {
		if len(vnBrandProtect.Keyword) == 0 {
			continue
		}
		keyword := strings.TrimSpace(strings.ToLower(vnBrandProtect.Keyword))
		vnBrandProtectKeywords[keyword] = true
		key := fmt.Sprintf("%s_%d", util.ToAscii(keyword), vnBrandProtect.CityId)
		if len(vnBrandProtect.StoreIds) > 0 {
			vnBrandProtectStoreIds[key] = append(vnBrandProtectStoreIds[key], vnBrandProtect.StoreIds...)
		}
	}
	rwMutex.Lock()
	VnBrandProtectKeywords = vnBrandProtectKeywords
	VnBrandProtectStoreIds = vnBrandProtectStoreIds
	rwMutex.Unlock()

	logkit.Info("LoadVnBrandProtectionKeywordJob.Run finish")
}

// 判断keyword是否配置品牌保护的词表
func GetVnBrandProtectionKeywords(keyword string) bool {
	rwMutex.RLock()
	defer rwMutex.RUnlock()
	return VnBrandProtectKeywords[keyword]
}

// 判断keyword是否配置品牌保护的词表
func GetVnBrandProtectStoreIds(keyword string) []uint32 {
	rwMutex.RLock()
	defer rwMutex.RUnlock()
	return VnBrandProtectStoreIds[keyword]
}
