package unsettled_store

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
)

type BuildUnsettledStoreServiceS3Job struct {
	buildUnsettledStoreService *BuildUnsettledStoreService
}

func NewBuildUnsettledStoreServiceS3Job(buildUnsettledStoreService *BuildUnsettledStoreService) *BuildUnsettledStoreServiceS3Job {
	return &BuildUnsettledStoreServiceS3Job{
		buildUnsettledStoreService: buildUnsettledStoreService,
	}
}

func (p *BuildUnsettledStoreServiceS3Job) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("BuildUnsettledStoreServiceS3Job start")
	p.buildUnsettledStoreService.Run()
	logkit.FromContext(ctx).Info("BuildUnsettledStoreServiceS3Job done")
}
