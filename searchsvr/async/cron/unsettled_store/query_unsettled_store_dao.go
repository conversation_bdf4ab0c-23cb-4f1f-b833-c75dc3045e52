package unsettled_store

import (
	"bufio"
	"context"
	"io"
	"os"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"
)

type UnsettledStoreInfo struct {
	StoreName         string
	Category          []string
	DishName          []string
	CategoryTokenizer []string // for th
	DishNameTokenizer []string // for th
}

type QueryUnsettledStoreDict struct {
	Dict map[string]UnsettledStoreInfo
	sync.RWMutex
}

var QueryUnsettledStoreDao *QueryUnsettledStoreDict

var queryUnsettledStoreOnce sync.Once

func NewQueryUnsettledStoreDao() *QueryUnsettledStoreDict {
	queryUnsettledStoreOnce.Do(func() {
		QueryUnsettledStoreDao = &QueryUnsettledStoreDict{
			Dict:    make(map[string]UnsettledStoreInfo),
			RWMutex: sync.RWMutex{},
		}
	})
	return QueryUnsettledStoreDao
}

func (d *QueryUnsettledStoreDict) GetUnsettledStoreFromDict(ctx context.Context, key string) (*UnsettledStoreInfo, bool) {
	d.RLock()
	defer d.RUnlock()
	if storeInfo, ok := d.Dict[key]; ok {
		logkit.FromContext(ctx).Info("GetUnsettledStoreFromDict", zap.String("query", key), zap.Any("result", storeInfo))
		return &storeInfo, ok
	}
	return nil, false
}

func (d *QueryUnsettledStoreDict) BuildQueryUnsettledStoreDict(ctx context.Context, filePwd string, qpService *qp.QPService) error {
	//准备读取文件
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildQueryUnsettledStoreDict Failed to open file",
			logkit.String("file path", filePwd))
		return err
	}
	defer fs.Close()

	tempMap := make(map[string]UnsettledStoreInfo, 512)
	br := bufio.NewReader(fs)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}

		var splits = strings.Split(string(a), "###")
		if len(splits) < 4 {
			logkit.Error("BuildQueryUnsettledStoreDict line format invalid, should be 4 or 5 item", zap.String("filename", filePwd))
			continue
		}

		keyword := splits[0]
		lowerKey := strings.ToLower(strings.TrimSpace(keyword))
		tempMap[lowerKey] = handleEveryKeyword(ctx, splits, qpService)
	}

	d.Lock()
	defer d.Unlock()
	d.Dict = tempMap
	logkit.Info("BuildQueryUnsettledStoreDict finished", zap.Int("size", len(tempMap)))
	return nil
}

func handleEveryKeyword(ctx context.Context, splits []string, qpService *qp.QPService) UnsettledStoreInfo {
	storeName := splits[1]
	categoryStr := splits[2]
	var dishStr string
	if len(splits) == 4 {
		dishStr = splits[3]
	} else {
		dishStr = ""
	}

	item := UnsettledStoreInfo{}
	item.StoreName = strings.ToLower(strings.TrimSpace(storeName))

	item.Category, item.CategoryTokenizer = handleStr2List(ctx, categoryStr, qpService)
	item.DishName, item.DishNameTokenizer = handleStr2List(ctx, dishStr, qpService)
	return item
}

func handleStr2List(ctx context.Context, originStr string, qpService *qp.QPService) ([]string, []string) {
	splits := strings.Split(originStr, ",")
	var words = make([]string, 0, 3)
	var tokenizer = make([]string, 0, 3)
	for _, name := range splits {
		words = append(words, strings.ToLower(strings.TrimSpace(name)))
	}
	if env.GetCID() == cid.TH {
		wordsTokenizer := qpService.GetOtherQuerySegments(ctx, words)
		for i := range wordsTokenizer {
			tokenizer = append(tokenizer, buildStrFromArr(wordsTokenizer[i]))
		}
	}
	return words, tokenizer
}

func buildStrFromArr(arr []string) string {
	var stringBuilder strings.Builder
	for _, s := range arr {
		stringBuilder.WriteString(s)
		stringBuilder.WriteString(" ")
	}
	return strings.TrimSpace(stringBuilder.String())
}
