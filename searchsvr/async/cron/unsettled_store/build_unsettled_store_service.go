package unsettled_store

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/qp"
)

type BuildUnsettledStoreService struct {
	s3Client                s3.S3Service
	queryUnsettledStoreDict *QueryUnsettledStoreDict
	s3FileName              *config2.S3FileName
	qpService               *qp.QPService
}

func NewBuildUnsettledStoreService(s3Client s3.S3Service, unsettledStoreDict *QueryUnsettledStoreDict, s3FileName *config2.S3FileName, qpService *qp.QPService) *BuildUnsettledStoreService {
	return &BuildUnsettledStoreService{
		s3Client:                s3Client,
		queryUnsettledStoreDict: unsettledStoreDict,
		s3FileName:              s3FileName,
		qpService:               qpService,
	}
}

var currentTimestamp int64
var isFirstInit = true

func (b *BuildUnsettledStoreService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(b.s3FileName.GetUnSettledStoreFileName())
	defer func() {
		if err != nil {
			_ = metric_reporter.ReportCronJobError(1, "UnsettledStore")
		}
	}()
	if err != nil {
		return
	}

	err = b.queryUnsettledStoreDict.BuildQueryUnsettledStoreDict(ctx, filePwd, b.qpService)
}

func (b *BuildUnsettledStoreService) Run() {
	fileName := b.s3FileName.GetUnSettledStoreFileName()
	logkit.Info("BuildUnsettledStoreService start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildUnsettledStoreService Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildUnsettledStoreService Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildUnsettledStoreService fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildUnsettledStoreService update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildUnsettledStoreService done", zap.String("filename", fileName))
}

func (b *BuildUnsettledStoreService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildUnsettledStoreService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}
