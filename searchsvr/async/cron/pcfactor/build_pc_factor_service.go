package pcfactor

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

type BuildPcFactorService struct {
	s3Client    s3.S3Service
	PcFactorDao *PcFactorDao
	s3FileName  *config2.S3FileName
}

var currentTimestamp int64
var isFirstInit = true

func NewBuildPcFactorService(s3Client s3.S3Service, pcFactorDao *PcFactorDao, s3FileName *config2.S3FileName) *BuildPcFactorService {
	return &BuildPcFactorService{
		s3Client:    s3Client,
		PcFactorDao: pcFactorDao,
		s3FileName:  s3FileName,
	}
}

func (b *BuildPcFactorService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(b.s3FileName.GetPcFactorFile())
	defer func() {
		if err != nil {
			_ = metric_reporter.ReportCronJobError(1, "PCFactor")
		}
	}()
	if err != nil {
		logkit.Error("DownloadFile error", zap.String("err", err.Error()), zap.String("file", filePwd))
		return
	}

	err = b.PcFactorDao.BuildPcFactorDict(ctx, filePwd)
	if err != nil {
		logkit.Error("BuildPcFactorDict error", zap.String("err", err.Error()))
	}
}

func (b *BuildPcFactorService) Run() {
	fileName := b.s3FileName.GetPcFactorFile()
	logkit.Info("BuildPcFactorDict start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildPcFactorDict Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildPcFactorDict Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	/*if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildUeFactorDict fist init done", zap.String("filename", fileName))
		return
	}*/
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildPcFactorDict update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildPcFactorDict done", zap.String("filename", fileName))
}

func (b *BuildPcFactorService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildPcFactorService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}
