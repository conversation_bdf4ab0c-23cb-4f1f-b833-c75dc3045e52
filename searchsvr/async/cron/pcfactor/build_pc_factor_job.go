package pcfactor

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
)

type BuildPcFactorJob struct {
	BuildPcFactorService *BuildPcFactorService
}

func NewBuildPcFactorServiceS3Job(BuildPcFactorService *BuildPcFactorService) *BuildPcFactorJob {
	return &BuildPcFactorJob{
		BuildPcFactorService: BuildPcFactorService,
	}
}

func (p *BuildPcFactorJob) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("BuildPcFactorJob start")
	p.BuildPcFactorService.Run()
	logkit.FromContext(ctx).Info("BuildPcFactorJob done")
}
