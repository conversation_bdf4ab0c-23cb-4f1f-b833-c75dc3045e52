package pcfactor

import (
	"bufio"
	"context"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"
)

type PCFactors struct {
	VersionId string
	W3        float64
	W4        float64
	W5        float64
}

type PcFactorDao struct {
	dict map[string]PCFactors
	sync.RWMutex
}

var PcFactorDict *PcFactorDao

var once sync.Once

func NewPcFactorDict() *PcFactorDao {
	once.Do(func() {
		PcFactorDict = &PcFactorDao{
			dict:    make(map[string]PCFactors),
			RWMutex: sync.RWMutex{},
		}
	})
	return PcFactorDict
}

func (d *PcFactorDao) GetPcFactor(groupId string) PCFactors {
	d.RLock()
	defer d.RUnlock()
	if value, ok := d.dict[groupId]; ok {
		return value
	}
	return PCFactors{}
}

func (d *PcFactorDao) IsEmpty() bool {
	d.RLock()
	defer d.RUnlock()
	return len(d.dict) == 0
}

func (d *PcFactorDao) BuildPcFactorDict(ctx context.Context, filePwd string) error {
	//准备读取文件
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildPeFactorDict Failed to open file",
			logkit.String("file path", filePwd))
		return err
	}
	defer fs.Close()
	buffer := bufio.NewReader(fs)
	buffer = bufio.NewReaderSize(buffer, 65535)
	tempDict := make(map[string]PCFactors)

	for {
		line, _, end := buffer.ReadLine()
		if end == io.EOF {
			break
		}
		row := strings.Split(strings.Trim(string(line), "  "), "\t")
		if len(row) != 5 {
			continue
		}

		groupId := row[1]
		if len(groupId) == 0 {
			continue
		}

		w3, err := strconv.ParseFloat(row[2], 32)
		if err != nil {
			continue
		}

		w4, err := strconv.ParseFloat(row[3], 32)
		if err != nil {
			continue
		}

		w5, err := strconv.ParseFloat(row[4], 32)
		if err != nil {
			continue
		}

		tempDict[groupId] = PCFactors{
			VersionId: strings.TrimSpace(row[0]),
			W3:        w3,
			W4:        w4,
			W5:        w5,
		}
	}

	d.Lock()
	defer d.Unlock()
	if len(tempDict) > 0 {
		d.dict = tempDict
	}
	logkit.Info("BuildPeFactorDict finished", zap.Int("size", len(tempDict)))
	return nil
}
