package user_level

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

type BuildUserLevelService struct {
	s3Client   s3.S3Service
	LevelDao   *LevelDao
	s3FileName *config2.S3FileName
}

var currentTimestamp int64
var isFirstInit = true

func NewBuildUserLevelService(s3Client s3.S3Service, levelDao *LevelDao, s3FileName *config2.S3FileName) *BuildUserLevelService {
	return &BuildUserLevelService{
		s3Client:   s3Client,
		LevelDao:   levelDao,
		s3FileName: s3FileName,
	}
}

func (b *BuildUserLevelService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(b.s3FileName.GetUserLevel())
	defer func() {
		if err != nil {
			_ = metric_reporter.ReportCronJobError(1, "UserLevel")
		}
	}()
	if err != nil {
		return
	}

	err = b.LevelDao.BuildUserLevelDict(ctx, filePwd)
	if err != nil {
		logkit.Error("BuildUserLevelDict error", zap.String("err", err.Error()))
	}
}

func (b *BuildUserLevelService) Run() {
	fileName := b.s3FileName.GetStoreCtrCvr()
	logkit.Info("BuildUserLevelDict start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildUserLevelDict Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildUserLevelDict Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildUserLevelDict fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildUserLevelDict update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildUserLevelDict done", zap.String("filename", fileName))
}

func (b *BuildUserLevelService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildUserLevelService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}
