package user_level

import (
	"bufio"
	"context"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"go.uber.org/zap"
)

type LevelDao struct {
	dict map[uint64]string
	sync.RWMutex
}

var LevelDict *LevelDao

var once sync.Once

func NewLevelDict() *LevelDao {
	once.Do(func() {
		LevelDict = &LevelDao{
			dict:    make(map[uint64]string),
			RWMutex: sync.RWMutex{},
		}
	})
	return LevelDict
}

func (d *LevelDao) GetUserLevel(uid uint64) []string {
	if d == nil {
		return nil
	}
	d.RLock()
	defer d.RUnlock()
	if value, ok := d.dict[uid]; ok {
		return strings.Split(value, ";")
	}
	return nil
}
func (d *LevelDao) BuildUserLevelDict(ctx context.Context, filePwd string) error {
	//准备读取文件
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildUserLevelDict Failed to open file",
			logkit.String("file path", filePwd))
		return err
	}
	defer fs.Close()
	buffer := bufio.NewReader(fs)
	buffer = bufio.NewReaderSize(buffer, 65535)
	tempDict := make(map[uint64]string)
	for {
		line, _, end := buffer.ReadLine()
		if end == io.EOF {
			break
		}
		row := strings.Split(strings.Trim(string(line), "  "), "\t")
		uid, err1 := strconv.ParseInt(row[0], 10, 64)
		if err1 != nil {
			logkit.Error("parse string to int or float fail", zap.String("value", strings.Join(row, ",")))
			continue
		}

		tempDict[uint64(uid)] = row[1]
	}

	d.Lock()
	defer d.Unlock()
	d.dict = tempDict
	logkit.Info("BuildUserLevelDict finished", zap.Int("size", len(tempDict)))
	return nil
}
