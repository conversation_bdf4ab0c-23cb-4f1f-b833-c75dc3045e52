package user_level

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
)

type BuildUserLevelJob struct {
	BuildUserLevelService *BuildUserLevelService
}

func NewBuildStoreCtrCvrServiceS3Job(BuildUserLevelService *BuildUserLevelService) *BuildUserLevelJob {
	return &BuildUserLevelJob{
		BuildUserLevelService: BuildUserLevelService,
	}
}

func (p *BuildUserLevelJob) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("BuildUserLevelJob start")
	//p.BuildUserLevelService.Run()
	logkit.FromContext(ctx).Info("BuildUserLevelJob done")
}
