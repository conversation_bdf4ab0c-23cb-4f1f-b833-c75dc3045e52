package store_intervention

import (
	"context"
	"database/sql"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/timeutil"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
)

type StoreIntervention struct {
	Id                      uint64 `json:"id" db:"id"`
	SearchKeywords          string `json:"search_keywords" db:"search_keywords"`
	InterventionType        int32  `json:"intervention_type" db:"intervention_type"`
	LocationGroupIds        string `json:"location_group_ids" db:"location_group_ids"`
	InterventionMerchantIds string `json:"intervention_merchant_ids" db:"intervention_merchant_ids"`
	InterventionBrandIds    string `json:"intervention_brand_ids" db:"intervention_brand_ids"`
	InterventionStoreIds    string `json:"intervention_store_ids" db:"intervention_store_ids"`
	UpdateTime              uint64 `json:"update_time" db:"update_time"`
	InterventionTexts       string `json:"intervention_texts" db:"intervention_texts"`
	InterventionStoreTagIds string `json:"intervention_store_tag_ids"`
	IsNewMerchantsOnly      uint32 `json:"is_new_merchants_only"`
	IsRotateMerchants       uint32 `json:"is_rotate_merchants"`
	SlotsPosition           string `json:"slots_position"`

	InterventionMerchantIdList []uint64
	InterventionBrandIdList    []uint64
	InterventionStoreIdList    []uint64
	InterventionStoreTagIdList []uint64
	SlotsPositionList          []uint64
}
type StoreInterventionDB struct {
	client *sql.DB
}

func NewStoreInterventionDB(dataStreamMysql *config2.DataStreamMysqlDB) *StoreInterventionDB {
	db := (*sql.DB)(dataStreamMysql)
	return &StoreInterventionDB{
		client: db,
	}
}

func (db *StoreInterventionDB) GetAllStoreInterventionFromDB(ctx context.Context) ([]*StoreIntervention, error) {
	// 以后可能需要多翻页
	storeInterventionList := make([]*StoreIntervention, 0)
	query := `SELECT  id, search_keywords, intervention_type, location_group_ids, intervention_merchant_ids, intervention_brand_ids, intervention_store_ids,
 				update_time, intervention_texts, intervention_store_tags, 
				slots_position, is_rotate_merchants, is_new_merchants_only 
                FROM  search_intervention_tab
                where delete_time=0`
	rows, err := db.client.QueryContext(ctx, query)
	if err != nil || rows == nil {
		return nil, err
	}
	defer rows.Close()
	for rows.Next() {
		storeIntervention := StoreIntervention{}
		err := rows.Scan(&storeIntervention.Id, &storeIntervention.SearchKeywords, &storeIntervention.InterventionType, &storeIntervention.LocationGroupIds, &storeIntervention.InterventionMerchantIds,
			&storeIntervention.InterventionBrandIds, &storeIntervention.InterventionStoreIds, &storeIntervention.UpdateTime,
			&storeIntervention.InterventionTexts, &storeIntervention.InterventionStoreTagIds,
			&storeIntervention.SlotsPosition, &storeIntervention.IsRotateMerchants, &storeIntervention.IsNewMerchantsOnly)
		if err != nil {
			if err == sql.ErrNoRows {
				logkit.FromContext(ctx).With(logkit.Err(err)).Error(
					"StoreDB.Get row.Scan",
					zap.Any("row", rows),
					zap.String("sql", query))
				return nil, errors.Wrap(errno.ErrStoreNotExisted)
			}
			logkit.FromContext(ctx).With(logkit.Err(err)).Error(
				"StoreDB.Get",
				zap.Any("row", rows),
				zap.String("sql", query))
			return nil, errors.Wrap(
				errno.ErrDBUnknown,
				zap.Error(err),
				zap.String("sql", query))
		}
		storeInterventionList = append(storeInterventionList, &storeIntervention)
	}

	return storeInterventionList, nil
}

func (db *StoreInterventionDB) InsertStoreIntervention(ctx context.Context, keyword, interventionTexts, brandIds string) (int64, error) {
	sqlStr := `INSERT INTO   
					search_intervention_tab (search_keywords, intervention_type, location_group_ids, intervention_merchant_ids, intervention_brand_ids,
						intervention_store_ids, create_time, update_time, creator, intervention_texts)	
				VALUES
					(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
	now := timeutil.Now()
	result, err := db.client.ExecContext(ctx, sqlStr,
		keyword,
		4, // 置顶标识
		"all",
		"",
		brandIds,
		"",
		now,
		now,
		"migrate",
		interventionTexts,
	)
	if err != nil {
		logkit.FromContext(ctx).Error("InterventionDB.Add() exec db error", logkit.Err(err))
		return 0, errors.Wrap(errno.ErrDBUnknown, zap.Error(err))
	}

	insertID, err := result.LastInsertId()
	if err != nil {
		logkit.FromContext(ctx).Error("InterventionDB.Add() get last id error", logkit.Err(err))
		return 0, errors.Wrap(errno.ErrDBUnknown, zap.Error(err))
	}

	return insertID, nil
}
