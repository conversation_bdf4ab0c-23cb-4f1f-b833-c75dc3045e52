package store_rank_factor

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/load_data_plugin"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"
)

const DefaultStoreRankFactorFileName = "food_search_default_rank_factor_"

var DefaultStoreRankFactorLoadPlugin *load_data_plugin.LoadS3FilePlugin
var DefaultStoreRankFactorLoadPluginOnce sync.Once

func GetDefaultStoreRankFactorFileName() string {
	return DefaultStoreRankFactorFileName + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".csv"
}

func NewDefaultStoreRankFactorLoadPlugin(service s3.S3Service) *load_data_plugin.LoadS3FilePlugin {
	DefaultStoreRankFactorLoadPluginOnce.Do(func() {
		DefaultStoreRankFactorLoadPlugin = load_data_plugin.NewLoadS3FilePlugin(service, GetDefaultStoreRankFactorFileName(), LoadDefaultStoreRankFactor, GetDefaultStoreRankFactor)
	})
	return DefaultStoreRankFactorLoadPlugin
}

func GetDefaultStoreRankFactor(dict, key interface{}) interface{} {
	if dict == nil || key == nil {
		logkit.Debug("[GetDefaultStoreRankFactor] key or dict is nil...")
		return nil
	}
	factor, ok := dict.(StoreRankFactor)
	if !ok {
		logkit.Error("[GetDefaultStoreRankFactor] dict parse to StoreRankFactor error, args type is error")
		return nil
	}
	return factor
}

func LoadDefaultStoreRankFactor(filePath string) (interface{}, error) {
	fs, err := os.Open(filePath)
	if err != nil {
		logkit.Error("LoadDefaultStoreRankFactor Failed to open file",
			zap.String("file path", filePath), zap.String("err", err.Error()))
		return nil, err
	}
	defer fs.Close()
	buffer := bufio.NewReader(fs)
	buffer = bufio.NewReaderSize(buffer, 65535)
	tempDict := StoreRankFactor{}
	line, _, _ := buffer.ReadLine()
	tmpLine := strings.Trim(string(line), "  ")
	row := strings.Split(strings.Trim(string(tmpLine), "\n\r"), "\t")
	if len(row) < 8 {
		return nil, fmt.Errorf("load default store rank factor error, len(row) < 8")
	}
	floatVals := parseRow(row)
	tempDict.CPriceIndex = floatVals[0]
	tempDict.CUeFactor = floatVals[1]
	tempDict.CUeRatioFactor = floatVals[2]
	tempDict.CUeFactorOri = floatVals[3]
	tempDict.CUeRatioFactorOri = floatVals[4]
	tempDict.CCommissionRate = floatVals[5]
	tempDict.CServiceFee = floatVals[6]
	tempDict.CTaxRate = floatVals[7]
	return tempDict, nil
}
