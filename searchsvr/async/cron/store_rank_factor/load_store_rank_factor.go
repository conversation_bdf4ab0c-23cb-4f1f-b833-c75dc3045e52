package store_rank_factor

import (
	"bufio"
	"errors"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/proto/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"github.com/coocood/freecache"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/load_data_plugin"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"
)

const StoreRankFactorFileName = "food_search_store_rank_factor_"

var StoreRankFactorLoadPlugin *load_data_plugin.LoadS3FilePlugin
var StoreRankFactorLoadPluginOnce sync.Once

var StoreRankFactorFreeCache *freecache.Cache

func GetStoreRankFactorFileName() string {
	return StoreRankFactorFileName + env.GetCID() + "_" + util.GetEnvLiveish(false) + ".csv"
}

func NewStoreRankFactorLoadPlugin(service s3.S3Service) *load_data_plugin.LoadS3FilePlugin {
	cacheSize := 512
	if apollo.SearchApolloCfg.StoreRankFactorCacheSize != 0 {
		cacheSize = apollo.SearchApolloCfg.StoreRankFactorCacheSize
	}
	cacheSize = cacheSize * 1024 * 1024 // Convert MB to bytes
	StoreRankFactorLoadPluginOnce.Do(func() {
		StoreRankFactorLoadPlugin = load_data_plugin.NewLoadS3FilePlugin(service, GetStoreRankFactorFileName(), LoadStoreRankFactor, GetStoreRankFactor)
		StoreRankFactorFreeCache = localcache.InitFreeCache("StoreRankFactorFreeCache", cacheSize)
	})
	return StoreRankFactorLoadPlugin
}

func GetStoreRankFactor(dict, key interface{}) interface{} {
	if key == nil {
		logkit.Debug("[GetStoreRankFactor] key or dict is nil...")
		return nil
	}

	keyVal, ok1 := key.(uint64)
	if !ok1 {
		logkit.Error("[GetStoreRankFactor] key or dict parse to Map or uint64 error, args type is error")
		return nil
	}
	pb := new(o2oalgo.StoreRankFactor)
	err := StoreRankFactorFreeCache.GetFn(util.IntToBytes(int64(keyVal)), func(bVal []byte) error {
		// 如果key没有找到，会直接报错，不会往下走
		if len(bVal) > 0 {
			// key找到，且有值
			return pb.XXX_Unmarshal(bVal)
		}

		return errors.New("entry is expire")
	})

	if err == nil {
		return pb
	}
	return nil
}

func LoadStoreRankFactor(filePath string) (interface{}, error) {
	if env.GetEnv() == "test" {
		return nil, nil
	}
	fs, err := os.Open(filePath)
	if err != nil {
		logkit.Error("LoadStoreRankFactor Failed to open file",
			zap.String("file path", filePath), zap.String("err", err.Error()))
		return nil, err
	}
	defer fs.Close()
	buffer := bufio.NewReader(fs)
	buffer = bufio.NewReaderSize(buffer, 65535)

	for {
		line, _, end := buffer.ReadLine()
		if end == io.EOF {
			break
		}
		tmpLine := strings.Trim(string(line), "  ")
		row := strings.Split(strings.Trim(tmpLine, "\n\r"), "\t")
		if len(row) < 19 {
			break
		}
		storeId, err1 := strconv.ParseInt(row[0], 10, 64)
		if err1 != nil {
			logkit.Error("[StoreRankFactor] parse id error", zap.Any("val", row))
			continue
		}
		floatVals := parseRow(row[1:])

		storeFactor := &o2oalgo.StoreRankFactor{
			CSalesScoreCate1:    floatVals[0],
			CAccSalesScoreCate1: floatVals[1],
			CGmvScoreCate1:      floatVals[2],
			CAccGmvScoreCate1:   floatVals[3],
			CSalesScoreCate2:    floatVals[4],
			CAccSalesScoreCate2: floatVals[5],
			CGmvScoreCate2:      floatVals[6],
			CAccGmvScoreCate2:   floatVals[7],
			CCommissionRate:     floatVals[8],
			CServiceFee:         floatVals[9],
			CTaxRate:            floatVals[10],
			CCtr:                floatVals[11],
			CCvr:                floatVals[12],
			CPriceIndex:         floatVals[13],
			CUeFactor:           floatVals[14],
			CUeRatioFactor:      floatVals[15],
			CUeFactorOri:        floatVals[16],
			CUeRatioFactorOri:   floatVals[17],
		}

		keyBytes := util.IntToBytes(storeId)
		valBytes, _ := storeFactor.Marshal()
		setErr := StoreRankFactorFreeCache.Set(keyBytes, valBytes, 0)
		if setErr != nil {
			_ = metric_reporter.ReportCronJobError(1, filePath+"_set_cache_error")
			logkit.Error("BuildQueryStoreRealNameDict set cache failed", zap.String("line", tmpLine), zap.Error(setErr))
			continue
		}
	}
	return nil, nil
}

func parseRow(row []string) []float64 {
	values := make([]float64, len(row))
	for idx, v := range row {
		fVal, err := strconv.ParseFloat(v, 64)
		if err != nil {
			values[idx] = 0.0
			//logkit.Error("[StoreRankFactor] parse float error", zap.String("val", v))
		} else {
			values[idx] = fVal
		}
	}
	return values
}
