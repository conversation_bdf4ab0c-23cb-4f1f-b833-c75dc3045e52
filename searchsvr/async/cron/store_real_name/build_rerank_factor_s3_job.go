package store_real_name

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
)

type BuildStoreRealNameServiceS3Job struct {
	buildStoreRealNameService *BuildStoreRealNameService
}

func NewBuildStoreRealNameServiceS3Job(buildStoreRealNameService *BuildStoreRealNameService) *BuildStoreRealNameServiceS3Job {
	return &BuildStoreRealNameServiceS3Job{
		buildStoreRealNameService: buildStoreRealNameService,
	}
}

func (p *BuildStoreRealNameServiceS3Job) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("BuildStoreRealNameServiceS3Job start")
	p.buildStoreRealNameService.Run()
	logkit.FromContext(ctx).Info("BuildStoreRealNameServiceS3Job done")
}
