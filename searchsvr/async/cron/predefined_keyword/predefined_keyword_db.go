package predefined_keyword

import (
	"context"
	"database/sql"
	"fmt"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/logger"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/query"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/json"

	"git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/foodalgo_search"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/errors"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/errno"
	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const (
	undeleted = uint32(0)
	deleted   = uint32(1)
)

type PredefinedKeywordDB struct {
	client *sql.DB
}

func NewPredefinedKeywordDB(dataStreamMysql *config2.DataStreamMysqlDB) *PredefinedKeywordDB {
	db := (*sql.DB)(dataStreamMysql)
	return &PredefinedKeywordDB{client: db}
}

func (p *PredefinedKeywordDB) Query(ctx context.Context, param *query.QueryParam) ([]*model2.PredefinedKeyword, error) {
	condStr, args, err := param.Sql()
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err), zap.Any("param", param))
	}

	query := fmt.Sprintf(`SELECT id, actual_search_keyword, user_search_keywords, creator, deleted, create_time, update_time, delete_time
				FROM predefined_keyword_tab %s`, condStr)

	logger.MyDebug(ctx, false, "PredefinedKeywordDB Query", logkit.String("query", query), logkit.Any("args", args))

	rows, err := p.client.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, errors.Wrap(errno.ErrDBUnknown, zap.Error(err),
			zap.String("sql", query), zap.Any("param", param), zap.Any("args", args))
	}
	defer rows.Close()

	pks := make([]*model2.PredefinedKeyword, 0, param.Limit)
	for rows.Next() {
		pk, _ := model2.NewPredefinedKeyword(&foodalgo_search.PredefinedKeyword{})
		err = rows.Scan(&pk.Id, &pk.ActualSearchKeyword, &pk.UserSearchKeywordsStr, &pk.Creator, &pk.Deleted, &pk.CreateTime, &pk.UpdateTime, &pk.DeleteTime)
		if err != nil && err != sql.ErrNoRows {
			logkit.FromContext(ctx).Error("Rows.Scan", logkit.Err(err))
			continue
		}

		err = json.Unmarshal([]byte(pk.GetUserSearchKeywordsStr()), &pk.UserSearchKeywords)
		if err != nil {
			logkit.FromContext(ctx).Error("json.Unmarshal", logkit.String("str", pk.GetUserSearchKeywordsStr()), logkit.Err(err))
			continue
		}

		pks = append(pks, pk)
	}
	return pks, nil
}
