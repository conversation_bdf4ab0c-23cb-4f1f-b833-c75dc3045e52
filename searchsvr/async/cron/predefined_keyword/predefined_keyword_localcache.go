package predefined_keyword

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/foody/service/common/cid"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"
	"strings"
	"sync/atomic"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

var m = &atomic.Value{}

type PredefinedKeywordCache struct {
}

var PredefinedKeywordDict *PredefinedKeywordCache

func NewPredefinedKeywordCache() *PredefinedKeywordCache {
	if PredefinedKeywordDict != nil {
		return PredefinedKeywordDict
	}
	return &PredefinedKeywordCache{}
}

func (p *PredefinedKeywordCache) Load(keywords []*model2.PredefinedKeyword) error {
	newM := map[string]string{}
	for _, v := range keywords {
		for _, uk := range v.UserSearchKeywords {
			if env.GetCID() == cid.VN {
				key := strings.ToLower(uk)
				key = vn_char_mapping_job.CharMappingInstance.ReplaceChar(key)
				val := strings.ToLower(v.GetActualSearchKeyword())
				val = vn_char_mapping_job.CharMappingInstance.ReplaceChar(val)
				newM[key] = val
			} else {
				// 统一用小写
				newM[strings.ToLower(uk)] = strings.ToLower(v.GetActualSearchKeyword())
			}
		}
	}
	m.Store(newM)
	logkit.Debug("predefined keyword map", logkit.Any("pk map", newM))
	return nil
}

func (p *PredefinedKeywordCache) ReplacePredefinedKeyword(ctx context.Context, uk string) string {
	v := m.Load()
	if v == nil {
		return uk
	}
	uk = strings.ToLower(uk)
	cm := v.(map[string]string)
	result := uk
	if _, ok := cm[uk]; ok {
		result = cm[uk]
	}
	return result
}
