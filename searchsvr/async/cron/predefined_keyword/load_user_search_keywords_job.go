package predefined_keyword

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

type LoadUserSearchKeywordJob struct {
	pkDao   *PredefinedKeywordDao
	pkCache *PredefinedKeywordCache
}

func NewLoadUserSearchKeywordJob(
	pkDao *PredefinedKeywordDao,
	pkCache *PredefinedKeywordCache,
) *LoadUserSearchKeywordJob {
	return &LoadUserSearchKeywordJob{
		pkDao:   pkDao,
		pkCache: pkCache,
	}
}

func (c *LoadUserSearchKeywordJob) Run() {
	logkit.Info("LoadUserSearchKeywordJob.Run start")
	ctx := context.Background()
	pks, err := c.pkDao.QueryAll(ctx)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("pkDao.QueryAll error")
		_ = metric_reporter.ReportCronJobError(1, "LoadUserSearchKeywordJob")
		return
	}
	err = c.pkCache.Load(pks)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("pkCache Load error")
		_ = metric_reporter.ReportCronJobError(1, "LoadUserSearchKeywordJob")
		return
	}

	logkit.Info("LoadUserSearchKeywordJob.Run finish")
}
