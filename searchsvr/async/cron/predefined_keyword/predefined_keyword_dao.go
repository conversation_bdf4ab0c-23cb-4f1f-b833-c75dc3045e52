package predefined_keyword

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/mysql/query"
	"github.com/gogo/protobuf/proto"

	model2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

type PredefinedKeywordDao struct {
	db *PredefinedKeywordDB
}

func NewPredefinedKeywordDao(db *PredefinedKeywordDB) *PredefinedKeywordDao {
	return &PredefinedKeywordDao{
		db: db,
	}
}

func (p *PredefinedKeywordDao) QueryAll(ctx context.Context) ([]*model2.PredefinedKeyword, error) {
	param := &query.QueryParam{
		Offset: proto.Uint64(0),
		Limit:  50,
	}
	param.AddCondition("id", query.GT, 0)
	param.AddCondition("deleted", query.EQ, 0)
	param.AddSort("id", query.ASC)

	var res []*model2.PredefinedKeyword
	for {
		pks, err := p.db.Query(ctx, param)
		if err != nil {
			logkit.FromContext(ctx).WithError(err).Error("db.Query parser")
			return nil, err
		}
		if len(pks) == 0 {
			break
		}
		res = append(res, pks...)
		param.Condition[0].Val = pks[len(pks)-1].GetId()
	}
	return res, nil
}
