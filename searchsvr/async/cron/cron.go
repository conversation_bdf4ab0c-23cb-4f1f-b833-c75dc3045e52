package cron

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/load_data_plugin"
	vn_brand_protection_keywords "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_brand_protect"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_store_incubation"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/vn_char_mapping_job"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/user_level"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_ctr_cvr"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_intervention"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_real_name"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/store_top_cron"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/unsettled_store"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/memqueue"
	"github.com/robfig/cron"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/pcfactor"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/predefined_keyword"
)

type Cron struct {
	cron *cron.Cron
	sub  []memqueue.Subscriber
}

func NewCron(
	pkJob *predefined_keyword.LoadUserSearchKeywordJob,
	storeTopJob *store_top_cron.LoadStoreTopJob,
	StoreInterventionJob *store_intervention.StoreInterventionDao,
	buildNotSettledStoreServiceS3Job *unsettled_store.BuildUnsettledStoreServiceS3Job,
	buildStoreCtrCvrjob *store_ctr_cvr.BuildStoreCtrCvrServiceS3Job,
	buildUserLevelJob *user_level.BuildUserLevelJob,
	buildStoreRealNameServiceS3Job *store_real_name.BuildStoreRealNameServiceS3Job,
	buildCharMappingService *vn_char_mapping_job.BuildCharMappingService,
	buildStoreIncubationJob *vn_store_incubation.BuildStoreIncubationJob,
	buildPcFactorJob *pcfactor.BuildPcFactorJob,
	buildStoreRankFactorJob *load_data_plugin.LoadS3FilePlugin,
	buildDefaultStoreRankFactorJob *load_data_plugin.LoadS3FilePlugin,
	buildLoadVnBrandProtectionKeywordJob *vn_brand_protection_keywords.LoadVnBrandProtectionKeywordJob,
) *Cron {
	c := cron.New()
	c.AddJob(fmt.Sprintf("%d %s * * * *", (time.Now().Unix()+3)%60, getEveryFiveMinute()), pkJob)
	if env.GetCID() == cid.VN {
		c.AddJob("0 */30 * * * *", buildCharMappingService)
		c.AddJob("0 * * * * *", buildStoreIncubationJob)
		c.AddJob("0 */5 * * * *", buildLoadVnBrandProtectionKeywordJob)
	} else {
		c.AddJob("0 30 * * * *", storeTopJob)
		c.AddJob("0 30 * * * *", buildNotSettledStoreServiceS3Job)
		c.AddJob("0 30 * * * *", buildUserLevelJob)
		c.AddJob("0 50 * * * *", buildPcFactorJob)
		c.AddJob("0 20 * * * *", buildStoreRankFactorJob)
		c.AddJob("0 40 * * * *", buildDefaultStoreRankFactorJob)
	}
	c.AddJob("0 30 * * * *", StoreInterventionJob)
	c.AddJob("0 30 * * * *", buildStoreCtrCvrjob)
	c.AddJob("0 40 * * * *", buildStoreRealNameServiceS3Job)
	return &Cron{
		cron: c,
		sub:  []memqueue.Subscriber{},
	}
}

func (c *Cron) Start() {
	c.cron.Start()
	for _, each := range c.sub {
		each.Start()
	}
	logkit.Info("cron started")
}

func (c *Cron) Stop() {
	c.cron.Stop()
	for _, each := range c.sub {
		each.Stop()
	}
	logkit.Info("cron stopped")
}

func getEveryFiveMinute() string {
	now := time.Now()
	m := now.Minute() % 5
	s := []string{}
	for ; m < 60; m += 5 {
		s = append(s, strconv.FormatUint(uint64(m), 10))
	}
	return strings.Join(s, ",")
}
