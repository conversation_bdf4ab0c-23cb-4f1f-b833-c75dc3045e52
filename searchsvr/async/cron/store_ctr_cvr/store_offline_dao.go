package store_ctr_cvr

import (
	"bufio"
	"context"
	"errors"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	o2oalgo2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/async/cron/proto/o2oalgo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/integrate/localcache"
	"github.com/coocood/freecache"
	"go.uber.org/zap"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"
)

type StoreOfflineDao struct {
	dict *freecache.Cache
}
type StoreOfflineData struct {
	StoreCtr                   float64
	StoreCvr                   float64
	StorePriceScore1           float64
	StorePriceScore2           float64
	StorePriceScore3           float64
	UE                         float64 //门店经营的平均毛利
	UERatio                    float64 //门店收入/门店成本
	Revenue                    float64
	Commission                 float64
	IsTargetA30                bool    //A30门店：是否是目标A30
	StoreImpCnt180d            int64   //A30门店：180天曝光量
	StoreOrderCnt180d          int64   //A30门店：180天订单量
	StoreCtCvr180d             float64 //A30门店：180天CtCvr
	IsMeetA30QualityConstraint bool    //A30门店：是否满足条件
}

var StoreCtrCvrDaoDict *StoreOfflineDao

var StoreCtrCvrDaoDictOnce sync.Once

func NewStoreCtrCvrDao() *StoreOfflineDao {
	cacheSize := 256
	if apollo.SearchApolloCfg.StoreOfflineCtCvrCacheSize != 0 {
		cacheSize = apollo.SearchApolloCfg.StoreOfflineCtCvrCacheSize
	}
	cacheSize = cacheSize * 1024 * 1024 // Convert MB to bytes
	StoreCtrCvrDaoDictOnce.Do(func() {
		StoreCtrCvrDaoDict = &StoreOfflineDao{
			dict: localcache.InitFreeCache("StoreCtrCvrDaoDict", cacheSize),
		}
	})
	return StoreCtrCvrDaoDict
}

func (d *StoreOfflineDao) GetStoreOfflineData(storeId uint64) *o2oalgo2.StoreOfflineData {
	pb := new(o2oalgo2.StoreOfflineData)
	err := d.dict.GetFn(util.IntToBytes(int64(storeId)), func(bVal []byte) error {
		// 如果key没有找到，会直接报错，不会往下走
		if len(bVal) > 0 {
			// key找到，且有值
			return pb.XXX_Unmarshal(bVal)
		}

		return errors.New("entry is expire")
	})

	if err == nil {
		return pb
	}
	return nil
}

func (d *StoreOfflineDao) BuildStoreCtrCvrDict(ctx context.Context, filePwd string) error {
	//准备读取文件
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildStoreCtrCvrDict Failed to open file",
			logkit.String("file path", filePwd))
		return err
	}
	defer fs.Close()
	buffer := bufio.NewReader(fs)
	buffer = bufio.NewReaderSize(buffer, 65535)
	count := 0
	for {
		line, _, end := buffer.ReadLine()
		if end == io.EOF {
			break
		}
		row := strings.Split(strings.Trim(string(line), "  "), "\t")
		if len(row) < 9 {
			break
		}
		storeId, err1 := strconv.ParseInt(row[0], 10, 64)
		ctr, err2 := strconv.ParseFloat(row[1], 64)
		cvr, err3 := strconv.ParseFloat(row[2], 64)
		price1, err4 := strconv.ParseFloat(row[3], 64)
		price2, err5 := strconv.ParseFloat(row[4], 64)
		price3, err6 := strconv.ParseFloat(row[5], 64)
		if err1 != nil || err2 != nil || err3 != nil || err4 != nil || err5 != nil || err6 != nil {
			logkit.Error("parse string to int or float fail", zap.String("value", strings.Join(row, ",")))
			continue
		}
		// 加入ue因子
		ue, err7 := strconv.ParseFloat(row[6], 64)
		ueRadio, err8 := strconv.ParseFloat(row[7], 64)
		revenue, err9 := strconv.ParseFloat(row[8], 64)
		commission, err10 := strconv.ParseFloat(row[9], 64)
		if err7 != nil || err8 != nil || err9 != nil || err10 != nil {
			logkit.Error("parse string to int or float fail", zap.String("value", strings.Join(row, ",")))
			continue
		}

		// 加入A30门店特征
		var isTargetA30 int64
		var storeImpCnt180d int64
		var storeOrderCnt180d int64
		var storeCtCvr180d float64
		var isMeetA30QualityConstraint int64
		var err11, err12, err13, err14, err15 error
		if len(row) >= 15 {
			isTargetA30, err11 = strconv.ParseInt(row[10], 10, 64)
			storeImpCnt180d, err12 = strconv.ParseInt(row[11], 10, 64)
			storeOrderCnt180d, err13 = strconv.ParseInt(row[12], 10, 64)
			storeCtCvr180d, err14 = strconv.ParseFloat(row[13], 64)
			isMeetA30QualityConstraint, err15 = strconv.ParseInt(row[14], 10, 64)
			if err11 != nil || err12 != nil || err13 != nil || err14 != nil || err15 != nil {
				logkit.Error("parse string to int or float fail, A30 factor ", zap.String("value", strings.Join(row, ",")))
				continue
			}
		}

		offlineData := &o2oalgo2.StoreOfflineData{
			StoreCtr:                   ctr,
			StoreCvr:                   cvr,
			StorePriceScore1:           price1,
			StorePriceScore2:           price2,
			StorePriceScore3:           price3,
			Ue:                         ue,
			UeRatio:                    ueRadio,
			Revenue:                    revenue,
			Commission:                 commission,
			IsTargetA30:                isTargetA30 == 1,
			StoreImpCnt_180D:           storeImpCnt180d,
			StoreOrderCnt_180D:         storeOrderCnt180d,
			StoreCtCvr_180D:            storeCtCvr180d,
			IsMeetA30QualityConstraint: isMeetA30QualityConstraint == 1,
		}
		keyBytes := util.IntToBytes(storeId)
		valBytes, _ := offlineData.Marshal()
		setErr := d.dict.Set(keyBytes, valBytes, 0)
		if setErr != nil {
			_ = metric_reporter.ReportCronJobError(1, filePwd+"_set_cache_error")
			logkit.Error("BuildStoreCtrCvrDict set cache failed", zap.String("storeId", strconv.FormatInt(storeId, 10)), zap.Error(setErr))
			continue
		}
		count++
	}

	logkit.Info("BuildStoreCtrCvrDict finished", zap.Int("size", count))
	return nil
}
