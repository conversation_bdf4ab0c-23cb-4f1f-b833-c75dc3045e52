package store_ctr_cvr

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"go.uber.org/zap"

	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

type BuildStoreCtrCvrService struct {
	s3Client       s3.S3Service
	StoreCtrCvrDao *StoreOfflineDao
	s3FileName     *config2.S3FileName
}

var currentTimestamp int64
var isFirstInit = true

func NewBuildStoreCtrCvrService(s3Client s3.S3Service, storeDao *StoreOfflineDao, s3FileName *config2.S3FileName) *BuildStoreCtrCvrService {
	return &BuildStoreCtrCvrService{
		s3Client:       s3Client,
		StoreCtrCvrDao: storeDao,
		s3FileName:     s3FileName,
	}
}

func (b *BuildStoreCtrCvrService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(b.s3FileName.GetStoreCtrCvr())
	defer func() {
		if err != nil {
			_ = metric_reporter.ReportCronJobError(1, "StoreCtrCvr")
		}
	}()
	if err != nil {
		return
	}

	err = b.StoreCtrCvrDao.BuildStoreCtrCvrDict(ctx, filePwd)
	if err != nil {
		logkit.Error("BuildStoreCtrCvrDict error", zap.String("err", err.Error()))
	}
}

func (b *BuildStoreCtrCvrService) Run() {
	fileName := b.s3FileName.GetStoreCtrCvr()
	logkit.Info("BuildStoreCtrCvrDict start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildStoreCtrCvrDict Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildStoreCtrCvrDict Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildStoreCtrCvrDict fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildStoreCtrCvrDict update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildStoreCtrCvrDict done", zap.String("filename", fileName))
}

func (b *BuildStoreCtrCvrService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildStoreCtrCvrService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}
