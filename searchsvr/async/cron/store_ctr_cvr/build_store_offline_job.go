package store_ctr_cvr

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
)

type BuildStoreCtrCvrServiceS3Job struct {
	buildStoreCtrCvrService *BuildStoreCtrCvrService
}

func NewBuildStoreCtrCvrServiceS3Job(buildStoreCtrCvrService *BuildStoreCtrCvrService) *BuildStoreCtrCvrServiceS3Job {
	return &BuildStoreCtrCvrServiceS3Job{
		buildStoreCtrCvrService: buildStoreCtrCvrService,
	}
}

func (p *BuildStoreCtrCvrServiceS3Job) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("BuildStoreCtrCvrServiceS3Job start")
	p.buildStoreCtrCvrService.Run()
	logkit.FromContext(ctx).Info("BuildStoreCtrCvrServiceS3Job done")
}
