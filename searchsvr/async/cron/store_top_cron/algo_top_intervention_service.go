package store_top_cron

import (
	"context"
	"fmt"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/apollo"
	config2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/config"
)

type AlgoTopInterventionService struct {
	s3Client               s3.S3Service
	s3FileNameConfig       *config2.S3FileName
	algoTopInterventionDao *AlgoTopInterventionDao
}

var algoTopInterventionService *AlgoTopInterventionService
var algoTopInterventionOnce sync.Once

func NewAlgoTopInterventionService(s3Client s3.S3Service, s3FileNameConfig *config2.S3FileName,
	algoTopInterventionDao *AlgoTopInterventionDao) *AlgoTopInterventionService {
	algoTopInterventionOnce.Do(func() {
		algoTopInterventionService = &AlgoTopInterventionService{
			s3Client:               s3Client,
			s3FileNameConfig:       s3FileNameConfig,
			algoTopInterventionDao: algoTopInterventionDao,
		}
	})
	return algoTopInterventionService
}

func (s *AlgoTopInterventionService) BuildAlgoTopInterventionDict(ctx context.Context) (int, error) {
	topInterventionControlMap := apollo.AlgoApolloCfg.TopInterventionControlMap
	if topInterventionControlMap == nil {
		return 0, fmt.Errorf("topInterventionControlMap is nil, BuildAlgoTopInterventionDict failed")
	}
	topInterventionControlMap.Lock()
	defer topInterventionControlMap.Unlock()

	// 遍历每个版本，加载各版本对应的词表
	versionNeedLoad := make(map[string]bool)
	versionSuccLoaded := make(map[string]bool)
	for _, value := range topInterventionControlMap.TopInterventionControl {
		version := value.DictVersion
		versionNeedLoad[version] = true
		if _, hasLoaded := versionSuccLoaded[version]; !hasLoaded {
			err := s.buildVersionDict(version)
			if err != nil {
				logkit.FromContext(ctx).WithError(err).Error("buildVersionDict failed",
					zap.String("version", version))
				continue
			}
			versionSuccLoaded[version] = true
		}
	}
	s.algoTopInterventionDao.ClearNotNeededVersionDicts(versionNeedLoad)

	if len(versionNeedLoad) != len(versionSuccLoaded) {
		return len(versionSuccLoaded), fmt.Errorf("BuildAlgoTopInterventionDict not all success, "+
			"versionNeedLoad=%d, versionSuccLoaded=%d", len(versionNeedLoad), len(versionSuccLoaded))
	}
	return len(versionSuccLoaded), nil
}

func (s *AlgoTopInterventionService) buildVersionDict(version string) error {
	s3FileName, err := s.s3FileNameConfig.GetAlgoTopInterventionTextFileName(version)
	if err != nil || s3FileName == "" {
		return fmt.Errorf("[buildVersionDict] get s3 file name failed, err=%v", err)
	}

	modifyTime, err := s.s3Client.GetLastModified(s3FileName)
	if err != nil || modifyTime <= 0 {
		return fmt.Errorf("[buildVersionDict] get s3 modify time failed, err=%v, s3FileName=%v", err, s3FileName)
	}

	currentTimestamp := s.algoTopInterventionDao.GetVersionModifyTime(version)
	if currentTimestamp != modifyTime {
		filePwd := "./" + s3FileName
		err = s.s3Client.Download(context.Background(), s3FileName, filePwd)
		if err != nil {
			return fmt.Errorf("[buildVersionDict] download s3 file failed, err=%v, s3FileName=%v", err, s3FileName)
		}

		dictLen, err := s.algoTopInterventionDao.LoadVersionDict(filePwd, version, modifyTime)
		if err != nil {
			return fmt.Errorf("[buildVersionDict] LoadVersionDict failed, err=%v", err)
		}

		logkit.Info("LoadVersionDict success", zap.String("version", version),
			zap.String("s3FileName", s3FileName), zap.Int64("modifyTime", modifyTime),
			zap.Int("dictLen", dictLen))
	} else {
		logkit.Debug("buildVersionDict: s3 file not updated, no need to load again",
			zap.String("version", version), zap.String("s3FileName", s3FileName),
			zap.Int64("modifyTime", modifyTime))
	}
	return nil
}
