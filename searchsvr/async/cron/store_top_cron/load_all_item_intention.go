package store_top_cron

import (
	"context"

	"go.uber.org/zap"

	"git.garena.com/shopee/feed/comm_lib/logkit"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
)

type LoadStoreTopJob struct {
	algoTopInterventionDAO *AlgoTopInterventionService
}

func NewReloadStoreTopJob(algoTopInterventionDAO *AlgoTopInterventionService) *LoadStoreTopJob {
	return &LoadStoreTopJob{
		algoTopInterventionDAO: algoTopInterventionDAO,
	}
}

func (l *LoadStoreTopJob) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("LoadStoreTopJob.Run start")

	// 仅加载算法导入的S3置顶词表，MySQL置顶词表已迁移至干预表
	algoVersionCnt, err := l.algoTopInterventionDAO.BuildAlgoTopInterventionDict(ctx)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildAlgoTopInterventionDict has error")
		_ = metric_reporter.ReportCronJobError(1, "LoadAllItemIntentionJob")
	}

	logkit.FromContext(ctx).Info("LoadStoreTopJob.Run finish",
		zap.Int("algoVersionCnt", algoVersionCnt))
}
