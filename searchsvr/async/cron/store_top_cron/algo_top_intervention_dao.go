package store_top_cron

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
	"go.uber.org/zap"

	util2 "git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
)

type AlgoTopInterventionDao struct {
	interventionDict   map[string]map[string][]string
	versionModifyTimes map[string]int64
	lock               sync.RWMutex
}

const (
	maxKeywordCount     = 10
	querySep            = "###"
	interventionTextSep = ";"
)

var AlgoTopIntervention *AlgoTopInterventionDao
var algoTopInterventionDaoOnce sync.Once

func NewAlgoTopInterventionDao() *AlgoTopInterventionDao {
	algoTopInterventionDaoOnce.Do(func() {
		AlgoTopIntervention = &AlgoTopInterventionDao{
			interventionDict:   make(map[string]map[string][]string),
			versionModifyTimes: make(map[string]int64),
			lock:               sync.RWMutex{},
		}
	})
	return AlgoTopIntervention
}

func (s *AlgoTopInterventionDao) LoadVersionDict(filePwd, version string, modifyTime int64) (int, error) {
	fs, err := os.Open(filePwd)
	if err != nil {
		return 0, fmt.Errorf("open local file failed: err=%v", err)
	}
	defer fs.Close()

	singleVersionDict := make(map[string][]string)
	br := bufio.NewReader(fs)
	lineNum := 0
	for {
		line, _, err := br.ReadLine()
		if err == io.EOF {
			break
		} else if err != nil {
			return 0, fmt.Errorf("scan local file failed: err=%v", err)
		}
		lineNum++

		lineStr := string(line)
		querySepIdx := strings.Index(lineStr, querySep)
		if querySepIdx < 0 {
			logkit.Error("[LoadVersionDict] line format invalid",
				zap.Int("lineNum", lineNum), zap.String("filePwd", filePwd))
			continue
		}
		query := strings.TrimSpace(lineStr[:querySepIdx])
		interventionTexts := strings.TrimSpace(lineStr[querySepIdx+1:])

		dictKey := transToDictKey(query)
		if _, alreadyExisted := singleVersionDict[dictKey]; alreadyExisted {
			continue
		}

		// interventionTexts 只取前 maxKeywordCount 个
		keywordList := strings.SplitN(interventionTexts, interventionTextSep, maxKeywordCount+1)
		keywordCount := len(keywordList)
		if keywordCount > maxKeywordCount {
			logkit.Error("[LoadVersionDict] keyword count out of range",
				zap.Int("lineNum", lineNum), zap.String("filePwd", filePwd))
			keywordCount = maxKeywordCount
		}
		for i := 0; i < keywordCount; i++ {
			keywordList[i] = strings.TrimSpace(keywordList[i])
		}
		singleVersionDict[dictKey] = keywordList[:keywordCount]
	}

	s.lock.Lock()
	defer s.lock.Unlock()
	s.interventionDict[version] = singleVersionDict
	s.versionModifyTimes[version] = modifyTime
	return len(singleVersionDict), nil
}

func (s *AlgoTopInterventionDao) ClearNotNeededVersionDicts(versionNeedLoad map[string]bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	var versionDel []string
	for version, _ := range s.interventionDict {
		if !versionNeedLoad[version] {
			versionDel = append(versionDel, version)
		}
	}
	for _, version := range versionDel {
		logkit.Info("clear not needed AlgoTopInterventionVersion", zap.String("version", version))
		delete(s.interventionDict, version)
		delete(s.versionModifyTimes, version)
	}
}

func (s *AlgoTopInterventionDao) GetVersionModifyTime(version string) int64 {
	s.lock.RLock()
	defer s.lock.RUnlock()
	return s.versionModifyTimes[version]
}

func (s *AlgoTopInterventionDao) GetAlgoTopIntervention(version, query string) ([]string, bool) {
	if query == "" || version == "" {
		return nil, false
	}
	s.lock.RLock()
	defer s.lock.RUnlock()
	if len(s.interventionDict) == 0 || len(s.interventionDict[version]) == 0 {
		return nil, false
	}
	dictKey := transToDictKey(query)
	interventions, ok := s.interventionDict[version][dictKey]
	return interventions, ok
}

func transToDictKey(query string) string {
	// MY/TH 站点需去标点
	lowerKey := ""
	if env.GetCID() == cid.MY || env.GetCID() == cid.TH {
		lowerKey = strings.ToLower(util2.RemoveAllPunctAndTrim(query))
	} else {
		lowerKey = strings.ToLower(query)
	}
	return lowerKey
}
