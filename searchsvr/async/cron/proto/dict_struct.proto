syntax = "proto3";
package o2oalgo;

option go_package = "/o2oalgo";

message StoreRankFactor {
    double c_sales_score_cate1 = 1;
    double c_acc_sales_score_cate1 = 2;
    double c_gmv_score_cate1 = 3;
    double c_acc_gmv_score_cate1 = 4;
    double c_sales_score_cate2 = 5;
    double c_acc_sales_score_cate2 = 6;
    double c_gmv_score_cate2 = 7;
    double c_acc_gmv_score_cate2 = 8;
    double c_commission_rate = 9;
    double c_service_fee = 10;
    double c_tax_rate = 11;
    double c_ctr = 12;
    double c_cvr = 13;
    double c_price_index = 14;
    double c_ue_factor = 15;
    double c_ue_ratio_factor = 16;
    double c_ue_factor_ori = 17;
    double c_ue_ratio_factor_ori = 18;
}

message StoreOfflineData {
    double store_ctr = 1;
    double store_cvr = 2;
    double store_price_score1 = 3;
    double store_price_score2 = 4;
    double store_price_score3 = 5;
    double ue = 6;
    double ue_ratio = 7;
    double revenue = 8;
    double commission = 9;
    bool is_target_a30 = 10;
    int64 store_imp_cnt_180d = 11;
    int64 store_order_cnt_180d = 12;
    double store_ct_cvr_180d = 13;
    bool is_meet_a30_quality_constraint = 14;
}

message StoreIntervention {
    uint64 id = 1;
    string search_keywords = 2;
    int32 intervention_type = 3;
    string intervention_texts = 4;
    uint32 is_new_merchants_only = 5;
    uint32 is_rotate_merchants = 6;
    repeated uint64 intervention_merchant_id_list = 7;
    repeated uint64 intervention_brand_id_list = 8;
    repeated uint64 intervention_store_id_list = 9;
    repeated uint64 intervention_store_tag_id_list = 10;
    repeated uint64 slots_position_list = 11;
}