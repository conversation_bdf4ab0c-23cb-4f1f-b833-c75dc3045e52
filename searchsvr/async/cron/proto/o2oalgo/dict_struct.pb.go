// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: dict_struct.proto

package o2oalgo

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type StoreRankFactor struct {
	CSalesScoreCate1     float64  `protobuf:"fixed64,1,opt,name=c_sales_score_cate1,json=cSalesScoreCate1,proto3" json:"c_sales_score_cate1,omitempty"`
	CAccSalesScoreCate1  float64  `protobuf:"fixed64,2,opt,name=c_acc_sales_score_cate1,json=cAccSalesScoreCate1,proto3" json:"c_acc_sales_score_cate1,omitempty"`
	CGmvScoreCate1       float64  `protobuf:"fixed64,3,opt,name=c_gmv_score_cate1,json=cGmvScoreCate1,proto3" json:"c_gmv_score_cate1,omitempty"`
	CAccGmvScoreCate1    float64  `protobuf:"fixed64,4,opt,name=c_acc_gmv_score_cate1,json=cAccGmvScoreCate1,proto3" json:"c_acc_gmv_score_cate1,omitempty"`
	CSalesScoreCate2     float64  `protobuf:"fixed64,5,opt,name=c_sales_score_cate2,json=cSalesScoreCate2,proto3" json:"c_sales_score_cate2,omitempty"`
	CAccSalesScoreCate2  float64  `protobuf:"fixed64,6,opt,name=c_acc_sales_score_cate2,json=cAccSalesScoreCate2,proto3" json:"c_acc_sales_score_cate2,omitempty"`
	CGmvScoreCate2       float64  `protobuf:"fixed64,7,opt,name=c_gmv_score_cate2,json=cGmvScoreCate2,proto3" json:"c_gmv_score_cate2,omitempty"`
	CAccGmvScoreCate2    float64  `protobuf:"fixed64,8,opt,name=c_acc_gmv_score_cate2,json=cAccGmvScoreCate2,proto3" json:"c_acc_gmv_score_cate2,omitempty"`
	CCommissionRate      float64  `protobuf:"fixed64,9,opt,name=c_commission_rate,json=cCommissionRate,proto3" json:"c_commission_rate,omitempty"`
	CServiceFee          float64  `protobuf:"fixed64,10,opt,name=c_service_fee,json=cServiceFee,proto3" json:"c_service_fee,omitempty"`
	CTaxRate             float64  `protobuf:"fixed64,11,opt,name=c_tax_rate,json=cTaxRate,proto3" json:"c_tax_rate,omitempty"`
	CCtr                 float64  `protobuf:"fixed64,12,opt,name=c_ctr,json=cCtr,proto3" json:"c_ctr,omitempty"`
	CCvr                 float64  `protobuf:"fixed64,13,opt,name=c_cvr,json=cCvr,proto3" json:"c_cvr,omitempty"`
	CPriceIndex          float64  `protobuf:"fixed64,14,opt,name=c_price_index,json=cPriceIndex,proto3" json:"c_price_index,omitempty"`
	CUeFactor            float64  `protobuf:"fixed64,15,opt,name=c_ue_factor,json=cUeFactor,proto3" json:"c_ue_factor,omitempty"`
	CUeRatioFactor       float64  `protobuf:"fixed64,16,opt,name=c_ue_ratio_factor,json=cUeRatioFactor,proto3" json:"c_ue_ratio_factor,omitempty"`
	CUeFactorOri         float64  `protobuf:"fixed64,17,opt,name=c_ue_factor_ori,json=cUeFactorOri,proto3" json:"c_ue_factor_ori,omitempty"`
	CUeRatioFactorOri    float64  `protobuf:"fixed64,18,opt,name=c_ue_ratio_factor_ori,json=cUeRatioFactorOri,proto3" json:"c_ue_ratio_factor_ori,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreRankFactor) Reset()         { *m = StoreRankFactor{} }
func (m *StoreRankFactor) String() string { return proto.CompactTextString(m) }
func (*StoreRankFactor) ProtoMessage()    {}
func (*StoreRankFactor) Descriptor() ([]byte, []int) {
	return fileDescriptor_a795647a6abd8555, []int{0}
}
func (m *StoreRankFactor) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreRankFactor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreRankFactor.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreRankFactor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreRankFactor.Merge(m, src)
}
func (m *StoreRankFactor) XXX_Size() int {
	return m.Size()
}
func (m *StoreRankFactor) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreRankFactor.DiscardUnknown(m)
}

var xxx_messageInfo_StoreRankFactor proto.InternalMessageInfo

func (m *StoreRankFactor) GetCSalesScoreCate1() float64 {
	if m != nil {
		return m.CSalesScoreCate1
	}
	return 0
}

func (m *StoreRankFactor) GetCAccSalesScoreCate1() float64 {
	if m != nil {
		return m.CAccSalesScoreCate1
	}
	return 0
}

func (m *StoreRankFactor) GetCGmvScoreCate1() float64 {
	if m != nil {
		return m.CGmvScoreCate1
	}
	return 0
}

func (m *StoreRankFactor) GetCAccGmvScoreCate1() float64 {
	if m != nil {
		return m.CAccGmvScoreCate1
	}
	return 0
}

func (m *StoreRankFactor) GetCSalesScoreCate2() float64 {
	if m != nil {
		return m.CSalesScoreCate2
	}
	return 0
}

func (m *StoreRankFactor) GetCAccSalesScoreCate2() float64 {
	if m != nil {
		return m.CAccSalesScoreCate2
	}
	return 0
}

func (m *StoreRankFactor) GetCGmvScoreCate2() float64 {
	if m != nil {
		return m.CGmvScoreCate2
	}
	return 0
}

func (m *StoreRankFactor) GetCAccGmvScoreCate2() float64 {
	if m != nil {
		return m.CAccGmvScoreCate2
	}
	return 0
}

func (m *StoreRankFactor) GetCCommissionRate() float64 {
	if m != nil {
		return m.CCommissionRate
	}
	return 0
}

func (m *StoreRankFactor) GetCServiceFee() float64 {
	if m != nil {
		return m.CServiceFee
	}
	return 0
}

func (m *StoreRankFactor) GetCTaxRate() float64 {
	if m != nil {
		return m.CTaxRate
	}
	return 0
}

func (m *StoreRankFactor) GetCCtr() float64 {
	if m != nil {
		return m.CCtr
	}
	return 0
}

func (m *StoreRankFactor) GetCCvr() float64 {
	if m != nil {
		return m.CCvr
	}
	return 0
}

func (m *StoreRankFactor) GetCPriceIndex() float64 {
	if m != nil {
		return m.CPriceIndex
	}
	return 0
}

func (m *StoreRankFactor) GetCUeFactor() float64 {
	if m != nil {
		return m.CUeFactor
	}
	return 0
}

func (m *StoreRankFactor) GetCUeRatioFactor() float64 {
	if m != nil {
		return m.CUeRatioFactor
	}
	return 0
}

func (m *StoreRankFactor) GetCUeFactorOri() float64 {
	if m != nil {
		return m.CUeFactorOri
	}
	return 0
}

func (m *StoreRankFactor) GetCUeRatioFactorOri() float64 {
	if m != nil {
		return m.CUeRatioFactorOri
	}
	return 0
}

type StoreOfflineData struct {
	StoreCtr                   float64  `protobuf:"fixed64,1,opt,name=store_ctr,json=storeCtr,proto3" json:"store_ctr,omitempty"`
	StoreCvr                   float64  `protobuf:"fixed64,2,opt,name=store_cvr,json=storeCvr,proto3" json:"store_cvr,omitempty"`
	StorePriceScore1           float64  `protobuf:"fixed64,3,opt,name=store_price_score1,json=storePriceScore1,proto3" json:"store_price_score1,omitempty"`
	StorePriceScore2           float64  `protobuf:"fixed64,4,opt,name=store_price_score2,json=storePriceScore2,proto3" json:"store_price_score2,omitempty"`
	StorePriceScore3           float64  `protobuf:"fixed64,5,opt,name=store_price_score3,json=storePriceScore3,proto3" json:"store_price_score3,omitempty"`
	Ue                         float64  `protobuf:"fixed64,6,opt,name=ue,proto3" json:"ue,omitempty"`
	UeRatio                    float64  `protobuf:"fixed64,7,opt,name=ue_ratio,json=ueRatio,proto3" json:"ue_ratio,omitempty"`
	Revenue                    float64  `protobuf:"fixed64,8,opt,name=revenue,proto3" json:"revenue,omitempty"`
	Commission                 float64  `protobuf:"fixed64,9,opt,name=commission,proto3" json:"commission,omitempty"`
	IsTargetA30                bool     `protobuf:"varint,10,opt,name=is_target_a30,json=isTargetA30,proto3" json:"is_target_a30,omitempty"`
	StoreImpCnt_180D           int64    `protobuf:"varint,11,opt,name=store_imp_cnt_180d,json=storeImpCnt180d,proto3" json:"store_imp_cnt_180d,omitempty"`
	StoreOrderCnt_180D         int64    `protobuf:"varint,12,opt,name=store_order_cnt_180d,json=storeOrderCnt180d,proto3" json:"store_order_cnt_180d,omitempty"`
	StoreCtCvr_180D            float64  `protobuf:"fixed64,13,opt,name=store_ct_cvr_180d,json=storeCtCvr180d,proto3" json:"store_ct_cvr_180d,omitempty"`
	IsMeetA30QualityConstraint bool     `protobuf:"varint,14,opt,name=is_meet_a30_quality_constraint,json=isMeetA30QualityConstraint,proto3" json:"is_meet_a30_quality_constraint,omitempty"`
	XXX_NoUnkeyedLiteral       struct{} `json:"-"`
	XXX_unrecognized           []byte   `json:"-"`
	XXX_sizecache              int32    `json:"-"`
}

func (m *StoreOfflineData) Reset()         { *m = StoreOfflineData{} }
func (m *StoreOfflineData) String() string { return proto.CompactTextString(m) }
func (*StoreOfflineData) ProtoMessage()    {}
func (*StoreOfflineData) Descriptor() ([]byte, []int) {
	return fileDescriptor_a795647a6abd8555, []int{1}
}
func (m *StoreOfflineData) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StoreOfflineData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StoreOfflineData.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StoreOfflineData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreOfflineData.Merge(m, src)
}
func (m *StoreOfflineData) XXX_Size() int {
	return m.Size()
}
func (m *StoreOfflineData) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreOfflineData.DiscardUnknown(m)
}

var xxx_messageInfo_StoreOfflineData proto.InternalMessageInfo

func (m *StoreOfflineData) GetStoreCtr() float64 {
	if m != nil {
		return m.StoreCtr
	}
	return 0
}

func (m *StoreOfflineData) GetStoreCvr() float64 {
	if m != nil {
		return m.StoreCvr
	}
	return 0
}

func (m *StoreOfflineData) GetStorePriceScore1() float64 {
	if m != nil {
		return m.StorePriceScore1
	}
	return 0
}

func (m *StoreOfflineData) GetStorePriceScore2() float64 {
	if m != nil {
		return m.StorePriceScore2
	}
	return 0
}

func (m *StoreOfflineData) GetStorePriceScore3() float64 {
	if m != nil {
		return m.StorePriceScore3
	}
	return 0
}

func (m *StoreOfflineData) GetUe() float64 {
	if m != nil {
		return m.Ue
	}
	return 0
}

func (m *StoreOfflineData) GetUeRatio() float64 {
	if m != nil {
		return m.UeRatio
	}
	return 0
}

func (m *StoreOfflineData) GetRevenue() float64 {
	if m != nil {
		return m.Revenue
	}
	return 0
}

func (m *StoreOfflineData) GetCommission() float64 {
	if m != nil {
		return m.Commission
	}
	return 0
}

func (m *StoreOfflineData) GetIsTargetA30() bool {
	if m != nil {
		return m.IsTargetA30
	}
	return false
}

func (m *StoreOfflineData) GetStoreImpCnt_180D() int64 {
	if m != nil {
		return m.StoreImpCnt_180D
	}
	return 0
}

func (m *StoreOfflineData) GetStoreOrderCnt_180D() int64 {
	if m != nil {
		return m.StoreOrderCnt_180D
	}
	return 0
}

func (m *StoreOfflineData) GetStoreCtCvr_180D() float64 {
	if m != nil {
		return m.StoreCtCvr_180D
	}
	return 0
}

func (m *StoreOfflineData) GetIsMeetA30QualityConstraint() bool {
	if m != nil {
		return m.IsMeetA30QualityConstraint
	}
	return false
}

func init() {
	proto.RegisterType((*StoreRankFactor)(nil), "o2oalgo.StoreRankFactor")
	proto.RegisterType((*StoreOfflineData)(nil), "o2oalgo.StoreOfflineData")
}

func init() { proto.RegisterFile("dict_struct.proto", fileDescriptor_a795647a6abd8555) }

var fileDescriptor_a795647a6abd8555 = []byte{
	// 645 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x94, 0xcb, 0x6e, 0xd3, 0x4e,
	0x14, 0xc6, 0x95, 0xde, 0xe2, 0x4e, 0xda, 0x5c, 0xa6, 0xff, 0x3f, 0x98, 0x8b, 0x22, 0x54, 0x09,
	0x89, 0x72, 0x6b, 0xea, 0xb0, 0x60, 0xdb, 0x06, 0x15, 0x75, 0x81, 0x02, 0x4e, 0xbb, 0x61, 0x33,
	0x1a, 0x4e, 0x4e, 0xaa, 0x11, 0xb1, 0x27, 0x8c, 0xc7, 0x56, 0x79, 0x27, 0x1e, 0x84, 0x25, 0x8f,
	0x80, 0xfa, 0x0e, 0xec, 0xd1, 0x1c, 0x8f, 0x7b, 0x09, 0xee, 0xd2, 0xdf, 0xf7, 0x9b, 0x33, 0x73,
	0xce, 0xf9, 0x64, 0xd6, 0x9b, 0x2a, 0xb0, 0x22, 0xb3, 0x26, 0x07, 0xfb, 0x7a, 0x61, 0xb4, 0xd5,
	0xbc, 0xa9, 0x23, 0x2d, 0xe7, 0xe7, 0x7a, 0xf7, 0xcf, 0x3a, 0xeb, 0x4c, 0xac, 0x36, 0x18, 0xcb,
	0xf4, 0xeb, 0xb1, 0x04, 0xab, 0x0d, 0x7f, 0xc5, 0x76, 0x40, 0x64, 0x72, 0x8e, 0x99, 0xc8, 0x40,
	0x1b, 0x14, 0x20, 0x2d, 0x1e, 0x84, 0x8d, 0x27, 0x8d, 0x67, 0x8d, 0xb8, 0x0b, 0x13, 0xe7, 0x4c,
	0x9c, 0x31, 0x72, 0x3a, 0x7f, 0xc3, 0xee, 0x83, 0x90, 0x50, 0x77, 0x64, 0x85, 0x8e, 0xec, 0xc0,
	0x21, 0xfc, 0x73, 0x6a, 0x8f, 0xf5, 0x40, 0x9c, 0x27, 0xc5, 0x2d, 0x7e, 0x95, 0xf8, 0x36, 0xbc,
	0x4f, 0x8a, 0x1b, 0xe8, 0x80, 0xfd, 0x5f, 0x5e, 0xb0, 0x8c, 0xaf, 0x11, 0xde, 0x73, 0xe5, 0x6f,
	0x9f, 0xa8, 0xed, 0x20, 0x0a, 0xd7, 0x6b, 0x3b, 0x88, 0xee, 0xee, 0x20, 0x0a, 0x37, 0xee, 0xea,
	0x20, 0xaa, 0xeb, 0x20, 0x0a, 0x9b, 0x35, 0x1d, 0x44, 0x77, 0x75, 0x10, 0x85, 0x41, 0x7d, 0x07,
	0x11, 0x7f, 0xee, 0x8a, 0x83, 0x4e, 0x12, 0x95, 0x65, 0x4a, 0xa7, 0xc2, 0x48, 0x8b, 0xe1, 0x26,
	0xd1, 0x1d, 0x18, 0x5d, 0xe9, 0xb1, 0xb4, 0xc8, 0x77, 0xd9, 0x36, 0x88, 0x0c, 0x4d, 0xa1, 0x00,
	0xc5, 0x0c, 0x31, 0x64, 0xc4, 0xb5, 0x60, 0x52, 0x6a, 0xc7, 0x88, 0xfc, 0x31, 0x63, 0x20, 0xac,
	0xbc, 0x28, 0x0b, 0xb5, 0x08, 0x08, 0xe0, 0x54, 0x5e, 0x50, 0x85, 0x1d, 0xb6, 0x0e, 0x02, 0xac,
	0x09, 0xb7, 0xc8, 0x58, 0x83, 0x91, 0x35, 0x5e, 0x2c, 0x4c, 0xb8, 0x5d, 0x89, 0x85, 0x29, 0xef,
	0x5a, 0x18, 0x77, 0x93, 0x4a, 0xa7, 0x78, 0x11, 0xb6, 0xfd, 0x5d, 0x1f, 0x9d, 0x76, 0xe2, 0x24,
	0xde, 0x67, 0x2d, 0x10, 0x39, 0x8a, 0x19, 0xc5, 0x29, 0xec, 0x10, 0xb1, 0x09, 0x67, 0xe8, 0xf3,
	0x45, 0x83, 0xcb, 0xd1, 0x3d, 0x45, 0xe9, 0x8a, 0xea, 0xfa, 0xc1, 0x9d, 0x61, 0xec, 0x64, 0x8f,
	0x3e, 0x65, 0x9d, 0x1b, 0xa5, 0x84, 0x36, 0x2a, 0xec, 0x11, 0xb8, 0x75, 0x55, 0x6e, 0x6c, 0x54,
	0x39, 0xdf, 0xa5, 0x8a, 0x04, 0x73, 0x3f, 0xdf, 0x5b, 0x55, 0xc7, 0x46, 0xed, 0xfe, 0x58, 0x63,
	0x5d, 0xca, 0xfd, 0x78, 0x36, 0x9b, 0xab, 0x14, 0xdf, 0x49, 0x2b, 0xf9, 0x23, 0xb6, 0x99, 0x59,
	0x5a, 0x8e, 0x35, 0x3e, 0xee, 0x01, 0x09, 0x6e, 0x1c, 0xd7, 0x66, 0x61, 0x7c, 0xb0, 0xbd, 0x59,
	0x18, 0xfe, 0x92, 0xf1, 0xd2, 0x2c, 0x47, 0x43, 0x2b, 0xae, 0xe2, 0xdc, 0x25, 0x87, 0xe6, 0x43,
	0x0b, 0x3e, 0xa8, 0xa5, 0x23, 0x9f, 0xe6, 0x65, 0x3a, 0xaa, 0xa5, 0x87, 0x55, 0x96, 0x97, 0xe8,
	0x21, 0x6f, 0xb3, 0x95, 0x1c, 0x7d, 0x6c, 0x57, 0x72, 0xe4, 0x0f, 0x58, 0x50, 0x0d, 0xc6, 0x87,
	0xb3, 0x99, 0x97, 0xc3, 0xe0, 0x21, 0x6b, 0x1a, 0x2c, 0x30, 0xcd, 0xd1, 0xe7, 0xb0, 0xfa, 0xe4,
	0x7d, 0xc6, 0xae, 0xb3, 0xe7, 0x63, 0x77, 0x43, 0x71, 0x29, 0x50, 0x99, 0xb0, 0xd2, 0x9c, 0xa3,
	0x15, 0x72, 0x38, 0xa0, 0xc4, 0x05, 0x71, 0x4b, 0x65, 0xa7, 0xa4, 0x1d, 0x0e, 0x07, 0xfc, 0x45,
	0xf5, 0x6c, 0x95, 0x2c, 0x04, 0xa4, 0x56, 0x1c, 0xbc, 0x1d, 0x4c, 0x29, 0x79, 0xab, 0x71, 0x87,
	0x9c, 0x93, 0x64, 0x31, 0x4a, 0xad, 0x93, 0xf9, 0x3e, 0xfb, 0xaf, 0x84, 0xb5, 0x99, 0xa2, 0xb9,
	0xc6, 0xb7, 0x08, 0xef, 0x91, 0x37, 0x76, 0x56, 0x75, 0x60, 0x8f, 0xf5, 0xaa, 0x55, 0xb9, 0x85,
	0x94, 0x74, 0x19, 0xd4, 0xb6, 0x5f, 0xd9, 0xa8, 0x30, 0x84, 0x1e, 0xb1, 0xbe, 0xca, 0x44, 0x82,
	0xe5, 0x53, 0xc5, 0xb7, 0x5c, 0xce, 0x95, 0xfd, 0x2e, 0x40, 0xa7, 0x99, 0x35, 0x52, 0xa5, 0x96,
	0x32, 0x1c, 0xc4, 0x0f, 0x55, 0xf6, 0x01, 0xe9, 0xed, 0x9f, 0x4a, 0x64, 0x74, 0x45, 0x1c, 0xdd,
	0xfb, 0x79, 0xd9, 0x6f, 0xfc, 0xba, 0xec, 0x37, 0x7e, 0x5f, 0xf6, 0x1b, 0x9f, 0x83, 0x7d, 0xff,
	0xfb, 0xfc, 0xb2, 0x41, 0xbf, 0xd3, 0xe1, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xca, 0xe7, 0xf5,
	0x67, 0x63, 0x05, 0x00, 0x00,
}

func (m *StoreRankFactor) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreRankFactor) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreRankFactor) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.CUeRatioFactorOri != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CUeRatioFactorOri))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x91
	}
	if m.CUeFactorOri != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CUeFactorOri))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x89
	}
	if m.CUeRatioFactor != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CUeRatioFactor))))
		i--
		dAtA[i] = 0x1
		i--
		dAtA[i] = 0x81
	}
	if m.CUeFactor != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CUeFactor))))
		i--
		dAtA[i] = 0x79
	}
	if m.CPriceIndex != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CPriceIndex))))
		i--
		dAtA[i] = 0x71
	}
	if m.CCvr != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CCvr))))
		i--
		dAtA[i] = 0x69
	}
	if m.CCtr != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CCtr))))
		i--
		dAtA[i] = 0x61
	}
	if m.CTaxRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CTaxRate))))
		i--
		dAtA[i] = 0x59
	}
	if m.CServiceFee != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CServiceFee))))
		i--
		dAtA[i] = 0x51
	}
	if m.CCommissionRate != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CCommissionRate))))
		i--
		dAtA[i] = 0x49
	}
	if m.CAccGmvScoreCate2 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CAccGmvScoreCate2))))
		i--
		dAtA[i] = 0x41
	}
	if m.CGmvScoreCate2 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CGmvScoreCate2))))
		i--
		dAtA[i] = 0x39
	}
	if m.CAccSalesScoreCate2 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CAccSalesScoreCate2))))
		i--
		dAtA[i] = 0x31
	}
	if m.CSalesScoreCate2 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CSalesScoreCate2))))
		i--
		dAtA[i] = 0x29
	}
	if m.CAccGmvScoreCate1 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CAccGmvScoreCate1))))
		i--
		dAtA[i] = 0x21
	}
	if m.CGmvScoreCate1 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CGmvScoreCate1))))
		i--
		dAtA[i] = 0x19
	}
	if m.CAccSalesScoreCate1 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CAccSalesScoreCate1))))
		i--
		dAtA[i] = 0x11
	}
	if m.CSalesScoreCate1 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.CSalesScoreCate1))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *StoreOfflineData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StoreOfflineData) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StoreOfflineData) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.IsMeetA30QualityConstraint {
		i--
		if m.IsMeetA30QualityConstraint {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x70
	}
	if m.StoreCtCvr_180D != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StoreCtCvr_180D))))
		i--
		dAtA[i] = 0x69
	}
	if m.StoreOrderCnt_180D != 0 {
		i = encodeVarintDictStruct(dAtA, i, uint64(m.StoreOrderCnt_180D))
		i--
		dAtA[i] = 0x60
	}
	if m.StoreImpCnt_180D != 0 {
		i = encodeVarintDictStruct(dAtA, i, uint64(m.StoreImpCnt_180D))
		i--
		dAtA[i] = 0x58
	}
	if m.IsTargetA30 {
		i--
		if m.IsTargetA30 {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x50
	}
	if m.Commission != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Commission))))
		i--
		dAtA[i] = 0x49
	}
	if m.Revenue != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Revenue))))
		i--
		dAtA[i] = 0x41
	}
	if m.UeRatio != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.UeRatio))))
		i--
		dAtA[i] = 0x39
	}
	if m.Ue != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.Ue))))
		i--
		dAtA[i] = 0x31
	}
	if m.StorePriceScore3 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StorePriceScore3))))
		i--
		dAtA[i] = 0x29
	}
	if m.StorePriceScore2 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StorePriceScore2))))
		i--
		dAtA[i] = 0x21
	}
	if m.StorePriceScore1 != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StorePriceScore1))))
		i--
		dAtA[i] = 0x19
	}
	if m.StoreCvr != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StoreCvr))))
		i--
		dAtA[i] = 0x11
	}
	if m.StoreCtr != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(math.Float64bits(float64(m.StoreCtr))))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func encodeVarintDictStruct(dAtA []byte, offset int, v uint64) int {
	offset -= sovDictStruct(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *StoreRankFactor) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.CSalesScoreCate1 != 0 {
		n += 9
	}
	if m.CAccSalesScoreCate1 != 0 {
		n += 9
	}
	if m.CGmvScoreCate1 != 0 {
		n += 9
	}
	if m.CAccGmvScoreCate1 != 0 {
		n += 9
	}
	if m.CSalesScoreCate2 != 0 {
		n += 9
	}
	if m.CAccSalesScoreCate2 != 0 {
		n += 9
	}
	if m.CGmvScoreCate2 != 0 {
		n += 9
	}
	if m.CAccGmvScoreCate2 != 0 {
		n += 9
	}
	if m.CCommissionRate != 0 {
		n += 9
	}
	if m.CServiceFee != 0 {
		n += 9
	}
	if m.CTaxRate != 0 {
		n += 9
	}
	if m.CCtr != 0 {
		n += 9
	}
	if m.CCvr != 0 {
		n += 9
	}
	if m.CPriceIndex != 0 {
		n += 9
	}
	if m.CUeFactor != 0 {
		n += 9
	}
	if m.CUeRatioFactor != 0 {
		n += 10
	}
	if m.CUeFactorOri != 0 {
		n += 10
	}
	if m.CUeRatioFactorOri != 0 {
		n += 10
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *StoreOfflineData) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StoreCtr != 0 {
		n += 9
	}
	if m.StoreCvr != 0 {
		n += 9
	}
	if m.StorePriceScore1 != 0 {
		n += 9
	}
	if m.StorePriceScore2 != 0 {
		n += 9
	}
	if m.StorePriceScore3 != 0 {
		n += 9
	}
	if m.Ue != 0 {
		n += 9
	}
	if m.UeRatio != 0 {
		n += 9
	}
	if m.Revenue != 0 {
		n += 9
	}
	if m.Commission != 0 {
		n += 9
	}
	if m.IsTargetA30 {
		n += 2
	}
	if m.StoreImpCnt_180D != 0 {
		n += 1 + sovDictStruct(uint64(m.StoreImpCnt_180D))
	}
	if m.StoreOrderCnt_180D != 0 {
		n += 1 + sovDictStruct(uint64(m.StoreOrderCnt_180D))
	}
	if m.StoreCtCvr_180D != 0 {
		n += 9
	}
	if m.IsMeetA30QualityConstraint {
		n += 2
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovDictStruct(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozDictStruct(x uint64) (n int) {
	return sovDictStruct(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *StoreRankFactor) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDictStruct
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreRankFactor: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreRankFactor: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CSalesScoreCate1", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CSalesScoreCate1 = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CAccSalesScoreCate1", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CAccSalesScoreCate1 = float64(math.Float64frombits(v))
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CGmvScoreCate1", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CGmvScoreCate1 = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CAccGmvScoreCate1", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CAccGmvScoreCate1 = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CSalesScoreCate2", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CSalesScoreCate2 = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CAccSalesScoreCate2", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CAccSalesScoreCate2 = float64(math.Float64frombits(v))
		case 7:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CGmvScoreCate2", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CGmvScoreCate2 = float64(math.Float64frombits(v))
		case 8:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CAccGmvScoreCate2", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CAccGmvScoreCate2 = float64(math.Float64frombits(v))
		case 9:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CCommissionRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CCommissionRate = float64(math.Float64frombits(v))
		case 10:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CServiceFee", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CServiceFee = float64(math.Float64frombits(v))
		case 11:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CTaxRate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CTaxRate = float64(math.Float64frombits(v))
		case 12:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CCtr", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CCtr = float64(math.Float64frombits(v))
		case 13:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CCvr", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CCvr = float64(math.Float64frombits(v))
		case 14:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CPriceIndex", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CPriceIndex = float64(math.Float64frombits(v))
		case 15:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CUeFactor", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CUeFactor = float64(math.Float64frombits(v))
		case 16:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CUeRatioFactor", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CUeRatioFactor = float64(math.Float64frombits(v))
		case 17:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CUeFactorOri", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CUeFactorOri = float64(math.Float64frombits(v))
		case 18:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field CUeRatioFactorOri", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.CUeRatioFactorOri = float64(math.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipDictStruct(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDictStruct
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StoreOfflineData) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowDictStruct
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StoreOfflineData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StoreOfflineData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreCtr", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StoreCtr = float64(math.Float64frombits(v))
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreCvr", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StoreCvr = float64(math.Float64frombits(v))
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorePriceScore1", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StorePriceScore1 = float64(math.Float64frombits(v))
		case 4:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorePriceScore2", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StorePriceScore2 = float64(math.Float64frombits(v))
		case 5:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorePriceScore3", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StorePriceScore3 = float64(math.Float64frombits(v))
		case 6:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Ue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Ue = float64(math.Float64frombits(v))
		case 7:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field UeRatio", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.UeRatio = float64(math.Float64frombits(v))
		case 8:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Revenue", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Revenue = float64(math.Float64frombits(v))
		case 9:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Commission", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Commission = float64(math.Float64frombits(v))
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsTargetA30", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsTargetA30 = bool(v != 0)
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreImpCnt_180D", wireType)
			}
			m.StoreImpCnt_180D = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreImpCnt_180D |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreOrderCnt_180D", wireType)
			}
			m.StoreOrderCnt_180D = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StoreOrderCnt_180D |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field StoreCtCvr_180D", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.StoreCtCvr_180D = float64(math.Float64frombits(v))
		case 14:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field IsMeetA30QualityConstraint", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMeetA30QualityConstraint = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipDictStruct(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthDictStruct
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipDictStruct(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowDictStruct
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowDictStruct
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthDictStruct
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupDictStruct
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthDictStruct
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthDictStruct        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowDictStruct          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupDictStruct = fmt.Errorf("proto: unexpected end of group")
)
