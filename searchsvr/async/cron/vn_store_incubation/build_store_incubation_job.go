package vn_store_incubation

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"

	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/model"
)

const StoreIncubation = "food_search_store_incubation"

type BuildStoreIncubationJob struct {
	s3Client            s3.S3Service
	storeIncubationDict *model.StoreIncubationDict
}

func NewBuildStoreIncubationJob(s3Client s3.S3Service, storeIncubationDict *model.StoreIncubationDict) *BuildStoreIncubationJob {
	return &BuildStoreIncubationJob{
		s3Client:            s3Client,
		storeIncubationDict: storeIncubationDict,
	}
}

var currentTimestamp int64
var isFirstInit = true

func (b *BuildStoreIncubationJob) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(getStoreIncubationName())
	if err != nil {
		return
	}
	b.storeIncubationDict.BuildStoreIncubationDict(ctx, filePwd)
}

func (b *BuildStoreIncubationJob) Run() {
	fileName := getStoreIncubationName()
	logkit.Info("BuildStoreIncubationJob start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildStoreIncubationJob Connect s3 client failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildStoreIncubationJob Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildStoreIncubationJob fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildStoreIncubationJob update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildStoreIncubationJob done", zap.String("filename", fileName))
}

func (b *BuildStoreIncubationJob) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildStoreIncubationJob Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}

func getStoreIncubationName() string {
	return StoreIncubation + "_" + util.GetEnvLiveish(false) + "_" + env.GetCID() + ".txt"
}
