package load_data_plugin

import (
	"context"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/metric_reporter"
	"go.uber.org/zap"
	"sync"
)

type LoadS3FilePlugin struct {
	s3Client   s3.S3Service
	fileName   string
	dataDict   interface{}
	modifyTime int64
	lock       sync.RWMutex

	loadDataFunc LoadDataFunc
	getDataFunc  GetDataFunc
}

type LoadDataFunc func(filePath string) (interface{}, error)

type GetDataFunc func(dict, key interface{}) interface{}

func NewLoadS3FilePlugin(service s3.S3Service, file string, loadFunc LoadDataFunc, getFunc GetDataFunc) *LoadS3FilePlugin {
	return &LoadS3FilePlugin{
		s3Client:     service,
		fileName:     file,
		dataDict:     nil,
		modifyTime:   0,
		lock:         sync.RWMutex{},
		loadDataFunc: loadFunc,
		getDataFunc:  getFunc,
	}
}

func (l *LoadS3FilePlugin) Run() {
	ctx := context.Background()
	logkit.FromContext(ctx).Info("load plugin start", zap.String("file", l.fileName))
	err := l.BuildData()
	if err != nil {
		_ = metric_reporter.ReportCronJobError(1, "load_file_"+l.fileName+"_error")
	}
	logkit.FromContext(ctx).Info("load plugin finish", zap.String("file", l.fileName))
}

func (l *LoadS3FilePlugin) BuildData() error {
	modifyTime, err := l.s3Client.GetLastModified(l.fileName)
	if err != nil {
		logkit.Error("LoadDataPlugin get last modify failed", zap.String("file", l.fileName), zap.String("err", err.Error()))
		return err
	}
	if modifyTime < 1 {
		logkit.Error("LoadDataPlugin get last modify, modifyTime < 1  ", zap.String("file", l.fileName))
		return err
	}
	if modifyTime <= l.modifyTime {
		return nil
	}
	l.modifyTime = modifyTime
	logkit.Info("LoadDataPlugin start download file and load", zap.String("file", l.fileName))
	filePwd, err := l.DownloadFile(l.fileName)
	if err != nil {
		logkit.Error("LoadDataPlugin download file error", zap.String("file", l.fileName), zap.String("err", err.Error()))
		return err
	}
	dict, err := l.loadDataFunc(filePwd)
	if err != nil {
		logkit.Error("LoadDataPlugin load file error", zap.String("file", l.fileName), zap.String("err", err.Error()))
		return err
	}
	l.lock.Lock()
	defer l.lock.Unlock()
	l.dataDict = dict
	return nil
}

func (l *LoadS3FilePlugin) GetData(key interface{}) interface{} {
	l.lock.RLock()
	defer l.lock.RUnlock()
	retVal := l.getDataFunc(l.dataDict, key)
	return retVal
}

func (l *LoadS3FilePlugin) DownloadFile(fileName string) (string, error) {
	err := l.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("LoadDataPlugin Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}
