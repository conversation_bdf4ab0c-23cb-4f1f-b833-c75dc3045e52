package vn_char_mapping_job

import (
	"bufio"
	"context"
	"io"
	"os"
	"strings"
	"sync"

	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/cid"
)

type CharMapping struct {
	Dict map[string]string
	sync.RWMutex
}

var CharMappingInstance *CharMapping

var CharMappingInstanceOnce sync.Once

func NewCharMappingInstance() *CharMapping {
	CharMappingInstanceOnce.Do(func() {
		CharMappingInstance = &CharMapping{
			Dict:    make(map[string]string),
			RWMutex: sync.RWMutex{},
		}
	})
	return CharMappingInstance
}

func (m *CharMapping) BuildCharMapping(ctx context.Context, filePwd string) error {
	fs, err := os.Open(filePwd)
	if err != nil {
		logkit.FromContext(ctx).WithError(err).Error("BuildCharMapping Failed to open file",
			logkit.String("file path", filePwd))
		return err
	}
	defer fs.Close()

	charTmpMap := make(map[string]string)
	br := bufio.NewReader(fs)
	for {
		a, _, c := br.ReadLine()
		if c == io.EOF {
			break
		}
		str := string(a)
		splits := strings.Split(str, "\t")
		if len(splits) != 2 {
			continue
		}

		charTmpMap[splits[0]] = splits[1]
	}
	m.Lock()
	defer m.Unlock()
	m.Dict = charTmpMap
	logkit.Info("Build Char Mapping success")
	return nil
}

func (m *CharMapping) ReplaceChar(query string) string {
	if cid.IsVN() == false {
		return query
	}
	m.RLock()
	defer m.RUnlock()
	retStr := query
	for key, val := range m.Dict {
		retStr = strings.ReplaceAll(retStr, key, val)
	}
	return retStr
}
