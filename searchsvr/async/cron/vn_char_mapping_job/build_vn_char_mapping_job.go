package vn_char_mapping_job

import (
	"context"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/o2o-intelligence/common/common-lib/s3"
	"git.garena.com/shopee/toc/foodalgo/service/searchsvr/common/util"
	"go.uber.org/zap"
)

const CharMappingFileName = "char_mapping"

type BuildCharMappingService struct {
	s3Client    s3.S3Service
	charMapping *CharMapping
}

func getCharMappingFileName() string {
	return CharMappingFileName + "_" + util.GetEnvLiveish(false) + "_" + env.GetCID() + ".txt"
}

func NewBuildCharMappingService(s3Config s3.S3Config, mapping *CharMapping) *BuildCharMappingService {
	s3Client := s3.NewS3Service(s3Config)
	return &BuildCharMappingService{
		s3Client:    s3Client,
		charMapping: mapping,
	}
}

var currentTimestamp int64
var isFirstInit = true

func (b *BuildCharMappingService) InitOrUpdate() {
	ctx := context.Background()
	filePwd, err := b.DownloadFile(getCharMappingFileName())
	if err != nil {
		logkit.Error("download file error", zap.String("file", getCharMappingFileName()), zap.String("err", err.Error()))
		return
	}
	err = b.charMapping.BuildCharMapping(ctx, filePwd)
	if err != nil {
		logkit.Error("BuildCharMapping error", zap.String("err", err.Error()))
		return
	}
}

func (b *BuildCharMappingService) DownloadFile(fileName string) (string, error) {
	err := b.s3Client.Download(context.Background(), fileName, fileName)
	if err != nil {
		logkit.Error("BuildCharMappingService Download s3 file failed ", zap.String("file", fileName))
		return "", err
	}
	filePwd := "./" + fileName
	return filePwd, nil
}

func (b *BuildCharMappingService) Run() {
	fileName := getCharMappingFileName()
	logkit.Info("BuildCharMappingService start", zap.String("filename", fileName))
	modifyTime, err := b.s3Client.GetLastModified(fileName)
	if err != nil {
		logkit.Error("BuildCharMappingService get last modify failed", logkit.String("error", err.Error()), logkit.String("fileName", fileName))
		return
	}
	if modifyTime < 1 {
		logkit.Error("BuildCharMappingService Get file last modified failed", logkit.String("fileName", fileName))
		return
	}
	if isFirstInit {
		isFirstInit = false
		currentTimestamp = modifyTime
		b.InitOrUpdate()
		logkit.Info("BuildCharMappingService fist init done", zap.String("filename", fileName))
		return
	}
	if modifyTime > currentTimestamp {
		currentTimestamp = modifyTime
		logkit.Info("BuildCharMappingService update...")
		b.InitOrUpdate()
	}
	logkit.Info("BuildCharMappingService done", zap.String("filename", fileName))
}
