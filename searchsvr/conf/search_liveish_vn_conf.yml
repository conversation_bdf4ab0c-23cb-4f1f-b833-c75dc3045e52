# this file is generated by protoc-gen-mocrokit at Mon Jul 20 17:03:43 CST 2020
client:
  pool_size: 200          # Connection pool size
  pool_ttl: 30000         # Connection TTL (ms)
  dial_timeout: 1000      # Transport Dial Timeout (ms)
  request_timeout: 3000   # Transport request Timeout (ms)
  retries: 0              # Request retries

log:
  level: info
  path: ./log
  max_size: 1000       # megabytes
  max_backups:  10
  max_age: 3          # days
  enable_caller: true
  enable_console: false
  error_async: false

datastream_mysql:
  host:         "db-backendslave-foodalgo-search-datastream-vn-sg1-live.shopeemobile.com"
  port:         6606
  schema:       "shopee_foodalgo_search_datastream_vn_db"
  dial_timeout:  2000
  read_timeout:  3000
  write_timeout: 2000
  max_idle_conns: 50
  max_open_conns: 100
  max_life_time: 3600000

dataplatform_mysql:
  host:         "db-master-foodalgo-search-dataplatform-vn-sg1-live.shopeemobile.com"
  port:         6606
  schema:       "shopee_foodalgo_search_dataplatform_vn_db"
  dial_timeout:  2000
  read_timeout:  3000
  write_timeout: 2000
  max_idle_conns: 50
  max_open_conns: 100
  max_life_time: 3600000

redis:
  host: "uwzbv.elasticredis.cloud.shopee.io"
  port: 10697
  db: 0
  pool_size: 100
  max_retries: 5
  dial_timeout: 500
  read_timeout: 50
  write_timeout: 50
  pool_timeout: 1000
  idle_timeout: 60000

# live XX 监听live ID GDS 并更新到 live XX DB
gds:
  broker:
    addrs:
      - gds-kafka-mh-01-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-02-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-03-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-04-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-05-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-06-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-07-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-08-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-09-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-10-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-11-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-12-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-13-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-14-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-15-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-16-sg1-live.shopeemobile.com:9092
      - gds-kafka-mh-17-sg1-live.shopeemobile.com:9092
    version: "1.0.0"
  consumers:
    store:
      topics:
        - "db_shopee_foody_core"
      group: "shopeefoodalgo_search_gds_store_liveish_vn"
    area:
      topics:
        - "db_shopee_foody_delivery_id"
      group: "shopeefoodalgo_search_gds_area_liveish_vn"

## live vn dr 集群
elastic:
  urls:
    - "es.es-foody-search-main-rc-dr.ap-sg-1-general-c.sg.live.dae.shopee.io:9201"
  timeout: 3000

## live vn dr 集群
replica_elastic:
  urls:
    - "es.es-foody-search-main-rc-dr.ap-sg-1-general-c.sg.live.dae.shopee.io:9201"
  timeout: 3000

gdswatch_schema:
  - "shopee_foody_item_id_db"
  - "shopee_foody_area_id_db"
  - "shopee_foody_merchant_id_db"

micro_config:
  region : "vn"
  env : "liveish"
  port : 9087
  zk_address : "microkit.zk01.i.sz.shopee.io:2181,microkit.zk02.i.sz.shopee.io:2181,microkit.zk03.i.sz.shopee.io:2181,microkit.zk04.i.sz.shopee.io:2181,microkit.zk05.i.sz.shopee.io:2181"
  conn_pool_size : 100

s3_file_name:
  unsettled_store: "unsettled_store"
  ue_factor_file: "food_search_store_offline_data"
  offline_algo_factor: "food_search_store_offline_ranking_factor_data"

s3:
  host : "http://proxy.uss.s3.sz.shopee.io"
  bucket_id : "foody-search-live"

geo:
  source: "geo"
  user: "shopeefood"
  project: "o2o_intelligence_food_search_rcmd"
  region: "vn"

vn_http:
  promotion:
    host: "http://nowpromotion-service-api-s2s.shopeefood.vn"
    url: "/s2s/v5/promotion/check_merchants_have_promotions"
  is_opening:
    host: "http://nowmerchant-service-api-s2s.shopeefood.vn"
    url: "/s2s/restaurant/get_status_by_restaurant_ids"