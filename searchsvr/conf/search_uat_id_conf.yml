# this file is generated by protoc-gen-mocrokit at Mon Jul 20 17:03:43 CST 2020
client:
  pool_size: 200          # Connection pool size
  pool_ttl: 30000         # Connection TTL (ms)
  dial_timeout: 1000      # Transport Dial Timeout (ms)
  request_timeout: 3000   # Transport request Timeout (ms)
  retries: 0              # Request retries

log:
  level: info
  path: ./log
  max_size: 1000       # megabytes
  max_backups:  10
  max_age: 3          # days
  enable_caller: true
  enable_console: false
  error_async: false

datastream_mysql:
  host:         "master.shopee_foodalgo_id.mysql.cloud.uat.shopee.io"
  port:         6606
  schema:       "shopee_foodalgo_search_datastream_id_db"
  dial_timeout:  2000
  read_timeout:  3000
  write_timeout: 2000
  max_idle_conns: 50
  max_open_conns: 100
  max_life_time: 3600000

redis:
  host: "kozzf.elasticredis.cloud.shopee.io"
  port: 13697
  user: "cache_readwrite_533757"
  db: 0
  pool_size: 100
  max_retries: 5
  dial_timeout: 500
  read_timeout: 500
  write_timeout: 100
  pool_timeout: 1000
  idle_timeout: 60000

gds:
  broker:
    addrs:
      - "gds-kafka-sw-01-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-02-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-03-sg1-nonlive.shopeemobile.com:9092"
    version: "1.0.0"
  consumers:
    store:
      topics:
        - "rds_foody_order_uat"
      group: "shopeefoodalgo_search_gds_store_uat_id"
    area:
      topics:
        - "rds_foody_order_uat"
      group: "shopeefoodalgo_search_gds_area_uat_id"

elastic:
  urls:
    - "http://es.ssbawcou.ap-sg-1-general-x.sg.uat.dae.shopee.io:19201"
  timeout: 3000

replica_elastic:
  urls:
    - "http://es.ssbawcou.ap-sg-1-general-x.sg.uat.dae.shopee.io:19201"
  timeout: 3000

gdswatch_schema:
  - "shopee_foody_item_id_db"
  - "shopee_foody_area_id_db"
  - "shopee_foody_merchant_id_db"

micro_config:
  region : "id"
  env : "uat"
  port : 9087
  zk_address : "microkit.zk01.i.test.sz.shopee.io:2181,microkit.zk02.i.test.sz.shopee.io:2181,microkit.zk03.i.test.sz.shopee.io:2181"
  conn_pool_size : 100

s3:
  host : "http://proxy.uss.s3.test.shopee.io"
  bucket_id : "foody-search-test"

s3_file_name:
  unsettled_store: "unsettled_store"
  algo_top_intervention_text: "algo_top_intervention_text"
  offline_algo_factor: "food_search_store_offline_ranking_factor_data"
  user_level: "food_search_user_level"
  pc_factor_file: "pc_factor_offline_data"
