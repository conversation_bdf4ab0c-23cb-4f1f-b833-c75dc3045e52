# this file is generated by protoc-gen-mocrokit at Mon Jul 20 17:03:43 CST 2020
client:
  pool_size: 200          # Connection pool size
  pool_ttl: 30000         # Connection TTL (ms)
  dial_timeout: 1000      # Transport Dial Timeout (ms)
  request_timeout: 3000   # Transport request Timeout (ms)
  retries: 0              # Request retries

log:
  level: debug
  path: ./log
  max_size: 1000       # megabytes
  max_backups:  10
  max_age: 3          # days
  enable_caller: true
  enable_console: false

redis:
  host : "proxy.cache-codis-sg2.i.test.sz.shopee.io"
  port : 8104
  password: ""
  db: 5
  pool_size: 100
  max_retries: 5
  dial_timeout: 500
  read_timeout: 100
  write_timeout: 100
  pool_timeout: 1000
  idle_timeout: 60000

gds:
  broker:
    addrs:
      - "gds-kafka-sw-01-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-02-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-03-sg1-nonlive.shopeemobile.com:9092"
    version: "1.0.0"
  consumers:
    store:
      topics:
        - "rds_foody_order_test"
      group: "foody_merchant_test_tw_gds"

event_mq:
  broker:
    addrs:
      - kafka.public-test.sg2.i.sz.shopee.io:9093
    version: 2.1.1
  consumers:
    rating:
      topics:
        - foody_rating_event_tw_test
      group: foody_merchant_group_tw_test
  producers:
    changelog: foody_changelog_tw_test

elastic:
  urls:
    - "foody.es.toc.test.sz.shopee.io:31625"
  timeout: 3000

full_update_cron_job:
  start_hour: 1 # 24小时制
  lock_key: "foody:merchant:sync:full:update:lock"
  lock_expiration: 7200000

pull_variation_cron_job:
  interval: 300000
  repeat: 6
  lock_key: "foody:merchant:sync:pull:variation:lock"
  lock_expiration: 30000

sync_task_consumer_config:
  pool:
    size: 3
    task_channel_size: 10

http:
  transport:
    dial_param:
      timeout: 5000
      keep_alive: 30000
    max_idle_conns: 100
    max_idle_conns_per_host: 10
    idle_conn_timeout: 90000
  timeout: 5000

s3:
  host: "s3.i.test.sz.shopee.io"
  ak: "Y9Q4GCI1XBHV45GUZMXZ"
  sk: "ckS9i94kLNXDGLY5ZQR7TX3lrSbV11tRWb5tIQvd"
  region: "default"
  disable_ssl: true
  s3force_path_style: true