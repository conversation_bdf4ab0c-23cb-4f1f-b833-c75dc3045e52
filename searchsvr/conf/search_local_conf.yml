# this file is generated by protoc-gen-mocrokit at Mon Jul 20 17:03:43 CST 2020
client:
  pool_size: 200          # Connection pool size
  pool_ttl: 30000         # Connection TTL (ms)
  dial_timeout: 1000      # Transport Dial Timeout (ms)
  request_timeout: 3000   # Transport request Timeout (ms)
  retries: 0              # Request retries

log:
  level: debug
  path: ./log
  max_size: 1000       # megabytes
  max_backups:  10
  max_age: 3          # days
  enable_caller: true
  enable_console: false
  error_async: false

redis:
  host : "proxy.cache-codis-sg2.i.test.sz.shopee.io"
  port : 8104
  db: 0
  pool_size: 100
  max_retries: 5
  dial_timeout: 500
  read_timeout: 500
  write_timeout: 100
  pool_timeout: 1000
  idle_timeout: 60000

gds:
  broker:
    addrs:
      - "gds-kafka-sw-01-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-02-sg1-nonlive.shopeemobile.com:9092"
      - "gds-kafka-sw-03-sg1-nonlive.shopeemobile.com:9092"
    version: "1.0.0"
  consumers:
    store:
      topics:
        - "rds_foody_order_test"
      group: "foody_merchant_test_id_gds"

elastic:
  urls:
    - "foody.es.toc.test.sz.shopee.io:31625"
  timeout: 3000