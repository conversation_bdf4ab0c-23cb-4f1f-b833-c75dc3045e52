@startuml 生成式召回流程

' 简单样式设置
skinparam backgroundColor white
skinparam handwritten false
skinparam defaultFontName "Microsoft YaHei"
skinparam ArrowColor #333333
skinparam shadowing false

title 生成式召回流程

' 开始节点
start

' 触发判断
:接收用户请求;
note right: 获取用户的query和cityid

if (query+cityid是否存在于KV表中?) then (是)
  :触发generative_recall;
  
  ' 获取候选
  :获取KV表中对应的value作为原始候选店铺;
  
  ' 过滤处理
  :遍历原始候选店铺;
  
  repeat
    if (店铺p_relevance > 0.1?) then (是)
      :保留该店铺;
    else (否)
      :过滤该店铺;
    endif
  repeat while (还有未处理的店铺?) is (是)
  
  ' 截断处理
  :获取筛选后的店铺列表(保持原始顺序);
  :根据召回店铺最大值进行截断;
  :输出最终召回店铺列表;
  
else (否)
  :不触发generative_recall;
endif

stop


@enduml
