@startuml
Actor  "user"
participant "BuyerBackend"
participant "SearchService" as ss


user -> BuyerBackend : 在 collection 中点击搜索

BuyerBackend -> BuyerBackend: 组装 SearchCollectionRequest\n（过滤条件带 Filter）
BuyerBackend -> ss: 发起 SearchCollectionRequest
ss -> ss: <font color=red>设置 pipeline 类型(SearchCollection)
ss -> ss: <font color=red>根据 pipeline 类型 拿到专属的召回配置列表
ss -> ss: <font color=red>组建 Collection 专属的 dsl
ss -> es: 召回
es -> ss: 返回召回结果

ss -> 正排模块: 获取正排
ss -> ss: 过滤

ss -> model平台: predict
model平台 -> ss: 返回predict结果
ss -> ss: rank
ss -> ss: rerank

ss->BuyerBackend: 返回搜索结果
BuyerBackend -> user: 返回搜索结果

@end