*.zip
*idea
*.swp
abcsvr/abcsvr
pb/foody_abc
.idea
config.yml
/kitsvr/example/
*.log*
.DS_Store
script/detector/dt
searchsvr/foodalgo-searchsvr
searchsvr/searchsvr
searchsvr/*.csv
searchsvr/*.txt
/vendor/golang.org/x/sys/cpu/parse.go
/vendor/golang.org/x/sys/cpu/proc_cpuinfo_linux.go
/searchsvr/integrate/query_normalize/query_norm_test.go
/vendor/golang.org/x/sys/unix/syscall_hurd.go
/vendor/golang.org/x/sys/unix/syscall_hurd_386.go
/vendor/git.garena.com/shopee/o2o-intelligence/common/common-lib/protobuf/o2oalgo_ml_search_feature/version_reporter.go
searchsvr/pc_factor_offline_data_id_test
searchsvr/__debug_bin*
.vscode*
searchsvr/script/recall_configuration/*.json
searchsvr/script/recall_configuration_vn/*.json
searchsvr/script/recall_configuration_dish/*.json
searchsvr/o2oalgo.food_search_rcmd.search-health_check.json
searchsvr/o2oalgo.food_search_rcmd.searchengine-health_check.json
/o2oalgo.food_search_rcmd.search-health_check.json
mr.diff
*.mr
*.diff
