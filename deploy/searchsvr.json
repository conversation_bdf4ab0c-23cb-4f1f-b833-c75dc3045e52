{"#": "doc : https://git.garena.com/shopee/shopee-deploy-common/blob/master/examples/example.json", "project_dir_depth": 2, "project_name": "foodalgo", "module_name": "search", "build": {"docker_image": {"base_image": "harbor.shopeemobile.com/shopee/golang-base:1.19.3"}, "commands": ["export GOBIN=$GOPATH/bin && make build service=searchsvr"]}, "run": {"enable_cni": true, "metadata.annotations.prometheus.io/scrape": true, "metadata.annotations.prometheus.io/port": 9090, "metadata.annotations.prometheus.io/path": "/metrics", "enable_prometheus": true, "prometheus_port": 9090, "prometheus_path": "/metrics", "acquire_prometheus_port": true, "command": "./searchsvr/foodalgo-searchsvr -config ./searchsvr/conf/config.yml", "smoke": {"protocol": "SPEX", "timeout": 30, "retry": 10, "grace_period": 300}, "check": {"protocol": "SPEX", "timeout": 30, "retry": 10}, "shutdown": {"test": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}, "uat": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}, "live": {"unregister_deadline_seconds": 5, "terminate_deadline_seconds": 8}}}, "deploy": {"idcs": {"test": {"id": ["sg2"], "sg": ["sg2"], "th": ["sg2"], "vn": ["sg2"], "tw": ["sg2"], "my": ["sg2"], "ph": ["sg2"]}, "staging": {"id": ["sg2"]}, "uat": {"id": ["sg2"], "my": ["sg2"], "ph": ["sg2"], "sg": ["sg2"], "tw": ["sg2"], "th": ["sg2"], "vn": ["sg2"]}, "live": {"id": ["sg2"]}}, "resources": {"test": {"cpu": 8, "mem": 8192}, "staging": {"cpu": 8, "mem": 8192}, "uat": {"cpu": 8, "mem": 8192}, "live": {"cpu": 8, "mem": 8192}}, "instances": {"test": {"id": 4, "sg": 4, "th": 4, "vn": 4, "tw": 4, "my": 4, "ph": 4}, "staging": {"id": 4}, "uat": {"id": 4, "sg": 4, "th": 4, "vn": 4, "tw": 4, "my": 4, "ph": 4}, "live": {"id": 4}}}}