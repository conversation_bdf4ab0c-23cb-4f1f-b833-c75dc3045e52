//+build dev

package soup

import (
	"log"
	"reflect"

	"git.garena.com/shopee/feed/ginweb/auth"
	"git.garena.com/shopee/feed/ginweb/middleware/authenticate"
)

type PermissionGetter interface {
	Permissions() []*auth.Permission
}

var modules []PermissionGetter

func RegisterModule(pg PermissionGetter) {
	if pg == nil {
		return
	}
	modules = append(modules, pg)
}

func TouchPerms(cid string, conf *authenticate.SoupConfig, pgs ...PermissionGetter) {
	perms := listPerms(append(modules, pgs...))

	// Touch permissions to SOUP.
	cli := auth.NewSoupClient(conf.SoupAddr, conf.Token)

	createdCount, err := cli.TouchBatchV2(&auth.TouchPermissionBatchV2{
		Code:        cid,
		Token:       conf.Token,
		Permissions: perms,
	})
	if err != nil {
		log.Fatalf("failed to touch perms: %s", err)
	}
	log.Printf("created %d new permissions", createdCount)
}

func listPerms(pgs []PermissionGetter) []*auth.Permission {
	var allPerms []*auth.Permission

	for _, m := range pgs {
		if m == nil {
			continue
		}

		name := reflect.ValueOf(m).Type().String()
		perms := m.Permissions()
		if len(perms) == 0 {
			log.Printf("skip module[%s]: not found any permissionss", name)
			continue
		}

		for _, p := range perms {
			log.Printf("found %s: %s", name, p.Code)
		}
		log.Printf("found %d permissions in module[%s]", len(perms), name)
		allPerms = append(allPerms, perms...)
	}
	return allPerms
}
