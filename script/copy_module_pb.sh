#!/bin/bash

PROJECT_ROOT=$1
COMMON_PB_PATH="${PROJECT_ROOT}/pb/"
PROJECT_MATCH_DIRS=$(find . -maxdepth 1 -type d -name '*svr' -or -type d -name '*api')

for SER in ${PROJECT_MATCH_DIRS[@]}; do \
		## 这里是拿到了每个符合条件的目录
    cd "$SER"; \
		echo "enter $(pwd)"; \

    DIR_MATCH_PBS=$(find . -maxdepth 2 -name '*.proto')
		## 在每个符合条件的目录里搜索是否有以proto结尾的文件，有的话拷贝到公共pb目录下
		## 这里根据proto名字在公共pb目录下建立对应目录然后拷贝当前的文件夹
		for item in ${DIR_MATCH_PBS[@]};do \
		  # 普通文件开始拷贝
		  if [ -f "${item}" ];then
		    itemDirName=$(dirname ${item})
		    if [[ ${itemDirName} == *"foodalgo_"* ]];then
		      cp -r ${itemDirName} ${COMMON_PB_PATH}
		      echo "cp -r ${itemDirName} ${COMMON_PB_PATH}"
		    fi
		  fi
		 done

    echo "leave $(pwd)";
		cd ..
	done

