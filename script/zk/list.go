package main

import (
	"fmt"
	"regexp"

	"github.com/urfave/cli/v2"
)

func NewShowCommand() *cli.Command {
	return &cli.Command{
		Name:         "show",
		Usage:        "show services nodes",
		Action:       showAction,
		ArgsUsage:    "service name",
		BashComplete: showComplete,
	}
}

func showComplete(ctx *cli.Context) {
	defer func() {
		recover()
	}()

	env := environment.GetValue()
	cid := region.GetValue()
	proj := project.GetValue()

	var input string
	if ctx.NArg() > 0 {
		input = ctx.Args().Get(ctx.NArg() - 1)
	}

	pattern := regexp.MustCompile(fmt.Sprintf("%s-(%s[a-zA-Z0-9]*)-%s-%s", proj, input, env, cid))
	children, _, _ := zk.Children(servicePathPrefix)

	for _, child := range children {
		if matched := pattern.FindStringSubmatch(child); len(matched) > 0 {
			fmt.Println(matched[0])
		}
	}
}

func showAction(ctx *cli.Context) error {
	if ctx.NArg() == 0 {
		return cli.ShowCommandHelp(ctx, ctx.Command.Name)
	}

	env := environment.GetValue()
	cid := region.GetValue()
	proj := project.GetValue()
	services := make(map[string][]string, ctx.NArg())

	for _, svrName := range ctx.Args().Slice() {
		fullName := buildFullName(proj, svrName, env, cid)
		if _, exists := services[fullName]; exists {
			continue
		}

		path := buildZKPath(fullName)
		exists, _, err := zk.Exists(path)
		if err != nil {
			return fmt.Errorf("failed to get %s: %s", fullName, err)
		}
		if !exists {
			services[fullName] = make([]string, 0)
			continue
		}

		nodes, _, err := zk.Children(path)
		if err != nil {
			return fmt.Errorf("failed to get %s nodes: %s", fullName, err)
		}
		services[fullName] = nodes
	}

	for name, nodes := range services {
		fmt.Println(name, ":", nodes)
	}

	return nil
}
