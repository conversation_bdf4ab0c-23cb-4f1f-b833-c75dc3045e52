package main

import (
	"fmt"
	"path"

	"github.com/urfave/cli/v2"
)

func NewDeleteCommand() *cli.Command {
	return &cli.Command{
		Name:         "delete",
		Usage:        "delete service nodes",
		Action:       deleteAction,
		ArgsUsage:    "name node[,node]",
		BashComplete: deleteBashComplete,
	}
}

func deleteBashComplete(ctx *cli.Context) {
	if ctx.NArg() == 0 {
		return
	}
	proj := project.GetValue()
	env := environment.GetValue()
	cid := region.GetValue()

	fullName := buildFullName(proj, ctx.Args().Get(0), env, cid)
	path := buildZKPath(fullName)

	exists, _, _ := zk.Exists(path)
	if !exists {
		return
	}

	inputNodes := make(map[string]bool, ctx.NArg())
	for _, node := range ctx.Args().Slice() {
		inputNodes[node] = true
	}

	nodes, _, _ := zk.Children(path)
	for _, n := range nodes {
		if !inputNodes[n] {
			fmt.Println(n)
		}
	}
}

func deleteAction(ctx *cli.Context) error {
	if ctx.NArg() <= 1 {
		return cli.ShowCommandHelp(ctx, ctx.Command.Name)
	}

	env := environment.GetValue()
	cid := region.GetValue()
	proj := project.GetValue()
	serviceName := ctx.Args().Get(0)

	fullName := buildFullName(proj, serviceName, env, cid)
	rootPath := buildZKPath(fullName)

	exists, _, err := zk.Exists(rootPath)
	if err != nil {
		return fmt.Errorf("failed to get %s: %s", fullName, err)
	}
	if !exists {
		fmt.Printf("service not exists: %s", fullName)
		return nil
	}

	for _, n := range ctx.Args().Slice()[1:] {
		nodePath := path.Join(rootPath, n)
		exists, _, err = zk.Exists(nodePath)
		if err != nil {
			return fmt.Errorf("failed to get %s node %s: %s", fullName, n, err)
		}
		if !exists {
			fmt.Printf("%s node %s not exists, skip it\n", fullName, n)
			continue
		}

		if err = zk.Delete(nodePath, -1); err != nil {
			return fmt.Errorf("failed to delete %s node %s: %s", fullName, n, err)
		}
		fmt.Printf("%s node %s deleted\n", fullName, n)
	}

	return nil
}
