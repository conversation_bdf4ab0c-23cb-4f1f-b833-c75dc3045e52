package main

import (
	"log"
	"os"
	"time"

	"git.garena.com/shopee/feed/comm_lib/env"
	"git.garena.com/shopee/feed/comm_lib/logkit"
	"git.garena.com/shopee/feed/microkit"
	zookeeper "github.com/samuel/go-zookeeper/zk"
	"github.com/urfave/cli/v2"
)

var version = "development"

var (
	brokers = &cli.StringSliceFlag{
		Name:    "broker",
		Aliases: nil,
		Usage:   "zookeeper brokers",
		Value:   cli.NewStringSlice(microkit.GetRegistryAddr()...),
	}
	environment = &cli.StringFlag{
		Name:  "env",
		Value: env.GetEnv(),
	}
	region = &cli.StringFlag{
		Name:  "cid",
		Value: env.GetCID(),
	}
	project = &cli.StringFlag{
		Name:  "project",
		Value: "foody",
	}

	cmdApp *cli.App
	zk     *zookeeper.Conn
)

func init() {
	logkit.Init(
		logkit.Level("fatal"),
		logkit.ErrorAsync(false),
	)
	zookeeper.DefaultLogger = noopLogger{}

	cmdApp = &cli.App{
		Name:    "zk",
		Usage:   "zookeeper commandline",
		Version: version,
		Flags: []cli.Flag{
			brokers,
			project,
		},
		Action: nil,
		Authors: <AUTHORS>
			Name:  "Yunxiang Huang",
			Email: "<EMAIL>",
		}},
		Commands: cli.Commands{
			NewShowCommand(),
			NewDeleteCommand(),
		},
		EnableBashCompletion: true,
		Before: func(context *cli.Context) error {
			conn, _, err := zookeeper.Connect(brokers.Value.Value(), 5*time.Second)
			if err != nil {
				return err
			}

			zk = conn
			return nil
		},
	}
}

func main() {
	if err := cmdApp.Run(os.Args); err != nil {
		log.Fatal(err)
	}
}
