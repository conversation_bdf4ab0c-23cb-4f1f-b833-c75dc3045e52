#!/bin/bash

echo "install birdayz/kaf..." >&2

# make temp dir.
temp_dir=$(mktemp -d)
pushd ${temp_dir}

# download binary archive.
echo "downloading from Github..." >&2
curl -L --output kaf.tar.gz  https://github.com/birdayz/kaf/releases/download/v0.1.39/kaf_0.1.39_Linux_x86_64.tar.gz

# extract and move it to /bin.
tar -xzvf kaf.tar.gz && mv kaf /bin/kaf

# clean temp dir and files.
popd
rm -rf ${temp_dir}

# detect env and link config.
echo "link kaf ${env}_config to ${HOME}/.kaf/config..." >&2
mkdir -p ${HOME}/.kaf
ln -s ${PWD}/${env}_config.yml ${HOME}/.kaf/config

