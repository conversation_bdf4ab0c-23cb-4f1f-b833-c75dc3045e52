# 检查各个 Server 的相互引用的情况

if [ "${CODE_BASE}" = "" ]; then
    echo "请通过 make 命令执行" >&2
    exit 1
fi
log_check_name "检查服务之间相互引用"

if [ "$(echo "$ALL_SERVER_PATH" | wc -l)" -lt 2 ]; then
    log_info "$(echo $ALL_SERVER_PATH | wc -l)"
    log_info "服务数量小于 2，跳过检查"
    log_check_pass
    return
fi

import_path_grep_condition=$(build_all_server_name_grep_condition)
common_package_import_path=$(build_all_common_name_grep_condition)
for server in ${ALL_SERVER_PATH}; do
    server_name=$(basename "${server}")
    if [ "${server}" = "" ]; then
        continue
    fi

    log_info "正在检查 ${server_name} 引用其他服务的情况..."

    all_files=$(find "${server}" -name '*.go' -exec echo {} \;)

    if invalid_import_path=$(
        grep -nF "git.garena.com/shopee/toc/foodalgo/service" ${all_files} |
            egrep "((${import_path_grep_condition}(/|$)).*){2}" |
            egrep -v "${server_name}.*${server_name}(/|$)" |
            egrep -v "${common_package_import_path}"
    ); then
        log_invalid_line "$invalid_import_path"
        log_fatal "发现了交叉引用的部分"
    fi
done

log_check_pass
