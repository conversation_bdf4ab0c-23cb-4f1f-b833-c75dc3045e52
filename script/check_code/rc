# Common env variables
export CODE_BASE=$(git rev-parse --show-toplevel)
export VENDOR_BASE="${CODE_BASE}/vendor"

# Shell tools
alias grep="grep --color=auto"
alias reset_project="git add \"${CODE_BASE}\" > /dev/null && git reset --hard head > /dev/null"

# Log methods
#if [ -t 2 ]; then
export _COLOR_BLUE="\x1B[0;94m"
export _COLOR_RED="\x1B[0;31m"
export _COLOR_GREEN="\x1B[0;92m"
export _COLOR_YELLOW="\x1B[0;93m"
export _COLOR_PURPLE="\x1B[0;95m"
export _COLOR_RST="\x1B[0;m"
#fi

function log_info() {
    echo -e "${_COLOR_BLUE}[I] $*${_COLOR_RST}" >&2
}

function log_warn() {
    echo -e "${_COLOR_YELLOW}[W] $*${_COLOR_RST}" >&2
}

function log_error() {
    echo -e "${_COLOR_RED}[E] $*${_COLOR_RST}" >&2
}

function log_invalid_line() {
    echo -e "${_COLOR_PURPLE}$*${_COLOR_RST}" >&2
}

function log_fatal() {
    echo -e "${_COLOR_RED}[F] $*${_COLOR_RST}" >&2
    exit 1
}

function log_check_name() {
    echo -e "${_COLOR_GREEN}====== $*${_COLOR_RST}"
}

function log_check_pass() {
    echo -e "${_COLOR_GREEN}====== Passed${_COLOR_RST}"
    echo ""
    echo ""
}

# list_all_server 用来列出我们所有的服务
# 会给出各个服务的绝对路径
function list_all_server() {
    # 通过 main.go 来确定服务的位置
    # 排除了下列位置：
    #  - script
    #  - vendor
    server_main_path=$(
        find "${CODE_BASE}" -maxdepth 2 -name 'main.go' | xargs ls -ld |
            grep -v -F 'vendor' | grep -v -F 'script' |
            awk '{ print $9 }'
    )
    server_count=$(echo "$server_main_path" | wc -l | awk '{ print $1 }')
    all_server_path=""

    for main_path in ${server_main_path}; do
        server_path=$(dirname "${main_path}")
        log_info "检测到服务：$(basename "${server_path}")"
        all_server_path="${server_path}\n${all_server_path}"
    done

    log_info "共检测到 ${server_count} 个服务"

    deploy_count=$(find "${CODE_BASE}/deploy" -name '*.json' | wc -l | awk '{ print $1 }')
    if [[ ${deploy_count} -ne ${server_count} ]]; then
        log_warn "服务数量和 Deploy 配置数量[${deploy_count}]不一致，检查可能遗漏"
    fi

    echo -e "${all_server_path}" | sort | egrep -v "^$"
}

export ALL_SERVER_PATH=$(list_all_server)

function build_all_server_name_grep_condition() {
    grep_cond=""
    for server in ${ALL_SERVER_PATH}; do
        if [ ! "$grep_cond" = "" ]; then
            grep_cond="${grep_cond}|"
        fi
        grep_cond="${grep_cond}$(basename "${server}")"
    done
    echo ${grep_cond}
}

export ALL_PACKAGE_PATH=$(ls -l "${CODE_BASE}" | grep '^d' |
    grep -vF "vendor" |
    grep -vF "scripts" |
    grep -vF "resource" |
    awk '{ print $9 }' |
    sed -e "s/^/${CODE_BASE//\//\\/}\//g") # 把仓库目录的 / 转译成 \/，然后添加到目录名前面

export ALL_COMMON_PACKAGE_PATH=$(ls -l "${CODE_BASE}" | grep '^d' |
    egrep -v "$(build_all_server_name_grep_condition)" |
    grep -vF "vendor" |
    grep -vF "scripts" |
    grep -vF "resource" |
    awk '{ print $9 }' |
    sed -e "s/^/${CODE_BASE//\//\\/}\//g") # 把仓库目录的 / 转译成 \/，然后添加到目录名前面

function build_all_common_name_grep_condition() {
    grep_cond=""

    for common_package_path in ${ALL_COMMON_PACKAGE_PATH}; do
        import_path=$(echo ${common_package_path} | egrep -o "foody/service/.+")
        if [ ! "${grep_cond}" = "" ]; then
            grep_cond="${grep_cond}|"
        fi
        grep_cond="${grep_cond}${import_path}"
    done
    echo ${grep_cond}
}

log_info "仓库目录: ${CODE_BASE}"
log_info "依赖目录: ${VENDOR_BASE}"
log_info "CI 环境初始化完毕\n\n"
