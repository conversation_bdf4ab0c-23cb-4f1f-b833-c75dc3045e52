package main

/*
  1. 读取 Token
  2. 读取服务名、Prof类型、时长
  3. SMC 到 机器执行命令
  4. sleep
  5. 执行下载
*/
import (
	"flag"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"strings"
)

var (
	h        bool
	ptype    int
	duration int
	index    int
	service  string
	filename string
)

func init() {
	flag.IntVar(&ptype, "t", 1, "PProf type: 1. CPU PProf; 2. Heap PProf 3. Trace PProf 4. Block PProf 5. Mutex PProf 6. GoRoutine PProf")
	flag.IntVar(&duration, "d", 30, "PProf duration(sec): default 30s")
	flag.IntVar(&index, "i", 0, "container index")
	flag.BoolVar(&h, "h", false, "help")

	flag.Usage = usage
}

func usage() {
	fmt.Fprintf(os.Stderr,
		`dt version: detector tool/1.0.0
Usage: dt -f 'file name' [-t ] [-p pprof type] [-d pprof duration] [-i container index] [-h]
Options:
`)
	flag.PrintDefaults()
}

func env(key string) string {
	return strings.TrimSpace(strings.ToLower(os.Getenv(key)))
}

func checkParam() bool {
	service = env("SERVICE_NAME")
	if h || service == "" {
		flag.Usage()
		return false
	}
	sSlice := strings.Split(service, "-")
	if len(sSlice) != 4 {
		fmt.Println("service name not valid!")
		return false
	}

	if filename == "" {
		switch ptype {
		case 1:
			filename = service + "-cpu.profile"
		case 2:
			filename = service + "-heap.profile"
		case 3:
			filename = service + ".trace"
		case 4:
			filename = service + "-block.txt"
		case 5:
			filename = service + "-mutex.txt"
		case 6:
			filename = service + "-routinue.txt"
		default:
			fmt.Println("PProf type not valid!")
			return false
		}
	}

	return true
}

func runCommand(c string) error {
	cmd := exec.Command("/bin/bash", "-c", c)
	stdin, err := cmd.StdinPipe()
	if err != nil {
		log.Fatalf("get stdin failed with '%s'\n", err)
	}
	stdin.Write([]byte(fmt.Sprintf("%v\n", index)))
	stdin.Close()
	fmt.Println(index)

	stdoutIn, _ := cmd.StdoutPipe()
	stderrIn, _ := cmd.StderrPipe()
	var (
		errStdout, errStderr error
	)
	err = cmd.Start()
	if err != nil {
		log.Fatalf("cmd.Start() failed with '%s'\n", err)
	}
	go func() {
		_, errStdout = io.Copy(os.Stdout, stdoutIn)
	}()
	go func() {
		_, errStderr = io.Copy(os.Stderr, stderrIn)
	}()
	err = cmd.Wait()
	if err != nil {
		return err
	}
	if errStdout != nil || errStderr != nil {
		return err
	}
	return err
}

func cpuProfileCmd(duration int, filename, token string) string {
	cmdProfile := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/profile?seconds=%v" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, duration, filename, token)
	return cmdProfile
}

func heapProfileCmd(filename, token string) string {
	cmdHeap := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/heap" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, filename, token)
	return cmdHeap
}

func traceProfileCmd(filename, token string) string {
	cmdTrace := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/trace" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, filename, token)
	return cmdTrace
}

func blockCmd(filename, token string) string {
	cmdBlock := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/block?debug=1" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, filename, token)
	return cmdBlock
}

func mutexCmd(filename, token string) string {
	cmdMutex := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/mutex?debug=1" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, filename, token)
	return cmdMutex
}

func routineCmd(filename, token string) string {
	cmdRoutine := fmt.Sprintf(`cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print "http://127.0.0.1:"$1"/debug/pprof/goroutine?debug=1" }'  | xargs curl -o /tmp/%v  --cookie 'token=%v'`, filename, token)
	return cmdRoutine
}

func downloadCmd(filename string) string {
	cmdDownload := fmt.Sprintf(`sz /tmp/%v`, filename)
	return cmdDownload
}

func main() {
	flag.Parse()

	if !checkParam() {
		return
	}

	// smc -e uat services run live-api-uat-id "cat /proc/*/environ | sed -n 's/.*PORT_prometheus=\([0-9]*\).*/\1/p' | awk '{print \"http://127.0.0.1:\"\$1\"/debug/pprof/profile?seconds=1\" }'"
	// 获取端口，拼接Prof端口
	token, ok := "", false
	for !ok {
		token, ok = GetToken()
	}
	var cmdProfile = ""
	switch ptype {
	case 1:
		cmdProfile = cpuProfileCmd(duration, filename, token)
	case 2:
		cmdProfile = heapProfileCmd(filename, token)
	case 3:
		cmdProfile = traceProfileCmd(filename, token)
	case 4:
		cmdProfile = blockCmd(filename, token)
	case 5:
		cmdProfile = mutexCmd(filename, token)
	case 6:
		cmdProfile = routineCmd(filename, token)
	default:
		return
	}

	fmt.Println("Perf Command:\n", cmdProfile)
	err := runCommand(cmdProfile)
	if err != nil {
		log.Printf("runCommand failed with %s\n", err)
		return
	}
	if runCommand("which sz") != nil {
		fmt.Println("\n\nInstalling lrzsz:")
		err := runCommand("apt-get install lrzsz")
		if err != nil {
			log.Printf("runCommand failed with %s\n", err)
			return
		}
	}
	cmdDownload := downloadCmd(filename)
	fmt.Printf("\n\n\033[1;32;40m%s\n%s\033[0m\n", "File download command:", cmdDownload)
}
