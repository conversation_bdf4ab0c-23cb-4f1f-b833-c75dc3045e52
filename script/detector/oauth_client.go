package main

import (
	"fmt"
	"net/http"
)

const (
	tips = `You have not no auth,\n
	please open\n
	http://smm.shopeemobile.com/oauth2/token\n
	to get your token`
	errMsg = "server error"

	url       = "https://smm.shopeemobile.com/oauth2/auth"
	cookieKey = "_oauth2_proxy_mesos"
)

// DefaultOAuthClient default smm oauth client
var DefaultOAuthClient = NewOAuthClient()

// CheckAuth smm oauth token check auth
func CheckAuth(cookieVal string) (bool, string) {
	return DefaultOAuthClient.ChckAuth(cookieVal)
}

// NewOAuthClient create smm oauth client
func NewOAuthClient() OAuthClient {
	return OAuthClient{
		client: http.Client{},
	}
}

// OAuthClient smm oauth client
type OAuthClient struct {
	client http.Client
}

// CheckAuth smm oauth token check auth
func (o *OAuthClient) ChckAuth(cookieVal string) (bool, string) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return false, errMsg
	}
	cookie := &http.Cookie{
		Name:  cookieKey,
		Value: cookieVal,
	}
	req.AddCookie(cookie)

	resp, err := o.client.Do(req)
	if err != nil {
		return false, errMsg
	}
	if resp.StatusCode != 200 && resp.StatusCode != 202 {
		fmt.Println(resp.StatusCode, resp)
		return false, tips
	}
	return true, ""
}
