package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"os/user"
)

var (
	smcConf = home() + "/.smc_config.json"
	smcEnv  = "live"
)

func home() string {
	user, err := user.Current()
	if nil == err {
		return user.HomeDir
	}
	return "~"
}

type smc struct {
	Tokens  map[string]string `json:"tokens"`
	canDump bool
}

func (s *smc) load() bool {
	if _, err := os.Stat(smcConf); os.IsNotExist(err) {
		return false
	}
	file, err := ioutil.ReadFile(smcConf)
	if err != nil {
		return false
	}
	if err := json.Unmarshal(file, s); err != nil {
		return false
	}
	return true
}

func (s *smc) dump() bool {
	if !s.canDump {
		return false
	}
	smcBytes, err := json.Marshal(s)
	if err != nil {
		return false
	}
	ioutil.WriteFile(smcConf, smcBytes, 0777)
	return true
}

func (s *smc) read() bool {
	fmt.Printf("\033[1;31;40m%s\033[0m\n", `You have not login,please open
http://smm.shopeemobile.com/oauth2/token
to get your token and type it at here`)
	token := ""
	fmt.Scanln(&token)
	s.Tokens = make(map[string]string)
	s.Tokens[smcEnv] = token
	s.canDump = true
	return true
}

func (s *smc) check() bool {
	token, ok := s.Tokens[smcEnv]
	if !ok {
		return false
	}
	hasAuth, _ := CheckAuth(token)
	if !hasAuth {
		return false
	}
	return true
}

func GetToken() (string, bool) {
	s := &smc{}
	switch {
	case s.load() && s.check():
	case s.read() && s.check():
	default:
		return "", false
	}
	s.dump()
	return s.Tokens[smcEnv], true
}
